<template>
    <div class="projectBaseInfoContainer">
        <div style="display: flex; justify-content: space-between">
            <project-selector
                @input="handleChange"
                :statusDisabled="false"
                :projectStatus="projectStatus"
                :key="projectSelectorKey"
                :queryOnMount="isQueryOnMount"
            ></project-selector>
            <el-button
                v-if="fromScene"
                type="primary"
                style="height: 27px; margin-top: 17px; margin-right: 16px"
                @click="goback"
                >返回</el-button
            >
        </div>
        <el-tabs v-model="activeName" class="tabs">
            <el-tab-pane label="仪表盘" name="instrumentPanel">
                <el-card class="milestone"
                    ><Milestone :projectId="projectId"></Milestone></el-card
            ></el-tab-pane>
            <el-tab-pane label="立项信息" name="baseInfo">
                <div class="projectInfoWrapper">
                    <el-card class="box-card">
                        <div slot="header" class="clearfix">
                            <span>市场立项信息</span>
                            <el-button
                                type="primary"
                                style="float: right"
                                @click="edit('market')"
                                v-show="showEditButton"
                                >编辑</el-button
                            >
                        </div>
                        <el-descriptions
                            :column="3"
                            size="small"
                            labelClassName="labelClass"
                        >
                            <el-descriptions-item
                                v-if="
                                    projectSpecies ===
                                        PROJECT_CLASSFICATION[1] ||
                                    projectSpecies === PROJECT_CLASSFICATION[2]
                                "
                                label="市场需求导入ID"
                                >{{
                                    marketInfo.marketDemandImportID
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="项目名称">{{
                                marketInfo.projectName
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                label="市场决议函下达时间"
                                v-if="
                                    projectSpecies ===
                                        PROJECT_CLASSFICATION[1] ||
                                    projectSpecies === PROJECT_CLASSFICATION[2]
                                "
                                >{{
                                    marketInfo.marketResoluteIssueDate
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="产品类别">{{
                                marketInfo.productCategory
                            }}</el-descriptions-item>
                            <el-descriptions-item label="市场定位">{{
                                marketInfo.marketPositioning
                            }}</el-descriptions-item>
                            <el-descriptions-item label="所属产品线">{{
                                marketInfo.productLine
                            }}</el-descriptions-item>
                            <el-descriptions-item label="细分产品线">{{
                                marketInfo.subProductLine
                            }}</el-descriptions-item>
                            <el-descriptions-item label="立项类型">{{
                                marketInfo.approvalType
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                v-if="
                                    projectSpecies ===
                                        PROJECT_CLASSFICATION[1] ||
                                    projectSpecies === PROJECT_CLASSFICATION[2]
                                "
                                label="需求类型"
                                >{{
                                    marketInfo.demandCategory
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item
                                v-if="
                                    projectSpecies === PROJECT_CLASSFICATION[2]
                                "
                                label="产品类型"
                                >{{
                                    marketInfo.productType
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="公司立项等级">{{
                                marketInfo.approvalGrade
                            }}</el-descriptions-item>
                            <el-descriptions-item label="项目经理">{{
                                projectManager
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                v-if="
                                    projectSpecies === PROJECT_CLASSFICATION[1]
                                "
                                label="排队未启动原因"
                                >{{
                                    marketInfo.queueNotStartedReason
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item
                                v-if="projectStatus === '已取消'"
                                label="项目已取消原因"
                                >{{
                                    marketInfo.projectCancelReason
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item
                                v-if="marketInfo.approvalType === '开发立项'"
                                label="关联预研时市场需求"
                                >{{
                                    marketInfo.assMarketStoryImportId
                                }}</el-descriptions-item
                            >
                        </el-descriptions>
                    </el-card>
                    <el-card
                        v-if="projectSpecies === PROJECT_CLASSFICATION[2]"
                        class="box-card"
                        style="margin-top: 20px"
                    >
                        <div slot="header" class="clearfix">
                            <span>项目立项信息</span>
                        </div>
                        <el-descriptions :column="3">
                            <el-descriptions-item label="OA项目任务书ID">{{
                                projectInfo.OAProjectTaskId
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                label="任务书类型"
                                v-if="!isSoftware"
                                >{{
                                    projectInfo.taskBookType
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item
                                :label="
                                    isSoftware ? '项目开始时间' : '项目启动时间'
                                "
                                >{{
                                    projectInfo.startDate
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="项目编号">{{
                                projectInfo.projectNumber
                            }}</el-descriptions-item>
                            <el-descriptions-item label="需求导入主体">{{
                                projectInfo.demandImportSubject
                            }}</el-descriptions-item>
                            <el-descriptions-item label="项目级别">{{
                                projectInfo.projectControlLevel
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                label="项目控制等级"
                                v-if="!isSoftware"
                                >{{
                                    projectInfo.projectControlLevel
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="项目技术难度">{{
                                projectInfo.technicalComplexity
                            }}</el-descriptions-item>
                            <el-descriptions-item label="项目规模">{{
                                projectInfo.projectScale
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                label="开发流程"
                                v-if="!isSoftware"
                                >{{
                                    projectInfo.developmentProcess
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="项目归属">{{
                                projectInfo.projectOwnership
                            }}</el-descriptions-item>
                            <el-descriptions-item
                                label="生产归属"
                                v-if="!isSoftware"
                                >{{
                                    projectInfo.productionOwnership
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item
                                label="产品型号"
                                v-if="!isSoftware"
                                >{{
                                    projectInfo.productModel
                                }}</el-descriptions-item
                            >
                            <el-descriptions-item label="产品经理">{{
                                productManager
                            }}</el-descriptions-item>
                            <el-descriptions-item label="禅道中对应的项目">{{
                                zendaoProjectName
                            }}</el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector.vue';
import { CONSTANTS } from '@/constants';
import Milestone from './components/Milestone.vue';

const { PROJECT_CLASSFICATION } = CONSTANTS;
// 市场立项信息的默认值
const defaultMarketInfo = () => {
    return {
        // 禅道项目ID
        marketDemandImportID: '',
        // 项目名称
        projectName: '',
        // 产品类别
        productCategory: '',
        // 市场定位
        marketPositioning: '',
        // 所属产品线
        productLine: '',
        // 细分产品线
        subProductLine: '',
        // 立项类型
        approvalType: '',
        // 需求类别
        demandCategory: '',
        // 产品类型
        productType: '',
        // 公司立项等级
        approvalGrade: '',
        // 项目经理
        projectManager: '',
        // 排队未启动原因
        queueNotStartedReason: ''
    };
};
// 项目立项信息的默认值
const defaultProjectInfo = () => {
    return {
        // 关联预研立项市场需求导入编号
        assMarketStoryImportId: '',
        // OA项目任务书ID
        OAProjectTaskId: '',
        // 项目编号
        projectNumber: '',
        // 需求导入主体
        demandImportSubject: '',
        // 项目控制等级
        projectControlLevel: '',
        // 项目技术难度
        technicalComplexity: '',
        // 项目规模
        projectScale: '',
        // 开发流程
        developmentProcess: '',
        // 项目归属
        projectOwnership: '',
        // 生产归属
        productionOwnership: '',
        // 产品型号
        productModel: '',
        // 产品经理
        productManager: '',
        // 禅道ID
        zendaoProjectId: ''
    };
};
export default {
    name: 'BaseInfo',
    components: { ProjectSelector, Milestone },
    data() {
        return {
            // 项目ID
            projectId: this.$route?.query?.id || '',
            // 市场立项信息
            marketInfo: defaultMarketInfo(),
            // 项目立项信息
            projectInfo: defaultProjectInfo(),
            // 项目归类
            projectSpecies: '',
            // 项目状态
            projectStatus: '',
            // 项目来源
            projectSource: '',
            // 禅道项目名称
            zendaoProjectName: '',
            // 项目经理
            projectManager: '',
            // 产品经理
            productManager: '',
            // 是否已经查询过
            alreadyQuery: false,
            // 挂载时是否查询
            isQueryOnMount: false,
            // 是否有编辑权限
            hasPermission:
                this.$store.state.permission.btnDatas.includes('edit_project'),
            // 常量
            CONSTANTS,
            // 项目归类
            PROJECT_CLASSFICATION,
            // 获取项目信息
            getProjectDetail:
                this.$service.project.projectInfo.getProjectDetail,
            projectSelectorKey: 0,
            // 当前页签
            activeName: 'instrumentPanel'
        };
    },
    computed: {
        // 是否显示编辑按钮
        showEditButton() {
            return this.projectId && this.hasPermission;
        },
        fromScene() {
            return !!this.$route?.query?.from;
        },
        // 是否是软件的项目
        isSoftware() {
            return this.projectSource === 'PLOUTO';
        }
    },
    created() {
        // 传入id进行查询
        if (this.$route?.query?.id) {
            this.projectId = this.$route?.query?.id;
            this.queryProjectDetail();
        } else {
            // 如果没有id,则挂载时利用缓存的值进行查询
            this.isQueryOnMount = true;
        }
    },
    methods: {
        /**
         * 选择后的回调
         * @param {Array} value  选中的值
         */
        handleChange(value) {
            // 有id的情况只会查询一次，不需要再次查询
            if (this.projectId && !this.alreadyQuery) {
                return;
            }
            this.projectId = value[value.length - 1];
            this.queryProjectDetail();
        },
        /**
         * 根据项目ID获取项目信息
         */
        queryProjectDetail() {
            this.getProjectDetail({ projectId: this.projectId })
                .then((result) => {
                    if (result.head.code !== '000000') {
                        this.$message.error(result.head.message);
                        return;
                    }
                    let res;
                    if (result && result.body) {
                        res = result.body;
                    }
                    this.marketInfo = {
                        marketDemandImportID:
                            res?.projectEx?.marketStoryImportFlowInstanceNo,
                        projectName: res?.project?.projectName,
                        productCategory: res?.project?.productClassification,
                        marketPositioning: res?.projectEx?.marketLocate,
                        productLine: res?.project?.productLine,
                        subProductLine: res?.project?.subProductLine,
                        approvalType: res?.project?.projectApproval,
                        demandCategory: res?.project?.storyType,
                        productType: res?.project?.productType,
                        approvalGrade: res?.projectEx?.approvalGrade,
                        projectManager: res?.project?.projectManager,
                        queueNotStartedReason:
                            res?.projectEx?.queuingNotStartingReason,
                        projectCancelReason: res?.project?.projectCancelReason,
                        marketResoluteIssueDate:
                            res?.projectEx?.marketResoluteIssueDate,
                        assMarketStoryImportId:
                            res?.projectEx?.assMarketStoryImportId
                    };
                    this.projectInfo = {
                        OAProjectTaskId: res?.projectEx?.oaProjectTaskId,
                        taskBookType: res?.projectEx?.taskBookType,
                        startDate: res?.project?.startDate,
                        projectNumber: res?.project?.projectNumber,
                        demandImportSubject:
                            res?.projectEx?.storyImportMainBody,
                        projectControlLevel:
                            res?.projectEx?.projectControlScale,
                        technicalComplexity:
                            res?.projectEx?.projectTechnicalDifficulty,
                        projectScale: res?.projectEx?.projectScale,
                        developmentProcess: res?.projectEx?.developFlow,
                        projectOwnership: res?.projectEx?.projectAttribution,
                        productionOwnership: res?.projectEx?.produceAttribution,
                        productModel: res?.projectEx?.productModeNo,
                        productManager: res?.project?.productManager,
                        zendaoProjectId: res?.project?.proProjectId
                    };
                    // 项目来源：PLOUTO为纯软件,其他为硬件和软硬混合
                    this.projectSource = res?.project?.projectSource;

                    this.projectSpecies = res?.project?.projectSpecies;
                    this.zendaoProjectName = res?.proProjectName;
                    this.projectManager = res?.projectManager;
                    this.productManager = res?.productManager;
                    const value = [this.marketInfo.productLine, this.projectId];
                    this.$store.dispatch('project/changeProject', value);
                    const projectInfoStore = {
                        projectName: this.marketInfo.projectName,
                        proProjectId: res?.project?.proProjectId,
                        projectStatus: res?.project?.projectStatus
                    };
                    this.$store.dispatch(
                        'project/changeProjectInfo',
                        projectInfoStore
                    );
                    this.projectStatus = res?.project?.projectStatus;
                    // 避免key无限更新
                    if (!this.alreadyQuery) {
                        this.alreadyQuery = true;
                        // 更新key让级联组件重新渲染,否则状态与值不会更新
                        this.projectSelectorKey += 1;
                    }
                })
                .catch((err) => {
                    console.error(err.message);
                });
        },
        /**
         * 编辑项目信息
         * @param {String} type 项目类型
         */
        edit(type) {
            if (!this.projectId) return;
            this.$router.push({
                path: '/project/editProject',
                query: {
                    id: this.projectId,
                    type
                }
            });
        },
        /**
         * 返回
         */
        goback() {
            history.go(-1);
        }
    }
};
</script>

<style lang="scss" scoped>
.flexCenter {
    display: flex;
    justify-content: center;
}
.projectBaseInfoContainer {
    height: 100%;
    overflow-y: scroll;
}
.projectInfoWrapper {
    padding: 16px;
}
.el-descriptions {
    margin: 8px;
    ::v-deep .el-descriptions-item__label {
        width: 140px;
        font-size: 14px;
        font-weight: 600;
    }
    ::v-deep .el-descriptions-item__content {
        font-size: 14px;
    }
}
.box-card {
    border-radius: 8px;
}

.el-card .el-descriptions {
    margin-top: 16px;
}

.el-descriptions-item {
    padding-bottom: 16px;
}

.clearfix::after {
    display: block;
    clear: both;
    content: '';
}

.clearfix span {
    line-height: 32px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.tabs {
    margin: 10px 10px 0 16px;
}
.milestone {
    margin-bottom: 20px;
}
</style>
