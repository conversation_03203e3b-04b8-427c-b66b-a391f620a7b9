import { CONSTANTS } from '@/constants';

const { COLOR_LIST } = CONSTANTS;

const handlePieData = (data, typeList) => {
    return typeList.map((i) => {
        let value = data.find((el) => el.xdata === i)?.total;
        if (!value) {
            value = 0;
        }
        return {
            value,
            name: i
        };
    });
};

// 正常预算，预算超概算比例
export const getOverEstimateChartoption = (data) => {
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}: ${
                    params.value
                } 个(${params.percent.toFixed(1)}%)`;
            }
        },
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: '85%',
                center: ['50%', '50%'],
                data: [
                    {
                        value: data.regularBudgetProjectTotal,
                        name: '正常预算项目'
                    },
                    {
                        value: data.budgetUpEstProjectTotal,
                        name: '预算超概算项目'
                    }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};

// 超概算项目分布：饼图
export const getOverEstimateDistributionChartoption = (data, productLine) => {
    const typeList = productLine.map((i) => i.label);
    const options = handlePieData(data, typeList);
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}: ${
                    params.value
                } 个(${params.percent.toFixed(1)}%)`;
            }
        },
        color: COLOR_LIST,
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: '85%',
                center: ['50%', '50%'],
                data: options,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};
