<template>
    <div class="container">
        <div class="view">
            <el-container>
                <el-aside>
                    <role-list
                        ref="roleList"
                        :is-enable="isEnable"
                        :select-role="currentRole"
                        @role-node-click="roleNodeClick"
                        @role-add-click="showAddRolePage"
                    />
                </el-aside>
                <el-main>
                    <div v-if="!isUnAssign" class="header">
                        {{ $t('systemManagement.role.roleTitle') }}
                    </div>
                    <div height="auto">
                        <div v-if="!isUnAssign" class="role-detail">
                            <div class="label">
                                {{
                                    $t(
                                        'systemManagement.role.columnName.roleName'
                                    )
                                }}：{{ currentRole.roleName }}
                                <i
                                    :title="
                                        $t('systemManagement.role.tooltip.look')
                                    "
                                    class="fa fa-eye edit-icon"
                                    @click="showRolePage(currentRole, true)"
                                />
                                <i
                                    v-if="
                                        currentRole.roleId !== '-1' &&
                                        currentRole.canOperate === 'true'
                                    "
                                    v-permission="['role_information_edit']"
                                    :title="
                                        $t('systemManagement.role.tooltip.edit')
                                    "
                                    class="fa fa-pencil edit-icon"
                                    @click="showRolePage(currentRole, false)"
                                />
                                <i
                                    v-if="
                                        currentRole.roleType === 1 &&
                                        currentRole.canOperate === 'true'
                                    "
                                    v-permission="['role_information_delete']"
                                    :title="
                                        $t(
                                            'systemManagement.role.tooltip.delete'
                                        )
                                    "
                                    class="fa fa-trash-o edit-icon"
                                    @click="showRoleDeletePage(currentRole)"
                                />
                                <!-- 系统内置的角色并且不是超级管理员，有恢复默认权限功能 -->
                                <i
                                    v-if="
                                        currentRole.roleType === 2 &&
                                        currentRole.roleId !== '-1'
                                    "
                                    v-permission="['role_information_refresh']"
                                    :title="
                                        $t(
                                            'systemManagement.role.tooltip.reset'
                                        )
                                    "
                                    class="fa fa-undo edit-icon"
                                    @click="showResetDialog(currentRole)"
                                />
                            </div>
                            <div class="label">
                                {{
                                    $t(
                                        'systemManagement.role.columnName.department'
                                    )
                                }}：{{ currentRole.orgNames }}
                            </div>
                        </div>
                        <div class="header">
                            <span class="header__title">{{
                                $t('systemManagement.listTitle')
                            }}</span>
                            <span class="header__body">
                                <el-input
                                    v-model="listQuery.queryParam"
                                    :placeholder="
                                        $t(
                                            'systemManagement.role.placeholder.userFilter'
                                        )
                                    "
                                    size="normal"
                                    class="query-input"
                                    @keyup.enter.native="getUserList"
                                >
                                    <i
                                        slot="suffix"
                                        class="fa fa-search el-input__icon"
                                        @click="getUserList"
                                    />
                                </el-input>
                            </span>
                            <div class="header__btns">
                                <el-button
                                    v-if="isUnAssign"
                                    v-permission="['role_manage_assign_roles']"
                                    icon="fa fa-plus-square-o"
                                    type="primary"
                                    class="margin-left"
                                    @click="showAssignRole"
                                    >{{
                                        $t('systemManagement.role.assignRole')
                                    }}</el-button
                                >
                                <el-button
                                    v-if="!isUnAssign"
                                    v-permission="['role_manage_add_user']"
                                    icon="fa fa-plus-square-o"
                                    type="primary"
                                    class="margin-left"
                                    @click="showAddUserRelation"
                                    >{{
                                        $t('systemManagement.role.addUser')
                                    }}</el-button
                                >
                                <el-button
                                    v-if="!isUnAssign"
                                    v-permission="['role_manage_remove_users']"
                                    icon="fa fa-trash-o"
                                    type="primary"
                                    class="margin-left"
                                    @click="showBatchDeleteUserRelation"
                                    >{{
                                        $t('systemManagement.role.removeUser')
                                    }}</el-button
                                >
                            </div>
                        </div>
                    </div>
                    <el-table
                        id="roleTable"
                        ref="tableUserList"
                        :header-cell-style="{ background: '#F5F6FA' }"
                        :data="dataUserList"
                        border
                    >
                        <el-table-column
                            type="selection"
                            width="55"
                            align="left"
                        />

                        <el-table-column
                            :label="
                                $t('systemManagement.role.columnName.userName')
                            "
                        >
                            <template slot-scope="{ row }">
                                <span>{{ row.userName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            :label="
                                $t('systemManagement.role.columnName.orgName')
                            "
                            align="left"
                        >
                            <template slot-scope="{ row }">
                                <span>{{ row.orgNames }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            :label="
                                $t('systemManagement.role.columnName.phone')
                            "
                            align="left"
                        >
                            <template slot-scope="{ row }">
                                <span>{{ row.phone }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            :label="
                                $t('systemManagement.role.columnName.isLock')
                            "
                            align="left"
                        >
                            <template slot-scope="{ row }">
                                <span
                                    v-if="row.isLock === 1"
                                    class="green-color"
                                    >{{
                                        $t('systemManagement.role.enable')
                                    }}</span
                                >
                                <span v-else class="red-color">{{
                                    $t('systemManagement.role.disable')
                                }}</span>
                            </template>
                        </el-table-column>
                    </el-table>

                    <el-pagination
                        class="right"
                        background
                        for="pagination"
                        :current-page.sync="listQuery.pageNum"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size.sync="listQuery.pageSize"
                        layout="total, prev, pager, next,sizes,  jumper"
                        :total="totalCount"
                        @size-change="getUserList"
                        @current-change="getUserList"
                    />
                </el-main>
            </el-container>

            <!-- 分配角色 -->
            <assign-role
                v-if="assignRoleInfo.isShow"
                :show.sync="assignRoleInfo.isShow"
                :user-list="assignRoleInfo.selectUserList"
                @save-finish="addUserRelationFinish"
            />

            <!-- 给角色添加用户 -->
            <add-user-relation
                v-if="addUserRelationInfo.isShow"
                :show.sync="addUserRelationInfo.isShow"
                :role-id="currentRole.roleId"
                :role-name="currentRole.roleName"
                @save-finish="addUserRelationFinish"
            />
        </div>

        <!-- 角色编辑、查看 -->
        <role-list-edit-info
            v-if="roleEditInfo.isShow"
            :show.sync="roleEditInfo.isShow"
            :edit="roleEditInfo.isEdit"
            :is-view="roleEditInfo.isView"
            :form-data="roleEditInfo.formData"
            :dataPermission="dataPermission"
            :orgList="orgList"
            :checkedKeys="checkedKeys"
            @view-save-finish="roleSaveFinish"
            @update-tree="updateTree"
        />
    </div>
</template>

<script>
import list from '../../mixins/list';
import AddUserRelation from './components/AddUserRelation.vue';
import AssignRole from './components/AssignRole.vue';
import RoleList from './components/RoleList.vue';
import RoleListEditInfo from './components/RoleListEditInfo.vue';

export default {
    name: 'Role',
    components: { RoleList, RoleListEditInfo, AssignRole, AddUserRelation },
    mixins: [list],
    data() {
        return {
            // 查询、分页表单
            listQuery: {
                roleId: '',
                // 查询参数
                queryParam: '',
                // 每页记录数
                pageSize: 10,
                // 当前页面
                pageNum: 1
            },
            // 数据总记录数
            totalCount: 0,
            // 列表数据对象
            dataUserList: [],
            // 当前选择的角色
            currentRole: {
                roleId: '',
                roleName: '',
                roleDesc: '',
                orgNames: ''
            },
            // 角色编辑相关变量
            roleEditInfo: {
                isShow: false,
                isEdit: false,
                // 编辑数据
                formData: {
                    roleId: '',
                    roleName: '',
                    roleDesc: '',
                    belongOrgIds: '',
                    oldParam: {},
                    orgNames: ''
                }
            },
            // 分配角色相关变量
            assignRoleInfo: {
                isShow: false,
                // 当前选中的用户,用于传给分配窗口组件
                selectUserList: []
            },
            // 角色添加关联用户
            addUserRelationInfo: {
                isShow: false
            },
            isEnable: false,
            // 角色权限树数据
            dataPermission: [],
            checkedKeys: [],
            orgList: []
        };
    },
    computed: {
        // 是否未分配角色列表
        isUnAssign() {
            return this.currentRole.roleId === '';
        }
    },
    created() {
        this.getUserList();
    },
    methods: {
        // 获取角色对应用户列表
        getUserList() {
            this.$service.systemManagement
                .getRoleUserList(this.listQuery)
                .then((response) => {
                    if (response.head.code === '000000') {
                        // 判断角色信息是否为空。避免角色信息在个别情况下不刷新问题
                        if (
                            response.body.roleInfo &&
                            response.body.roleInfo.roleId
                        ) {
                            this.currentRole = response.body.roleInfo;
                            this.currentRole.orgNames = this.currentRole.orgInfo
                                .map((item) => item.orgName)
                                .join(',');
                        } else {
                            // 如果未查询到角色信息，则设置为初始值
                            Object.assign(
                                this.currentRole,
                                this.$options.data().currentRole
                            );
                        }
                        this.totalCount = response.body.userPage.total;
                        this.dataUserList = response.body.userPage.list;
                        // 分页信息与服务器保持一致
                        this.listQuery.pageNum = response.body.userPage.pageNum;
                        this.listQuery.pageSize =
                            response.body.userPage.pageSize;
                        this.isEnable = true;
                    } else {
                        const { code } = response.head;
                        const msg = `systemManagement.bgReturnError[${code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                })
                .catch(() => {
                    // 解决接口查询失败，删除的角色信息依然显示。并且可以点击编辑问题
                    Object.assign(
                        this.currentRole,
                        this.$options.data().currentRole
                    );
                });
        },
        // 调用接口删除数据
        postDeleteUserRelation(userIds, roleIds, roleName, userNames) {
            this.$service.systemManagement
                .postRomveUserRoleRelation({
                    roleName,
                    userIds,
                    roleIds,
                    userNames
                })
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.role.message.deleteSuccess'
                            ),
                            type: 'success'
                        });
                        this.getUserList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message:
                                this.$t(msg) ||
                                this.$t(
                                    'systemManagement.role.message.deleteFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 获取表格选中的用户信息列表
        getSelectUserList() {
            const rows = this.$refs.tableUserList.selection;
            if (!rows || rows.length === 0) {
                this.$message({
                    message: this.$t(
                        'systemManagement.role.message.userListEmptySelect'
                    ),
                    type: 'error'
                });
                return null;
            }
            return rows;
        },
        // 角色列表点击事件
        roleNodeClick(data) {
            // 解决右侧角色区域删除，信息刷新问题
            if (data === null || data === undefined) {
                Object.assign(
                    this.currentRole,
                    this.$options.data().currentRole
                );
                this.totalCount = 0;
                this.dataUserList = [];
                return;
            }

            Object.assign(this.listQuery, this.$options.data().listQuery, {
                pageSize: this.listQuery.pageSize
            });
            this.listQuery.roleId = data.roleId || '';

            this.getUserList();
        },
        // 获取角色信息及权限树
        getRoleInfoAndTree(roleId) {
            const params = {
                sysType: '0',
                roleId
            };
            return new Promise((resolve, reject) => {
                this.$service.systemManagement
                    .getRoleInfoAndTree(params)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            resolve(response.body);
                        }
                    });
            });
        },
        /**
         * 获取当前所有选中的权限
         * @param {*} treeNode 树节点
         * @return {*} {Array} 不存在时返回空数组
         */
        getTreeNode(treeNode) {
            if (!treeNode) {
                return [];
            }
            treeNode.forEach((node) => {
                if (node.checked) {
                    this.checkedKeys.push(node.permissionId);
                }
                if (
                    node.childrenPermissions &&
                    node.childrenPermissions.length > 0
                ) {
                    this.getTreeNode(node.childrenPermissions);
                }
            });
        },
        // 显示新增角色弹框
        showAddRolePage() {
            this.getRoleInfoAndTree('').then((res) => {
                this.dataPermission = res.permissionInfo;
                this.roleEditInfo.isView = false;
                this.roleEditInfo.isEdit = false;
                this.roleEditInfo.formData = {
                    roleId: '',
                    roleName: '',
                    roleDesc: '',
                    belongOrgIds: '',
                    oldParam: {},
                    orgNames: ''
                };
                this.checkedKeys = [];
                this.roleEditInfo.isShow = true;
            });
        },
        // 新增弹框更新权限树
        updateTree() {
            this.getRoleInfoAndTree('').then((res) => {
                this.dataPermission = res.permissionInfo;
            });
        },
        // 查看、编辑角色弹框
        showRolePage(currentRole, flag) {
            this.checkedKeys = [];
            this.getRoleInfoAndTree(currentRole.roleId).then((res) => {
                Object.assign(this.roleEditInfo.formData, currentRole);
                this.roleEditInfo.formData.oldParam = JSON.parse(
                    JSON.stringify(currentRole)
                );
                this.dataPermission = res.permissionInfo;
                this.orgList = res.orgInfo;
                this.getTreeNode(res.permissionInfo);
                // 重置查看状态
                this.roleEditInfo.isView = flag;
                this.roleEditInfo.isEdit = !flag;
                this.roleEditInfo.isShow = true;
            });
        },
        // 显示角色角色提示
        showRoleDeletePage(data) {
            this.$refs.roleList.showDeleteRole(data);
        },
        // 显示重置权限确认框
        showResetDialog(currentRole) {
            this.$confirm(
                this.$t('systemManagement.role.defaultPermissionPrompt'),
                this.$t('systemManagement.messageNotice.drawer.tipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                // 调用权限重置的接口
                const data = {
                    roleId: currentRole.roleId,
                    roleName: currentRole.roleName
                };
                this.$service.systemManagement
                    .putPermissionReset(data)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.$message.success(
                                this.$t('systemManagement.role.reSetSuccess')
                            );
                            // 刷新页面
                            const tag = {
                                fullPath: '/systemManagement/role',
                                hash: '',
                                name: 'Role',
                                path: '/systemManagement/role',
                                status: 0,
                                title: 'role'
                            };
                            this.refreshSelectedTag(tag);
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        },
        // 显示分配角色窗口
        showAssignRole() {
            const rows = this.getSelectUserList();
            if (rows === null) {
                return;
            }

            this.assignRoleInfo.selectUserList = rows;
            this.assignRoleInfo.isShow = true;
        },
        // 显示添加用户接口
        showAddUserRelation() {
            this.addUserRelationInfo.isShow = true;
        },
        // 批量移除用户关系
        showBatchDeleteUserRelation() {
            const rows = this.getSelectUserList();
            if (rows === null) {
                return;
            }

            // 用于删除提示，显示删除名称
            const userNameList = rows.map((item) => {
                return item.userName;
            });
            const userIdList = rows.map((item) => {
                return item.userId;
            });
            this.$confirm(
                this.$t('systemManagement.role.message.removeUserTip', [
                    userNameList.join('、'),
                    this.currentRole.roleName
                ]),
                this.$t('systemManagement.role.message.deleteTipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.postDeleteUserRelation(
                    userIdList,
                    [this.currentRole.roleId],
                    this.currentRole.roleName,
                    userNameList.join(',')
                );
            });
        },
        // 角色编辑完成后回调
        roleSaveFinish() {
            if (this.roleEditInfo.isEdit) {
                this.getUserList();
            }
            this.$refs.roleList.getRoleList();
        },
        addUserRelationFinish(isSuccess) {
            if (isSuccess) {
                this.getUserList();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.role-detail {
    width: 100%;
    background: #f5f6fa;
    box-sizing: border-box;
    padding: 20px;
    margin-bottom: 20px;
    .label {
        font-size: 14px;
        font-weight: 400;
        color: #676c6f;
        margin-bottom: 20px;
        word-break: break-all;
        &:last-child {
            margin-bottom: 0;
        }
    }
    .edit-icon {
        margin-left: 20px;
        cursor: pointer;
        color: #3370ff;
    }
}
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.red-color {
    color: #ff4949;
}
.green-color {
    color: #13ce66;
}
</style>
