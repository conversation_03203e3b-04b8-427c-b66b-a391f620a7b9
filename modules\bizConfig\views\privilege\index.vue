<template>
    <div class="view">
        <department-selector :infoDisabled="true"></department-selector>
        <el-container class="container-privilege">
            <div class="main">
                <div class="flex">
                    <people-selector
                        class="people-selector"
                        :isMultipled="false"
                        placeholder="请输入人员"
                        @input="handleLevelTwoClick"
                        size="medium"
                    ></people-selector>
                    <div
                        class="update-button-container"
                        v-if="isPersonSelected"
                    >
                        <el-button
                            size="small"
                            type="primary"
                            @click="handleUpdateClick"
                            icon="el-icon-help"
                            >更新</el-button
                        >
                    </div>
                </div>
                <div v-if="isPersonSelected">
                    <el-tree
                        :data="priviTree"
                        show-checkbox
                        default-expand-all
                        node-key="id"
                        ref="tree"
                        highlight-current
                        :props="defaultProps"
                        class="tree-style"
                    >
                    </el-tree>
                </div>
                <div v-else class="privilege-tips">
                    <p>说明：请选择具体人员进行白名单设置</p>
                </div>
            </div>
        </el-container>
    </div>
</template>

<script>
import privilegeTree from './components/privilegeTree.vue';
import departmentSelector from 'department/components/departmentSelector';
import PeopleSelector from 'Components/PeopleSelector';

export default {
    name: 'Organization',
    components: { privilegeTree, departmentSelector, PeopleSelector },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            defaultProps: {
                children: 'projects',
                label: 'name'
            },
            priviTree: [],
            choosePeople: '',
            isPersonSelected: false
        };
    },
    methods: {
        // 查询右侧树
        async departNameLevel() {
            try {
                const res =
                    await this.$service.project.whiteMenu.getProjectLine();
                if (res.head.code === '000000') {
                    this.priviTree = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                this.$message.error('操作失败!');
            }
        },
        handleDropClick() {
            this.isPersonSelected = false;
        },
        // 选择一级显示提示信息
        handleLevelOneClick() {
            this.isPersonSelected = false;
        },
        // 点击名称查询勾选状态
        async handleLevelTwoClick(people) {
            await this.departNameLevel();
            this.isPersonSelected = true;
            this.choosePeople = people;
            const params = {
                account: people
            };
            try {
                const res = await this.$service.project.whiteMenu.getwhiteMenu(
                    params
                );
                if (res.head.code === '000000') {
                    const backendIds = res.body;
                    if (backendIds && backendIds.length > 0) {
                        backendIds.forEach((id) => {
                            this.setCheckedById(this.priviTree, id);
                        });
                    } else {
                        this.$refs.tree.setCheckedKeys([]);
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                this.$message.error('操作失败!');
            }
        },
        // 递归判断选中状态
        setCheckedById(nodes, backendId) {
            for (let i = 0; i < nodes.length; i++) {
                const node = nodes[i];
                if (node.id === backendId) {
                    // 如果当前节点的 id 等于 backendId，则勾选该节点
                    this.$refs.tree.setChecked(node, true);
                    break;
                } else if (node.projects) {
                    // 如果当前节点有子节点，则递归查找子节点
                    this.setCheckedById(node.projects, backendId);
                }
            }
        },
        // 点击更新按钮时触发的方法，获取所有被勾选的 ID
        handleUpdateClick() {
            const checkedNodes = this.$refs.tree.getCheckedNodes();
            const secondLevelIds = [];
            checkedNodes.forEach((node) => {
                const hasNoChildren =
                    !Array.isArray(node.projects) || node.projects.length === 0;
                if (hasNoChildren) {
                    secondLevelIds.push(node.id);
                }
            });
            const params = {
                account: this.choosePeople,
                projectIds: secondLevelIds
            };
            this.$service.project.whiteMenu
                .editwhiteMenu(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message.success(res.head.message);
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.aside-box {
    width: 25% !important;
}
.main {
    width: 100%;
    height: 100%;
    margin-left: 10px;
    padding: 20px;
    background-color: #ffffff;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.container-privilege {
    padding-top: 5px;
}
.privilege-tips {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #00c1de;
    font-family: fangsong;
    font-size: 24px;
    font-weight: bolder;
}
.update-button-container {
    margin-left: 10px;
}
.tree-style {
    overflow: auto;
    height: calc(100vh - 120px);
}
.people-selector {
    width: 400px;
}
</style>
