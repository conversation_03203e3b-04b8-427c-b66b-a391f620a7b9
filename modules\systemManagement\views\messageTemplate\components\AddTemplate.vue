<template>
  <el-dialog
    :title="params.title"
    :visible.sync="isShow"
    custom-class="template-add-dialog"
  >
    <el-form
      ref="templateForm"
      :model="params.data"
      label-position="right"
      label-width="auto"
      :rules="templateRules"
      :disabled="isDisabled"
      class="custom-form"
    >
      <div class="item-box">
        <el-form-item
          :label="
            $t(
              'systemManagement.messageTemplate.templateNumber'
            )
          "
          prop="messageTemplateNo"
        >
          <el-input
            v-model="params.data.messageTemplateNo"
            maxlength="30"
            :placeholder="$t('systemManagement.messageTemplate.placeholder.number')"
          />
        </el-form-item>
        <el-form-item
          :label="
            $t(
              'systemManagement.messageTemplate.templateName'
            )
          "
          prop="messageTemplateTitle"
        >
          <el-input
            v-model="params.data.messageTemplateTitle"
            maxlength="200"
            :placeholder="$t('systemManagement.messageTemplate.placeholder.name')"
          />
        </el-form-item>
      </div>
      <div class="item-box">
        <el-form-item
          :label="$t('systemManagement.messageTemplate.templateType')"
          prop="messageType"
        >
          <el-select
            v-model="params.data.messageType"
            clearable
            :popper-append-to-body="false"
            :placeholder="
              $t('systemManagement.messageTemplate.placeholder.templateType')
            "
          >
            <el-option
              v-for="item in templateType"
              :key="item.valueCode"
              :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)"
              :value="item.valueCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('systemManagement.messageTemplate.messageSendType')" prop="messageSendType">
          <el-select
            v-model="params.data.messageSendType"
            clearable
            :popper-append-to-body="false"
            :placeholder="
              $t('systemManagement.messageTemplate.placeholder.messageType')
            "
          >
            <el-option
              v-for="item in templateChannel"
              :key="item.valueCode"
              :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)"
              :value="item.valueCode"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="item-box">
        <el-form-item :label="$t('systemManagement.messageTemplate.sendingPlatform')" prop="messageSendTypePlatform">
          <el-select
            v-model="params.data.messageSendTypePlatform"
            clearable
            :popper-append-to-body="false"
            :placeholder="
              $t('systemManagement.messageTemplate.placeholder.platform')
            "
          >
            <el-option
              v-for="item in templateForm"
              :key="item.valueCode"
              :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)"
              :value="item.valueCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="platform-id" :label="$t('systemManagement.messageTemplate.platformId')" prop="templateId">
          <el-input
            v-model="params.data.templateId"
            maxlength="100"
            :placeholder="$t('systemManagement.messageTemplate.placeholder.platformId')"
            @keyup.enter.native="getList"
          />
        </el-form-item>
      </div>
      <el-form-item :label="$t('systemManagement.messageTemplate.column.templateContent')" prop="messageTemplateContent">
        <el-input
          v-model="params.data.messageTemplateContent"
          maxlength="5000"
          type="textarea"
          resize="none"
          show-word-limit
          :placeholder="$t('systemManagement.messageTemplate.placeholder.templateContent')"
        />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.messageTemplate.column.templateParamMap')" prop="messageTemplateParam">
        <el-input
          v-model="params.data.messageTemplateParam"
          class="param-map"
          maxlength="600"
          type="textarea"
          resize="none"
          show-word-limit
          :placeholder="$t('systemManagement.messageTemplate.placeholder.templateParamMap')"
        />
      </el-form-item>
    </el-form>
    <span v-if="operateType !== 'detail'" slot="footer">
      <el-button @click="cancel">{{
        $t("table.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">{{
        $t("table.confirm")
      }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
    name: 'AddTemplate',
    props: {
        params: {
            type: Object,
            default() {
                return {
                    data: {
                        messageTemplateNo: '',
                        messageTemplateTitle: '',
                        messageType: '',
                        messageSendType: '',
                        messageSendTypePlatform: '',
                        templateId: '',
                        messageTemplateContent: '',
                        messageTemplateParam: ''
                    }
                };
            }
        },
        show: {
            type: Boolean,
            default: false
        },
        // 操作组件的类型
        operateType: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 模板类型列表
            templateType: [],
            //  消息渠道列表
            templateChannel: [],
            // 发送平台
            templateForm: [],
            // 是否禁用表单编辑
            isDisabled: false
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        templateRules() {
            return {
                messageTemplateNo: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.number')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageTemplateTitle: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.name')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageType: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.templateType')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageSendType: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.messageType')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageSendTypePlatform: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.platform')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageTemplateId: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.platformId')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageTemplateContent: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.templateContent')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                messageTemplateParam: [
                    {
                        required: true,
                        message: `${this.$t('systemManagement.messageTemplate.placeholder.templateParamMap')}`,
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    mounted() {
        if (this.operateType === 'detail') {
            this.isDisabled = true;
        }
        // 获取模板类型
        this.getTemplateType();
        // 获取消息渠道
        this.getTemplateChannel();
        //  获取发送平台
        this.getTemplatePlatform();
    },
    methods: {
        // 获取模板类型数据
        getTemplateType() {
            this.$service.systemManagement
                .getMessageDictType()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.templateType = response.body;
                    } else {
                        this.templateType = [];
                    }
                });
        },
        // 获取消息渠道数据
        getTemplateChannel() {
            this.$service.systemManagement
                .getMessageChannel()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.templateChannel = response.body;
                    } else {
                        this.templateChannel = [];
                    }
                });
        },
        // 获取平台类型数据
        getTemplatePlatform() {
            this.$service.systemManagement
                .getMessagePlatformType()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.templateForm = response.body;
                    } else {
                        this.templateForm = [];
                    }
                });
        },
        //  取消弹框
        cancel() {
            this.isShow = false;
            this.clearItem();
        },
        // 关闭弹窗的回调
        clearItem() {
            this.isEdit = false;
            // 重置表单内容
            this.$refs['templateForm'].resetFields();
        },
        // 保存消息模板
        save() {
            this.$refs['templateForm'].validate((valid) => {
                if (valid) {
                    // 验证通过，判断是新增还是编辑
                    if (this.operateType === 'add') {
                        // 触发父元素的新增函数
                        this.$emit('addTemplate', this.params.data);
                    } else {
                        // 触发父元素的编辑函数
                        this.$emit('editTemplate', this.params.data);
                    }
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
  .custom-form{
      .item-box{
        display:flex;
        margin-bottom:20px;
        justify-content: space-between;
        .el-form-item--medium{
          width:50%;
        }
      }
  }
  ::v-deep .el-textarea.el-input--medium{
    height:120px;
    .el-textarea__inner{
        height:100%;
    }

  }
  ::v-deep .param-map.el-textarea.el-input--medium{
    height:60px;
    .el-textarea__inner{
        height:100%;
    }

  }
  ::v-deep .template-add-dialog.el-dialog{
      width:44% !important;
  }
  ::v-deep .is-disabled {
    .el-input__inner {
      color: #606266 !important;
      cursor: default;
    }
    .el-textarea__inner{
        color: #606266 !important;
        cursor: default;
    }
  }

</style>
