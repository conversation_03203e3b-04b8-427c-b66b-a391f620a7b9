<template>
    <div
        :style="{ padding: projectList.length > 0 ? '15px' : 0 }"
        id="SceneProductNodePopoverId"
        class="outer"
    >
        <div
            v-for="i in projectList"
            style="
                display: flex;
                width: 100%;
                padding: 0 10px;
                font-size: 24px;
                height: 70px;
            "
        >
            <el-badge
                :value="getBadgeValue(i.projectStatus, i.hrCost)"
                class="area-badge"
                :style="{
                    '--project-status-color': i.projectStatusColor
                }"
            >
                <div style="display: flex; font-size: 24px; padding: 5px">
                    <span
                        class="title-prefix"
                        :style="{ backgroundColor: i.strategyColor }"
                    >
                        {{ i.strategy }} </span
                    ><el-link
                        style="height: 40px; line-height: 40px; font-size: 24px"
                        type="primary"
                        @click="gotoProjectDetail(i.projectId)"
                        >{{ i.projectName }}</el-link
                    >
                </div>
            </el-badge>
        </div>
    </div>
</template>
<script>
import router from 'wtf-core-vue/src/router/index.js';

export default {
    name: 'ProjectList',
    props: {
        projectList: {
            type: Array,
            default: () => []
        }
    },
    methods: {
        /**
         * 跳转至项目详情页面
         * @param {String} projectId 项目编号
         */
        async gotoProjectDetail(projectId) {
            const valid = projectId;
            if (!valid) {
                this.$message.warning('该项目暂未录入系统');
                return;
            }
            router.push({
                path: '/project/baseInfo',
                query: {
                    id: projectId,
                    from: 'scene'
                }
            });
        },
        /**
         * 不显示除在研之外状态的人力
         * @param {String} projectStatus 项目状态
         * @param {String} hrCost 人力
         * @returns {String} 描述
         */
        getBadgeValue(projectStatus, hrCost) {
            if (projectStatus !== '在研') {
                return projectStatus;
            }
            return `${projectStatus}，投入${hrCost}人月`;
        }
    }
};
</script>
<style lang="scss" scoped>
.outer {
    width: 100%;
    overflow: auto;
    max-height: 400px;
}
.area-badge {
    margin-top: 8px;
    // 修改职称背景颜色
    ::v-deep .el-badge__content {
        background-color: var(--project-status-color);
        height: 35px;
        font-size: 20px;
        line-height: 35px;
        margin-left: 15px;
    }
}
.title-prefix {
    width: 40px;
    height: 40px;
    color: white;
    margin-right: 20px;
    border-radius: 50%;
    line-height: 40px;
    text-align: center;
}
</style>
