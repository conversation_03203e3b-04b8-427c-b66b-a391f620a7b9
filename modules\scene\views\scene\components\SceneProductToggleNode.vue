<template>
    <svg-icon icon-class="scene-toggle" class="scene-product-toggle" />
</template>
<script>
export default {
    name: 'SceneProductToggleNode',
    methods: {
        // 初始化数据
        initData() {
            const { showToggleNode } = this.$vnode.data.node.data;
            this.showToggleNode = showToggleNode;
        }
    }
};
</script>
<style lang="scss" scoped>
.scene-product-toggle {
    display: inline-block;
    width: 20px;
    height: 20px;
    cursor: pointer;
    background-color: #ffffff;
    padding: 0;
}
</style>
