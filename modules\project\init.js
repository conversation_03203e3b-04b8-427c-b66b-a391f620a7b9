/**
 * 项目模块初始化文件
 * 用于从 localStorage 加载持久化状态
 */
import {
    loadStateFromStorage,
    setupBeforeUnloadHandler
} from './utils/storeHelpers';

/**
 * 初始化项目模块，从 localStorage 加载持久化的状态
 * @param {Object} store Vuex store 实例
 */
export default function init(store) {
    try {
        // 确保project模块已注册
        if (store && store.state.project) {
            // 从 localStorage 加载数据到 store
            loadStateFromStorage(store);

            // 设置页面关闭前的处理程序，确保数据被保存
            setupBeforeUnloadHandler(store);
        } else {
            console.warn('Project module not registered in store');
        }
    } catch (error) {
        console.error('Error initializing project module:', error);
    }
}
