<template>
    <div class="container">
        <div class="container-top">
            <el-select
                v-model="firstDepartvalue"
                filterable
                placeholder="请选择一级部门"
                @change="handleFirstDepartChange"
                class="onelevel"
            >
                <el-option
                    v-for="item in firstDepart"
                    :key="item.orgCode"
                    :label="item.orgName"
                    :value="item.orgCode"
                    filterable
                >
                </el-option>
            </el-select>
        </div>
        <el-tree
            ref="orgTree"
            for="orgTree"
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            :current-node-key="currentNodeKey"
            default-expand-all
            highlight-current
            node-key="orgId"
            draggable
            :empty-text="$t('systemManagement.logger.noData')"
            @node-click="handleNodeClick"
        >
            <el-tooltip
                slot-scope="{ node, data }"
                :content="node.label"
                placement="right"
            >
                <span class="block">
                    <span class="block__text">{{ node.label }}</span>
                    <span class="block__btns">
                        <!-- 仅在一级节点时显示增加小组按钮 -->
                        <el-button
                            v-if="node.level === 1"
                            v-permission="['org_new_department']"
                            type="text"
                            size="mini"
                            icon="fa fa-plus-circle"
                            @click.stop="addOrg(data, 'add')"
                        />
                        <!-- 仅在第二级节点时显示删除小组按钮 -->
                        <el-button
                            v-if="node.level === 2"
                            v-permission="['org_delete_department']"
                            type="text"
                            size="mini"
                            icon="fa fa-trash-o"
                            @click.stop="delOrg(data)"
                        />
                        <!-- 仅在第二级节点时显示编辑小组按钮 -->
                        <el-button
                            v-if="node.level === 2"
                            v-permission="['org_edit_department']"
                            type="text"
                            size="mini"
                            icon="fa fa-pencil"
                            @click.stop="addOrg(data, 'edit')"
                        />
                    </span>
                </span>
            </el-tooltip>
        </el-tree>
        <el-dialog :title="editInfo.title" :visible.sync="editInfo.isShow">
            <el-form
                ref="dataForm"
                label-position="right"
                label-width="120px"
                :model="userEditDataInfo"
                :rules="userRules"
                class="custom-form"
            >
                <el-form-item label="小组名称" prop="teamName">
                    <el-input
                        v-model="userEditDataInfo.teamName"
                        placeholder="请输入小组名称"
                        autocomplete="text"
                        maxlength="20"
                    />
                </el-form-item>
                <el-form-item label="组长姓名" prop="teamLeaderId">
                    <el-select
                        v-model="userEditDataInfo.teamLeaderId"
                        filterable
                        clearable
                        placeholder="请选择组长姓名"
                    >
                        <el-option
                            v-for="item in groupLeaderData"
                            :key="item.loginName"
                            :label="item.employeeName"
                            :value="item.loginName"
                            filterable
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="组成员" prop="members">
                    <el-select
                        class="custom-select"
                        v-model="userEditDataInfo.members"
                        placeholder="请选择组成员"
                        filterable
                        multiple
                        clearable
                    >
                        <el-option
                            v-for="item in groupMembersData"
                            :key="item.loginName"
                            :label="item.employeeName"
                            :value="item.loginName"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="save" :disabled="isSave"
                    >确认</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';

const { TECH_CENTER_CODE } = CONSTANTS;
// 事件名称定义
const constEventName = {
    TREE_NODE_CLICK: 'org-node-click',
    TREE_EDIT_FINISH: 'org-edit-finish'
};

export default {
    props: {
        isEnable: {
            type: Boolean,
            dafault: false
        }
    },
    data() {
        return {
            firstDepart: [],
            firstDepartvalue: '',
            // 树结构数据
            treeData: [],
            // 弹窗信息
            userEditDataInfo: {
                teamName: '',
                teamLeaderId: '',
                members: []
            },
            groupLeaderData: [],
            groupMembersData: [],
            // 部门ID
            departmentCode: '',
            passData: {},
            editType: '',
            teamId: '',
            // 表单必填
            userRules: {
                teamName: [
                    {
                        required: true,
                        message: '请输入小组名称，最多20个字符',
                        trigger: 'blur'
                    }
                ],
                teamLeaderId: [
                    {
                        required: true,
                        message: '请选择组长姓名',
                        trigger: 'change'
                    }
                ],
                members: [
                    {
                        required: true,
                        message: '请选择组成员',
                        trigger: 'change'
                    }
                ]
            },
            isSave: false,

            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            // 当前选中的key
            currentNodeKey: '',
            // 编辑操作相关数据
            editInfo: {
                // 是否显示窗口a
                isShow: false,
                isEdit: false,
                title: ''
            }
        };
    },
    created() {
        this.getfirstDepart();
    },
    methods: {
        // 获取一级部门下拉列表
        getfirstDepart() {
            const data = {
                orgCode: TECH_CENTER_CODE
            };
            this.$service.department.group
                .getSubDepartment(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.firstDepart = res.body || [];
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        handleFirstDepartChange() {
            this.getOrgTree(this.firstDepartvalue);
        },
        // 获取机构树
        getOrgTree(value) {
            const params = {
                orgCode: value
            };
            this.$service.department.group
                .getSubDepartment(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.treeData = res.body.map((i) => {
                            return { ...i, children: [] };
                        });
                        if (
                            !this.currentNodeKey &&
                            this.treeData &&
                            this.treeData.length > 0
                        ) {
                            this.currentNodeKey = this.treeData[0].orgId || '';
                            this.$emit(
                                constEventName.TREE_NODE_CLICK,
                                this.treeData[0]
                            );
                        }
                        this.$nextTick(() => {
                            this.$refs.orgTree.setCurrentKey(
                                this.currentNodeKey
                            );
                            const curNode = this.$refs.orgTree.getCurrentNode();
                            this.$emit(
                                constEventName.TREE_EDIT_FINISH,
                                curNode,
                                this.editInfo.isEdit
                            );
                        });
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        async handleNodeClick(node, nodeValue) {
            try {
                if (nodeValue.level === 1) {
                    this.$emit('level-one-click');
                    this.loadThirdLevel(node);
                } else {
                    this.$emit('other-level-click');
                    await this.getGroupPeople(node.id);
                    this.$emit('pass-level-click', this.passData);
                }
            } catch (error) {
                console.error('操作失败', error);
            }
        },
        // 获取小组下成员信息
        async getGroupPeople(value) {
            // eslint-disable-next-line no-useless-catch
            try {
                const params = {
                    teamId: value
                };
                const res = await this.$service.department.group.getPeople(
                    params
                );
                if (res.head.code === '000000') {
                    this.passData = res.body;
                    this.userEditDataInfo.teamName = this.passData.teamName;
                    this.userEditDataInfo.teamLeaderId =
                        this.passData.teamLeaderId;
                    this.userEditDataInfo.members = this.passData.members;
                    this.$emit('pass-data', this.passData);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                throw error;
            }
        },
        // 获取部门下小组信息
        loadThirdLevel(node) {
            const params = {
                departmentCode: node.orgCode
            };
            this.$service.department.group.getGroupInfo(params).then((res) => {
                if (res.head.code === '000000') {
                    if (res.body.length === 0) {
                        this.$message.error('暂无数据');
                    }
                    const modifiedData = res.body.map((item) => ({
                        id: item.id,
                        orgCode: item.departmentCode,
                        teamLeaderId: item.teamLeaderId,
                        orgName: item.teamName,
                        members: item.members
                    }));
                    // 获取树节点对象
                    const target = this.treeData.find(
                        (item) => item.orgCode === node.orgCode
                    );
                    this.$nextTick(() =>
                        this.$set(target, 'children', modifiedData)
                    );
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },

        // 增加部门
        addOrg(data, type) {
            if (this.$refs.dataForm) {
                this.$refs.dataForm.resetFields();
            }
            this.editType = type;
            if (type === 'add') {
                this.userEditDataInfo.teamLeaderId = '';
                this.userEditDataInfo.teamName = '';
                this.userEditDataInfo.members = [];
                this.editInfo.title = '新增小组信息';
            } else {
                this.editInfo.title = '编辑小组信息';
                this.teamId = data.id;
                this.getGroupPeople(data.id);
            }
            this.departmentCode = data.orgCode;
            this.editInfo.isShow = true;
            this.departNameLevel(data);
        },
        // 确认
        save() {
            this.$refs.dataForm.validate((valid) => {
                if (valid) {
                    const params = Object.assign({}, this.userEditDataInfo, {
                        departmentCode: this.departmentCode
                    });
                    const departmentObject = { orgCode: this.departmentCode };
                    const serviceFunction =
                        this.editType === 'add'
                            ? 'postAddGroup'
                            : 'postEditGroup';
                    const successMessage =
                        this.editType === 'add' ? '新增成功' : '编辑成功';

                    params.id =
                        this.editType === 'edit' ? this.teamId || '' : '';

                    this.$service.department.group[serviceFunction](
                        params
                    ).then((res) => {
                        if (res.head.code === '000000') {
                            this.editInfo.isShow = false;
                            this.loadThirdLevel(departmentObject);
                            if (this.editType === 'edit') {
                                this.getGroupPeople(this.teamId);
                            }
                            this.$message.success(successMessage);
                            this.isSave = false;
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        // 取消
        cancel() {
            this.editInfo.isShow = false;
        },
        // 删除小组信息
        delOrg(data) {
            const departmentObject = {
                orgCode: data.orgCode
            };
            this.$confirm('请确认删除该小组?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                cancelButtonClass: 'custom-cancel-button',
                type: 'warning',
                showCancelButton: true,
                showClose: false
            }).then(() => {
                const params = {
                    teamId: data.id + ''
                };
                this.$service.department.group
                    .deleteGroup(params)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.loadThirdLevel(departmentObject);
                            this.$message.success('删除成功');
                            this.$emit('delete-false');
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        },
        // 获取部门下成员信息
        departNameLevel(data) {
            const params = {
                orgCode: data.orgCode,
                isAll: 0
            };
            this.$service.department.group.getDepartName(params).then((res) => {
                if (res.head.code === '000000') {
                    this.groupLeaderData = res.body || [];
                    this.groupMembersData = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.container-top {
    width: 100%;
    height: 45px;
    margin-bottom: 5px;
}
.onelevel {
    width: 100%;
    height: 100%;
}
::v-deep .el-select {
    height: auto !important;
}
::v-deep .el-input {
    height: auto !important;
}
</style>
