import { basePathInit } from '@/envConst';
import storage from 'wtf-core-vue/src/methods/storage';
// 文件下载/导出接口超时时间
const downlaodTimeout = 300000;
export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    // 消息模块公共参数
    const commonQuery = {
        // 消息通道
        msgChannel: '1',
        // 系统编码
        sysCode: 'bbpfboss',
        // 消息模式，1私信 2广播
        msgModel: '1'
    };

    /**
     * 组织消息模块公共请求参数
     * @param {object} reqData 接口传过来的参数，不含公共参数
     * @return {function} 返回接口对象
     */
    function getHttpRequestData(reqData) {
        const data = {
            receiverGroup: 'bbpfboss',
            receiverId: storage.getLocalStorage('userId')
        };

        const newParams = Object.assign({}, commonQuery, data, reqData);
        return newParams;
    }

    const service = {
        // 消息中心获取字典值
        getMessageDictValueList(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/dict/getDictValueListByDictTypeCode',
                method: 'get',
                params: query
            });
        },
        // 消息中心获取字典值--消息类型
        getMessageDictType() {
            return this.getMessageDictValueList({
                dictTypeCode: 'message_type'
            });
        },
        // 消息中心获取字典值--消息状态;已读，未读
        getMessageDictStatus() {
            return this.getMessageDictValueList({
                dictTypeCode: 'message_status'
            });
        },
        // 消息中心获取字典值--消息发送时间：今天、昨天、近七天、近30天
        getMessageDictSendTime() {
            return this.getMessageDictValueList({
                dictTypeCode: 'message_send_time'
            });
        },
        // 消息中心获取字典值--平台类型 aliyun，huyiwuxian，wechat，tencent，dingding
        getMessagePlatformType() {
            return this.getMessageDictValueList({
                dictTypeCode: 'message_send_platform'
            });
        },
        // 消息中心获取字典值--消息渠道 sys，email，sms，wechat，dingding
        getMessageChannel() {
            return this.getMessageDictValueList({
                dictTypeCode: 'message_channel'
            });
        },
        // 获取消息类型数量
        getMessageGrouping(data) {
            // eslint-disable-next-line no-param-reassign
            data = data || {};
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/grouping',
                method: 'post',
                data: getHttpRequestData(data)
            });
        },
        // 获取消息类型对应消息列表
        getMessageGroupingList(data) {
            // eslint-disable-next-line no-param-reassign
            data = data || {};
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/groupingList',
                method: 'post',
                data: getHttpRequestData(data)
            });
        },
        // 获取消息详情
        getMessageDetail(data) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/msgDetail',
                method: 'post',
                data: getHttpRequestData(data)
            });
        },
        // 消息标记为全部已读
        postMessageSetAllRead(data) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/setAllReadMsg',
                method: 'post',
                data: getHttpRequestData(data)
            });
        },
        /**
         *
         * 消息通知方法开始wjc1
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        deleteMessage(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/deleteMsgs',
                method: 'delete',
                params: getHttpRequestData(query)
            });
        },
        // 删除全部
        deleteAllMessage(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/deleteAllMsgs',
                method: 'delete',
                data: getHttpRequestData(query)
            });
        },
        // 标记已读:参数msgIds，tenantId，receiverId，receiverName
        setMessageRead(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/setReadMsg',
                method: 'post',
                params: getHttpRequestData(query)
            });
        },
        setAllMessageRead(query) {
            return this.postMessageSetAllRead(query);
        },
        // 获取消息类型值
        getMessageTypeList(query) {
            return this.getMessageDictValueList(query);
        },
        // 关键入参：tenantId,receiverId,sysCode等
        getMessageNoticeList(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/getMsgs',
                method: 'post',
                data: getHttpRequestData(query)
            });
        },
        /**
         * 未读消息数
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        getUnReadCount(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msg/unReadCount',
                method: 'post',
                data: getHttpRequestData(query)
            });
        },
        /**
         * 获取未读消息数的长链接
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        getWebsocket() {
            const index = basePath.bossapi.messagecenter.indexOf('//');
            const domain = basePath.bossapi.messagecenter.slice(index + 2);
            return `wss://${domain}/websocket/message?token=`;
        },
        /**
         * 获取信息公告列表
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        getInformationList(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/notice/noticeWithPage',
                method: 'get',
                params: query
            });
        },
        /**
         * 新增信息公告
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        addInformation(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/notice/addNotice',
                method: 'POST',
                data: query
            });
        },
        /**
         * 编辑信息公告
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        editInformation(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/notice/modifyNotice',
                method: 'PUT',
                data: query
            });
        },
        /**
         * 发布信息公告
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        publishInformation(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/notice/releaseNotice',
                method: 'PUT',
                data: query
            });
        },
        /**
         * 信息公告详情
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        informationDetail(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/notice/noticeDetail',
                method: 'get',
                params: query
            });
        },
        /**
         * 删除信息公告
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        deleteInformation(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/notice/delNotice',
                method: 'DELETE',
                data: query
            });
        },
        /**
         * 上传附件
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        getUploadFileUrl() {
            return `${basePath.bossapi.messagecenter}/console/file/uploadFile`;
        },
        /**
         * 获取消息模板列表
         * @param {object} query messageSendType 消息类型 messageTemplateTitle 消息名称 messageType 模板类型
         * @return {function} 返回接口
         */
        getMessageTemplateList(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/list',
                method: 'post',
                data: query
            });
        },
        /**
         * 新增消息模板
         * @param {object} query query 消息类型 messageTemplateTitle 消息名称 messageType 模板类型
         * @return {function} 返回接口
         */
        addTemplateItem(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/add',
                method: 'post',
                data: query
            });
        },
        /**
         * 编辑消息模板
         * @param {object} query query 消息类型 messageTemplateTitle 消息名称 messageType 模板类型
         * @return {function} 返回接口
         */
        editTemplateItem(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/edit',
                method: 'post',
                data: query
            });
        },
        /**
         * 消息模板详情
         * @param {object} query query 消息类型 messageTemplateTitle 消息名称 messageType 模板类型
         * @return {function} 返回接口
         */
        detailTemplateItem(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/detail',
                method: 'get',
                params: query
            });
        },
        /**
         * 批量删除消息
         * @param {object} query query 消息类型 messageTemplateTitle 消息名称 messageType 模板类型
         * @return {function} 返回接口
         */
        batchDelete(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/delete',
                method: 'DELETE',
                data: query
            });
        },
        /**
         * 下载模板
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        downTemplate() {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/export',
                method: 'get',
                responseType: 'blob',
                timeout: downlaodTimeout
            });
        },
        /**
         * 消息模板导入
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        templateImport(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/import',
                method: 'post',
                data: query
            });
        },
        /**
         * 消息模板启用禁用
         * @param {object} query 接口传过来的参数，不含公共参数
         * @return {function} 返回接口对象
         */
        templateStatus(query) {
            return http({
                baseDomain: basePath.bossapi.messagecenter,
                url: '/console/msgtemplate/changestatus',
                method: 'post',
                data: query
            });
        }
    };

    return service;
};
