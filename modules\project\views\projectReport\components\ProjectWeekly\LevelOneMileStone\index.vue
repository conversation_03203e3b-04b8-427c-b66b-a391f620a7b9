<template>
    <div class="progress-situation">
        <div class="title-container">
            <div class="title">一级里程碑控制点进度情况</div>
            <el-button
                type="text"
                @click="edit"
                v-if="editable && isArchived && this.milestones.length > 0"
                >编辑</el-button
            >
        </div>
        <el-empty
            v-if="this.milestones.length === 0"
            :image-size="30"
        ></el-empty>
        <template v-else>
            <!-- 添加阶段标题行 -->
            <div class="phase-headers">
                <div class="phase-header-spacer"></div>
                <div
                    class="phases-container"
                    :style="{
                        maxWidth: `${this.milestones.length * 200}px`
                    }"
                >
                    <div
                        v-for="(phase, index) in phases"
                        :key="`phase-${index}`"
                        class="phase-item"
                        :style="{ flex: phase.span }"
                    >
                        {{ phase.name }}
                    </div>
                </div>
            </div>

            <div class="milestone-steps">
                <div class="grid-header-spacer">里程碑</div>
                <el-steps
                    :active="activeStep"
                    align-center
                    class="milestone-steps-content"
                >
                    <!-- 注意：这里需要内联设置max-width，否则会被原有的element-ui的内联样式覆盖 -->
                    <el-step
                        v-for="(milestone, index) in milestones"
                        :key="index"
                        :status="getStepStatus(milestone.detailProgressStatus)"
                        style="max-width: 200px"
                    >
                        <template #icon>
                            <svg-icon
                                v-if="
                                    milestone.detailProgressStatus === '已完成'
                                "
                                icon-class="project-check-mark"
                                class="check-mark-flag"
                            />
                            <svg-icon
                                v-else-if="
                                    milestone.detailProgressStatus === '进行中'
                                "
                                icon-class="project-milestone-ongoing"
                                class="clock-flag"
                            />
                            <svg-icon
                                v-else-if="
                                    milestone.detailProgressStatus === '待启动'
                                "
                                icon-class="project-clock"
                                class="clock-flag"
                            />
                        </template>
                        <template #title>
                            <div class="milestone-subtitle">
                                {{ milestone.detailName }}
                            </div>
                        </template>
                    </el-step>
                </el-steps>
            </div>

            <div class="milestone-grid-container" :style="gridStyle">
                <!-- 表头 -->
                <div class="grid-header">{{ currentBaselineTitle }}</div>
                <div
                    v-for="(milestone, index) in milestones"
                    :key="`baseline-${index}`"
                    class="grid-cell"
                >
                    {{ milestone.currentBaseline }}
                </div>

                <template v-if="shouldHideAdjustmentRow">
                    <!-- 预调整至 -->
                    <div class="grid-header">预调整至</div>
                    <div
                        v-for="(milestone, index) in milestones"
                        :key="`adjustment-${index}`"
                        class="grid-cell"
                    >
                        <el-tooltip
                            effect="dark"
                            placement="top"
                            :disabled="!milestone.adjustedReason"
                            v-if="
                                milestone.adjustedBaseline ||
                                milestone.adjustedReason
                            "
                        >
                            <template
                                slot="content"
                                v-if="milestone.adjustedReason"
                            >
                                <div class="pre-line">
                                    调整原因：{{ milestone.adjustedReason }}
                                </div>
                            </template>
                            <span
                                class="adjustment-text-container"
                                :class="
                                    milestone.adjustedReason ? 'pointer' : ''
                                "
                            >
                                <template v-if="milestone.adjustedBaseline">
                                    {{ milestone.adjustedBaseline }}
                                </template>
                                <span v-else class="date-placeholder"></span>
                                <span
                                    v-if="milestone.adjustedReason"
                                    class="corner-badge"
                                ></span>
                            </span>
                        </el-tooltip>
                    </div>
                </template>

                <!-- 实际完成 -->
                <div class="grid-header">实际完成</div>
                <div
                    v-for="(milestone, index) in milestones"
                    :key="`completion-${index}`"
                    class="grid-cell"
                >
                    {{ milestone.endDate }}
                </div>

                <!-- 完成状态 -->
                <div class="grid-header">完成状态</div>
                <div
                    v-for="(milestone, index) in milestones"
                    :key="`status-${index}`"
                    class="grid-cell"
                >
                    {{ milestone.completionStatus }}
                </div>
            </div>
        </template>
        <EditDialog
            :visible.sync="editDialogVisible"
            :data="editDialogData"
            :currentBaselineTitle="currentBaselineTitle"
            :isArchived="isArchived"
            @refresh="refresh"
        ></EditDialog>
    </div>
</template>

<script>
import EditDialog from './EditDialog.vue';

export default {
    name: 'LevelOneMileStone',
    components: {
        EditDialog
    },
    props: {
        milestones: {
            type: Array,
            default: () => []
        },
        editable: {
            type: Boolean,
            default: false
        },
        // 当前项目周报生命周期内的项目名称
        projectName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            editDialogVisible: false,
            editDialogData: {}
        };
    },

    computed: {
        // 整体里程碑宽度
        gridStyle() {
            return {
                gridTemplateColumns: `var(--grid-header-width, 80px) repeat(${this.milestones.length}, minmax(0, 1fr))`,
                maxWidth: `calc(var(--grid-header-width, 80px) + ${this.milestones.length} * 200px)`
            };
        },
        // 当前里程碑
        activeStep() {
            return this.milestones.findIndex(
                (milestone) => milestone.detailProgressStatus === '进行中'
            );
        },
        // 预调整至这一行是否要隐藏，只要有数据就不隐藏
        shouldHideAdjustmentRow() {
            return this.milestones.some(
                (milestone) =>
                    milestone.adjustedBaseline || milestone.adjustedReason
            );
        },
        // 当前基线的标题
        currentBaselineTitle() {
            if (!this.projectName) return '';
            return this.projectName.includes('审批中')
                ? '当前基线(审批中)'
                : '当前基线';
        },
        // 是否已归档,归档才允许编辑
        isArchived() {
            if (!this.projectName) return false;
            return !this.projectName.includes('审批中');
        },
        phases() {
            const phases = [];
            // 获取所有阶段
            const phasesOnly = this.milestones.map((milestone) => {
                return milestone.detailStage;
            });
            // 去重
            const phasesOnlyUnique = [...new Set(phasesOnly)];
            // 根据阶段名称获取阶段span
            phasesOnlyUnique.forEach((phase) => {
                const phaseSpan = phasesOnly.filter((p) => p === phase).length;
                phases.push({ name: phase, span: phaseSpan });
            });
            return phases;
        }
    },
    mounted() {
        this.$nextTick(() => {
            // 设置CSS变量以确保表头宽度一致
            const headerWidth =
                document.querySelector('.grid-header')?.offsetWidth || 80;
            document.documentElement.style.setProperty(
                '--grid-header-width',
                `${headerWidth}px`
            );
        });
    },
    methods: {
        /**
         * 获取里程碑步骤条状态
         * @param {String} status 状态
         * @returns {String} 状态
         */
        getStepStatus(status) {
            if (status === '已完成') {
                return 'finish';
            } else if (status === '进行中') {
                return 'process';
            } else if (status === '待启动') {
                return 'wait';
            }
        },
        /**
         * 编辑
         */
        edit() {
            // 准备编辑数据
            this.editDialogData = {
                milestones: JSON.parse(JSON.stringify(this.milestones)),
                phases: this.phases
            };
            this.editDialogVisible = true;
        },
        /**
         * 刷新页面
         */
        refresh() {
            this.$emit('refresh');
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.title {
    @include section-title;
}
.progress-situation {
    width: 100%;
}
.title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    border-bottom: 1px solid #8c8c8c;
    height: 28px;
}

.phase-headers {
    display: flex;
    width: 100%;
    margin-bottom: 15px;

    .phase-header-spacer {
        width: var(--grid-header-width, 80px);
        flex-shrink: 0;
    }

    .phases-container {
        display: flex;
        flex: 1;
    }

    .phase-item {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        font-weight: bold;
        font-size: 14px;
        border-right: 1px solid #3370ff;

        &:first-child {
            border-left: 1px solid #3370ff;
        }
    }
}

.milestone-steps {
    display: flex;
    width: 100%;

    .grid-header-spacer {
        display: flex;
        justify-content: center;
        align-items: center;
        width: var(--grid-header-width, 80px);
        flex-shrink: 0;
        background-color: #f5f7fa;
        padding: 8px;
        font-weight: bold;
    }

    .milestone-steps-content {
        flex: 1;
        padding-bottom: 20px;
    }
    .milestone-title {
        font-weight: bold;
        font-size: 14px;
    }

    .milestone-subtitle {
        font-weight: bold;
        font-size: 14px;
        margin-top: 5px;
        white-space: normal;
        line-height: 1.2;
        width: 100%;
        text-align: center;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        min-width: 0;
    }

    .custom-icon {
        width: 15px;
        height: 15px;
        border-radius: 50%;

        &.process {
            background-color: #e6a23c;
        }

        &.wait {
            background-color: #909399;
        }
    }
}

.milestone-grid-container {
    width: 100%;
    display: grid;
    gap: 1px;

    .grid-header {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f7fa;
        padding: 8px;
        font-weight: bold;
    }

    .grid-cell {
        padding: 8px;
        text-align: center;
        max-width: 200px;
    }

    .status-on-time {
        color: #67c23a;
    }

    .status-delayed {
        color: #e6503c;
    }
}

.adjustment-text {
    position: relative;
    display: inline-block;
}

.adjustment-text-container {
    position: relative;
    display: inline-block;
    min-width: 15px;
    min-height: 15px;

    .corner-badge {
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 8px 8px 0;
        border-color: transparent #ff4949 transparent transparent;
        transform: translate(50%, -50%);
    }
}

.pointer {
    cursor: pointer;
}

.pre-line {
    white-space: pre-line;
}
::v-deep .milestone-steps-content .el-step {
    padding-left: 0;
    padding-right: 0;
    flex-basis: 0;
    flex-grow: 1;
    min-width: 0;
    max-width: 200px;

    // 完全隐藏默认图标容器
    .el-step__head {
        .el-step__icon {
            width: 100%;
            height: 100%;
            background: none;
            border: none;
            box-shadow: none;
        }
    }
}

// 为自定义图标添加样式
.check-mark-flag {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #0064f0;
}

.clock-flag {
    width: 25px;
    height: 25px;
    padding: 0;
    border-radius: 50%;
    background-color: #fff;
    fill: #999;
    &:focus {
        outline: 0;
    }
}

.date-placeholder {
    display: inline-block;
    width: 80px;
    height: 1em;
}
</style>
