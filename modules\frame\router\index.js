export default [
    {
        path: '/redirect',
        useLayout: true,
        hidden: true,
        noPermission: true,
        name: 'redirect',
        children: [
            {
                path: '/redirect/:path(.*)',
                name: 'redirectIndex',
                hidden: true,
                noPermission: true,
                component: () => import('../views/redirect/index')
            }
        ]
    },
    {
        path: '/login',
        name: 'login',
        noPermission: true,
        component: () => import('../views/login/index'),
        hidden: true
    },
    {
        path: '/auth-redirect',
        name: 'authRedirect',
        noPermission: true,
        component: () => import('../views/login/auth-redirect'),
        hidden: true
    },
    {
        path: '/',
        name: 'dashboard',
        noPermission: true,
        useLayout: true,
        redirect: '/dashboard-index',
        children: [
            {
                path: 'dashboard-index',
                component: () => import('../views/dashboard/index'),
                name: 'DashboardIndex',
                noPermission: true,
                meta: { title: 'dashboard', icon: 'fa fa-home', affix: true }
            }
        ]
    },
    {
        path: '/userinfo',
        name: 'userinfo',
        useLayout: true,
        hidden: true,
        noPermission: true,
        redirect: '/userinfo/base',
        meta: { title: 'userInfo', icon: 'el-icon-user-solid' },
        children: [
            {
                path: 'base',
                component: () => import('../views/userinfo'),
                name: 'Userinfo-base',
                noPermission: true,
                hidden: true,
                meta: { title: 'baseUserInfo', icon: 'el-icon-user-solid' }
            },
            {
                path: 'changepwd',
                component: () => import('../views/userinfo/change-pwd'),
                name: 'Changepwd',
                noPermission: true,
                hidden: true,
                meta: { title: 'changePwd', icon: 'el-icon-key' }
            }
        ]
    },
    {
        path: '/401',
        name: '401',
        noPermission: true,
        component: () => import('../views/error-page/401'),
        hidden: true,
        meta: { title: '401', icon: 'el-icon-key' }
    },
    {
        path: '/403',
        name: '403',
        noPermission: true,
        component: () => import('../views/error-page/403'),
        hidden: true,
        meta: { title: '403', icon: 'el-icon-key' }
    },
    {
        path: '/404',
        name: '404',
        noPermission: true,
        component: () => import('../views/error-page/404'),
        hidden: true,
        meta: { title: '404', icon: 'el-icon-key' }
    },
    {
        path: '/500',
        name: '500',
        noPermission: true,
        component: () => import('../views/error-page/500'),
        hidden: true,
        meta: { title: '500', icon: 'el-icon-key' }
    },
    {
        path: '/error',
        name: 'error',
        noPermission: true,
        hidden: true,
        useLayout: true,
        meta: { title: 'error', icon: 'el-icon-key' },
        children: [
            {
                path: '401',
                name: '401',
                noPermission: true,
                component: () => import('../views/error-page/401'),
                hidden: true,
                meta: { title: '401', icon: 'el-icon-key' }
            },
            {
                path: '403',
                name: '403',
                noPermission: true,
                component: () => import('../views/error-page/403'),
                hidden: true,
                meta: { title: '403', icon: 'el-icon-key' }
            },
            {
                path: '404',
                name: '404',
                noPermission: true,
                component: () => import('../views/error-page/404'),
                hidden: true,
                meta: { title: '404', icon: 'el-icon-key' }
            },
            {
                path: '500',
                name: '500',
                noPermission: true,
                component: () => import('../views/error-page/500'),
                hidden: true,
                meta: { title: '500', icon: 'el-icon-key' }
            }
        ]
    }
];
