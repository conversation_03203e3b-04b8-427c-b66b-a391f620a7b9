/* eslint-disable */
import Vue from 'vue';
import ResourceDetail from './resourceDetail.vue';

const skeleton =
    '<div class="skeleton" style="height:30px;margin:10px"></div><div class="skeleton" style="height:30px;margin:10px"></div><div class="skeleton" style="height:30px;margin:10px"></div><div class="skeleton" style="height:30px;margin:10px"></div>';
// 创建一个新的Vue实例，并挂载组件以拿到真实dom
const getRenderedHTML = (taskDetail, type) => {
    return new Promise((resolve) => {
        const vueInstance = new Vue({
            render: (h) =>
                h(ResourceDetail, {
                    props: {
                        data: taskDetail,
                        type
                    }
                })
        });
        vueInstance.$mount();
        vueInstance.$nextTick(() => {
            resolve(vueInstance.$el);
            vueInstance.$el.remove();
        });
    });
};
let dayArr;
const MonthArr = Array.from({ length: 12 }, (item, index) => index + 1);
// 获取tooltip数据
const getTaskDetailData = (
    hasQueried,
    cacheKey,
    chartParams,
    that,
    isQueryPlanHour,
    wholeList,
    api
) => {
    return new Promise((resolve) => {
        // 查询过，直接返回
        if (hasQueried[cacheKey]) {
            return;
        }
        hasQueried[cacheKey] = true;
        if (chartParams.value === 0) {
            return resolve([]);
        }
        const params = {
            loginNames: [that.loginName],
            selectDate: wholeList[chartParams.dataIndex].time,
            isQueryPlanHour
        };
        api(params)
            .then((res) => {
                if (res.head.code === '000000') {
                    resolve(res.body);
                } else {
                    that.$message.error(res.head.message);
                    hasQueried[cacheKey] = false;
                }
            })
            .catch((err) => {
                hasQueried[cacheKey] = false;
                console.error('Error', err);
            });
    });
};
const setDetail = (
    callback,
    ticket,
    hasQueried,
    cacheKey,
    chartParams,
    that,
    isQueryPlanHour,
    wholeList,
    api,
    cache,
    type
) => {
    getTaskDetailData(
        hasQueried,
        cacheKey,
        chartParams,
        that,
        isQueryPlanHour,
        wholeList,
        api
    )
        .then((res) => {
            return getRenderedHTML(res, type);
        })
        .then((html) => {
            const s = new XMLSerializer();
            const str = s.serializeToString(html);
            cache[cacheKey] = str;
            callback(ticket, str);
        });
};
// 图例配置
const getLegend = () => {
    return {
        y: 'top',
        x: 'right',
        padding: 15,
        textStyle: {
            color: '#666666'
        },
        itemWidth: 15,
        itemHeight: 10,
        itemGap: 25
    };
};
// x轴配置
const getXAxis = (view) => {
    return {
        type: 'category',
        data: view === 'day' ? dayArr : MonthArr,
        axisLine: {
            lineStyle: {
                color: '#333'
            }
        },
        axisLabel: {
            textStyle: {
                color: '#333'
            },
            color: '#333',
            interval: 0
        },
        name: `单位（${view === 'day' ? '天' : '月'}）`
    };
};
// y轴配置
const getYAxis = () => {
    return {
        type: 'value',
        axisLine: {
            show: false
        },
        splitLine: {
            show: false
        },
        axisLabel: {
            textStyle: {
                color: '#333'
            }
        },
        name: '单位（小时）',
        nameTextStyle: {
            padding: [0, 70, 0, 0]
        }
    };
};
// 图标内容位置
const getGrid = () => {
    return {
        x: 30,
        y: 80,
        x2: 70,
        y2: 30,
        containLabel: true
    };
};
/**
/**
 * 个人资源负载折线图配置（日视图）
 * @param {*} that this
 * @param {Array} data 数据
 * @param {Number} dividedNumber 从那天开始是预计负载
 * @returns {Array} 配置
 */
const getResoureLoadChartByDay = function (
    that,
    hourList,
    dividedNumber,
    api,
    wholeList,
    daysNumInMonth
) {
    dayArr = Array.from({ length: daysNumInMonth }, (item, index) => index + 1);

    // 定义一个缓存来存储已经创建的Vue实例
    const cache = {};
    // 定义一个数组存储每个日期是否被查询过
    const hasQueried = Array.from({ length: daysNumInMonth }, (i, k) => false);
    const chartDataList = hourList.map((value, index) => ({
        value: value,
        label: {
            show: true,
            distance: 7,
            color: '#999'
        },
        tooltip: {
            confine: true,
            trigger: 'item',
            show: true,
            transitionDuration: 0,
            formatter: function (chartParams, ticket, callback) {
                const cacheKey = JSON.stringify({
                    dataIndex: chartParams.dataIndex
                });
                if (cache[cacheKey]) {
                    // 如果缓存中有相应的内容，直接返回
                    return cache[cacheKey];
                }
                setDetail(
                    callback,
                    ticket,
                    hasQueried,
                    cacheKey,
                    chartParams,
                    that,
                    chartParams.dataIndex + 1 >= dividedNumber,
                    wholeList,
                    api,
                    cache,
                    'day'
                );
                return skeleton;
            },
            backgroundColor: '#fff',
            extraCssText:
                'width: 400px;box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);color: #333;overflow:auto;max-height: 350px;display:flex;'
        }
    }));
    const seriesData = [
        {
            data: chartDataList,
            type: 'line',
            symbolSize: 11,
            markLine: {
                symbol: 'none',
                data: [
                    {
                        silent: false,
                        lineStyle: {
                            type: 'dashed',
                            color: '#006400'
                        },
                        yAxis: 8
                    },
                    {
                        silent: false,
                        lineStyle: {
                            type: 'dashed',
                            color: '#FF0000'
                        },
                        yAxis: 12
                    }
                ],
                tooltip: {
                    show: false
                },
                silent: true
            },
            stack: 'Total'
        }
    ];
    const option = {
        grid: {
            top: 80
        },
        title: {
            text: '个人负载',
            left: 'center',
            padding: [20, 0, 0, 0]
        },
        tooltip: {
            trigger: 'item',
            show: true,
            // 让鼠标可以移入tooltip中，否则tooltip无法滚动
            enterable: true
        },
        visualMap: {
            y: 'top',
            x: 'right',
            padding: 15,
            type: 'piecewise',
            showLabel: true,
            dimension: 0,
            seriesIndex: 0,
            selectedMode: false,
            pieces: [
                {
                    min: -10,
                    max: dividedNumber - 2,
                    color: '#4169E1',
                    label: '实际负载'
                },
                {
                    min: dividedNumber - 2,
                    max: 50,
                    color: '#7effb2',
                    label: '预计负载'
                }
            ],
            textStyle: {
                color: '#333',
                fontSize: 12
            }
        },
        xAxis: getXAxis('day'),
        yAxis: getYAxis(),
        series: seriesData
    };
    return option;
};
/**
 * 个人资源负载折线图配置（月视图）
 * @param {*} that this
 * @param {Array} data 数据
 * @param {Number} dividedNumber 从那天开始是预计负载
 * @returns {Array} 配置
 */
const getResoureLoadChartByMonth = function (
    that,
    dividedNumber,
    actualHourList,
    planHourList,
    api,
    wholeList
) {
    const data = [
        {
            name: '实际负载',
            data: actualHourList,
            color: '#7ec5ff'
        },
        {
            name: '预计负载',
            data: planHourList,
            color: '#00da88'
        }
    ];
    // 定义一个对象缓存来存储已经创建的Vue实例
    const cache = {};
    // 定义一个对象存储每个日期是否被查询过
    const hasQueried = {};
    const chartDataList = data.map((item, index) => ({
        data: item.data,
        name: item.name,
        color: item.color,
        label: {
            show: true,
            distance: 10,
            position: 'inside',
            color: '#333'
        },
        type: 'bar',
        barWidth: 30,
        symbolSize: 11,
        markLine: {
            symbol: 'none',
            data: [
                {
                    silent: false,
                    lineStyle: {
                        type: 'dashed',
                        color: '#006400'
                    },
                    yAxis: 168
                },
                {
                    silent: false,
                    lineStyle: {
                        type: 'dashed',
                        color: '#FF0000'
                    },
                    yAxis: 252
                }
            ],
            tooltip: {
                show: false
            },
            silent: true
        },
        stack: 'Total',
        tooltip: {
            trigger: 'item',
            show: true,
            confine: true,
            formatter: function (chartParams, ticket, callback) {
                const cacheKey = JSON.stringify({
                    dataIndex: chartParams.dataIndex,
                    type: chartParams.seriesName
                });
                if (cache[cacheKey]) {
                    // 如果缓存中有相应的内容，直接返回
                    return cache[cacheKey];
                }
                setDetail(
                    callback,
                    ticket,
                    hasQueried,
                    cacheKey,
                    chartParams,
                    that,
                    chartParams.seriesName === '预计负载',
                    wholeList,
                    api,
                    cache,
                    'month'
                );
                return skeleton;
            },
            backgroundColor: '#fff',
            extraCssText:
                'width: 400px;box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);color: #333;overflow:auto;max-height: 350px;display:flex;z-index:999;'
        }
    }));
    const legend = getLegend();

    const option = {
        title: {
            text: '个人负载',
            left: 'center',
            padding: [20, 0, 0, 0]
        },
        tooltip: {
            trigger: 'item',
            show: true,
            // 让鼠标可以移入tooltip中，否则tooltip无法滚动
            enterable: true
        },
        xAxis: getXAxis('Month'),
        yAxis: getYAxis(),
        series: chartDataList,
        legend
    };
    return option;
};

export { getResoureLoadChartByDay, getResoureLoadChartByMonth };
