/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 预警：确认消息情况
        message: {
            // 查询事件执行情况列表
            getEventCondition(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/system_monitor/get_event_execute_info_list',
                    method: 'post',
                    data
                });
            },
            // 查询消息发送情况列表
            getMessageCondition(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/system_monitor/get_message_send_info_list',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
