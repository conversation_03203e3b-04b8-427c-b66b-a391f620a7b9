<template>
    <div style="height: 100vh">
        <el-container style="height: 100%; overflow: auto">
            <Transition name="el-fade-in">
                <el-aside
                    width="220px"
                    v-show="showAside"
                    class="checkbox flexLayout"
                    style="padding-right: 0"
                >
                    <div class="query">
                        <el-button
                            @click="resetSearch"
                            type="primary"
                            style="margin: 0 auto 10px auto; display: block"
                            >重置</el-button
                        >
                        <el-select
                            class="checkbox"
                            v-model="productLine"
                            multiple
                            placeholder="产品线"
                            @change="handleQuery"
                        >
                            <el-option
                                v-for="item in this.$store.state.project
                                    .productLine"
                                :key="item.code"
                                :label="item.label"
                                :value="item.label"
                            >
                            </el-option>
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="approvalType"
                            @change="handleQuery"
                            multiple
                            placeholder="立项类型"
                        >
                            <el-option
                                v-for="(item, index) in CONSTANTS.APPROVAL_TYPE"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="demandCategory"
                            @change="handleQuery"
                            multiple
                            placeholder="需求分类"
                        >
                            <el-option
                                v-for="(
                                    item, index
                                ) in CONSTANTS.DEMAND_CATEGORY"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="productType"
                            @change="handleQuery"
                            multiple
                            placeholder="产品类型"
                        >
                            <el-option
                                v-for="(item, index) in CONSTANTS.PRODUCT_TYPE"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="technicalComplexity"
                            multiple
                            placeholder="难度等级"
                            @change="handleQuery"
                        >
                            <el-option
                                v-for="(
                                    item, index
                                ) in CONSTANTS.TECHNICAL_COMPLEXITY"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="projectStage"
                            multiple
                            placeholder="所处阶段"
                            @change="handleQuery"
                        >
                            <el-option
                                v-for="(item, index) in CONSTANTS.PROJECT_STAGE"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="demandImportSubject"
                            @change="handleQuery"
                            multiple
                            placeholder="需求导入类型"
                        >
                            <el-option
                                v-for="(
                                    item, index
                                ) in CONSTANTS.DEMAND_IMPORT_SUBJECT"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <el-select
                            class="checkbox"
                            v-model="currentScheduleStatus"
                            @change="handleQuery"
                            multiple
                            placeholder="当前进度状态"
                        >
                            <el-option
                                v-for="(
                                    item, index
                                ) in CONSTANTS.CURRENT_SCHEDULE_STATUS"
                                :key="index"
                                :label="item"
                                :value="item"
                                >{{ item }}</el-option
                            >
                        </el-select>
                        <people-selector
                            class="checkbox"
                            v-model="projectManager"
                            placeholder="项目经理"
                            :isAll="1"
                            @input="handleQuery"
                        ></people-selector>
                        <people-selector
                            class="checkbox"
                            v-model="productManager"
                            placeholder="产品经理"
                            :isAll="1"
                            @input="handleQuery"
                        ></people-selector>
                    </div>
                    <el-button
                        type="primary"
                        style="padding: 15px 0px"
                        @click="() => (showAside = false)"
                    >
                        <svg-icon
                            icon-class="project-side-select-bar"
                        ></svg-icon>
                    </el-button>
                </el-aside>
            </Transition>
            <el-main style="overflow: hidden; height: 100%">
                <div class="flexLayout">
                    <el-tabs
                        class="tabs"
                        v-model="activeIndex"
                        @tab-click="handleTabChange"
                        :stretch="true"
                    >
                        <el-tab-pane
                            v-for="item in projectStatusGroup"
                            :label="`${item.projectStatus} (${item.projectStatusNum})`"
                            :key="`${tabKey}${item.projectStatus}`"
                            :lazy="true"
                        ></el-tab-pane>
                    </el-tabs>
                    <el-button
                        class="selectButton"
                        style="margin: 10px 0 0 20px"
                        type="primary"
                        @click="() => (showAside = !showAside)"
                        >筛选
                    </el-button>
                    <form
                        class="projectSearch flexLayout"
                        @submit.prevent="handleQuery"
                    >
                        <el-input
                            v-model="projectName"
                            placeholder="请输入项目名称或编号"
                            size="medium"
                            style="max-width: 300px; min-width: 200px"
                        ></el-input>
                        <el-button
                            icon="el-icon-search"
                            @click="handleQuery"
                            type="primary"
                            style="height: 36px; margin-left: -10px; z-index: 2"
                        ></el-button>
                    </form>
                </div>
                <SnbcBaseTable
                    class="table"
                    ref="tableRef"
                    :table-config="tableConfig"
                >
                    <template #currentMilestone="{ row }">
                        <el-tooltip
                            v-if="row.currentProjectDetailName === '--'"
                            effect="dark"
                            :content="
                                row.projectName.includes(`审批中`)
                                    ? '立项审批中，里程碑未发布'
                                    : '当前里程碑已全部完成'
                            "
                            placement="top"
                            trigger="hover"
                        >
                            <div>{{ row.currentProjectDetailName }}</div>
                        </el-tooltip>
                        <span v-else>{{ row.currentProjectDetailName }} </span>
                    </template>
                    <template #pausedOaFlowId="{ row }">
                        <div class="flexLayout center">
                            {{ row.oaFlowId }}
                            <el-popover
                                v-if="row.oaFlowId"
                                placement="bottom"
                                trigger="click"
                            >
                                <el-table
                                    class="paused-reason-table"
                                    :data="pausedReasonList"
                                    :cell-style="{
                                        border: '1px solid #8c8c8c!important'
                                    }"
                                    :header-cell-style="{
                                        border: '1px solid #8c8c8c'
                                    }"
                                >
                                    <el-table-column
                                        width="150"
                                        property="delayOneLevelReason"
                                        label="暂停原因一级分类"
                                    ></el-table-column>
                                    <el-table-column
                                        width="150"
                                        property="delayTwoLevelReason"
                                        label="暂停原因二级分类"
                                    ></el-table-column>
                                    <el-table-column
                                        width="300"
                                        property="detailsReason"
                                        label="计划暂停具体原因"
                                    ></el-table-column>
                                </el-table>
                                <svg-icon
                                    slot="reference"
                                    class="question-mark"
                                    icon-class="project-question-mark"
                                    :aria-hidden="false"
                                    @click="getPausedReason(row.oaFlowId)"
                                />
                            </el-popover>
                        </div>
                    </template>
                    <template #terminatedOaFlowId="{ row }">
                        <div class="flexLayout center">
                            {{ row.oaFlowId }}
                            <el-popover
                                v-if="row.oaFlowId"
                                placement="bottom"
                                title="终止原因"
                                width="330"
                                trigger="click"
                            >
                                <div class="pre-line">
                                    {{ curStopReason }}
                                </div>
                                <svg-icon
                                    slot="reference"
                                    class="question-mark"
                                    icon-class="project-question-mark"
                                    :aria-hidden="false"
                                    @click="
                                        handleStopReasonClick(row.stopReason)
                                    "
                                />
                            </el-popover>
                        </div>
                    </template>
                </SnbcBaseTable>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import PeopleSelector from 'Components/PeopleSelector';
import { CONSTANTS } from '@/constants';
import { getStatusArray } from './tableHeader';
import { getTableConfig } from './tableConfig';
// 项目状态
const { PROJECT_STATUS } = CONSTANTS;
// 构建项目状态列表
const getProjectStatusGroup = () => {
    return [...PROJECT_STATUS, { label: '全部', value: '全部' }].map(
        (item) => ({
            projectStatus: item.label,
            projectStatusNum: '0',
            projectValue: item.value
        })
    );
};

export default {
    name: 'ProjectList',
    components: { SnbcBaseTable, PeopleSelector },
    data() {
        return {
            // 列表各项配置
            tableConfig: getTableConfig(this),
            // 产品线
            productLine: [],
            // 立项类型
            approvalType: [],
            // 需求类型
            demandCategory: [],
            // 产品类型
            productType: [],
            // 项目技术难度
            technicalComplexity: [],
            // 需求导入主体
            demandImportSubject: [],
            // 当前进度状态
            currentScheduleStatus: [],
            // 是否显示侧边栏
            showAside: false,
            // 项目所处阶段
            projectStage: [],
            // 项目经理
            projectManager: [],
            // 产品经理
            productManager: [],
            // 暂停原因列表
            pausedReasonList: [],
            PROJECT_STATUS,
            CONSTANTS,
            // 当前激活的tab
            activeIndex: '2',
            // 项目名称
            projectName: '',
            // tab页的key
            tabKey: 0,
            // 项目状态列表
            projectStatusGroup: getProjectStatusGroup(),
            // 当前选择的终止时间
            curStopReason: '',
            // 是否有编辑权限
            hasPermission:
                this.$store.state.permission.btnDatas.includes('edit_project'),
            // 获取项目状态下各项目的数量
            getProjectStatusCountApi:
                this.$service.project.projectInfo.getProjectStatusCount,
            // 点击获取暂停原因
            getPausedReasonApi:
                this.$service.project.projectInfo.getPausedReason
        };
    },
    computed: {
        // 查询参数
        computedQueryParams() {
            return {
                projectName: this.projectName,
                // 产品线
                productLineList: this.productLine,
                // 立项类型
                projectApprovalList: this.approvalType,
                // 需求类型
                storyTypeList: this.demandCategory,
                // 产品类型
                productTypeList: this.productType,
                // 需求导入主体
                storyImportMainBodyList: this.demandImportSubject,
                // 项目技术难度
                projectTechnicalDifficultyList: this.technicalComplexity,
                // 当前进度状态
                currentScheduleStatusList: this.currentScheduleStatus,
                // 项目所处阶段
                projectStageList: this.projectStage,
                // 项目经理
                projectManagerList: this.projectManager,
                // 产品经理
                productManagerList: this.productManager
            };
        },
        // 项目状态
        projectStatus() {
            return this.projectStatusGroup[parseInt(this.activeIndex)]
                .projectValue;
        }
    },
    watch: {
        // 不同项目状态展示不同的表头
        activeIndex(newVal) {
            this.tableConfig.elTableColumns = getStatusArray(
                parseInt(this.activeIndex),
                this
            );
        }
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
    },
    mounted() {
        // 没有权限，操作列隐藏
        if (!this.hasPermission) {
            this.tableConfig.operationColumnWidth = 0;
            this.tableConfig.operations = [];
        }
        this.setDefaultSortOrder();
        this.handleQuery();
    },
    activated() {
        this.setDefaultSortOrder();
        this.handleQuery();
    },
    methods: {
        /**
         * 查询
         */
        handleQuery() {
            this.tableConfig.queryParams = {
                projectStatus: this.projectStatus,
                ...this.computedQueryParams
            };
            this.tableConfig.operationColumnWidth =
                parseInt(this.activeIndex) <= 1 ? 125 : 80;
            this.getProjectStatusCount();
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 新增项目
         */
        handleAdd() {
            this.$router.push('/project/addProject');
        },
        /**
         * 编辑项目
         * @param {*} row 行数据
         */
        handleEdit(row) {
            this.$router.push({
                path: '/project/editProject',
                query: {
                    id: row.projectId
                }
            });
        },

        /**
         * 获取项目状态下各项目的数量
         */
        getProjectStatusCount() {
            this.getProjectStatusCountApi(this.computedQueryParams)
                .then((result) => {
                    if (result.head.code !== '000000') {
                        this.$message.error(result.head.message);
                    }
                    const res = result.body;
                    // 重置数据列表
                    this.projectStatusGroup = getProjectStatusGroup();
                    // 对数量重新赋值
                    this.projectStatusGroup.forEach((item) => {
                        for (let i = 0; i < res.length; i++) {
                            if (item.projectValue === res[i].projectStatus) {
                                item.projectStatusNum = res[i].projectStatusNum;
                            }
                        }
                    });
                    this.tabKey += 1;
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        /**
         * 排序前的hook
         * @param {Object} params 参数
         */
        sortChangeHook(params) {
            const { prop: sortKey, order: sortOrder } = params;
            this.tableConfig.sortParams.sortKey = sortKey;
            this.tableConfig.sortParams.sortOrder =
                sortOrder === 'ascending' ? 'ASC' : 'DESC';
            // 注意：项目难度等级后端接口有误，排序的时候需要反着传
            if (sortKey === 'projectTechnicalDifficulty') {
                this.tableConfig.sortParams.sortOrder =
                    sortOrder === 'ascending' ? 'DESC' : 'ASC';
            }
        },
        /**
         * 重置筛选条件
         */
        resetSearch() {
            this.productLine = [];
            this.approvalType = [];
            this.demandCategory = [];
            this.productType = [];
            this.demandImportSubject = [];
            this.technicalComplexity = [];
            this.currentScheduleStatus = [];
            this.projectStage = [];
            this.projectManager = [];
            this.productManager = [];
            this.setDefaultSortOrder();
            this.handleQuery();
        },
        /**
         * 设置产品线在各个状态时的默认排序
         */
        setDefaultSortOrder() {
            const orderList = [
                // 市场待立项
                {
                    sortKey: '',
                    sortOrder: ''
                },
                // 排队中
                {
                    sortKey: 'marketResoluteIssueDate',
                    sortOrder: 'DESC'
                },
                // 进行中
                {
                    sortKey: 'projectNumber',
                    sortOrder: 'DESC'
                },
                // 已暂停
                {
                    sortKey: 'stopDate',
                    sortOrder: 'DESC'
                },
                // 已终止
                {
                    sortKey: 'stopDate',
                    sortOrder: 'DESC'
                },
                // 已结项
                {
                    sortKey: 'completedDetailDate',
                    sortOrder: 'DESC'
                },
                // 全部
                {
                    sortKey: '',
                    sortOrder: ''
                }
            ];
            this.tableConfig.sortParams = orderList[this.activeIndex];
        },
        /**
         * 获取暂停原因列表
         * @param {String} oaFlowId 流程ID
         */
        async getPausedReason(oaFlowId) {
            try {
                const res = await this.getPausedReasonApi({ oaFlowId });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.pausedReasonList = res.body.map((i) => {
                    if (i.detailsReason) {
                        i.detailsReason = i.detailsReason.replace(
                            /<br\s*\/?>/gi,
                            '\n'
                        );
                    }
                    return i;
                });
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 切换tab页签的处理函数
         */
        handleTabChange() {
            // 清除排序
            const ref = this.$refs.tableRef?.$children[1]?.$children[0];
            ref.clearSort();
            this.setDefaultSortOrder();
            this.handleQuery();
        },
        handleStopReasonClick(oriStopReason = '') {
            if (!oriStopReason) {
                this.curStopReason = '暂无数据';
                return;
            }
            this.curStopReason = oriStopReason.replace(/<br\s*\/?>/gi, '\n');
        },
        /**
         * 是否展示删除按钮
         * @param {Object} row 当前行数据
         * @returns {Boolean} 是否展示
         */
        showDeleteButton(row) {
            // 市场待立项的项目以及没有市场需求导入实例号的排队中的项目，才能删除
            if (this.activeIndex === '0') return true;
            if (
                this.activeIndex === '1' &&
                !row.marketStoryImportFlowInstanceNo
            ) {
                return true;
            }
            return false;
        },
        /**
         * 删除项目
         * @param {*} row 行数据
         */
        async handleDelete(row) {
            await this.$confirm('确定该删除项目吗?', '提示', {
                type: 'warning'
            });
            const api = this.$service.project.projectInfo.deleteProject;
            const params = { projectId: row.projectId };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.handleQuery();
                this.$message.success('删除成功');
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flexLayout {
    display: flex;
    align-items: center;
}
.center {
    justify-content: center;
}
.projectSearch {
    height: 30px;
    margin-left: auto;
    min-width: 200px;
    padding-left: 10px;
}
.checkbox {
    background-color: #fff;
    padding: 5px;
    margin-bottom: 5px;
    .el-checkbox {
        margin-right: 10px;
    }
}
.productLineCheck {
    .el-checkbox {
        min-width: 80px;
    }
}
.productTypeCheck {
    .el-checkbox {
        min-width: 83px;
    }
}
.techCheckbox {
    .el-checkbox {
        min-width: 57px;
    }
}
.el-aside {
    background-color: #f7f7fa;
    border-right: 1px solid #d9d9d9;
    padding: 10px;
}
.el-main {
    display: flex;
    flex-direction: column;
}
.tabs {
    max-width: 700px;
    ::v-deep .el-tabs__item {
        padding: 0 16px;
    }
}
.question-mark {
    width: 20px !important;
    height: 20px !important;
    margin-top: 5px;
    &:hover {
        transform: scale(1.2);
        cursor: pointer;
    }
}
.pre-line {
    white-space: pre-line;
}
.paused-reason-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .cell {
        white-space: pre-line;
    }
}
::v-deep.el-table th {
    background-color: #3370ff !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
::v-deep .el-tabs__item.is-top {
    padding: 0 8px;
}
// 修改排序箭头样式
::v-deep .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #ffdc37;
}
::v-deep .el-table .descending .sort-caret.descending {
    border-top-color: #ffdc37;
}
// 修改查询区域placeholder样式
.query {
    margin-bottom: auto;
    width: 200px;
    ::v-deep .el-input__inner::placeholder {
        color: rgba(0, 0, 0, 0.685) !important;
    }
}
</style>
<style lang="scss">
// 修改tab选项卡样式,去掉点开弹窗后的蓝色边框
::v-deep .el-tabs__item {
    &:focus.is-active.is-focus:not(&:active) {
        -webkit-box-shadow: none;
        box-shadow: none;
    }
}
</style>
