<template>
    <div class="view">
        <div class="container">
            <div class="filter">
                <div class="header">{{
          $t("systemManagement.filterTitle")
        }}</div>

                <el-form label-width="160px">
                    <el-form-item :label="$t('systemManagement.configuration.paramName')" prop="paramName">
                        <el-input v-model="formInline.paramName" :placeholder="
                $t('systemManagement.configuration.placeholderParamName')
              " @keyup.enter.native="queryConfig" />
                    </el-form-item>

                    <el-form-item :label="$t('systemManagement.configuration.paramTypeName')" prop="paramTypeName">
                        <el-select v-model="formInline.paramTypeName" :placeholder="$t('systemManagement.configuration.selectParamTypeName')" clearable :popper-append-to-body="false">
                            <el-option v-for="item in paramTypeNameOptions" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                        </el-select>
                    </el-form-item>

                    <div class="filter__btns">
                        <el-button type="primary" icon="fa fa-search" class="button option-button margin-left" @click="queryConfig">{{ $t("common.searchBtn") }}</el-button>
                        <el-button v-waves class="button default-button filter-item" icon="fa fa-undo" @click="queryReset">
                            {{ $t("systemManagement.dictionary.reset") }}
                        </el-button>
                    </div>
                </el-form>
            </div>

            <div class="container">
                <div class="header">
                    <span class="header__title">{{
            $t("common.queryList")
          }}</span>
                    <span class="header__btns">
                        <el-button v-permission="['config_manage_add']" type="primary" icon="fa fa-plus-square-o" @click="handleAdd">
                            {{ $t("common.add") }}
                        </el-button>
                    </span>
                </div>

                <el-table id="configurationTable" :data="tableData" border :header-cell-style="{ background: '#F5F6FA' }">
                    <el-table-column prop="paramName" :label="$t('systemManagement.configuration.paramName')" />
                    <el-table-column prop="paramCode" :label="$t('systemManagement.configuration.paramCode')" />
                    <el-table-column prop="paramValue" :label="$t('systemManagement.configuration.paramValue')" />
                    <el-table-column prop="paramTypeName" :label="
              $t('systemManagement.configuration.paramTypeName')
            ">
                        <template slot-scope="{ row }">
                            {{ $t('systemManagement.dictionaryManagement.value.'+row.paramTypeName) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="createTime" :label="$t('systemManagement.configuration.createTime')" width="160px">
                        <template slot-scope="{ row }">
                            <span>{{
                row.createTime.split("T").join(" ")
              }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')" width="190px">
                        <template slot-scope="{ row, $index }">
                            <el-button v-permission="['config_manage_edit']" type="primary" icon="fa fa-pencil" size="mini" class="button table-inner-button" @click="handleEdit(row)">
                                {{ $t("table.edit") }}
                            </el-button>
                            <el-button v-permission="['config_manage_delete']" size="mini" icon="fa fa-trash-o" class="button table-delete-button" @click="handleDelete(row, $index)">
                                {{ $t("table.delete") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background for="pagination" class="right" layout="total, prev, pager, next, sizes, jumper" :page-sizes="[10, 20, 50, 100]" :total="pager.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </div>

        <!-- 添加配置弹窗 -->
        <el-dialog :title="$t('systemManagement.configuration.addParam')" :visible.sync="addDialogVisible" @closed="clearParamItem">
            <el-form ref="paramItem" label-position="right" label-width="auto" :model="paramItem" :rules="paramAddRules" class="custom-form">
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramName'
            )
          " prop="paramName">
                    <el-input v-model="paramItem.paramName" maxlength="45" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.paramName')}`" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramCode'
            )
          " prop="paramCode">
                    <el-input v-model="paramItem.paramCode" maxlength="45" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.paramCode')}`" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramValue'
            )
          " prop="paramValue">
                    <el-input v-model="paramItem.paramValue" maxlength="200" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.paramValue')}`" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramTypeName'
            )
          " prop="paramTypeName">
                    <el-select v-model="paramItem.paramTypeName" :placeholder="$t('systemManagement.configuration.selectParamTypeName')" style="width: 100%" clearable :popper-append-to-body="false">
                        <el-option v-for="item in paramTypeNameOptions" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="
            $t('systemManagement.configuration.content')
          " prop="paramDesc">
                    <el-input v-model="paramItem.paramDesc" type="textarea" maxlength="200" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.content')}`" />
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="addDialogVisible = false">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="saveParam">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>

        <!-- 编辑配置弹窗 -->
        <el-dialog :title="$t('systemManagement.configuration.editParam')" :visible.sync="editDialogVisible">
            <el-form ref="paramItemEdit" label-position="right" label-width="auto" :model="paramItemEdit" :rules="paramAddRules" class="custom-form">
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramName'
            )
          " prop="paramName">
                    <el-input v-model="paramItemEdit.paramName" maxlength="45" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.paramName')}`" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramCode'
            )
          " prop="paramCode">
                    <el-input v-model="paramItemEdit.paramCode" disabled maxlength="45" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.paramCode')}`" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramValue'
            )
          " prop="paramValue">
                    <el-input v-model="paramItemEdit.paramValue" maxlength="200" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.paramValue')}`" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.configuration.paramTypeName'
            )
          " prop="paramTypeName">
                    <el-select v-model="paramItemEdit.paramTypeName" style="width: 100%" :placeholder="$t('systemManagement.configuration.selectParamTypeName')" clearable :popper-append-to-body="false" @change="selectType">
                        <el-option v-for="item in paramTypeNameOptions" :key="item.valueId" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="
            $t('systemManagement.configuration.content')
          " prop="paramDesc">
                    <el-input v-model="paramItemEdit.paramDesc" type="textarea" maxlength="200" :placeholder="`${$t('systemManagement.configuration.pleaseInput')}${$t('systemManagement.configuration.content')}`" />
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="editDialogVisible = false">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="updateParam">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>

    </div>
</template>

<script>
export default {
    name: 'Configuration',
    data() {
        return {
            formInline: {
                // 参数名称
                paramName: '',
                // 参数类型名称
                paramTypeName: ''
            },
            // 参数类型值list
            paramTypeNameOptions: [],
            // 表格数据
            tableData: [],
            pager: {
                pageNum: 1,
                pageSize: 10,
                total: 0
            },
            // 保存地域弹层
            addDialogVisible: false,
            // 保存地域 - 地域数据
            paramItem: {
                paramName: '',
                paramCode: '',
                paramValue: '',
                paramTypeCode: '',
                paramTypeName: '',
                paramDesc: ''
            },
            // 编辑参数弹层
            editDialogVisible: false,
            // 编辑参数表单数据对象
            paramItemEdit: {
                paramId: '',
                paramName: '',
                paramCode: '',
                paramValue: '',
                paramTypeCode: '',
                paramTypeName: '',
                paramDesc: ''
            },
            // 删除数据存储对象
            paramItemDetete: {}
        };
    },
    computed: {
        // 表单验证规则
        paramAddRules() {
            return {
                // 参数名称。非空，最大长度maxlen控制
                paramName: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.configuration.inputParamName'
                        ),
                        trigger: ['blur', 'change']
                    }
                ],
                // 参数编码,字母、数字，最大长度为45
                paramCode: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.configuration.inputParamCode'
                        ),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[a-zA-Z0-9]{1,45}$/,
                        message: this.$t(
                            'systemManagement.configuration.paramCodeRule'
                        ),
                        trigger: ['blur', 'change']
                    }
                ],
                // 配置值
                paramValue: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.configuration.inputParamValue'
                        ),
                        trigger: ['blur', 'change']
                    }
                ],
                paramTypeName: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.configuration.selectParamTypeName'
                        ),
                        trigger: ['change']
                    }
                ]
            };
        }
    },
    created() {
        // 获取参数类型值
        this.getParamTypeCodeList();
        this.getParamByMapList();
    },
    methods: {
        // 查询
        queryConfig() {
            this.getParamByMapList();
        },
        queryReset() {
            this.formInline.paramName = '';
            this.formInline.paramTypeName = '';
        },
        // 获取参数类型值
        getParamTypeCodeList() {
            this.$service.systemManagement
                .getDictionaryValueListAll({
                    typeCode: 'param_type'
                })
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.paramTypeNameOptions = response.body;
                    } else {
                        this.paramTypeNameOptions = [];
                    }
                });
        },
        // 获取参数列表
        getParamByMapList() {
            const data = {
                paramName: this.formInline.paramName,
                paramTypeName:
                    this.formInline.paramTypeName === '请选择'
                        ? ''
                        : this.formInline.paramTypeName,
                pageNum: this.pager.pageNum,
                pageSize: this.pager.pageSize
            };
            this.$service.systemManagement
                .getParamByMapList(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.tableData = res.body.list;
                        this.pager.total = res.body.total;
                        this.pager.pageNum = res.body.pageNum;
                        this.pager.pageSize = res.body.pageSize;
                    } else {
                        const msg = `productService.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 没写大小改变时触发
        handleSizeChange(val) {
            this.pager.pageSize = val;
            this.getParamByMapList();
        },
        // 当前页码改变触发
        handleCurrentChange(val) {
            this.pager.pageNum = val;
            this.getParamByMapList();
        },

        // 增加
        handleAdd() {
            this.addDialogVisible = true;
        },
        // 编辑
        handleEdit(param) {
            this.editDialogVisible = true;
            // 编辑的表单数据对象赋值
            const rowData = JSON.parse(JSON.stringify(param));
            this.paramItemEdit.oldParam = JSON.parse(JSON.stringify(rowData));
            const paramTypeName = this.$t(
                `systemManagement.dictionaryManagement.value.${rowData.paramTypeName}`
            );
            rowData.paramTypeName = paramTypeName;
            Object.assign(this.paramItemEdit, rowData);
        },
        // 删除
        handleDelete(param) {
            this.paramItemDetete = param;
            const _this = this;
            this.$confirm(
                this.$t('systemManagement.configuration.message.deleteTypeTip'),
                this.$t(
                    'systemManagement.configuration.message.deleteTipTitle'
                ),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.deleteParam();
                this.$message.success(
                    _this.$t(
                        'systemManagement.configuration.message.deleteSuccess'
                    )
                );
            });
        },
        // 保存和更新参数
        saveParam() {
            const params = {
                paramName: this.paramItem.paramName,
                paramCode: this.paramItem.paramCode,
                paramValue: this.paramItem.paramValue,
                paramTypeCode: this.paramItem.paramTypeName,
                paramTypeName: this.paramItem.paramTypeName,
                paramDesc: this.paramItem.paramDesc
            };
            this.$refs.paramItem.validate((valid) => {
                if (valid) {
                    this.$service.systemManagement
                        .putSaveParam(params)
                        .then((res) => {
                            if (res.head.code === '000000') {
                                this.addDialogVisible = false;
                                this.getParamByMapList();
                            } else {
                                const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error'
                                });
                            }
                        });
                }
            });
        },
        // 编辑参数
        updateParam() {
            const params = {
                paramId: this.paramItemEdit.paramId,
                paramName: this.paramItemEdit.paramName,
                paramCode: this.paramItemEdit.paramCode,
                paramValue: this.paramItemEdit.paramValue,
                paramTypeCode: this.paramItemEdit.paramTypeCode,
                paramTypeName: this.paramItemEdit.paramTypeCode,
                paramDesc: this.paramItemEdit.paramDesc,
                oldParam: this.paramItemEdit.oldParam
            };
            this.$refs.paramItemEdit.validate((valid) => {
                if (valid) {
                    this.$service.systemManagement
                        .postUpdateParam(params)
                        .then((res) => {
                            if (res.head.code === '000000') {
                                // 保存成功，关闭弹窗，并刷新列表
                                this.editDialogVisible = false;
                                this.getParamByMapList();
                            } else {
                                const msg = `productService.bgReturnError[${res.head.code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error'
                                });
                            }
                        });
                }
            });
        },
        // 关闭弹窗的回调
        clearParamItem() {
            // 重置表单内容
            this.$refs.paramItem.resetFields();
        },
        // 删除参数
        deleteParam() {
            const params = {
                paramIds: this.paramItemDetete.paramId,
                paramCode: this.paramItemDetete.paramCode,
                paramName: this.paramItemDetete.paramName
            };
            this.$service.systemManagement.deleteParam(params).then((res) => {
                if (res.head.code === '000000') {
                    this.getParamByMapList();
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        selectType(val) {
            this.paramItemEdit.paramTypeCode = val;
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
</style>
