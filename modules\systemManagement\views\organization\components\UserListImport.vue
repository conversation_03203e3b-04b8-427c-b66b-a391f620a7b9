<template>
    <!-- 导入用户 -->
    <el-dialog :title="$t('systemManagement.organization.dialog.importUser')" :visible.sync="isShow">
        <span>
            <div class="import-title">
                <i class="el-icon-warning" />
                <div class="import-box">
                    <div>
                        {{ $t('systemManagement.organization.dialog.pleaseDownload') }}
                        <el-link type="primary" @click.native.stop="downloadUserTemplate"> {{ $t('systemManagement.organization.dialog.importDocument') }} </el-link>
                        {{ $t('systemManagement.organization.dialog.finishedImport') }}
                    </div>
                    <p>
                        {{ $t('systemManagement.organization.dialog.finishedNotice') }}
                    </p>
                    <p class="tips">
                        {{ $t('systemManagement.organization.dialog.finishedWarning') }}
                    </p>
                </div>
            </div>
            <div class="import-contnent">
                {{ $t('systemManagement.organization.dialog.importUser') }}：
                <el-button class="btn-upload upload" plain size="small" onclick="chooseUser.click()">
                    {{ $t('systemManagement.organization.dialog.clickUpload') }}
                </el-button>
                <input id="chooseUser" name="files" type="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" @change="handleUpload()">
            </div>
        </span>
        <div v-show="oFiles.name" class="file-name">
            <i class="el-icon-document" />
            <span>{{ oFiles.name }}</span>
        </div>
        <span slot="footer">
            <el-button @click="isShow = false">{{
        $t("table.cancel")
      }}</el-button>
            <el-button type="primary" @click="importUser">{{
        $t("table.confirm")
      }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
import list from '../../../mixins/list';
import ExcelExport from '../../../mixins/excel-export';

export default {
    mixins: [ExcelExport, list],
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            oFiles: { name: '' },
            isDownResult: false,
            downResultList: [],
            fileName: this.$t(
                'systemManagement.organization.dialog.userListResult'
            )
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        }
    },
    methods: {
        // 导入
        handleUpload() {
            const oFiles = document.querySelector('#chooseUser').files;
            this.oFiles = oFiles[0];
        },
        // 下载导入模板
        downloadUserTemplate() {
            this.$service.systemManagement
                .getDownloadUserTemplate({})
                .then((response) => {
                    this.download(
                        response,
                        `${this.$t(
                            'systemManagement.organization.dialog.userList'
                        )}.xlsx`
                    );
                });
        },
        // eslint-disable-next-line max-lines-per-function
        async importUser() {
            // 用户导入
            const _this = this;
            const formData = new FormData();
            formData.append('file', this.oFiles);
            this.isDownResult = false;
            // 上传文件
            await this.$service.systemManagement
                .postImportUser(formData)
                .then((res) => {
                    //  如果body为null，则提示所有用户信息导入成功，下载失败的结果
                    if (res.head.code === '000000') {
                        this.isShow = false;
                        //  刷新页签
                        const tag = {
                            fullPath: '/systemManagement/organization',
                            hash: '',
                            name: 'Organization',
                            path: '/systemManagement/organization',
                            status: 0,
                            title: 'organization'
                        };
                        this.refreshSelectedTag(tag);
                    } else if (res.head.code === '111111') {
                        this.downResultList = res.body;
                        this.isDownResult = true;
                        this.isShow = false;
                    } else if (res.head.code === '991314') {
                        // 导入的用户数量大于1000条
                        this.$alert(
                            `${this.$t(
                                'systemManagement.organization.dialog.exceedMax'
                            )}\n${this.$t(
                                'systemManagement.organization.dialog.exceedMaxTip'
                            )}`,
                            this.$t(
                                'systemManagement.organization.dialog.tipMsg.importTip'
                            ),
                            {
                                type: 'warning',
                                customClass: 'message_box_alert'
                            }
                        ).then(() => {
                            // 超过最大数提示
                        });
                        this.isShow = false;
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
            if (this.isDownResult) {
                import('../../../mixins/Export2Excel').then((excel) => {
                    const tHeader = [
                        '序号',
                        '姓名',
                        '工号',
                        '手机号',
                        '邮箱',
                        '部门',
                        '部门描述',
                        '是否为部门主管(是/否)',
                        '角色',
                        '状态',
                        '错误信息'
                    ];
                    const filterVal = [
                        'number',
                        'userName',
                        'jobNumber',
                        'phone',
                        'email',
                        'belongOrgNames',
                        'orgRemarks',
                        'isManagers',
                        'belongRoleNames',
                        'hasLockName',
                        'errorMsg'
                    ];
                    const data = this.formatJson(
                        this.downResultList,
                        filterVal
                    );
                    excel.export_json_to_excel({
                        header: tHeader,
                        data,
                        filename: _this.fileName
                    });
                });
            }
        },
        // eslint-disable-next-line no-shadow
        formatJson(list, filterVal) {
            // 格式化JSON
            return list.map((v) =>
                filterVal.map((j) => {
                    if (j === 'timestamp') {
                        return this.$tools.parseTime(v[j]);
                    }
                    return v[j];
                })
            );
        }
    }
};
</script>

<style lang="scss" scoped>
#chooseUser {
    display: none;
}
//  导入弹框样式
.import-title {
    margin-top: 26px;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    display: flex;
    align-items: center;
    .el-icon-warning {
        color: #1890ff;
        margin-right: 5px;
        font-size: 18px !important;
    }
    .import-box {
        margin-left: 20px;
    }
    .tips {
        font-size: 12px;
        color: #666666;
    }
}
.import-content {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    .upload {
        color: #3370ff;
        border: 1px solid #3370ff;
    }
}
.file-name {
    font-size: 14px;
    color: #2f2f2f;
    margin-top: 15px;
    i {
        font-size: 18px;
        margin-right: 5px;
    }
}
</style>
<style lang="scss">
.message_box_alert {
    white-space: pre-wrap;
}
</style>
