import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        zentao: {
            // 创建禅道任务并关联缺陷
            creatZentaoTask(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/fault/create_pro_task_of_fault`,
                    method: 'post',
                    data
                });
            },
            // 查询禅道项目列表
            getZentaoProjectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/pro_project/get_pro_project_list`,
                    method: 'post',
                    data
                });
            },
            // 根据禅道项目查询禅道任务列表
            getZentaoTaskList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/pro_task/get_pro_parent_task_list`,
                    method: 'post',
                    data
                });
            },
            // 查询禅道项目名称
            getZentaoProjectNameById(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/pro_project/get_pro_project_name_by_id`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 查询某个项目下关联的禅道任务列表（包含子任务）
            getZentaoTaskListInProject(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/pro_task/get_pro_task_list_contain_child`,
                    method: 'post',
                    loading: false,
                    data
                });
            }
        }
    };

    return service;
};
