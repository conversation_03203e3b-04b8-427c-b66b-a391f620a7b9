<template>
    <div class="info-platform-container" ref="overallChartBox">
        <div class="flex info-container">
            <el-date-picker
                class="custom-date-picker"
                v-model="week"
                type="week"
                :format="displayFormat"
                :picker-options="pickerOptions"
                placeholder="选择周"
                :clearable="false"
                @change="handleWeekChange"
            >
            </el-date-picker>
        </div>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">本周未填写日志人员占比</div>
                <el-button
                    type="text"
                    class="export-button"
                    @click="exportMissingLogStaffRatio"
                    >导出</el-button
                >
            </div>
            <div class="chart" ref="missingLogStaffChart"></div>
        </el-card>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">{{ nextWeekWorkLoadTitle }}</div>
                <el-button
                    type="text"
                    class="export-button"
                    @click="exportWorkloadHours"
                    >导出</el-button
                >
            </div>
            <div class="chart" ref="workloadHoursChart"></div>
        </el-card>
        <el-card class="card chart-container">
            <div class="chart-box-title">本周未分配预计工时任务数量</div>
            <div class="chart" ref="unassignedProjectedTimeChart"></div>
        </el-card>
        <el-card class="card chart-container last">
            <div class="chart-box-title">本周未关闭工期工时预警任务数量</div>
            <div class="chart" ref="unClosedRiskChart"></div>
        </el-card>
        <el-card class="card chart-container last">
            <div class="flex">
                <div class="chart-box-title">
                    产品线未完成人工费用核算条目数量
                </div>
                <el-button
                    type="text"
                    class="export-button"
                    @click="exportUnFinishedFeeNumbers"
                    >导出</el-button
                >
            </div>
            <div class="chart" ref="unFinishedFeeNumbersChart"></div>
        </el-card>
    </div>
</template>
<script>
import moment from 'moment';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import {
    getMissingLogStaffChartOptions,
    getWorkloadHoursChartOptions,
    getUnassignedProjectedTimeChartOptions,
    getUnClosedRiskChartOptions,
    getUnFinishedFeeNumbersOptions
} from './chartOptions';

moment.locale('en', {
    // 设置周一为一周的第一天
    week: {
        dow: 1
    }
});
export default {
    name: 'InfoPlatform',
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            week: new Date(),
            // 设置每周的第一天为周一
            pickerOptions: {
                firstDayOfWeek: 1,
                disabledDate(time) {
                    // 获取当前日期所在周的最后一天
                    const currentWeekEnd = moment().endOf('week');
                    // 禁用当前周之后的日期
                    return time.getTime() > currentWeekEnd;
                }
            },
            charts: {},
            optionsMap: {}
        };
    },
    computed: {
        startOfWeek() {
            const date = moment(this.week);
            return date.startOf('week').format('YYYY-MM-DD');
        },
        endOfWeek() {
            const date = moment(this.week);
            return date.endOf('week').format('YYYY-MM-DD');
        },
        // 是否为历史日期
        isHistory() {
            const currentDate = moment(new Date()).startOf('week');
            const selectedDate = moment(this.week).startOf('week');

            return selectedDate.isBefore(currentDate);
        },
        // 计算显示文本
        displayFormat() {
            const date = moment(this.week);

            const startOfWeek = date.startOf('week').format('M月D日');
            const endOfWeek = date.endOf('week').format('M月D日');
            const weekNumber = date.week();
            const year = date.year();

            return `${year}年第${weekNumber}周（${startOfWeek}-${endOfWeek}）`;
        },
        // 下周工时负载分布占比标题
        nextWeekWorkLoadTitle() {
            const date = moment(this.week).add(7, 'days');
            const nextStartOfWeek = date.startOf('week').format('YYYY-MM-DD');
            const nextEndOfWeek = date.endOf('week').format('YYYY-MM-DD');
            const timePeriod = `${nextStartOfWeek} - ${nextEndOfWeek}`;
            return `下周(${timePeriod})异常工时负载分布占比`;
        }
    },
    mounted() {
        this.activeName === 'infoPlatform' && this.handleWeekChange();
    },
    methods: {
        /**
         * 周改变时的处理
         */
        handleWeekChange() {
            this.setMissingLogStaffChart();
            this.setWorkloadHoursChart();
            this.setUnassignedProjectedTimeChart();
            this.setUnClosedRiskChart();
            this.setUnFinishedFeeNumbersChart();
        },
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const { overallChartBox } = this.$refs;
            const observer = new ResizeObserver(
                debounce((entries) => {
                    myChart.resize();
                }, 100)
            );
            observer.observe(overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 未填写日志人员占比
         */
        async setMissingLogStaffChart() {
            const api = this.$service.reportForm.review.getMissingLogStaffRatio;
            const params = {
                // 是否是历史日期
                dateHistoryFlag: this.isHistory,
                startDate: this.startOfWeek,
                endDate: this.endOfWeek
            };

            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.missingLogStaff =
                    getMissingLogStaffChartOptions(res.body);
                this.initChart('missingLogStaff');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 导出未填写日志人员占比（明细）
         */
        async exportMissingLogStaffRatio() {
            try {
                const api =
                    this.$service.reportForm.review
                        .getMissingLogStaffRatioDetail;
                const params = {
                    // 是否是历史日期
                    dateHistoryFlag: this.isHistory,
                    startDate: this.startOfWeek,
                    endDate: this.endOfWeek
                };
                const res = await api(params);
                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);
                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                this.$tools
                    .downloadExprotFile(
                        res,
                        '本周未填写日志人员占比数据',
                        'xlsx'
                    )
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 下周工时负载情况
         * 因为永远不会查看本周的数据，所以选择历史的日期时，就向后端传历史日期，
         * 选择当周的日期时，就向后端传下周的日期
         */
        async setWorkloadHoursChart() {
            const date = moment(this.week).add(7, 'days');
            const nextStartOfWeek = date.startOf('week').format('YYYY-MM-DD');
            const nextEndOfWeek = date.endOf('week').format('YYYY-MM-DD');

            const api = this.$service.reportForm.review.getWorkloadHoursRatio;
            const params = {
                // 是否是历史日期
                dateHistoryFlag: this.isHistory,
                startDate: this.isHistory ? this.startOfWeek : nextStartOfWeek,
                endDate: this.isHistory ? this.endOfWeek : nextEndOfWeek
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.workloadHours = getWorkloadHoursChartOptions(
                    res.body
                );
                this.initChart('workloadHours');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 导出下周工时负载情况（明细）
         */
        async exportWorkloadHours() {
            try {
                const api =
                    this.$service.reportForm.review.getWorkloadHoursRatioDetail;
                const date = moment(this.week).add(7, 'days');
                const nextStartOfWeek = date
                    .startOf('week')
                    .format('YYYY-MM-DD');
                const nextEndOfWeek = date.endOf('week').format('YYYY-MM-DD');

                const params = {
                    // 是否是历史日期
                    dateHistoryFlag: this.isHistory,
                    startDate: this.isHistory
                        ? this.startOfWeek
                        : nextStartOfWeek,
                    endDate: this.isHistory ? this.endOfWeek : nextEndOfWeek
                };
                const res = await api(params);
                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);
                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                this.$tools
                    .downloadExprotFile(
                        res,
                        `${this.nextWeekWorkLoadTitle}数据`,
                        'xlsx'
                    )
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 未分配预计工时数量
         */
        async setUnassignedProjectedTimeChart() {
            const api =
                this.$service.reportForm.review
                    .getUnassignedProjectedTimeNumbers;
            const params = {
                // 是否是历史日期
                dateHistoryFlag: this.isHistory,
                startDate: this.startOfWeek,
                endDate: this.endOfWeek
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.unassignedProjectedTime =
                    getUnassignedProjectedTimeChartOptions(res.body);
                this.initChart('unassignedProjectedTime');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 未关闭风控数量
         */
        async setUnClosedRiskChart() {
            const api =
                this.$service.reportForm.review.getUnClosedRiskTaskNumbers;
            const params = {
                // 是否是历史日期
                dateHistoryFlag: this.isHistory,
                startDate: this.startOfWeek,
                endDate: this.endOfWeek
            };

            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.unClosedRisk = getUnClosedRiskChartOptions(
                    res.body
                );
                this.initChart('unClosedRisk');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 产品线未完成人工费用核算条目数量
         */
        async setUnFinishedFeeNumbersChart() {
            const api = this.$service.reportForm.review.getUnFinishedFeeNumbers;
            const params = {
                // 是否是历史日期
                dateHistoryFlag: this.isHistory,
                startDate: this.startOfWeek,
                endDate: this.endOfWeek
            };

            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.unFinishedFeeNumbers =
                    getUnFinishedFeeNumbersOptions(res.body);
                this.initChart('unFinishedFeeNumbers');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 导出产品线未完成人工费用核算条目数量（明细）
         */
        async exportUnFinishedFeeNumbers() {
            try {
                const api =
                    this.$service.reportForm.review
                        .getUnFinishedFeeNumbersDetail;
                const params = {
                    // 是否是历史日期
                    dateHistoryFlag: this.isHistory,
                    startDate: this.startOfWeek,
                    endDate: this.endOfWeek
                };
                const res = await api(params);
                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);
                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                this.$tools
                    .downloadExprotFile(
                        res,
                        '产品线未完成人工费用核算条目数量数据',
                        'xlsx'
                    )
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
.info-platform-container {
    .info-container .custom-date-picker {
        width: 300px;
    }
    .card {
        margin-top: 10px;
    }
    .last {
        margin-bottom: 15px;
    }
    .chart-container {
        margin-top: 10px;
        width: 100%;
        height: 300px;
        padding: 10px;
        height: 400px;
        .chart-box-title {
            width: fit-content;
            height: 30px;
            background-color: #3370ff;
            color: #fff;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            padding: 8px;
            border-radius: 10px;
        }
        .radio-group {
            margin-left: auto;
            line-height: 30px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-top: 20px;
        }
        .export-button {
            margin-left: auto;
            font-size: 14px;
        }
        .select-icon {
            width: 15px;
            height: 15px;
        }
        .selector {
            margin-left: 10px;
            margin-top: 2px;
        }
    }
}
</style>
