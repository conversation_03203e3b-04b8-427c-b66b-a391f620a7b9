<template>
    <div>
        <el-dialog :visible.sync="dialogVisible" title="选择风险" width="70%">
            <el-table
                ref="riskTable"
                :data="list"
                @selection-change="handleSelectionChange"
            >
                <el-table-column
                    type="selection"
                    width="50"
                    header-align="center"
                    :selectable="(row) => row.checkedBtnDisabled !== true"
                >
                </el-table-column>
                <el-table-column
                    prop="riskId"
                    label="风险编号"
                    width="150"
                    header-align="center"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="riskTitle"
                    label="风险标题"
                    header-align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="handleClick(scope.row)"
                            class="wrap-text-button"
                            >{{ scope.row.riskTitle }}</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    prop="riskStatus"
                    label="风险状态"
                    width="90"
                    header-align="center"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="riskLevel"
                    label="风险等级"
                    width="90"
                    header-align="center"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="hasActiveSupportItems"
                    label="是否有激活的需支持事项"
                    header-align="center"
                    width="150"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmSelection"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'SelectListDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default: () => []
        },
        weeklyId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            selectedRisks: []
        };
    },
    computed: {
        hasSelectedItems() {
            return this.selectedRisks.length > 0;
        },
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        // 表单内容更新时，自动执行勾选逻辑
        list: {
            handler(val) {
                this.$nextTick(() => {
                    // 清除之前的选择
                    this.$refs.riskTable &&
                        this.$refs.riskTable.clearSelection();
                    // 根据checkedFlag字段自动勾选行
                    if (val && val.length > 0) {
                        val.forEach((row, index) => {
                            if (row.checkedFlag === true) {
                                this.$refs.riskTable.toggleRowSelection(
                                    row,
                                    true
                                );
                            }
                        });
                    }
                });
            },
            immediate: true
        },
        // 在对话框打开时也执行一次勾选逻辑
        visible(val) {
            if (val) {
                this.$nextTick(() => {
                    // 清除之前的选择
                    this.$refs.riskTable.clearSelection();
                    // 根据checkedFlag字段自动勾选行
                    if (this.list && this.list.length > 0) {
                        this.list.forEach((row) => {
                            if (row.checkedFlag === true) {
                                this.$refs.riskTable.toggleRowSelection(
                                    row,
                                    true
                                );
                            }
                        });
                    }
                });
            }
        }
    },
    methods: {
        /**
         * 用户选择时的处理
         * @param {Array} val 选择值
         */
        handleSelectionChange(val) {
            this.selectedRisks = val;
        },
        /**
         * 成功选择风险之后
         */
        async confirmSelection() {
            const api = this.$service.project.weekly.saveSelectedRisk;
            const params = {
                riskIds: this.selectedRisks.map((i) => i.riskId),
                weekReportId: this.weeklyId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.dialogVisible = false;
                this.$emit('success');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 点击风险标题之后的跳转
         * @param {Object} row 行数据
         */
        handleClick(row) {
            this.dialogVisible = false;

            // 先触发路由跳转
            this.$router.push({
                path: 'projectRiskDetail',
                query: {
                    risk_id: row.riskId
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-dialog__body {
    padding: 10px 20px;
}

.wrap-text-button {
    white-space: normal;
    word-break: break-word;
    line-height: 1.5;
    text-align: left;
    height: auto;
    padding: 5px 0;
    width: 100%;
}
</style>
