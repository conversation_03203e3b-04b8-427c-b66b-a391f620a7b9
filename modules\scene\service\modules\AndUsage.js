import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        // 查询关键模块和使用情况
        getKeyModuleAndUsage(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: `/business_project_finance/getKeyModuleAndUsage`,
                method: 'post',
                data
            });
        }
    };

    return service;
};
