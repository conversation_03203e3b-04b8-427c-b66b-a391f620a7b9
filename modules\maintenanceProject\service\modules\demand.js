import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        demand: {
            // 需求列表查询
            getDemandList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/demand/getDemandList`,
                    method: 'post',
                    data
                });
            },
            // 需求详情查询
            getDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/demand/getDemandDetails`,
                    method: 'get',
                    params: data
                });
            },
            // 编辑需求
            updateDemand(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/demand/editDemandDetails `,
                    method: 'post',
                    data
                });
            },
            // 创建禅道任务并关联需求
            createZentaoTask(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/demand/createZenTaoTask`,
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
