<template>
    <div class="related-project-container">
        <el-table
            class="related-project"
            :data="data"
            border
            :show-overflow-tooltip="true"
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :span-method="objectSpanMethod"
        >
            <el-table-column
                v-for="(item, index) in tableConfig"
                :key="index"
                :prop="item.prop"
                :label="item.label"
                :width="item.width ? item.width : ''"
                :align="item.align ? item.align : 'center'"
            >
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { tableConfig } from './tableConfig';

export default {
    name: 'RelatedProject',
    components: {},
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return { tableConfig };
    },
    methods: {
        /**
         * 合并单元格
         * @param {Object} param { row, column, rowIndex, columnIndex }
         * @returns {Object} 单元格合并方式
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只在“禅道项目负责人”这一列进行合并, 其他保持现状
            if (columnIndex !== 0) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }
            // 如果当前行的“项”与上一行的“项”相同，则合并
            if (
                rowIndex > 0 &&
                row.projectManager === this.data[rowIndex - 1].projectManager
            ) {
                return {
                    // 隐藏当前行的单元格
                    rowspan: 0,
                    colspan: 0
                };
            }
            // 计算当前“项”需要合并的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.data.length; i++) {
                if (row.projectManager === this.data[i].projectManager) {
                    rowspan += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.related-project {
    margin-top: 58px;
    border: 1px solid #8c8c8c !important;
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep .el-table .el-table__row {
    height: 35px !important;
}
</style>
