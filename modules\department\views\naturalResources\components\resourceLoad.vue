<template>
    <div class="resource-load">
        <div class="tabGroup">
            <el-radio-group v-model="view" style="align-self: flex-start">
                <el-radio-button label="day">日视图</el-radio-button>
                <el-radio-button label="month">月视图</el-radio-button>
            </el-radio-group>
            <div class="status-info">
                <span class="status-info--low"
                    >工时<={{ view === 'day' ? 8 : 168 }}小时</span
                >
                <span class="status-info--mid"
                    >工时{{ view === 'day' ? '8-12' : '168-252' }}小时</span
                >
                <span class="status-info--high"
                    >工时>{{ view === 'day' ? 12 : 252 }}小时</span
                >
            </div>
            <div class="filter" v-show="view === 'day'">
                <el-button icon="el-icon-arrow-left" @click="prevMonth"
                    >上月</el-button
                >
                <el-date-picker
                    v-model="selectedDate"
                    type="month"
                    placeholder="选择月"
                    @change="handleChange()"
                    :clearable="false"
                    style="width: 150px"
                >
                </el-date-picker>
                <el-button @click="nextMonth"
                    >下月<i class="el-icon-arrow-right"></i
                ></el-button>
            </div>
            <div class="filter" v-show="view === 'month'">
                <el-button icon="el-icon-arrow-left" @click="prevYear"
                    >上一年</el-button
                >
                <el-date-picker
                    v-show="view === 'month'"
                    v-model="selectedDate"
                    type="year"
                    placeholder="选择年"
                    @change="handleChange()"
                    :clearable="false"
                    style="width: 150px"
                >
                </el-date-picker>
                <el-button @click="nextYear"
                    >下一年<i class="el-icon-arrow-right"></i
                ></el-button>
            </div>
            <div style="margin-right: 10px">人员</div>
            <PeopleSelector
                v-model="inputSelectedPeople"
                placeholder="请选择人员"
                :options="options"
                isCollapseTags
                :isRemote="false"
            ></PeopleSelector>
            <el-button
                style="margin-left: 20px"
                type="primary"
                @click="handleChange('view')"
                >查询</el-button
            >
        </div>
        <ElTableVirtualScroll
            :data="allData"
            :height="30"
            @change="(renderData) => (staffList = renderData)"
            keyProp="loginName"
            :throttleTime="2"
            :slowOnMousewheelTime="1"
            :buffer="500"
        >
            <el-table
                ref="resourceLoadRef"
                :data="staffList"
                :header-cell-style="{
                    'text-align': 'center'
                }"
                border
                :height="'calc(100vh - 220px)'"
                row-key="loginName"
                class="resourceTable"
                :header-cell-class-name="hanlderTableHeader"
            >
                <el-table-column
                    label="No."
                    prop="index"
                    width="50"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="人员"
                    min-width="100px"
                    :resizable="false"
                    prop="person"
                    align="left"
                    class-name="table-person"
                >
                    <template slot-scope="scope">
                        <div v-if="scope.row.person !== '汇总'">
                            <el-badge
                                :value="scope.row.area"
                                class="area-badge"
                            >
                                <div
                                    style="
                                        display: flex;
                                        width: 100%;
                                        padding: 0 10px;
                                    "
                                >
                                    <span
                                        v-if="showTitlePermission"
                                        class="title-prefix"
                                    >
                                        {{ scope.row.titleVal }}
                                    </span>
                                    <span v-else style="width: 20px"></span>
                                    <el-link
                                        type="primary"
                                        @click="
                                            showPersonResourceLoad(scope.row)
                                        "
                                    >
                                        {{ scope.row.person }}
                                    </el-link>
                                </div>
                            </el-badge>
                        </div>
                        <div v-else>
                            <span style="margin-left: 16px">{{
                                scope.row.person
                            }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    v-for="(item, index) in this.view === 'day'
                        ? getDaysInMonth(year, month)
                        : getMonthsInYear()"
                    :key="index"
                    :label="item.toString()"
                    :resizable="false"
                    :min-width="columnWidth"
                    :class-name="getHeaderClassName(index)"
                >
                    <template slot-scope="scope">
                        <WorkTimeBar
                            :taskList="
                                scope.row.hoursViewShowVoList[item - 1] || {}
                            "
                            :view="view"
                            @click="
                                workTimeBarClick([
                                    scope.row,
                                    scope.row.hoursViewShowVoList[item - 1],
                                    index >= dividedNumber - 1
                                ])
                            "
                            :isLast="scope.row.person === '汇总'"
                            :isPlanHour="index >= dividedNumber - 1"
                        ></WorkTimeBar>
                    </template>
                </el-table-column>
            </el-table>
        </ElTableVirtualScroll>
        <div class="people-numbers">总计 {{ peopleNumbers }} 人</div>
    </div>
</template>

<script>
import WorkTimeBar from './workTimeBar.vue';
import ElTableVirtualScroll from 'Components/ElTableVirtualScroll.vue';
import { Loading } from 'element-ui';
import i18n from 'wtf-core-vue/src/lang';
import PeopleSelector from 'Components/PeopleSelector';

export default {
    name: 'ResourceLoad',
    components: { WorkTimeBar, ElTableVirtualScroll, PeopleSelector },
    props: {
        // 资源负载的两种数据来源：组织、小组
        orgCode: { type: String, default: '' },
        teamId: { type: String, default: '' }
    },
    data() {
        return {
            // 表格可视区域渲染的数据
            staffList: [],
            // 存储所有表格数据
            allData: [],
            // 日期
            selectedDate: new Date(),
            // 日/月视图
            view: 'day',
            // 可视列表的开始索引
            firstItemIndex: 0,
            // 可视列表的结束索引
            lastItemIndex: 20,
            // 人员组件选项
            options: [],
            // 同一视图同一日期的所有选项
            oriOptions: [],
            // 输入框已选择的人员
            inputSelectedPeople: [],
            // 表格内已选择的人员
            tableSelectedPeople: [],
            // 全选框的状态
            selectAllStatus: false,
            // 当前组织机构下的所有人员
            allLoginNames: [],
            // 日视图的接口
            getResourceLoadByDay:
                this.$service.department.naturalResources.getResourceLoadByDay,
            // 月视图的接口
            getResourceLoadByMonth:
                this.$service.department.naturalResources
                    .getResourceLoadByMonth,
            // 部门资源负载-日视图-任务列表查询
            getTaskListByDay:
                this.$service.department.naturalResources.getTaskListByDay,
            // 部门资源负载-日视图-任务列表查询
            getTaskListByMonth:
                this.$service.department.naturalResources.getTaskListByMonth
        };
    },
    computed: {
        // 是否有查看职称的权限
        showTitlePermission() {
            return this.$store.state.permission.btnDatas.includes(
                'showTitlePermission'
            );
        },
        // 表格列宽
        columnWidth() {
            if (this.view === 'month') {
                return 70;
            }
            return 30;
        },
        // 每个月的天数
        daysNumInMonth() {
            return this.getDaysInMonth(this.year, this.month).length;
        },
        year() {
            return this.selectedDate.getFullYear();
        },
        month() {
            return this.selectedDate.getMonth() + 1;
        },
        // 判断参数是否为空
        isValid() {
            return this.orgCode || this.teamId;
        },
        curDayOrMonth() {
            if (this.view === 'day') {
                return new Date().getDate();
            }
            return new Date().getMonth() + 1;
        },
        // 当前部门总人数
        peopleNumbers() {
            if (this.allData.length === 0) return 0;
            return this.allData.length - 1;
        }
    },
    watch: {
        orgCode(newVal) {
            this.inputSelectedPeople = [];
            this.tableSelectedPeople = [];
            this.oriOptions = [];
            // 清除表格选择项
            this.$refs.resourceLoadRef.clearSelection();
            newVal && this.handleChange();
        },
        teamId(newVal) {
            this.inputSelectedPeople = [];
            this.tableSelectedPeople = [];
            this.oriOptions = [];
            // 清除表格选择项
            this.$refs.resourceLoadRef.clearSelection();
            newVal && this.handleChange();
        },
        selectedDate() {
            // 切换视图视图时使用原来的option
            if (this.options.length >= this.oriOptions.length) {
                this.oriOptions = this.options;
            }
            this.handleChange('date');
        },
        view() {
            if (this.options.length >= this.oriOptions.length) {
                this.oriOptions = this.options;
            }
            this.handleChange('view');
        }
    },
    methods: {
        /**
         * 获取选择的日期
         * @param {String} type 日视图或月视图
         * @return {String} 选择的日期
         */
        getSelectedDate(type) {
            let selectDate;
            if (type === 'day' && this.month <= 9) {
                selectDate = `${this.year}-0${this.month}`;
            } else if (type === 'day' && this.month > 9) {
                selectDate = `${this.year}-${this.month}`;
            } else if (type === 'month') {
                selectDate = `${this.year}`;
            }
            return selectDate;
        },
        /**
         * 获取当月天数
         * @param {*} year 当年
         * @param {*} month 当月
         * @returns {Array} 当月天数的数组
         */
        getDaysInMonth(year, month) {
            const days = new Date(year, month, 0).getDate();
            return Array.from({ length: days }, (v, k) => k + 1);
        },
        /**
         * 获取一年中的月份
         * @returns {Array} 一年中月份的数组
         */
        getMonthsInYear() {
            return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        },
        /**
         * 处理参数变更
         * @param {String} type 类型
         */
        handleChange(type) {
            if (type === 'view' || type === 'date') {
                if (this.options.length >= this.oriOptions.length) {
                    this.oriOptions = this.options;
                }
            }
            if (!this.isValid) {
                this.$message.warning('请选择部门后查询');
                return;
            }
            const loadingInstance = Loading.service({
                text: i18n.t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            this.allData = [];
            const selectDate = this.getSelectedDate(this.view);
            const requestData = {
                orgCode: this.orgCode || '',
                teamId: this.teamId || '',
                loginNames: [
                    ...this.tableSelectedPeople,
                    ...this.inputSelectedPeople
                ],
                selectDate
            };
            let api;
            if (this.view === 'day') {
                api = this.getResourceLoadByDay;
            } else {
                api = this.getResourceLoadByMonth;
            }
            api(requestData)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.allData = res.body.map((i, index, arr) => {
                            i.hoursViewShowVoList.map((j) => {
                                j.taskShowVoList = [];
                                return j;
                            });
                            if (index !== arr.length - 1) {
                                i.index = index + 1;
                            }
                            return i;
                        });

                        this.dividedNumber = res.body[0].dividedNumber;
                        this.allLoginNames = this.allData.map(
                            (i) => i.loginName
                        );
                        if (this.allLoginNames.length > 0) {
                            this.allLoginNames.pop();
                        }
                        this.getOtherDepartmentOptions(this.allLoginNames);
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                })
                .finally(() => {
                    loadingInstance && loadingInstance.close();
                });
        },
        /**
         * 上一月
         */
        prevMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 0) {
                this.selectedDate = new Date(currentYear - 1, 11, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth - 1, 1);
            }
        },
        /**
         * 下一月
         */
        nextMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 11) {
                this.selectedDate = new Date(currentYear + 1, 0, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth + 1, 1);
            }
        },
        /**
         * 上一年
         */
        prevYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear - 1, 0, 1);
        },
        /**
         * 下一年
         */
        nextYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear + 1, 0, 1);
        },
        /**
         * 点击工时获取任务详情
         * @param {Array} value 工时信息
         */
        workTimeBarClick(value) {
            const { loginName } = value[0];
            const { time } = value[1];
            const api =
                this.view === 'day'
                    ? this.getTaskListByDay
                    : this.getTaskListByMonth;
            const params = {
                loginNames: [loginName],
                selectDate: time,
                isQueryPlanHour: value[2]
            };
            api(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        value[1].taskShowVoList = res.body;
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        /**
         * 查看个人资源负载
         * @param {Object} data 数据
         */
        async showPersonResourceLoad(data) {
            const date = this.getSelectedDate(this.view);
            const { loginName, person } = data;
            this.$store.dispatch('frame/setResourceName', person);
            // 缓存页面
            await this.$store.dispatch('tagsView/addView', this.$route);
            this.$router.push({
                path: '/dashboard-index',
                query: {
                    name: loginName,
                    view: this.view,
                    date,
                    consume: this.isConsumed
                }
            });
        },
        /**
         * 当前日期高亮
         * @param {Object} param 当前样式
         * @returns {String?} 类名
         */
        hanlderTableHeader({ row, column, rowIndex, columnIndex }) {
            let curDayOrMonth;
            if (this.selectedDate.getFullYear() !== new Date().getFullYear()) {
                return;
            }
            if (this.view === 'day') {
                if (this.selectedDate.getMonth() !== new Date().getMonth()) {
                    return;
                }
                curDayOrMonth = new Date().getDate() + 1;
            } else {
                curDayOrMonth = new Date().getMonth() + 2;
            }
            if (columnIndex === curDayOrMonth) {
                return 'curDate';
            }
        },
        /**
         * 固定其他资源人员对应的option，令其只能在这个范围内选择
         * @param {String} loginNames 域账号
         */
        async getOtherDepartmentOptions(loginNames) {
            if (this.oriOptions.length !== 0) {
                this.options = this.oriOptions;
                return;
            }
            if (loginNames.length === 0) {
                this.options = [];
                return;
            }
            const allPersonData = this.$store.state.project.currentEmployeeList;
            this.options = allPersonData
                .filter((item) => {
                    return loginNames.includes(item.loginName);
                })
                .sort((a, b) => {
                    if (a.loginName < b.loginName) {
                        return -1;
                    } else if (a.loginName > b.loginName) {
                        return 1;
                    }
                    return 0;
                });
        },
        /**
         * 获取每一列的样式
         * @param {Number} index 序号
         * @returns {String} 样式
         */
        getHeaderClassName(index) {
            // 只有汇总这一列，就不展示节假日
            if (this.allData.length <= 1 || this.view === 'month') return '';
            return this.allData[0]?.hoursViewShowVoList[index]?.workDay
                ? ''
                : 'rest-day';
        }
    }
};
</script>

<style lang="scss" scoped>
.resource-load {
    width: 100%;
    height: 100%;
}
.tabGroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
    margin-bottom: 10px;
    .filter {
        flex: 1;
        display: flex;
        justify-content: center;
    }
}
.people-numbers {
    margin-top: 10px;
}

// 突出显示当天/当月
::v-deep .curDate .cell {
    height: 30px;
    width: 30px !important;
    background-color: rgb(235, 58, 19);
    display: grid;
    place-items: center;
    text-align: center;
    border-radius: 50%;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    line-height: 30px;
}
.status-info--project {
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid #b67a7a;
        margin-right: 5px;
        transform: rotate(90deg);
    }
}
.status-info--non-project {
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid #4a9ef7;
        margin-right: 5px;
        transform: rotate(90deg);
    }
}
.status-info--plan {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        margin-right: 5px;
        border-radius: 50%;
        background-color: #85b67a;
    }
}
.status-info--whole {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: #4a9ef7;
    }
}
.status-info--consumed {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: #999;
    }
}
.area-badge {
    font-size: 12px;
    margin-top: 8px;

    // 修改职称背景颜色
    ::v-deep .el-badge__content {
        background-color: #ff860d;
        font-size: 11px;
    }
}
.title-prefix {
    width: 20px;
    height: 20px;
    border: 1px solid #5a9ef8;
    border-radius: 50%;
    line-height: 20px;
    text-align: center;
}

.status-info--low {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        margin-right: 5px;
        border-radius: 50%;
        background-color: #13ce66;
    }
}
.status-info--mid {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: #ffba00;
    }
}
.status-info--high {
    width: 100px;
    height: 16px;
    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: #ff4949;
    }
}
::v-deep .el-checkbox__inner {
    border: 1px solid !important;
}
// 统一表头高度，修正操作列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}
::v-deep .el-table__cell {
    padding: 3px 0 !important;
}
::v-deep .el-table__cell.rest-day {
    background-color: rgb(245, 245, 245, 0.4);
}
::v-deep td:not(.table-person) .cell {
    padding: 0 auto !important;
    display: flex;
    justify-content: center;
}
::v-deep .rest-day:not(th) .cell {
    background-color: rgb(245, 245, 245, 0.4);
}
// 去除斑马纹
::v-deep .el-table tr.el-table__row:nth-child(even) {
    background-color: #fff !important;
}
::v-deep th.rest-day {
    position: relative;
}
// 留给人员展示更大空间
::v-deep .table-person .cell {
    padding-left: 0 !important;
}

::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
