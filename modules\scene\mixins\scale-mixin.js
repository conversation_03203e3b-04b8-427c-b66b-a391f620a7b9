export default {
    data() {
        return {
            // 父节点ID
            parentId: '',
            // 缩放比例
            scale: 1,
            // 父容器宽度
            parentWidth: 0
        };
    },

    mounted() {
        // 计算缩放比例
        this.computeScale();
        this.initResizeObserver();
    },
    beforeDestroy() {
        const el = document.getElementById(this.parentId);
        if (el && this.resizeObserver) {
            this.resizeObserver.unobserve(el);
        }
    },
    methods: {
        // 监听父元素尺寸变化，重新计算缩放比例
        initResizeObserver() {
            const el = document.getElementById(this.parentId);
            if (el) {
                this.resizeObserver = new ResizeObserver(() => {
                    this.computeScale();
                });
                this.resizeObserver.observe(el);
            }
        },
        // 计算缩放比例
        computeScale() {
            const container = this.$refs.pageRef;
            const parent = container.parentElement;
            this.scale = parent.offsetWidth / container.offsetWidth;
            this.parentWidth = parent.offsetWidth;
        }
    }
};
