import moment from 'moment';
import storage from 'wtf-core-vue/src/methods/storage';
import { v4 as uuidv4 } from 'uuid';
import html2pdf from 'html2pdf.js';
import { Loading } from 'element-ui';
import i18n from 'wtf-core-vue/src/lang';

/**
 * 获取用户域账号
 * @param {VueComponent} scope this
 * @returns {String} 用户域账号
 */
export const getUserAccount = (scope) => {
    const userInfo = storage.getLocalStorage('loginName');

    if (!userInfo) {
        scope.$message.warning('无法获取用户信息，请重新登录！');
    }
    return userInfo;
};

/**
 * 用于获取选中的label，在没有匹配的情况下返回externalStaff（外部评委)
 * @param {VueComponent} Component PeopleSelector组件
 * @returns {String} 选中的label
 */
export const getSelectedLabel = (Component) => {
    const item = Component.$refs.refElSelect.selected;
    if (Array.isArray(item)) {
        return item.map((i) => i.label);
    }
    return item.label;
};

/**
 *
 * 获取默认的日期范围
 * @returns {[Date, Date]} 开始时间和结束时间
 */
export const getDefaultDateRange = () => {
    const now = new Date();
    const start = new Date(
        now.getFullYear(),
        now.getMonth() - 2,
        now.getDate()
    );
    const end = new Date(now.getFullYear(), now.getMonth() + 2, now.getDate());
    return [start, end];
};

/**
 * 获取包含外部评委在内的全部人员信息，用于人员选择的下拉选项
 * @param {*} scope this
 */
export const getExternalStaffPeople = async (scope) => {
    try {
        const api = scope.$service.feature.employee.getExternalStaffList;
        const res = await api();
        if (res.head.code === '000000') {
            scope.$store.commit('feature/SET_EXTERNAL_STAFF', res.body);
        } else {
            scope.$message.error(this.head.message);
        }
    } catch (error) {
        console.error(error);
    }
};
/**
 * 获取该周的所有天数
 * @param {Date} currentWeek 当前周中的任意一天
 * @param {String?} format 格式
 * @returns {Array} 该周的所有天数
 */
export const getDaysOfWeek = (currentWeek, format = 'MM月DD日') => {
    const startOfWeek = moment(currentWeek).startOf('week');
    const endOfWeek = moment(currentWeek).endOf('week');

    // 生成该周的所有天数的数组
    const daysOfWeek = [];
    const currentDay = startOfWeek.clone();

    while (currentDay.isSameOrBefore(endOfWeek)) {
        daysOfWeek.push(currentDay.format(format));
        currentDay.add(1, 'day');
    }
    return daysOfWeek;
};
/**
 * 生成uuid
 * @returns {String} uuid
 */
export const getUuid = () => {
    return uuidv4().replace(/-/g, '');
};
/**
 * 生成pdf
 * @param {HTMLElement} element 元素
 * @param {String} meetingName 会议名称
 */
export const generatePDF = async (element, meetingName = '') => {
    const loadingInstance = Loading.service({
        text: i18n.t('frame.msg.handling'),
        background: 'rgba(0, 0, 0, 0.1)'
    });
    const ratio = element.offsetHeight / element.offsetWidth;
    let length = element.offsetHeight;

    // 根据宽高比例调整pdf的宽高（调整至A4纸的比例）
    if (ratio < 1.5) {
        length = element.offsetWidth * 1.5;
    }
    const options = {
        margin: 20,
        filename: '会议详情.pdf',
        image: { type: 'jpeg', quality: 1 },
        html2canvas: {
            scale: 2,
            useCORS: true,
            letterRendering: true
        },
        jsPDF: {
            unit: 'px',
            // 这里+40px，是因为设置了margin为20，同时放大2倍，导致宽度有40px偏差
            format: [length, element.offsetWidth + 40],
            orientation: 'portrait'
        }
    };
    try {
        html2pdf()
            .set(options)
            .from(element)
            .save(`${meetingName}会议信息.pdf`);
    } catch (error) {
        console.error('PDF generation failed:', error);
    } finally {
        loadingInstance && loadingInstance.close();
    }
};
/**
 * 判断是否为组织者或会议纪要编写人
 * @param {Object} peopleList 会议人员名单
 * @returns {Boolean} 是否为组织者或会议纪要编写人
 */
export const isOrganizerOrWriter = (peopleList) => {
    const userInfo = storage.getLocalStorage('loginName');
    if (!Array.isArray(peopleList) || peopleList.length === 0) {
        return false;
    }
    const writer = peopleList.filter(
        (i) => i.meetingRole === '会议纪要编制人'
    )[0]?.userAccount;

    const organizer = peopleList.filter(
        (i) => i.meetingRole === '会议组织人'
    )[0]?.userAccount;
    return userInfo === writer || userInfo === organizer;
};

/**
 * 判断是否同时为会议纪要编写人与会议纪要审核人
 * @param {Object} peopleList 会议人员名单
 * @returns {Boolean} 是否为组织者或会议纪要编写人
 */
export const isWriterAndReviewer = (peopleList) => {
    const userInfo = storage.getLocalStorage('loginName');
    if (!Array.isArray(peopleList) || peopleList.length === 0) {
        return false;
    }
    const writer = peopleList.filter(
        (i) => i.meetingRole === '会议纪要编制人'
    )[0]?.userAccount;

    const reviewer = peopleList.filter(
        (i) => i.meetingRole === '会议纪要审核人'
    )[0]?.userAccount;

    return userInfo === writer && userInfo === reviewer;
};

/**
 * 获取参会人员列表，转为下拉选项的option
 * @param {Object} headerInfo 会议信息
 * @returns {Array} 下拉选项
 */
export const getMeetingPeopleOptions = (headerInfo) => {
    const list = headerInfo.meetingPartRelateList || [];
    const res = list.map((i) => {
        return {
            loginName: i.replaceUserAccount
                ? i.replaceUserAccount
                : i.userAccount,
            employeeName: i.replaceUserName ? i.replaceUserName : i.userName
        };
    });
    // 使用 reduce 进行去重
    const uniqueRes = res.reduce((acc, curr) => {
        acc[curr.loginName] = curr;
        return acc;
    }, {});

    // 将对象的值转换回数组
    return Object.values(uniqueRes);
};

/**
 * 判断两个对象是否不同（浅拷贝比较）
 * @param {Object} obj1 对象1
 * @param {Object} obj2 对象2
 * @returns {Boolean} 是否不同
 */
export const isObjectDifferent = (obj1, obj2) => {
    return JSON.stringify(obj1) !== JSON.stringify(obj2);
};
