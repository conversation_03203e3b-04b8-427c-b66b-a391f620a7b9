import CommonItems from 'snbcCommon/common/form-items.js';
import { CONSTANTS } from '@/constants';

const { select, input, radio } = CommonItems;
const flowStatus = {
    ...select,
    name: 'OA流程状态',
    modelKey: 'flowStatus',
    elOptions: CONSTANTS.OA_FLOW_STATUS.map((i) => ({
        label: i,
        value: i
    }))
};
const priorityLevel = {
    ...select,
    name: '优先级别',
    modelKey: 'priorityLevel',
    elOptions: CONSTANTS.PRIORITY_LEVEL.map((i) => ({
        label: i,
        value: i
    }))
};
const customerName = {
    ...select,
    name: '客户名称',
    modelKey: 'customerName',
    elOptions: []
};
const productModel = {
    ...select,
    name: '产品型号',
    modelKey: 'productModel',
    elOptions: []
};
const hasRisk = {
    ...radio,
    name: '存在风险',
    modelKey: 'hasRisk',
    elRadios: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
    ]
};
const demandName = {
    ...input,
    name: '需求名称',
    modelKey: 'demandName'
};
// 查询参数初始化
export const queryParams = {
    flowStatus: '',
    priorityLevel: '',
    customerName: '',
    productModel: '',
    hasRisk: '',
    demandName: ''
};
// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '160px'
    },
    items: [
        demandName,
        flowStatus,
        priorityLevel,
        customerName,
        productModel,
        hasRisk
    ]
};
export const navItems = [
    { field: '', name: '所有', queryField: '' },
    { field: '审批', name: 'OA流程未关闭的', queryField: 'flowStatus' },
    {
        field: '高',
        name: '高优先级的',
        queryField: 'priorityLevel'
    },
    {
        field: '是',
        name: '存在风险的',
        queryField: 'hasRisk'
    }
];
