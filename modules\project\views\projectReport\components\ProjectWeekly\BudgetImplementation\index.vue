<template>
    <div>
        <div class="budget-header">
            <div class="title">预算执行情况</div>
        </div>
        <div class="budget-progress-container">
            <div v-if="overallBudget" class="budget-progress-title">
                总体进度条
            </div>
            <BudgetProgress
                v-if="overallBudget"
                :percentage="overallBudget"
                class="budget-progress"
                width="100%"
            />
            <span v-else>未提交预算</span>
        </div>
        <div class="budget-table-container" v-show="overallBudget">
            <el-table :data="tableData" border class="budget-table">
                <el-table-column
                    prop="category"
                    label="科目"
                    width="120"
                    header-align="center"
                    align="center"
                ></el-table-column>
                <el-table-column
                    v-for="item in projectBudget"
                    :key="item.subject"
                    :label="item.subject"
                    header-align="center"
                    align="center"
                >
                    <template slot-scope="scope">
                        <!-- 预算值为0时，执行比例和预算值都显示'-' -->
                        <span v-if="item.budgetValue === 0">-</span>
                        <BudgetProgress
                            v-else-if="scope.row.category === '执行比例'"
                            :percentage="item.executionRatio"
                        />
                        <span v-else
                            >{{ formatBudgetValue(item.budgetValue) }}万</span
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import BudgetProgress from 'project/views/projectReport/components/common/BudgetProgress.vue';

export default {
    name: 'BudgetImplementation',
    components: {
        BudgetProgress
    },
    props: {
        // 总体预算执行比例
        overallBudget: {
            type: Number,
            default: 0
        },
        // 项目预算表格
        projectBudget: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            tableData: [{ category: '预算值' }, { category: '执行比例' }]
        };
    },
    computed: {},
    watch: {},
    methods: {
        /**
         * 将预算值除以10000后截取到一位小数，不进行四舍五入
         * @param {Number} value - 原始预算值
         * @returns {String} - 格式化后的预算值
         */
        formatBudgetValue(value) {
            // 将值除以10000
            const dividedValue = value / 10000;
            // 转为字符串并截取到小数点后一位（不进行四舍五入）
            const stringValue = dividedValue.toString();
            const decimalIndex = stringValue.indexOf('.');

            if (decimalIndex === -1) {
                // 如果没有小数点，添加.0
                return `${stringValue}.0`;
            }
            // 截取到小数点后一位
            return stringValue.substring(0, decimalIndex + 2);
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.budget-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #8c8c8c;
    height: 28px;
    .title {
        @include section-title;
    }
}

.budget-table-container {
    width: 100%;
    overflow-x: auto;
}

.budget-table {
    width: 100%;
}

.budget-progress-container {
    display: flex;
    width: 100%;
    margin-bottom: 15px;
    .budget-progress-title {
        width: 120px;
        text-align: center;
        font-weight: bold;
    }
    .budget-progress {
        width: 100%;
    }
}
</style>
