<template>
    <div class="view">
        <div class="container">
            <div class="password-header">
                {{ $t('frame.changePwd.changePassword') }}
            </div>
            <el-form ref="dataForm" :model="formDataInfo" :rules="rules" :inline="false" label-width="160px" class="content" size="normal" autocomplete="off">
                <el-form-item :label="`${$t('frame.changePwd.columnName.newPwd')}`" prop="newPwd">
                    <el-input v-model.trim="formDataInfo.newPwd" type="password" autocomplete="new-password" :placeholder="$t('frame.changePwd.placeholder.newPwd')" @paste.native.capture.prevent="handlePaste" />
                </el-form-item>

                <el-form-item :label="`${$t('frame.changePwd.columnName.confirmNewPwd')}`" prop="confirmPwd">
                    <el-input v-model.trim="formDataInfo.confirmPwd" type="password" autocomplete="new-password" :placeholder="
              $t('frame.changePwd.placeholder.confirmNewPwd')
            " @paste.native.capture.prevent="handlePaste" />
                </el-form-item>

                <el-form-item :label="`${$t('frame.changePwd.columnName.smsCaptcha')}`" prop="smsCaptcha">
                    <el-input v-model="formDataInfo.smsCaptcha" type="text" autocomplete="new-password" :placeholder="$t('frame.changePwd.placeholder.smsCaptcha')">
                        <el-button type="primary" v-if="sendCountDown <= 0" slot="append" icon="el-icon-position" class="send-sms-code" @click="sendSmsCaptcha">{{ $t("frame.changePwd.sendCodeButton") }}</el-button>
                        <el-button type="info" v-else slot="append" disabled class="send-sms-code--disabled" @click="sendSmsCaptcha">{{
                        $t("frame.changePwd.sendCountdown", [
                            sendCountDown,
                        ])
                        }}</el-button>
                    </el-input>
                </el-form-item>
            </el-form>
            <div class="line" />
            <div class="footer">
                <el-button type="primary" @click="save">{{
          $t("frame.changePwd.save")
        }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { regData } from '../../constants/regular';

export default {
    data() {
        return {
            // 短信发送倒计时
            sendCountDown: 0,
            formDataInfo: {
                newPwd: '',
                confirmPwd: '',
                smsCaptcha: ''
            },
            cipherStrength: 'high'
        };
    },
    computed: {
        // eslint-disable-next-line max-lines-per-function
        rules() {
            // 密码校验
            const ruleFun = (value, callback) => {
                if (value.length < 8) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordLength'))
                    );
                }
                if (this.cipherStrength === 'low') {
                    const passwordReg = regData.PASSWORD_LOW;
                    if (!passwordReg.test(value)) {
                        return callback(
                            new Error(this.$t('frame.msg.checkPasswordLength'))
                        );
                    }
                    callback();
                } else if (this.cipherStrength === 'middle') {
                    const passwordReg = regData.PASSWORD_MIDDLE;
                    if (!passwordReg.test(value)) {
                        return callback(
                            new Error(this.$t('frame.msg.checkPasswordMiddle'))
                        );
                    }
                    callback();
                } else {
                    const passwordReg = regData.PASSWORD_HIGH;
                    if (!passwordReg.test(value)) {
                        return callback(
                            new Error(this.$t('frame.msg.checkPasswordHigh'))
                        );
                    }
                    callback();
                }
            };
            // 校验密码
            const validPassword = (rule, value, callback) => {
                if (!value) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordLength'))
                    );
                }
                ruleFun(value, callback);
            };
            // 校验确认密码
            const validConfirmPassword = (rule, value, callback) => {
                if (!value) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordLength'))
                    );
                }
                if (value !== this.formDataInfo.newPwd) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordSame'))
                    );
                }
                ruleFun(value, callback);
            };
            return {
                newPwd: [
                    {
                        required: true,
                        validator: validPassword,
                        trigger: 'blur'
                    }
                ],
                confirmPwd: [
                    {
                        required: true,
                        validator: validConfirmPassword,
                        trigger: 'blur'
                    }
                ],
                smsCaptcha: [
                    {
                        required: true,
                        message: this.$t(
                            'frame.changePwd.message.ruleEmptySmsCaptcha'
                        ),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[0-9]{6}$/,
                        message: this.$t(
                            'frame.changePwd.message.ruleSmsCaptcha'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    mounted() {
        //  获取密码强度
        this.getPasswordStretch();
    },
    methods: {
        // 发送短信验证码
        sendSmsCaptcha() {
            // 执行倒计时
            this.sendCountDown = 60;
            const t = setInterval(() => {
                if (this.sendCountDown === 0) {
                    clearInterval(t);
                }
                // eslint-disable-next-line no-plusplus
                this.sendCountDown--;
            }, 1000);
            this.$service.frame
                .getSmsVerifCode({ token: '1' })
                .then((response) => {
                    const isSuccess = response.head.code === '000000';
                    if (!isSuccess) {
                        this.$message({
                            message: response.head.message,
                            type: 'error'
                        });
                        clearInterval(t);
                        this.sendCountDown = 0;
                    }
                });
        },
        // 保存接口
        postSave() {
            const data = {
                newPwd: this.$tools.encrypt(this.formDataInfo.newPwd, '3DES'),
                confirmPwd: this.$tools.encrypt(
                    this.formDataInfo.confirmPwd,
                    '3DES'
                ),
                smsCaptcha: this.formDataInfo.smsCaptcha
            };
            this.$service.frame.updatePwd(data).then((response) => {
                const { code } = response.head;
                if (code === '000000') {
                    this.$message({
                        message: this.$t('frame.changePwd.message.saveSuccess'),
                        type: 'success'
                    });
                    // 跳转到登录接口
                    this.loginOutHandler();
                } else {
                    let messageTitle = '';
                    if (code === '990313') {
                        messageTitle = response.head.message;
                    } else {
                        const msg = `frame.bgReturnError[${code}]`;
                        messageTitle = this.$t(msg);
                    }
                    this.$message({
                        message: messageTitle,
                        type: 'error'
                    });
                }
            });
        },
        handlePaste() {
            // 禁止密码复制粘贴，所以该函数为空即可
        },
        save() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.postSave();
                }
            });
        },
        // 登出处理函数
        loginOutHandler() {
            const _this = this;
            // 获取权限按钮
            const params = {
                // 系统标识 0：web系统 1:移动端 2：终端
                sysType: '0'
            };
            this.$service.frame
                .putlogOut(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.loginOutSuccessHandler(_this);
                    } else {
                        this.loginOutFailedHandler(_this);
                    }
                })
                .catch(() => {
                    this.loginOutFailedHandler(_this);
                });
        },
        // 登出成功处理函数
        loginOutSuccessHandler(_this) {
            _this.$store.dispatch('user/logout');
            _this.$router.push(`/login`);
        },
        // 登出失败处理函数
        loginOutFailedHandler(_this) {
            _this.$message({
                title: _this.$t('common.failed'),
                message: _this.$t('frame.msg.loginOutFailed'),
                type: 'success',
                duration: 2000
            });
        },
        // 获取密码强度
        getPasswordStretch() {
            // 调用获取密码强度的接口
            this.$service.frame.getPasswordStrength().then((subRes) => {
                if (subRes.head.code === '000000') {
                    this.cipherStrength = subRes.body.cipherStrength;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    .content {
        width: 650px;
        margin: 25px auto;
        font-weight: 400;
    }

    .line {
        background: #e7e7e7;
        margin-bottom: 25px;
    }

    .password-header {
        height: 35px;
        font-size: 14px;
        font-weight: 500;
        color: #1e222d;
        border-bottom: 1px solid #e7e7e7;
    }

    .el-form-item.is-required.el-form-item--normal {
        margin-top: 26px;
    }
}

.footer {
    text-align: center;
}
.send-sms-code,
.send-sms-code:hover {
    border-radius: 0px;
    color: #ffffff !important;
    width: 160px;
    font-size: 14px;
    background: linear-gradient(0deg, #3370ff, #6b91f8);
}

.el-input-group__append .send-sms-code--disabled {
    border-radius: 0px;
    width: 160px;
    background-color: #f5f7fa;
    color: #909399;
    border: 1px solid #dcdfe6;
}

</style>
