import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange } = CommonItems;
const eventName = {
    ...input,
    name: '事件名称',
    modelKey: 'eventName'
};
const objectType = {
    ...input,
    name: '分类',
    modelKey: 'objectType'
};

const executeResult = {
    ...select,
    name: '执行结果',
    modelKey: 'executeResult',
    elOptions: [
        {
            label: '成功',
            value: '成功'
        },
        {
            label: '失败',
            value: '失败'
        }
    ]
};

const calculateDateRange = {
    ...dateRange,
    name: '执行时间',
    modelKey: 'calculateDateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};
// 查询参数初始化
const queryParams = {
    eventName: '',
    objectType: '',
    executeResult: '',
    calculateDateRange: []
};

// 查询区域配置项
const queryConfigItems = [
    eventName,
    objectType,
    executeResult,
    calculateDateRange
];

const getTableConfig = (scope) => {
    return {
        // 列表查询参数
        queryParams,
        // 查询项配置
        queryConfig: {
            items: queryConfigItems
        },
        // 查询api配置
        queryApi: scope.$service.monitor.message.getEventCondition,
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: [
            {
                label: '事件名称',
                prop: 'eventName',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '分类',
                prop: 'objectType',
                show: true,
                minWidth: 220,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '对象编号',
                prop: 'objectId',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '执行时间',
                prop: 'executeTime',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '执行结果',
                prop: 'executeResult',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '描述',
                prop: 'remark',
                show: true,
                minWidth: 240,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        // 固定表头
        elTableAttrs: {
            'header-cell-style': '{text-align:center}'
        },
        hooks: {
            queryParamsHook: scope.queryParamsHook
        }
    };
};
export { getTableConfig };
