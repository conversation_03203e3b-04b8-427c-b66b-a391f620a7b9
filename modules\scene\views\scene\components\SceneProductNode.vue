<template>
    <div class="scene-product">
        <div
            ref="chartRef"
            class="scene-product-chart"
            v-popover:SceneProductNodePopover
        ></div>
        <el-popover
            ref="SceneProductNodePopover"
            width="450"
            trigger="click"
            popper-class="resources-sceneNode--popper"
            @show="getProjectData"
            ><div
                :style="{ padding: projectList.length > 0 ? '15px' : 0 }"
                id="SceneProductNodePopoverId"
                class="project-detail"
            >
                <div v-for="i in projectList" :key="i.projectName">
                    <el-badge
                        :value="getBadgeValue(i.projectStatus, i.hrCost)"
                        class="area-badge"
                        :style="{
                            '--project-status-color': i.projectStatusColor
                        }"
                    >
                        <div
                            style="display: flex; width: 100%; padding: 0 10px"
                        >
                            <span
                                class="title-prefix"
                                :style="{ backgroundColor: i.strategyColor }"
                            >
                                {{ i.strategy }} </span
                            ><el-link
                                style="height: 20px; line-height: 20px"
                                type="primary"
                                @click="gotoProjectDetail(i.projectId)"
                                >{{ i.projectName }}</el-link
                            >
                        </div>
                    </el-badge>
                </div>
            </div>
        </el-popover>
        <div class="product-text">
            <div class="product-name">{{ name }}</div>
            <div class="product-count">在研{{ hrCost }}人月</div>
        </div>
        <div
            v-if="counts"
            class="product-count-box"
            :class="`product-${position}`"
        >
            <div
                class="count1"
                :class="{ 'visibility-hidden': !counts.strategyNum }"
            >
                {{ counts.strategyNum }}战
            </div>
            <div
                class="count2"
                :class="{ 'visibility-hidden': !counts.noStrategyNum }"
            >
                {{ counts.noStrategyNum }}非
            </div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import router from 'wtf-core-vue/src/router/index.js';
import { getDeviceImg } from 'scene/views/scene/scene-tools.js';

export default {
    name: 'SceneProductNode',
    data() {
        return {
            // 图表实例
            chart: null,
            // 产品名称
            name: '',
            // 产品图片
            iconName: '',
            // 人员统计
            hrCost: 0,
            // 统计数据
            counts: null,
            // 位置标识
            position: '',
            // 点击一级节点之后弹出的项目列表
            projectList: [],
            // 产品集合（前端不展示，用于查询）
            projectSet: '',
            // 场景名称
            sceneName: '',
            // 场景类型（金融机具：国内/海外/商业零售，物流分拣：分拣中心/县级局）
            sceneType: '',
            // 外部大类（金融机具/物流分拣等）
            businessUnit: ''
        };
    },
    mounted() {
        // 初始化数据
        this.initData();
        // 初始化图表
        this.initChart();
    },
    beforeDestroy() {
        if (!this.chart) {
            return;
        }
        this.chart.dispose();
        this.chart = null;
    },
    methods: {
        // 初始化数据
        initData() {
            const { position, name, iconName, hrCost, counts, moreData } =
                this.$vnode.data.node.data;
            this.sceneName = moreData.parentData.sceneName;
            this.productSet = moreData.parentData.productSet;
            this.sceneType = moreData.parentData.sceneType;
            this.businessUnit = moreData.parentData?.businessUnit;
            this.position = position;
            this.name = name;
            this.iconName = getDeviceImg(iconName);
            this.hrCost = hrCost;
            this.counts = counts;
        },
        // 初始化图表
        initChart() {
            const target = this.$refs.chartRef;
            this.chart = echarts.init(target);
            const { planProjectNum, doingProjectNum, publishProjectNum } =
                this.counts;
            const data = [
                { color: '#FF8D1A', value: planProjectNum, name: '规划' },
                { color: '#3FCD7B', value: doingProjectNum, name: '在研' },
                { color: '#1C86FF', value: publishProjectNum, name: '发布' }
            ].filter((item) => item.value > 0);
            const option = {
                tooltip: { show: false },
                legend: { show: false },
                series: [
                    {
                        type: 'pie',
                        radius: ['30px', '35px'],
                        center: ['50%', '50%'],
                        color: data.map((item) => item.color),
                        data,
                        label: { show: true, formatter: '{c}' },
                        labelLine: { show: false, length: -5, length2: -1 },
                        silent: true
                    }
                ],
                graphic: {
                    elements: [
                        {
                            type: 'circle',
                            shape: { cx: 35, cy: 35, r: 30 },
                            style: {
                                fill: 'rgba(25,227,193,0.16)',
                                stroke: null
                            },
                            left: 'center',
                            top: 'center'
                        },
                        {
                            type: 'image',
                            style: {
                                image: this.iconName,
                                width: 36,
                                height: 36
                            },
                            left: 'center',
                            top: 'center'
                        }
                    ]
                }
            };
            this.chart.setOption(option);
        },
        async getProjectData() {
            try {
                const params = {
                    sceneName: this.sceneName,
                    sceneType: this.sceneType,
                    productType: this.name,
                    productSet: this.productSet ? [this.productSet] : [],
                    businessUnit: this.businessUnit
                };
                const api = this.$service.scene.getProjectList;
                const res = await api(params);
                if (res.head.code === '000000') {
                    if (res.body.length === 0) {
                        this.projectList = [];
                        this.$refs.SceneProductNodePopover.doClose();
                        this.$message.warning('暂无数据');
                        return;
                    }
                    this.projectList = res.body.map((i) => {
                        i.strategy = i.strategy[0];
                        i.strategyColor =
                            i.strategy === '战' ? '#00baad' : '#2a82e4';
                        if (i.projectStatus === '在研') {
                            i.projectStatusColor = '#3fcd7b';
                        } else if (i.projectStatus === '发布') {
                            i.projectStatusColor = '#2a82e4';
                        } else {
                            i.projectStatusColor = '#ff8d1a';
                        }
                        return i;
                    });
                    this.$nextTick(() => {
                        this.$refs.SceneProductNodePopover.updatePopper();
                    });
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 跳转至项目详情页面
         * @param {String} projectId 项目编号
         * @param {String} projectName 项目名称
         */
        async gotoProjectDetail(projectId) {
            const valid = projectId;
            if (!valid) {
                this.$message.warning('该项目暂未录入系统');
                return;
            }
            await this.$refs?.SceneProductNodePopover.doClose();
            router.push({
                path: '/project/baseInfo',
                query: {
                    id: projectId,
                    from: 'scene'
                }
            });
        },
        /**
         * 不显示除在研之外状态的人力
         * @param {String} projectStatus 项目状态
         * @param {String} hrCost 人力
         * @returns {String} 描述
         */
        getBadgeValue(projectStatus, hrCost) {
            if (projectStatus !== '在研') {
                return projectStatus;
            }
            return `${projectStatus}，投入${hrCost}人月`;
        }
    }
};
</script>
<style lang="scss" scoped>
.scene-product {
    width: 130px;
    height: 110px;
    position: relative;
    overflow: visible;
    cursor: pointer !important;
    .scene-product-chart {
        position: absolute;
        width: 130px;
        height: 110px;
        top: -20px;
        left: -30px;
    }
    .product-text {
        position: absolute;
        width: 100%;
        top: 80px;
        text-align: center;
        font-size: 10px;
        line-height: 15px;
        .product-name {
            position: absolute;
            top: 0;
            font-weight: bold;
            white-space: nowrap;
            transform: translateX(calc(-50% + 35px));
        }
        .product-count {
            position: absolute;
            top: 15px;
            min-width: 100%;
            transform: translateX(calc(-50% + 35px));
        }
    }
    .product-count-box {
        position: absolute;
        display: flex;
        .count1,
        .count2 {
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            font-size: 10px;
            border-radius: 12px;
            border: 1px solid;
        }
        .count1 {
            color: #00baad;
            border-color: #00baad;
        }
        .count2 {
            color: #2a82e4;
            border-color: #2a82e4;
        }
    }
    .product-left {
        flex-direction: column;
        height: 54px;
        justify-content: space-between;
        top: 8px;
        right: 30px;
    }
    .product-right {
        flex-direction: column;
        height: 54px;
        justify-content: space-between;
        transform: translateX(-35px) translateY(8px);
    }
    .product-bottom {
        flex-direction: row;
        width: 54px;
        justify-content: space-between;
        top: -30px;
        left: 27%;
        margin-left: -27px;
    }
}
.area-badge {
    font-size: 12px;
    margin-top: 8px;

    // 修改职称背景颜色
    ::v-deep .el-badge__content {
        background-color: var(--project-status-color);
    }
}
.title-prefix {
    width: 22px;
    height: 22px;
    color: white;
    margin-right: 10px;
    border-radius: 50%;
    line-height: 22px;
    text-align: center;
}
.project-detail {
    max-height: 360px;
    overflow: auto;
}
::v-deep .scene-product-chart > div {
    cursor: pointer !important;
}
.scene-product {
    transition: all 0.5s;
}
.scene-product:hover {
    transform: scale(1.05);
    transition: all 0.5s;
}
.visibility-hidden {
    visibility: hidden;
}
</style>
<style lang="scss">
// 弹窗样式，与app同级，只能写在这里
.el-popper.el-popover.resources-sceneNode--popper {
    padding: 0;
    border: 0;
}
</style>
