// 未填写日志人员占比
export const getMissingLogStaffChartOptions = (data) => ({
    grid: {
        left: '4%',
        right: '2%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [
        {
            type: 'category',
            data: data.map((i) => i.item),
            axisTick: {
                alignWithLabel: true
            },
            axisLabel: {
                fontSize: 12,
                width: 80,
                interval: 0,
                margin: 15
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            position: 'left',
            axisLabel: {
                formatter: (value) => `${value}%`
            }
        }
    ],
    series: [
        {
            type: 'bar',
            data: data.map((i) => i.prop),
            label: {
                show: true,
                formatter: (params) => `${params.value}%`
            },
            markPoint: {
                symbol: 'pin',
                symbolSize: 40,
                itemStyle: {
                    color: '#2168e6'
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: (params) => `${data[params.dataIndex].value}人`,
                    fontSize: 10
                },
                data: data.map((item, index) => ({
                    coord: [item.item, item.prop],
                    value: item.value
                }))
            }
        }
    ]
});

// 下周工时负载情况
export const getWorkloadHoursChartOptions = (data) => ({
    legend: {
        data: ['工时负载<=80%占比', '工时负载>=150%占比'],
        textStyle: {
            fontSize: 12
        }
    },
    grid: {
        left: '4%',
        right: '2%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [
        {
            type: 'category',
            data: data.map((i) => i.orgName),
            axisTick: {
                alignWithLabel: true
            },
            axisLabel: {
                fontSize: 12,
                width: 80,
                interval: 0,
                margin: 15
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            position: 'left',
            axisLabel: {
                formatter: (value) => `${value}%`
            }
        }
    ],
    series: [
        {
            name: '工时负载<=80%占比',
            type: 'bar',
            data: data.map((i) => i.lessThan80Prop),
            label: {
                show: true,
                formatter: (params) => `${params.value}%`
            }
        },
        {
            name: '工时负载>=150%占比',
            type: 'bar',
            data: data.map((i) => i.moreThan150Prop),
            label: {
                show: true,
                formatter: (params) => `${params.value}%`
            },
            color: '#a6a6a6'
        }
    ]
});

// 未分配预计工时的任务数量
export const getUnassignedProjectedTimeChartOptions = (data) => ({
    grid: {
        left: '4%',
        right: '2%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [
        {
            type: 'category',
            data: data.map((i) => i.item),
            axisTick: {
                alignWithLabel: true
            },
            axisLabel: {
                fontSize: 12,
                width: 80,
                interval: 0,
                margin: 15
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            position: 'left'
        }
    ],
    series: [
        {
            type: 'bar',
            data: data.map((i) => i.value),
            label: {
                show: true
            }
        }
    ]
});

// 未关闭风控数量
export const getUnClosedRiskChartOptions = (data) => ({
    legend: {
        data: ['关键', '非关键'],
        textStyle: {
            fontSize: 12
        }
    },
    grid: {
        left: '4%',
        right: '2%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [
        {
            type: 'category',
            data: data.map((i) => i.item),
            axisTick: {
                alignWithLabel: true
            },
            axisLabel: {
                fontSize: 12,
                width: 80,
                interval: 0,
                margin: 15
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            position: 'left'
        }
    ],
    series: [
        {
            name: '非关键',
            type: 'bar',
            data: data.map((i) => i.value),
            label: {
                show: true
            },
            stack: 'unClosedRisk',
            color: '#5470c6'
        },
        {
            name: '关键',
            type: 'bar',
            data: data.map((i) => i.total),
            label: {
                show: true
            },
            stack: 'unClosedRisk',
            color: '#c0504e'
        }
    ]
});

// 产品线未完成人工费用核算条目数量
export const getUnFinishedFeeNumbersOptions = (data) => ({
    legend: {
        data: ['开发项目核算条目', '维护项目核算条目'],
        textStyle: {
            fontSize: 12
        }
    },
    grid: {
        left: '4%',
        right: '2%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: [
        {
            type: 'category',
            data: data.map((i) => i.item),
            axisTick: {
                alignWithLabel: true
            },
            axisLabel: {
                fontSize: 12,
                width: 80,
                interval: 0,
                margin: 15
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            position: 'left'
        }
    ],
    series: [
        {
            name: '开发项目核算条目',
            type: 'bar',
            // 开发费用条目
            data: data.map((i) => i.value),
            label: {
                show: true
            }
        },
        {
            name: '维护项目核算条目',
            type: 'bar',
            // 维护费用
            data: data.map((i) => i.total),
            label: {
                show: true
            },
            color: '#c0504e'
        }
    ]
});
