<template>
    <div class="work-time-bar">
        <el-popover
            popper-class="resources-workTimeBar--popper"
            placement="bottom"
            width="320"
            :disabled="workHour === '0.0' || taskList.taskShowVoList === null"
            ref="popover"
        >
            <div class="popoverContent">
                <el-skeleton
                    :rows="4"
                    animated
                    :loading="loading"
                    :throttle="1500"
                >
                    <template>
                        <div
                            class="taskContainer"
                            v-for="(item, index) in taskDetail"
                            :key="index"
                        >
                            <WorkTimeDetailList
                                :index="index"
                                :item="item"
                                :taskDetail="taskDetail"
                                :view="view"
                            ></WorkTimeDetailList>
                        </div>
                    </template>
                </el-skeleton>
            </div>
            <el-button
                v-show="
                    this.workHour !== -1 && this.workHour !== '0.0' && !isLast
                "
                slot="reference"
                :type="type"
                :style="{
                    'height': '30px',
                    'min-width': view === 'day' ? '25px' : '60px',
                    'cursor': 'pointer'
                }"
                @click="handleClick"
            >
                {{ workHour }}
            </el-button>
            <div
                slot="reference"
                v-show="this.workHour !== -1 && isLast"
                :style="{
                    'min-width': view === 'day' ? '25px' : '60px'
                }"
                class="specialHour"
            >
                {{ workHourTextForLast }}
            </div>
            <div
                slot="reference"
                v-show="this.workHour === '0.0' && !isLast"
                :style="{
                    'min-width': view === 'day' ? '25px' : '60px',
                    'color': '#333'
                }"
                class="specialHour"
            >
                {{ workHourText }}
            </div>
        </el-popover>
    </div>
</template>
<script>
import { CONSTANTS } from '@/constants.js';
import WorkTimeDetailList from 'Components/WorkTimeDetailList.vue';

export default {
    name: 'WorkTimeBar',
    components: {
        WorkTimeDetailList
    },
    props: {
        view: { type: String, default: 'day' },
        taskList: { type: Object, default: () => ({ taskShowVoList: [] }) },
        isLast: { type: Boolean, default: false },
        isPlanHour: { type: Boolean, default: false },
        isNormal: { type: Boolean, default: false }
    },
    data() {
        return {
            CIRCLED_NUMBERS: CONSTANTS.CIRCLED_NUMBERS
        };
    },
    computed: {
        // 分配的工作时长可以为0，如果为null，则不显示
        workHour() {
            if (this.isPlanHour) {
                return this.taskList.planHours ?? -1;
            }
            return this.taskList.hours ?? -1;
        },
        // 任务详情
        taskDetail() {
            return this.taskList.taskShowVoList ?? [];
        },
        loading() {
            return this.taskDetail.length === 0;
        },
        // 显示柱状图样式的逻辑
        type() {
            if (this.view === 'day') {
                if (this.workHour > 12) {
                    return 'danger';
                } else if (this.workHour <= 8) {
                    return 'success';
                }
            } else if (this.view === 'month') {
                if (this.workHour > 252) {
                    return 'danger';
                } else if (this.workHour <= 168) {
                    return 'success';
                }
            }
            return 'warning';
        },
        // 动态计算高度
        buttonHeight() {
            if (this.workHour === -1) {
                return 0;
            }
            return 30;
        },
        // 最终页面展示出来的workHour数据
        workHourText() {
            if (!this.isNormal && !this.isLast) return '';
            return this.workHour === '0.0' ? '-' : this.workHour;
        },
        // ‘汇总’这一行展示的数据
        workHourTextForLast() {
            if (this.workHour === '0.0') {
                if (this.isNormal) return '-';
                return '';
            }
            return this.workHour;
        }
    },
    watch: {
        taskDetail(newVal) {
            if (newVal.length !== 0) {
                this.$nextTick(() => {
                    this.$refs.popover.updatePopper();
                });
            }
        }
    },
    methods: {
        handleClick() {
            this.$emit('click');
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.work-time-bar {
    height: 30px;
    display: flex;
    .el-button {
        font-weight: bold;
        color: #fff;
        padding: 0px;
        min-width: 20px;
        font-size: 10px;
        text-align: center;
        margin-top: auto;
    }
    span {
        display: inline-block !important;
    }
}

.success {
    background-color: #67c23a;
}
.danger {
    background-color: #f56c6c;
}
.warning {
    background-color: #e6a23c;
}
.specialHour {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    font-weight: 600;
    line-height: 30px;
}
.work-hour-dialog {
    color: #409eff;
    width: 40px;
    white-space: nowrap;
    margin-left: auto;
}
.task-title {
    margin-left: 25px;
    width: fit-content;
}
</style>
<style lang="scss">
// 弹窗样式，与app同级，只能写在这里
.el-popper.el-popover.resources-workTimeBar--popper {
    max-height: 360px;
    overflow: auto;
    width: 420px !important;
}
</style>
