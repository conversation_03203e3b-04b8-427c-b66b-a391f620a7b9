<template>
    <el-form ref="loginForm" class="login-form" autocomplete="off" label-position="left" label-width="0px">
        <div class="title-container">
            <div>
                <div class="tab-box" :class="{'tab-box-new':isActive}" style="justify-content: center;">
                    <div class="tab-item" :class="{'tab-acitve':isActive}">{{ $t("frame.scanningLogin") }}</div>
                </div>
            </div>
            <div class="code-box">
                <div class="code-content">
                    <div id="qrcode" ref="qrcode" />
                    <div v-show="showMask" class="qrcode-mask" @click="getScan" />
                </div>
                <!-- <div>
                    <span>{{ $t("frame.tip.des") }}</span>
                </div> -->
            </div>
            <!-- <div class="quick-switch-box">
                <div class="wechat-box">
                    <img src="../../../assets/wechat-active.png">
                    <div>{{ $t("frame.weChat") }}</div>
                </div>
                <div class="division-line" />
                <div class="dingding-box">
                    <img src="../../../assets/dingding-disabled.png">
                    <div>{{ $t("frame.dingding") }}</div>
                </div>
            </div> -->
        </div>
    </el-form>
</template>
<script>
import QRCode from 'qrcodejs2';
import storage from 'wtf-core-vue/src/methods/storage';

export default {
    name: 'QrCode',
    props: {
        isActive: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            // 是否展示二维码遮罩
            showMask: false,
            // 二维码定时器
            qrcodeTimer: null
        };
    },
    mounted() {
        // 只要切换就先关闭之前的定时器
        window.clearTimeout(this.qrcodeTimer);
        this.qrcodeTimer = null;
        // 调用获取二维码接口
        this.getScan();
    },
    destroyed() {
        window.clearTimeout(this.qrcodeTimer);
        this.qrcodeTimer = null;
    },
    methods: {
        /**
         * 设置是否正常登录标识
         * @param {Boolean} flag 是否正常登录标识，true: 走正常登录  false：刷新登录，不显示登录页
         */
        setIsNormalLogin(flag) {
            if (flag) {
                this.$store.dispatch('user/logout');
            }
            this.$emit('changNormal', flag);
        },
        // 转二维码
        qrcodeScan(url) {
            // 清空二维码
            this.$refs.qrcode.innerHTML = '';
            const qrcode = new QRCode('qrcode', {
                // 设置宽度，单位像素
                width: 240,
                // 设置高度，单位像素
                height: 240,
                // 设置二维码内容或跳转地址
                text: url
            });
            qrcode._el.title = '';
        },
        /**
         * 登录二维码
         */
        getScan() {
            const params = {
                thirdType: 41
            };
            this.$service.frame.getWechatUrl(params).then((res) => {
                if (res.head.code === '000000') {
                    this.$nextTick(() => {
                        this.qrcodeScan(res.body.qrCodeUrl);
                        this.showMask = false;
                    });
                    // 调用获取用户二维码状态的接口
                    this.qrcodeTimer = setTimeout(() => {
                        this.getCodeStatus(res.body.qrCodeParams);
                    }, 1000);
                } else {
                    const msg = `frame.bgReturnError[${res.head.code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        //  获取用户二维码状态
        getCodeStatus(data) {
            const params = {
                bindId: data
            };
            this.$service.frame.getCodeStatus(params).then((res) => {
                if (res.head.code === '000000') {
                    //  存储isOriginPwd,用于判断是否需要提示修改密码
                    localStorage.setItem('isOriginPwd', res.body.isOriginPwd);
                    localStorage.setItem('isPwdExpired', res.body.isPwdExpired);
                    this.$store.dispatch('user/login', res.body.token);
                    this.$store.dispatch('user/setUserInfo', res.body);
                    this.$service.frame.getUserInfo({}).then((response) => {
                        if (response.head.code === '000000') {
                            // 存一下用户id
                            storage.setLocalStorage(
                                'userId',
                                response.body.userId || ''
                            );
                        }
                    });
                    // 触发父组件方法
                    this.$emit('toLogin');
                } else if (res.head.code === '900014') {
                    // 如果用户未扫码，则进行轮询
                    window.clearTimeout(this.qrcodeTimer);
                    this.qrcodeTimer = setTimeout(() => {
                        this.getCodeStatus(data);
                    }, 1000);
                } else if (res.head.code === '900013') {
                    // 出现二维码遮罩
                    this.showMask = true;
                    // 关闭定时器
                    window.clearTimeout(this.loginCountTimer);
                    this.loginCountTimer = null;
                    window.clearTimeout(this.qrcodeTimer);
                    this.qrcodeTimer = null;
                } else if (res.head.code === '900011') {
                    const { code } = res.head;
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                    // 出现二维码遮罩
                    this.showMask = true;
                    // 关闭定时器
                    window.clearTimeout(this.loginCountTimer);
                    this.loginCountTimer = null;
                    window.clearTimeout(this.qrcodeTimer);
                    this.qrcodeTimer = null;
                } else {
                    const { code } = res.head;
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                    // 关闭定时器
                    window.clearTimeout(this.loginCountTimer);
                    this.loginCountTimer = null;
                    window.clearTimeout(this.qrcodeTimer);
                    this.qrcodeTimer = null;
                }
            });
        },
        /**
         * 获取顶部菜单列表
         */
        getMenuFuncList() {
            this.$service.systemManagement
                .getMenuList()
                .then((res) => {
                    if (res.head.code === '000000') {
                        if (res.body.length > 0) {
                            this.currentMenuId = res.body[0].navigationId;
                            sessionStorage.setItem(
                                'currentId',
                                this.currentMenuId
                            );
                            // 存储当前的顶部菜单
                            this.$store.dispatch('user/setTopMenu', res.body);
                        }
                        this.getPermissionList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                })
                .catch(() => {
                    // 这里异常后，设置为正常登录，显示登录页面。解决刷新登录空白页问题
                    this.setIsNormalLogin(true);
                });
        }
    }
};
</script>
<style lang="scss" scoped>
// $bg: #2d3a4b;
$bg: #121217;
$dark_gray: #889aa4;
// $light_gray: #eee;
$light_gray: #373d41;

.login-container {
    background: url('../../../assets/login-bg.png') #121217 no-repeat 50px 350px;
    min-height: 100%;
    width: 100%;
    background-color: $bg;
    overflow: hidden;

    .carousel {
        height: 100vh;
        display: flex;
        align-items: center;

        .el-carousel__item {
            text-align: center;
        }
    }

    .login-form {
        background: #fff;
        position: relative;
        width: 460px;
        height: 460px;
        border-radius: 10px;
        box-sizing: border-box;
        padding: 40px;

        .login-btn {
            width: 100%;
            height: 60px;
            font-size: 24px;
            font-weight: normal;
            margin-top: 10px;
            background: linear-gradient(0deg, #3370ff, #6b91f8);
            border-radius: 33px;
        }

        ::v-deep.el-form-item {
            margin-bottom: 30px;
        }
    }

    .tips {
        font-size: 14px;
        color: #373d41;
        margin-bottom: 10px;

        span {
            &:first-of-type {
                margin-right: 16px;
            }
        }
    }

    .svg-container {
        padding: 10px 5px 6px 15px;
        color: $dark_gray;
        vertical-align: middle;
        width: 45px;
        display: inline-block;

        .svg-icon {
            width: 1.5em;
            height: 1.9em;
        }
    }

    .title-container {
        position: relative;
        margin-bottom: 40px;

        .title {
            font-size: 26px;
            color: $light_gray;
            margin: 0px auto 20px auto;
            text-align: center;
            font-weight: bold;
        }

        .set-language {
            color: #373d41;
            position: absolute;
            top: 3px;
            font-size: 26px;
            right: 0px;
            cursor: pointer;
            background: #6f6f6f;
            border-radius: 5px;
        }

        .code-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 30px;

            .code-content {
                border: 1px solid silver;
                width: 260px;
                height: 260px;
                border-radius: 10px;
                margin-bottom: 10px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;

                img {
                    width: 96%;
                    height: 96%;
                }

                .qrcode-mask {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    border-radius: 8px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: url(../../../assets/code-mask.png);
                    cursor: pointer;

                    .refresh {
                        color: rgb(51, 112, 255);
                        cursor: pointer;
                        font-size: 20px;
                    }
                }
            }
        }

        .quick-switch-box {
            display: flex;
            justify-content: center;
            line-height: 30px;
            height: 30px;
            margin-top: 30px;

            .wechat-box {
                margin-right: 45px;
                display: inherit;

                .img {
                    width: 30px;
                }

                div {
                    display: inline;
                    margin-left: 5px;
                }
            }

            .division-line {
                display: block;
                width: 1px;
                background-color: #d8d8d8;
            }

            .dingding-box {
                margin-left: 45px;
                display: inherit;

                img {
                    width: 30px;
                }

                div {
                    display: inline;
                    margin-left: 5px;
                }
            }
        }
    }

    .show-pwd {
        position: absolute;
        right: 10px;
        top: 14px;
        font-size: 16px;
        color: $dark_gray;
        cursor: pointer;
        user-select: none;
    }

    .thirdparty-button {
        position: absolute;
        right: 0;
        bottom: 6px;
    }

    @media only screen and (max-width: 470px) {
        .thirdparty-button {
            display: none;
        }
    }
}

::v-deep .el-form-item--medium {
    .el-form-item__content {
        height: 60px;

        .el-form-item__error {
            margin-right: 40px;
        }
    }
}

::v-deep .el-input--medium {
    .el-input__inner {
        font-size: 18px;

        // 自动填充文本样式
        &:-webkit-autofill::first-line {
            font-size: 18px;
        }
    }
}

.tab-box {
    display: flex;
    padding: 0 52px;
    justify-content: center;
    font-size: 26px;
    font-family: PingFang SC;
    font-weight: bold;
    box-sizing: border-box;

    .tab-item {
        color: #7f7f7f;
        padding-bottom: 10px;
        border-bottom: 4px solid transparent;
        cursor: pointer;
    }

    .tab-acitve {
        color: #3370ff;
        border-bottom: 4px solid #3370ff;
    }
}
.tab-box-new {
    justify-content: space-between;
}

.code-item {
    .el-input {
        width: 65%;

        ::v-deep .el-input-group__append {
            background-color: #fff;
            border-left: 0;
            border-top: 0;
            border-bottom: 0;
            border-right: 0;
        }
    }

    .get-code-nomal {
        position: absolute;
        color: #3370ff;
        top: 12px;
    }

    .get-code {
        right: 25px;
        cursor: pointer;
    }

    .code-count {
        right: 12px;
    }

    .split-line {
        right: 120px;
        top: 14px;
    }
}
</style>
