<template>
  <el-dialog
    :title="$t('systemManagement.role.addUser')"
    :visible.sync="isShow"
    width="50%"
    custom-class="transfer-dialog"
    @close="cancel"
  >
    <el-card>
      <!-- 树结构 -->
      <el-tree
        ref="tree"
        :data="dataOrgTreeList"
        node-key="orgId"
        :props="orgTreeProps"
        :highlight-current="true"
        :default-expanded-keys="defaultExpandOrgTree"
        height="350px"
        @node-click="orgTreeNodeClick"
      >
        <el-tooltip
          slot-scope="{ node }"
          :content="node.label"
          placement="right"
        >
          <span class="custom-tree-node">
            <span class="tree-node-text">{{ node.label }}</span>
          </span>
        </el-tooltip>
      </el-tree>
    </el-card>
    <el-card>
      <div class="header">
        <el-input
          v-model="userFilterText"
          suffix-icon="fa fa-search"
          :placeholder="$t('systemManagement.role.placeholder.msg2')"
        />
      </div>
      <div class="content">
        <div
          v-for="row in dataUserSourceFilterList"
          :key="row.userId"
          class="user-item"
          :class="{ 'card-active': isSelected(row) }"
          @click="addTargetUserItem(row)"
        >
          <img src="../../../assets/default-img.png" alt="" class="user-item-img">
          <div class="user-item-text">
            <div class="left">
              <div class="text-title text-ellipsis">
                {{ row.userName }}
              </div>
              <div class="text-desc">
                <span>{{ row.phone }}</span>
                <span class="text-jobnumber">
                  {{
                    $t(
                      "systemManagement.role.columnName.jobNumber"
                    )
                  }}:{{ row.jobNumber }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="header already-selected">
        <span class="title">{{
          $t("systemManagement.role.message.currentSelectRoleCount", [
            dataTargetUserList.length
          ])
        }}</span>
        <i
          v-show="hasSelected"
          class="el-icon-error text-close-all"
          @click.stop="removeAll()"
        />
      </div>
      <div class="content">
        <div
          v-for="row in dataTargetUserList"
          :key="row.userId"
          class="user-item user-item-selected"
        >
          <img src="../../../assets/default-img.png" alt="" class="user-item-img">
          <span class="user-item-text">
            <div class="text-title-chk">
              <el-tooltip
                :content="row.userName"
                placement="right"
              >
                <span class="text-content text-ellipsis">{{
                  row.userName
                }}</span>
              </el-tooltip>
            </div>
            <i
              class="el-icon-error text-close"
              @click.stop="removeTargetUserItem(row)"
            />
          </span>
        </div>
      </div>
    </el-card>
    <span slot="footer">
      <el-button @click="cancel">{{
        $t("systemManagement.role.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">{{
        $t("systemManagement.components.treeSelectOrg.confirm")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        },
        // 当前选中的角色编号
        roleId: {
            type: String,
            default: ''
        },
        roleName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 定义tree显示的名称字段
            orgTreeProps: {
                label: 'orgName'
            },
            // 用户列表过滤
            userFilterText: '',
            // 接口返回的用户信息列表，用于数据展示
            dataSourceUserList: [],
            // 已选择用户列表
            dataTargetUserList: [],
            // 组织机构树
            dataOrgTreeList: [],
            // 默认展开的树列表
            defaultExpandOrgTree: [],
            // 是否显示全部删除按钮
            hasSelected: false
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        // 当前选中的角色id
        curRoleId() {
            return this.roleId;
        },
        // 当前选中的角色名称
        curRoleName() {
            return this.roleName;
        },
        // 表格数据过滤
        dataUserSourceFilterList() {
            // 忽略空格
            const filterText = (this.userFilterText || '').trim();
            return this.dataSourceUserList.filter(item => {
                // 根据用户名称、手机号、工号进行本地搜索
                return (
                    (item.userName || '').includes(filterText) ||
                    (item.phone || '').includes(filterText) ||
                    (`${  item.jobNumber || ''}`).includes(filterText)
                );
            });
        }
    },
    watch: {
        dataTargetUserList: {
            handler(newVal, oldVal) {
                if (newVal.length > 0) {
                    this.hasSelected = true;
                } else {
                    this.hasSelected = false;
                }
            }
        }
    },
    created() {
        // 先查左侧组织机构树
        this.getOrgTreeList();
        // 默认选中根节点
        this.getUserList({ orgId: '0' });
    },
    methods: {
        // 用户原有列表选中，如果选中显示深色背景
        isSelected(dataItem) {
            return this.dataTargetUserList.some(item => {
                return dataItem.userId === item.userId;
            });
        },
        // 获取组织机构数据
        getOrgTreeList() {
            this.$service.systemManagement
                .getRoleOrgTree({ orgLevel: 0, orgId: '' })
                .then(response => {
                    if (response.head.code === '000000') {
                        const dataTree = response.body || [];

                        this.defaultExpandOrgTree.splice(0);
                        if (dataTree.length > 0) {
                            // 默认展开第一个节点
                            const firstNodeOrgId = dataTree[0].orgId || '0';
                            this.defaultExpandOrgTree.push(firstNodeOrgId);
                        }
                        this.dataOrgTreeList = dataTree;
                    } else {
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.role.message.queryListFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 获取角色对应用户列表
        getUserList({ orgId }) {
            this.$service.systemManagement
                .getRoleOrgUserList({ orgId })
                .then(response => {
                    if (response.head.code === '000000') {
                        this.dataSourceUserList = response.body || [];
                    } else {
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.role.message.queryListFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 添加用户关系
        postAddUserRelation() {
            const param = {};
            param.userIds = this.dataTargetUserList.map(item => {
                return item.userId;
            });
            param.userNames = this.dataTargetUserList.map(item => {
                return item.userName;
            });
            param.userNames = param.userNames.join(',');
            param.roleIds = [this.curRoleId];
            param.roleName = this.curRoleName;
            this.$service.systemManagement
                .postAddUserRoleRelation(param)
                .then(response => {
                    const isSuccess = response.head.code === '000000';
                    // 判断显示结果
                    if (isSuccess) {
                        this.$message({
                            message: this.$t(
                                'systemManagement.role.message.saveSuccess'
                            ),
                            type: 'success'
                        });
                        this.isShow = false;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message:
                            this.$t(msg) ||
                                this.$t(
                                    'systemManagement.role.message.saveFailure'
                                ),
                            type: 'error'
                        });
                    }

                    this.$emit('save-finish', isSuccess);
                });
        },
        // 组织机构节点点击事件
        orgTreeNodeClick(data) {
            this.getUserList(data);
        },
        // 点击选择用户
        addTargetUserItem(row) {
            // 判断列表数据是否已经存在，存在则不重复添加
            const isExist = this.dataTargetUserList.some(item => {
                return item.userId === row.userId;
            });
            if (isExist) {
                this.removeTargetUserItem(row);
                return;
            }

            const tempData = this.dataSourceUserList.filter(item => {
                return item.userId === row.userId;
            });
            if (tempData.length >= 1) {
                this.dataTargetUserList.push(tempData[0]);
            }
        },
        // 移除已选择用户列表
        removeTargetUserItem(row) {
            this.dataTargetUserList = this.dataTargetUserList.filter(item => {
                return item.userId !== row.userId;
            });
        },
        /**
		 * 移除全部已选择用户
		 */
        removeAll() {
            this.dataTargetUserList = [];
        },
        // 取消
        cancel() {
            this.isShow = false;
        },
        // 保存
        save() {
            if (this.dataTargetUserList.length === 0) {
                this.$message({
                    message: this.$t(
                        'systemManagement.role.message.addUserEmptySelect'
                    ),
                    type: 'error'
                });
                return;
            }

            this.postAddUserRelation();
        }
    }
};
</script>
<style lang="scss" scoped>
// 弹框中的面板样式eg. 角色管理中的添加用户弹框
.user-item {
    display: flex;
    align-items: center;
    border-radius: 5px;
    box-sizing: border-box;
    padding: 6px 10px;
    color: #8B8B8B;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 10px;
	box-sizing: border-box;
	border: 1px solid #DFDFDF;
    .user-item-img {
        margin-right: 11px;
		width:30px;
		height:30px;
    }
    .user-item-text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 30px;
        flex: 1;
        .text-desc {
            margin-top: 5px;
        }
        .text-jobnumber {
            margin-right: 11px;
        }
        .text-close {
            font-size: 15px;
            color: #C7C7C7 ;
        }
    }
    &:hover {
        background: #EAF0FF;
        color: #000000;
		border:1px solid transparent;
    }
}
// 选中样式
.user-item-selected {
    background: #EAF0FF;
	color:#2F2F2F;
	border:1px solid transparent;
}
// 高亮样式
.card-active {
    background: #EAF0FF;
    color: #000000;
	border:1px solid transparent;
}
.transfer-dialog{
	.header{
		.text-close-all{
			font-size: 15px;
            color: #8996A8;
			cursor:pointer;
		}
	}
	.already-selected{
		box-sizing: border-box;
		border-bottom:1px solid #EAF0FF;
		padding-bottom:19px;
	}
}
</style>
