const handleBaseData = (data) => {
    return data.map((i) => {
        const { sceneName } = i;
        return {
            label: sceneName,
            value: sceneName
        };
    });
};

// 获取金融机具场景数据
const getFinanceData = (scope, params) => {
    return new Promise((resolve, reject) => {
        scope.$service.scene.business_project_finance
            .get_product_type_by_scene_name(params)
            .then((res) => {
                if (res.head.code !== '000000') {
                    scope.$message.error(res.head.message);
                    reject();
                }
                resolve(res.body);
            })
            .catch((err) => {
                scope.$message.error('场景产品数据获取失败!');
                reject();
            });
    });
};

/**
 * 获取金融机具场景数据
 * @param {Object} scope 作用域
 * @returns {Object} 金融场景的场景构成的下拉选项
 */
// eslint-disable-next-line max-lines-per-function
export const getFinanceSceneList = async (scope) => {
    const domesticParams = [
        {
            productSet: '系统集成',
            sceneType: '国内网点',
            sceneNameList: ['全场景']
        },
        {
            productSet: '整机终端',
            sceneType: '国内网点',
            sceneNameList: ['柜员场景', '厅堂自助', '24H自助区', '外拓营销']
        }
    ];
    const overseaParams = [
        {
            productSet: '系统集成',
            sceneType: '海外网点',
            sceneNameList: ['全场景']
        },
        {
            productSet: '整机终端',
            sceneType: '海外网点',
            sceneNameList: ['柜员场景', '24H自助区']
        }
    ];
    const businessParams = [
        {
            productSet: '整机终端',
            sceneType: '商业零售',
            sceneNameList: ['前台结账场景', '后台现金管理']
        }
    ];
    try {
        const [domestic, oversea, business] = await Promise.all([
            getFinanceData(scope, domesticParams),
            getFinanceData(scope, overseaParams),
            getFinanceData(scope, businessParams)
        ]);
        const handleData = (data) => {
            return data
                .filter((i) => i.sceneName !== '全场景')
                .map((i) => {
                    const { sceneName } = i;
                    let label = sceneName;
                    if (sceneName === '厅堂自助') {
                        label = '大堂场景';
                    }
                    if (sceneName === '24H自助区') {
                        label = '服务场景';
                    }
                    return {
                        label,
                        value: sceneName
                    };
                });
        };
        const domesticOption = handleData(domestic);
        const overseaOption = handleData(oversea);
        const businessOption = handleBaseData(business);
        return {
            国内网点: domesticOption,
            海外网点: overseaOption,
            商业零售: businessOption
        };
    } catch (error) {
        console.error(error);
    }
};

// 获取关键零部件列表
const getKeyModule = (scope, params) => {
    return new Promise((resolve, reject) => {
        scope.$service.scene.finance
            .getProductSetListInFineneceScene(params)
            .then((res) => {
                if (res.head.code === '000000') {
                    if (res.body) {
                        resolve(res.body || []);
                    }
                } else {
                    scope.$message.error(res.head.message);
                    reject();
                }
            })
            .catch((err) => {
                console.error(err, 'error');
                reject();
            });
    });
};
/**
 * 获取关键零部件数据（只有金融机具有）
 * @param {Object} scope 作用域
 * @returns {Object} 关键零部件列表
 */
export const getKeyModuleList = async (scope) => {
    const params = {
        sceneNameList: []
    };
    try {
        const res = await getKeyModule(scope, params);
        return res
            .filter((i) => i.productSet === '核心模块')[0]
            .productTypeVos.map((i) => i.productType);
    } catch (error) {
        console.error(error);
    }
};

// 获取分拣产品场景数据
const getSortingSystemData = (scope, params) => {
    return new Promise((resolve, reject) => {
        scope.$service.scene.business_project_finance
            .get_product_type_by_scene_name(params)
            .then((res) => {
                if (res.head.code !== '000000') {
                    scope.$message.error(res.head.message);
                } else {
                    resolve(res.body || []);
                }
            })
            .catch((error) => {
                console.error(error, 'err');
            });
    });
};

/**
 * 获取分拣产品场景数据
 * @param {Object} scope 作用域
 * @returns {Object} 分拣产品的场景构成的下拉选项
 */
export const getSortingSceneList = async (scope) => {
    const sortingCenterParams = [
        {
            businessUnit: '物流分拣',
            sceneType: '分拣中心',
            sceneNameList: ['自动分拣', '自动识别', '自动输送', '自动装卸']
        }
    ];
    const countyParams = [
        {
            businessUnit: '物流分拣',
            sceneType: '县级局',
            sceneNameList: ['自动分拣', '自动输送', '自动装卸']
        }
    ];
    try {
        const [sortingCenter, country] = await Promise.all([
            getSortingSystemData(scope, sortingCenterParams),
            getSortingSystemData(scope, countyParams)
        ]);
        const sortingCenterOption = handleBaseData(sortingCenter);
        const countryOption = handleBaseData(country);
        return {
            分拣中心: sortingCenterOption,
            县级局: countryOption
        };
    } catch (error) {
        console.error(error);
    }
};
