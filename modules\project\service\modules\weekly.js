/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        weekly: {
            // 获取项目周报下拉列表
            getProjectWeekReportList(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getProjectWeekReportList',
                    method: 'get',
                    params: query
                });
            },
            // 更新项目周报信息(在窗口期内才会更新)
            updateWeekReportInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/updateWeekReportInfo',
                    method: 'put',
                    loading: false,
                    data
                });
            },
            // 编辑周报状态（用户手动切换待更新/已更新）
            editWeeklyStatus(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/updateWeekReportStatus',
                    method: 'put',
                    params: query
                });
            },
            // 获取项目基本信息
            getProjectBaseInfo(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getProjectBaseInfo',
                    method: 'get',
                    loading: false,
                    params: query
                });
            },
            // 获取市场目标表格数据
            getMarketTargetList(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getMarketTargetList',
                    method: 'get',
                    loading: false,
                    params: query
                });
            },
            // 获取一级里程碑信息
            getLevelOneMileStone(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getFirstDetailProgress',
                    method: 'get',
                    loading: false,
                    params: query
                });
            },
            // 编辑一级里程碑
            editLevelOneMileStone(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/updateFirstDetailProgress',
                    method: 'post',
                    data
                });
            },
            // 查询二级里程碑阶段（以防没有一级里程碑但是有二级里程碑的情况）
            getLevelTwoMileStonePhase(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getSecondDetailStage',
                    method: 'get',
                    params: query
                });
            },
            // 查询二级里程碑列表
            getLevelTwoMileStone(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getSecondDetailProgress',
                    method: 'get',
                    params: query
                });
            },
            // 查询周报风险情况
            getRiskList(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getWeekReportRiskInfo',
                    method: 'get',
                    params: query
                });
            },
            // 查询风险列表的选择情况(点击选择风险之后弹出的风险列表)
            getSelectRiskList(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getProjectRiskInfo',
                    method: 'get',
                    params: query
                });
            },
            // 选择需要展示的风险之后，保存选择的风险
            saveSelectedRisk(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/addRiskImageInfo',
                    method: 'post',
                    data
                });
            },
            // 在用户更新完风险之后，更新项目周报的风险
            updateWeeklyRisk(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/updateRiskImageInfo',
                    method: 'put',
                    params: query
                });
            },
            // 查询预算执行情况
            getBudgetInfo(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getBudgetInfo',
                    method: 'get',
                    params: query
                });
            },
            // 查询上周进度和计划
            getLastWeekProgressAndPlanList(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/getWeekReportTaskProgress',
                    method: 'get',
                    params: query
                });
            },
            // 编辑上周进展与本周计划
            editLastWeekProgressAndPlanList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/updateProjectTaskProgress',
                    method: 'post',
                    data
                });
            },
            // 新增项目周报
            addNewWeekly(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/devProjectWeekReport/insertWeekReportInfo',
                    method: 'put',
                    data
                });
            }
        }
    };

    return service;
};
