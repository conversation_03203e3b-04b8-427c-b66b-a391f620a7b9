<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="view detail-box" :class="{'detail-dialog':isDialog}">
    <div class="container detail-view" :class="{'detail-view-dialog':isDialog}">
      <div v-if="!isDialog" class="detail-back">
        <div class="back" @click="goBack">
          <i class="fa fa-arrow-left" />
          <span>{{ $t("systemManagement.informationAnnounce.addPage.back") }}</span>
        </div>
        <div>
          <i class="fa fa-trash-o detail-delete" @click="deleteItem" />
        </div>
      </div>
      <div class="content-box">
        <h3 class="title" :class="{'dialog-title':isDialog}">{{ noticeForm.msgTitle }}</h3>
        <div class="edit-context" v-html="noticeForm.msgContent" />
      </div>
      <div class="file-list">
        <span class="file-title">{{ $t('systemManagement.informationAnnounce.addPage.attachmentsList') }}</span>
        <el-tag
          v-for="(tag,index) in fileList"
          :key="tag.name + index"
          type="info"
        >
          <i v-if="tag.icon === 'pdf'" class="fa fa-file-pdf-o" aria-hidden="true" />
          <i v-else-if="tag.icon === 'png' || tag.icon === 'jpg'|| tag.icon === 'jpeg' " class="fa fa-file-image-o" aria-hidden="true" />
          <i v-else-if="tag.icon === 'doc' || tag.icon === 'docx' " class="fa fa fa-file-word-o" aria-hidden="true" />
          <i v-else-if="tag.icon === 'xls' || tag.icon === 'xlsx' " class="fa fa-file-excel-o" aria-hidden="true" />
          <i v-else class="fa fa-file-powerpoint-o" aria-hidden="true" />
          <span>{{ tag.name }}</span>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
    props: {
        noticeForm: {
            type: Object,
            default() {
                return {};
            }
        },
        isDialog: {
            type: Boolean,
            default: false
        },
        fileList: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
        };
    },
    mounted() {
        if (this.noticeForm.fileUrl.length > 0) {
            this.fileList.length = 0;
            this.noticeForm.fileUrl.forEach(item => {
                const fileNameIndex = item.lastIndexOf('/');
                const name = item.slice(fileNameIndex + 15);
                const suffixIndex = item.lastIndexOf('.');
                const suffix = item.slice(suffixIndex + 1);
                this.fileList.push({
                    name,
                    icon: suffix,
                    url: item
                });
            });
        }
    },
    methods: {
        goBack() {
            this.$emit('goBack');
        },
        deleteItem() {
            this.$emit('handleDelete', { msgId: this.noticeForm.msgId, flag: 'detail' });
        }
    }
};
</script>
<style lang="scss" scoped>
 .detail-back{
	display:flex;
	justify-content: space-between;
	.back{
		color: #5584FB;  // 颜色
		cursor: pointer;   // 悬浮出现小手
	}
	.detail-delete{
		color: #5584FB;  // 颜色
		cursor: pointer;   // 悬浮出现小手

		font-size:16px;
	}
 }
 .detail-box{
	padding:0px;
 }
 .detail-dialog{
	height:calc(100vh - 300px);
}
.detail--view-dialog{
	padding:0px;
}
 .content-box{
	padding:0 10%;
	.title{
		text-align: center;
		padding-bottom:20px;
		border-bottom:1px solid #ececec;
	}
	.dialog-title{
		margin:0;
	}
	.edit-context{
		padding:20px;
		margin-top:30px;
		border: 1px solid #999;
	}
 }
 .file-list{
		margin-top:30px;
		margin-left:10%;
		.file-title{
			font-size:16px;
		}
	}
</style>
