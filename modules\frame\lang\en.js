/**
 * 英文国际化配置：项目模块国际化配置文件route和project必填、httpCode可选
 * @param {Object} route 项目模块路由国际化
 * @param {Object} httpCode 项目模块httpCode国际化
 * @param {Object} project 项目模块除路由、httpCode外，其他信息国际化
 */
export default {
    route: {
        dashboard: 'Home page',
        documentation: 'File',
        userInfo: 'User information',
        baseUserInfo: 'Basic information',
        changePwd: 'Change the password'
    },
    project: {
        systmeTitle: 'BOSS management platform',
        title: 'System login',
        logIn: 'Login',
        username: 'Account number',
        password: 'Password',
        usernamePlaceholder: 'Enter the account number',
        passwordPlaceholder: 'Enter the password',
        checkWindowTitle: 'Check',
        capsTooltip: 'In uppercase',
        goBack: 'Back to home page',
        accountLogin: 'Password',
        scanningLogin: 'Scanning',
        mobileLogin: 'Mobile',
        phonePlaceholder: 'Enter phone number',
        pleaseSelect: 'Please select',
        verCodePlaceholder: 'Enter the validation code',
        getVerificationCode: 'Get',
        forgetPassword: 'Forget the password',
        nextStep: 'Next step',
        pleaseEnterNewPassword: 'Enter your new password',
        confirmPassword: 'Enter the password again',
        changeSucc: 'Modification completed',
        changeSuccText:
            'Password is set successfully, Enter the new password to login.',
        loginNow: 'Login now',
        weChat: 'WeChat',
        dingding: 'dingding',
        lang: {
            zh: '简体中文',
            en: 'English'
        },
        tip: {
            scanning: 'Scan the code to log in quickly. Come and have a try~',
            accountOrMobile: 'SMS login or account password login',
            des: 'Please use wechat [scan]'
        },
        msg: {
            loginSuccess: 'Login succeeded',
            loginFailed: 'Login failed',
            loginOutSuccess: 'Exit succeeded',
            loginOutFailed: 'Exit failed',
            errorPage:
                'Sorry, an internal error 500 occurred on the page you were trying to access',
            errorPageMsg1: 'Sorry, you are not allowed to access this page.',
            errorPageMsg2:
                'If you want to access, please contact the administrator',
            checkPhone: 'Please enter the correct mobile phone number',
            validVerCode: 'Enter the validation code',
            checkPasswordLength: 'The password length is 8-20 digits',
            checkPasswordMiddle: 'Need to include letters and numbers',
            checkPasswordHigh:
                'Need to include uppercase and lowercase letters, numbers, and special characters',
            checkPasswordSame: 'The two passwords are inconsistent',
            handling: 'Processing, please wait...',
            interfaceError: 'There is an abnormality in the system, please contact the administrator!'
        },
        bgReturnError: {
            991701: 'Wrong user name or password',
            991702: 'User ID is frozen',
            990706: 'Account number is already logged in at another terminal',
            991705: 'verification code error',
            111111: 'Operation failed',
            990303: 'The password strength does not match, please re-enter',
            990304: 'The verification code you entered is incorrect, please try again',
            990305: 'The verification code does not exist or has expired, please obtain it again',
            990714: 'Password decryption failed',
            991312: 'Unregistered mobile number',
            990302: 'the password is inconsistent with the new password',
            990312: 'This number is bound to the user. Please change another mobile number',
            990001: 'Missing key parameters',
            990201: 'Your login has failed for 4 times. If you fail again, your account will be frozen',
            990202: 'the account has been frozen because the number of login failures has exceeded. It will return to normal after {0} hours',
            900001: 'User binding without code scanning',
            900002: 'User binding timed out',
            900003: 'The user has bound other accounts',
            900011: 'You are not bound to a WeChat account. Please log in using other methods.',
            900016: 'The user has been unbound',
            900012: 'The user is disabled',
            900013: 'QR code timeout, please refresh the QR code',
            900015: 'The login type is not recognized',
            990310: 'The verification code you entered is incorrect or invalid, please re-enter',
            990311: 'You have entered too many verification codes. Please resend the verification code',
            900017: 'Your SMS verification has failed for 4 times, and your account will be frozen if it fails again',
            900018: 'The account has been frozen because it has exceeded the number of SMS verification failures. It will return to normal after {0} hours',
            920101: 'User Id error',
            920102: 'File path error',
            920103: 'File error or empty file',
            920104: 'File suffix error, unable to upload this type of file',
            920105: 'Cannot upload this type of file',
            920301: 'Upload frequency too high',
            920302: 'Download frequency too high',
            920303: 'The file exceeds {0} and cannot be uploaded',
            920304: 'File format error',
            920305: 'File already exists',
            920306: 'File does not exist',
            920307: 'Breakpoint continuation error'
        },
        // 用户个人信息
        userInfo: {
            title: 'Basic information',
            thirdBindTitle: 'Third party account binding',
            infoName: {
                name: 'Full name',
                phone: 'Phone number',
                jobNumber: 'Employee ID'
            },
            columnName: {
                workStatus: 'Work status',
                merchant: 'Merchant',
                department: 'Department',
                role: 'Role',
                weChat: 'WeChat',
                dingding: 'dingding',
                bind: 'Bind',
                Unbound: 'Unbound',
                Unbinding: 'Unbinding'
            },
            message: {
                filtTypeErrorMsg:
                    'Upload the head image in JPG/PNG format only',
                imgSizeErrorMsg:
                    'The image uploaded must be less than or equal to 50kb',
                imgPixelErrorMsg: 'The uploaded image must be 64 * 64 pixels',
                uploadFailure: 'Upload failed',
                notEmpty: 'Mobile number cannot be empty'
            },
            change: {
                title: 'Tips',
                cancel: 'Cancel',
                tipContent:
                    'Get the current validation code first - then enter the changed Phone number and validation code',
                next: 'Next step',
                verification: 'Validation code',
                placeholderPhone: 'Enter the validation code',
                getVerification: 'Get validation code '
            },
            reselect: 'Reselect',
            changePhone: {
                change: 'Change',
                securityVerify: 'Safety verification',
                setNewPhone: 'Set up a new phone number',
                newPhone: 'New phone number',
                finished: 'Finish',
                msg: {
                    msg1: 'You are',
                    msg2: 'changing the security phone for the user, please conduct security verification firstly:',
                    msg3: 'Click to get the verification code and a text message will be sent to the secure mobile phone',
                    msg4: 'Please enter the phone number',
                    msg5: 'The new phone number can only be composed of 5-16 digits',
                    msg6: 'Please enter the new phone number'
                }
            },
            bind: {
                title: 'Mobile phone verification',
                tip: 'Please verify the current mobile number before binding the third-party account!',
                phone: 'cell-phone number',
                veritify: 'Verification Code',
                getVerification: 'Get verification code',
                verificationMsg: 'Please enter a 6-digit verification code',
                confirm: 'determine',
                wxLogin: 'WeChat login',
                confirmBox: {
                    title: 'Unbinding prompt',
                    msg: 'Are you sure to unbind the third-party account with WeChat platform?'
                },
                msg: {
                    success: 'Unbinding succeeded',
                    canceled: 'Canceled already'
                }
            }
        },
        // 首页
        dashboard: {
            successTip:
                'If the password is changed successfully, you will log in to the system again!',
            welcome: 'Welcome to visit '
        },
        // 修改密码界面
        changePwd: {
            save: 'Confirm to modify',
            confirm: 'confirm',
            cancel: 'Cancel',
            sendCodeButton: 'Send',
            sendCountdown: 'Resend after {0}',
            columnName: {
                newPwd: 'New password',
                confirmNewPwd: 'New password',
                smsCaptcha: 'Validation code'
            },
            placeholder: {
                newPwd: 'Enter your new password',
                confirmNewPwd: 'Enter the new password again',
                smsCaptcha: 'Enter the validation code'
            },
            message: {
                ruleEmptyNewPwd: 'Password cannot be empty',
                ruleNewPwd:
                    'The input content must contain at least two of numbers, English letters or special characters, and shall be greater than or equal to 8 digits, with a maximum length of 20 characters.',
                ruleEqualPwd: 'The two passwords are inconsistent',
                ruleEmptySmsCaptcha: 'Validation code cannot be empty',
                ruleSmsCaptcha:
                    'Validation code format is incorrect. It can only be 6 digits.',
                ruleNoPass: 'Please fill in the input items correctly!',
                saveSuccess:
                    'Password changed successfully, please login again!',
                saveFailure: 'Password modification failed',
                uploadFailure: 'Upload failed'
            },
            changePassword: 'Change Password',
            // 重新选择
            reselect: 'Reselect'
        },
        // 图片选择框
        selectPicBox: {
            title: 'Picture selection box',
            selectPic: 'Select the image...',
            picPreview: 'Image preview',
            picDownload: 'Download images',
            picSave: 'Save the image',
            message: {
                picErrorMsg:
                    'The length of uploaded image cannot exceed {0} bytes',
                picFormatErrorMsg:
                    'Image type must be Gif, JPEG, JPG, PNG or BMP'
            }
        },
        login: {
            welcome: 'Welcome ',
            passwordTips:
                'The initial password poses security risks. The password needs to be changed.',
            expireTitle: 'Password modification prompt',
            expirePasswordTips:
                'The password you used has expired. According to management regulations, please modify the password immediately! Thank you.'
        }
    },
    httpCode: {
        http400: 'The interface signature is incorrect, and the system refuses to process it!',
        http401: 'Your account login time has expired, please log in again',
        http404: 'The URL is inaccessible, please contact your system administrator!',
        http406: 'Your account is logged in on another device, if it is not your own operation, please change the password as soon as possible!',
        http500: 'There is an exception on the server, please contact the administrator!'
    }
};
