import CommonItems from 'snbcCommon/common/form-items.js';
import { CONSTANTS } from '@/constants';

const { select, input, radio } = CommonItems;
const faultSource = {
    ...select,
    name: '缺陷来源',
    modelKey: 'faultSource',
    elOptions: []
};
const faultStatus = {
    ...select,
    name: '缺陷状态',
    modelKey: 'faultStatus',
    elOptions: CONSTANTS.DEFECT_STATUS.map((i) => ({ label: i, value: i }))
};
const faultLevelList = {
    ...select,
    name: '缺陷严重等级',
    modelKey: 'faultLevelList',
    elOptions: CONSTANTS.DEFECT_LEVEL.map((i) => ({ label: i, value: i }))
};
const productModel = {
    ...select,
    name: '产品型号',
    modelKey: 'productModel',
    elOptions: []
};
const whetherAscendTechCommittee = {
    ...radio,
    name: '已上升技术委员会',
    modelKey: 'whetherAscendTechCommittee',
    elRadios: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
    ]
};
const faultPlanType = {
    ...radio,
    name: '关联禅道任务',
    modelKey: 'faultPlanType',
    elRadios: [
        { label: '是', value: '需要创建' },
        { label: '否', value: '不需要创建' }
    ]
};
const hasRisk = {
    ...radio,
    name: '存在风险',
    modelKey: 'hasRisk',
    elRadios: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
    ]
};
const faultTitle = {
    ...input,
    name: '缺陷标题',
    modelKey: 'faultTitle'
};

// 查询参数初始化
export const queryParams = {
    faultPlanType: '',
    faultLevelList: '',
    faultSource: '',
    faultStatus: '',
    faultTitle: '',
    hasRisk: '',
    productModel: '',
    whetherAscendTechCommittee: ''
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '160px'
    },
    items: [
        faultTitle,
        faultSource,
        faultStatus,
        faultLevelList,
        whetherAscendTechCommittee,
        faultPlanType,
        hasRisk,
        productModel
    ]
};

export const navItems = [
    { field: '', name: '所有', queryField: '' },
    { field: '激活', name: '未关闭的', queryField: 'faultStatus' },
    {
        field: ['严重的', '致命的'],
        name: '严重及以上的',
        queryField: 'faultLevelList'
    },
    {
        field: '是',
        name: '存在风险的',
        queryField: 'hasRisk'
    },
    {
        field: '需要创建',
        name: '关联禅道任务的',
        queryField: 'faultPlanType'
    },
    {
        field: '电子流（顾客投诉OA）',
        name: '来自于客诉的',
        queryField: 'faultSource',
        selected: true
    },
    {
        field: '售后服务（TOP问题）',
        name: '来自于售后TOP问题的',
        queryField: 'faultSource',
        selected: true
    }
];

// 校验规则
const shortLengthLimit = {
    min: 0,
    max: 145,
    message: '最多输入145个字符',
    trigger: ['change', 'blur']
};
const longLengthLimit = {
    min: 0,
    max: 2000,
    message: '最多输入2000个字符',
    trigger: ['change', 'blur']
};

const charLimit = (numbers) => {
    return {
        min: 0,
        max: numbers,
        message: `最多输入${numbers}个字符`,
        trigger: ['change', 'blur']
    };
};

export const required = {
    required: true,
    // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
    message: ' ',
    trigger: ['change', 'blur']
};

// 数字校验（最多两位小数）
const numberValidator = (rule, value, callback) => {
    if (value === '' || value === null || value === undefined) {
        callback();
        return;
    }
    const reg = /^(\d+)(\.\d{1,2})?$|^(\.\d{1,2})$/;
    if (!reg.test(value)) {
        callback(new Error('请输入数字，最多两位小数'));
    } else {
        callback();
    }
};

// 表单校验配置
export const rules = {
    faultTitle: [required, shortLengthLimit],
    faultStatus: [required],
    projectManager: [required],
    faultSource: [required],
    productModel: [required, charLimit(30)],
    productName: [charLimit(100)],
    productLine: [required],
    subProductLine: [required],
    applyTime: [required],
    faultLevel: [required],
    faultProp: [{ validator: numberValidator, trigger: ['change', 'blur'] }],
    problemOverview: [required, longLengthLimit],
    causeAnalyse: [longLengthLimit],
    solutionMeasures: [longLengthLimit],
    whetherAscendTechCommittee: [required],
    resolvePersonAccountList: [required],
    faultPlanType: [required]
};
