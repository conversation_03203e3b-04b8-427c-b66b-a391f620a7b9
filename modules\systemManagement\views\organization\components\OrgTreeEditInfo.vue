<template>
  <!-- 添加部门 -->
  <el-dialog
    :title="title"
    :visible.sync="isShow"
  >
    <el-form
      ref="dataForm"
      label-position="right"
      label-width="110px"
      :model="formDataInfo"
      :rules="orgRules"
    >
      <el-form-item :label="$t('systemManagement.organization.dialog.departName')" prop="orgName">
        <el-input v-model="formDataInfo.orgName" :placeholder="$t('systemManagement.organization.dialog.placeholder.msg10')" maxlength="20" />
      </el-form-item>
      <el-form-item v-if="!isEdit" :label="$t('systemManagement.organization.dialog.superiorDepartment')" prop="parentOrgName">
        <el-input
          v-model="formDataInfo.parentOrgName"
          disabled
        />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.organization.dialog.departmentDescription')" prop="orgDesc">
        <el-input
          v-model="formDataInfo.orgDesc"
          :placeholder="$t('systemManagement.organization.dialog.placeholder.msg11')"
          type="textarea"
          maxlength="200"
        />
      </el-form-item>
      <el-form-item v-show="isEdit" :label="$t('systemManagement.organization.dialog.departmentHead')" prop="departmentHead">
        <el-select
          v-model="formDataInfo.supervisor"
          filterable
          clearable
          :placeholder="$t('systemManagement.organization.dialog.placeholder.msg12')"
          :filter-method="filterSupervisor"
          @visible-change="leadersFilterText = ''"
        >
          <el-option
            v-for="item in filterLeaders"
            :key="item.userId"
            :label="item.userName"
            :value="item.userId"
            @click.native="orgLeaderChange(item)"
          >
            {{ item.userName }}({{ $t('systemManagement.organization.dialog.telPhone') }}{{ item.phone }})
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="isEdit" :label="$t('systemManagement.organization.dialog.phone')" prop="supervisorPhone">
        <el-input v-model="formDataInfo.supervisorPhone" maxlength="16" :placeholder="$t('systemManagement.organization.dialog.placeholder.msg3')" />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="cancel">{{
        $t("table.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">{{
        $t("table.confirm")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import listEditInfo from '../../../mixins/list-edit-info.js';

export default {
    mixins: [listEditInfo],
    props: {
        // 当前表单数据
        formData: {
            type: Object,
            default() {
                return {
                    orgName: '',
                    parentId: '',
                    orgDesc: '',
                    supervisor: '',
                    supervisorPhone: '',
                    supervisorName:'',
                    orgId: ''
                };
            }
        }
    },
    data() {
        return {
            // 部门主管列表数据源
            leaders: [],
            // 过滤文本
            leadersFilterText: '',
            // 表单验证规则
            orgRules: {
                orgName: [
                    {
                        required: true,
                        message: this.$t('systemManagement.organization.dialog.rules.msg1'),
                        trigger: ['blur', 'change']
                    }
                ],
                parentOrgName: [
                    {
                        required: true,
                        message: this.$t('systemManagement.organization.dialog.rules.msg2'),
                        trigger: ['blur', 'change']
                    }
                ],
                orgDesc: [
                    {
                        required: false,
                        message: this.$t('systemManagement.organization.dialog.rules.msg3'),
                        trigger: ['blur', 'change']
                    }
                ],
                supervisorPhone: [
                    {
                        required: false,
                        message: this.$t('systemManagement.organization.dialog.rules.msg7'),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^\d{5,16}$/,
                        message: this.$t('systemManagement.organization.dialog.rules.msg4'),
                        trigger: ['blur', 'change']
                    }
                ]
            },
            supervisorCopy: ''
        };
    },
    computed: {
        title() {
            return ((this.isEdit ? this.$t('systemManagement.organization.dialog.edit') : this.$t('systemManagement.organization.dialog.add')) + this.$t('systemManagement.organization.dialog.depart'));
        },
        // 当前表单数据
        formDataInfo() {
            return this.formData;
        },
        // 部门主管过滤的数据
        filterLeaders() {
            return this.leaders.filter((item) => {
                return (item.userName || '').includes(this.leadersFilterText) || (item.phone || '').includes(this.leadersFilterText);
            });
        }
    },
    created() {
        if (this.isEdit) {
            // bugfix:54956,解决第一次加载，出现id后半秒左右显示名称的问题
            this.supervisorCopy = this.formDataInfo.supervisor;
            this.formDataInfo.supervisor = '';
            this.getUserListForOrg();
        }
    },
    methods: {
        // 获取主管列表
        getUserListForOrg() {
            this.leaders = [];
            const params = {
                orgId: this.formDataInfo.orgId
            };
            this.$service.systemManagement.getUserListForOrg(params).then((res) => {
                if (res.head.code === '000000' && res.body && res.body.length > 0) {
                    const data = res.body || [];
                    if (this.isEdit) {
                        // 处理编辑找不到下拉框值的时候，显示未空
                        const isExist = data.some((item) => {
                            return item.userId === this.supervisorCopy;
                        });
                        if (isExist) {
                            this.formDataInfo.supervisor = this.supervisorCopy;
                        }
                        this.leaders = data;
                    }
                }
            });
        },
        // 保存组织机构 - 确认
        postSave() {
            const params = Object.assign({}, this.formDataInfo);
            let service;
            if (!this.isEdit) {
                service = this.$service.systemManagement
                    .postAddOrg(params);
            } else {
                service = this.$service.systemManagement
                    .putUpdateOrg(params);
            }
            service.then((response) => {
                this.doResponse(response);
            });
        },
        filterSupervisor(filterText) {
            this.leadersFilterText = filterText;
        },
        orgLeaderChange(data) {
            this.formDataInfo.supervisorPhone = data.phone;
            this.formDataInfo.supervisorName = data.userName;
        }
    }
};
</script>

<style lang="scss" scoped>
    .el-select{
        width: 100%;
    }
</style>
