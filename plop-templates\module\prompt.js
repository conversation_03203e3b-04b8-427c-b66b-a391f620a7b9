const fs = require('fs');
const path = require('path');
const { notEmpty } = require('../utils.js');

// 当前模板的相对根路径
const rootPath = 'plop-templates/module';

/**
 * 获取目录下所有文件，及其子目录文件
 * @param {string} dir 目录地址
 */
function getAllFiles(dir) {
    let fileList = [];
    fs.readdirSync(dir).forEach(f => {
        // 排除脚本文件
        if (f === 'prompt.js') {
            return;
        }
        // 文件路径转换为全路径
        const filePath = path.resolve(dir, f);
        // 判断文件是否目录，目录则继续读取
        if (fs.statSync(filePath).isDirectory()) {
            fileList = fileList.concat(getAllFiles(filePath));
        } else {
            fileList.push(filePath);
        }
    });
    return fileList;
}

module.exports = {
    description: '创建一个新的模块',
    prompts: [
        {
            type: 'input',
            name: 'moduleName',
            message: '请输入模块名称',
            validate: notEmpty('moduleName')
        }
    ],
    actions: (data) => {
        // 输入的模块名称
        const moduleName = '{{moduleName}}';
        // 执行动作列表
        const actions = [];

        // 模板通用参数
        const actionData = {
            moduleName: moduleName
        };

        // 获取当前目录
        const dir = path.resolve(rootPath);
        const files = getAllFiles(dir);

        // 将读取的文件列表，转换为action
        files.forEach((item) => {
            const relativePath = path.relative(dir, item).replace('.hbs', '');
            actions.push({
                type: 'add',
                path: `modules/${moduleName}/${relativePath}`,
                templateFile: item,
                data: actionData
            });
        });

        return actions;
    }
};
