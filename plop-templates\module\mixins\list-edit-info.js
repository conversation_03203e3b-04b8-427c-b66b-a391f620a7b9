/**
 * 此文件用于封装编辑弹出窗口的共同部分
 * 封装内容包含：
 * 1、props熟悉以及对应的计算属性
 * 2、通用的增删改查操作
 */
export default {
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        },
        // 编辑标识，true：编辑，false：表示新增
        edit: {
            type: Boolean,
            default: false
        },
        // 当前表单数据
        formData: {
            type: Object
        }
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        // 是否为编辑模式
        isEdit: {
            get() {
                return this.edit;
            }
        },
        // 当前表单数据
        formDataInfo() {
            return this.formData;
        }
    },
    methods: {
        // 默认处理响应值接口
        doResponse(response) {
            // 保存是否成功
            const isSuccess = response.head.code === '000000';
            if (isSuccess) {
                this.$message({ message: this.$t('systemManagement.dictionary.message.saveSuccess'), type: 'success' });
                // 关闭窗口
                this.isShow = false;
            } else {
                this.$message({ message: response.head.message || this.$t('systemManagement.dictionary.message.saveFailure'), type: 'error' });
            }
            this.$emit('view-save-finish', isSuccess);
        },
        // 这个保存数据方法，留给组件实现
        postSave() { },
        // 保存新增、编辑内容
        save() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.postSave();
                } else {
                    // 为了与其他页面保持一致，这里的消息暂时去掉
                    // this.$message({ message: this.$t('systemManagement.dictionary.message.ruleNoPass'), type: 'error' });
                }
            });
        },
        // 取消按钮
        cancel() {
            this.isShow = false;
        }
    }
};
