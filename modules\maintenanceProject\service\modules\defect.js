import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        defect: {
            // 缺陷列表查询
            getDefectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/fault/get_fault_list`,
                    method: 'post',
                    data
                });
            },
            // 缺陷详情查询
            getDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/fault/get_fault_detail_info`,
                    method: 'post',
                    data
                });
            },
            // 新建/编辑缺陷
            updateDefect(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/fault/update_or_insert_fault_info `,
                    method: 'post',
                    data
                });
            },
            // 删除缺陷
            deleteDefect(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/fault/delete_fault_info',
                    method: 'post',
                    data
                });
            },
            // 创建禅道任务并关联缺陷
            createZentaoTask(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/fault/create_pro_task_of_fault`,
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
