const state = {
    isLogin: false,
    // 个人资源负载的姓名
    resourceName: ''
};

const mutations = {
    SET_ISLOGIN: (state, flag) => {
        state.isLogin = flag;
    },
    SET_RESOURCENAME: (state, name) => {
        state.resourceName = name;
    }
};

const actions = {
    toggleDevice({ commit }, flag) {
        commit('SET_ISLOGIN', flag);
    },
    setResourceName({ commit }, name) {
        commit('SET_RESOURCENAME', name);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
