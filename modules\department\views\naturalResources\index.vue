<template>
    <div>
        <department-selector
            @input="handleDepartmentChange"
            :teamDisabled="teamDisabled"
            :placeholder="placeholderDepart"
            :key="departmentSelectorKey"
        ></department-selector>
        <div class="box-main">
            <el-tabs v-model="activeName" @tab-click="tabChange">
                <el-tab-pane label="资源负载" name="resourceLoad" :lazy="true">
                    <resource-load
                        :orgCode="departmentCode"
                        :teamId="teamId"
                        class="resourceLoad"
                    ></resource-load>
                </el-tab-pane>
                <el-tab-pane
                    label="资源概况"
                    name="resourceOverview"
                    :lazy="true"
                >
                    <ResourceOverview
                        :orgCode="departmentCode"
                        :isTopLevelDepart="isTopLevelDepart"
                    ></ResourceOverview>
                </el-tab-pane>
                <el-tab-pane
                    label="项目缺编资源"
                    name="vacantResource"
                    v-if="vacantTabPermission"
                    :lazy="true"
                >
                    <vacanct-table
                        :vacanctData="vacancList"
                        :queryParams="vacanctqueryParams"
                        @vacanct-size-change="sizeChange"
                        @vacanct-current-change="currentChange"
                        :departmentCode="departmentCode"
                        @add-success="handleAddSuccess"
                        @search-delete="handleDelSuccess"
                    >
                    </vacanct-table>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import vacanctTable from './components/vacanctTable.vue';
import departmentSelector from 'department/components/departmentSelector';
import ResourceLoad from './components/resourceLoad.vue';
import ResourceOverview from './components/resourceOverview.vue';

export default {
    name: 'NaturalResources',
    components: {
        departmentSelector,
        vacanctTable,
        ResourceLoad,
        ResourceOverview
    },
    data() {
        return {
            // 部门
            departmentCode: '',
            // 是否为一级部门
            isTopLevelDepart: false,
            // 小组
            teamId: '',
            // 当前选中的值
            selectedValue: '',
            // 缺编资源部门选择器
            departmentSelectorKey: 0,
            // 项目所属组织
            vacancList: [],
            vacanctqueryParams: {
                currentPage: 1,
                pageSize: 50,
                total: 0
            },
            vacanctpopInfo: {
                title: '',
                isShow: false,
                type: ''
            },
            activeName: 'resourceLoad'
        };
    },
    computed: {
        teamDisabled() {
            return this.activeName !== 'resourceLoad';
        },
        placeholderDepart() {
            return this.activeName !== 'resourceLoad'
                ? '请选择部门'
                : '请选择部门或小组';
        },
        vacantTabPermission() {
            return this.$store.state.permission.btnDatas.includes(
                'projectVacantResourceTab'
            );
        }
    },
    activated() {
        this.departmentSelectorKey += 1;
    },
    methods: {
        /**
         * 组织或小组改变之后的动作
         * @param {Array} value 选中的值
         */
        handleDepartmentChange(value) {
            this.selectedValue = value[value.length - 1];
            if (value.length === 1) {
                this.isTopLevelDepart = true;
            } else {
                this.isTopLevelDepart = false;
            }
            if (value.length <= 2) {
                // 选择部门
                this.teamId = '';
                this.departmentCode = this.selectedValue;
            } else if (value.length === 3) {
                // 选择小组
                this.departmentCode = '';
                this.teamId = value[2];
            }
            if (this.teamDisabled) {
                // 查询项目缺编资源
                this.getResources(this.departmentCode);
            }
        },
        // 查询表格
        getResources(orgCode) {
            if (this.activeName !== 'vacantResource') return;
            const params = {
                departmentCode: orgCode,
                year: new Date().getFullYear()
            };
            params.pageNum = this.vacanctqueryParams.currentPage;
            params.pageSize = this.vacanctqueryParams.pageSize;
            this.$service.department.naturalResources
                .getnaturalResources(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.vacancList = res.body.list || [];
                        this.vacanctqueryParams.total = res.body.total;
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        handleAddSuccess() {
            this.getResources(this.departmentCode);
        },
        // 分页
        sizeChange(val) {
            this.vacanctqueryParams.pageSize = val;
            this.getResources(this.departmentCode);
        },
        currentChange(val) {
            this.vacanctqueryParams.currentPage = val;
            this.getResources(this.departmentCode);
        },
        handleDelSuccess() {
            this.getResources(this.departmentCode);
        },
        tabChange() {
            // 查询条件为空
            if (!this.teamDisabled && !this.selectedValue) {
                Message.closeAll();
                this.$message.warning('请选择部门或小组后查询');
            }
            // 修改key值，保证重新渲染，否则不显示项目下面的小组
            this.departmentSelectorKey += 1;
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 10px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
.resourceLoad {
    margin-top: 20px;
}
</style>
