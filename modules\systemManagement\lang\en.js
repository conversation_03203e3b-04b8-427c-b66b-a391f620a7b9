/* eslint-disable max-lines */
/**
 * 英文国际化配置：项目模块国际化配置文件route和project必填、httpCode可选
 * @param {Object} route 项目模块路由国际化
 * @param {Object} httpCode 项目模块httpCode国际化
 * @param {Object} project 项目模块除路由、httpCode外，其他信息国际化
 */
export default {
    route: {
        systemManagement: 'System mgmt',
        configuration: 'Configuration mgmt',
        dictionary: 'Dictionary mgmt',
        area: 'Region mgmt',
        organization: 'Organization',
        logger: 'Log mgmt',
        informationAnnouncement: 'Information Notice',
        messageNotice: 'Message notification',
        role: 'Role mgmt',
        permissionManagement: 'Permission mgmt',
        tenant: 'Merchant list',
        tenantlogs: 'Merchant log',
        tenantDetail: 'Merchant Details',
        exportRecords: 'Export Records',
        topMenu: 'Top Menu',
        messageTemplate: 'Message Template',
        superset: 'BI report'
    },
    project: {
        filterTitle: 'Query',
        listTitle: 'Information list',
        submit: 'Save',
        configure: 'To configure',
        cancled: 'Canceled',
        components: {
            treeSelectOrg: {
                title: 'Organization selection',
                confirm: 'Yes',
                cancel: 'Cancel',
                filterPlaceholder: 'Enter the keywords for filtering'
            }
        },
        msg: {
            serviceFailed: 'Connection server exception',
            operateSuccess: 'Operation successful',
            success: 'Success',
            failed: 'Failed',
            createSuccess: 'Create successfully!',
            createFailed: 'Failed to create!',
            editSuccess: 'Modification succeeded!',
            editFailed: 'Modification failed!',
            deleteSuccess: 'Delete succeeded!',
            deleteFailed: 'Delete failed!',
            deleteFailed_1:
                'There are users under this organization, unable to delete',
            deleteFailed_2: 'The region number already exists',
            canceled: 'Cancelled!',
            treeWarningMsg_1: 'Select a tree node of menu type!',
            treeWarningMsg_2:
                'Only buttons and interface type can be added to menu nodes. Select the tree node of menu type!',
            treeWarningMsg_3:
                'Are you sure to change the location of tree node?',
            tableWarningMsg_1: 'Select the data in the list and delete it!',
            deleteDataWarningMsg_1:
                'This operation will permanently delete the selected data. Do you want to continue?',
            required: 'Required !',
            checkMsg_1:
                'Only English, numbers and special characters can be entered!',
            checkMsg_2: 'Length: 1-10 characters!',
            checkMsg_3: 'Rule type must be selected!',
            checkMsg_4: 'One icon must be selected!',
            checkMsg_5: 'Length: 1-45 characters!',
            checkMsg_6: 'The length cannot exceed 255 characters!',
            checkMsg_7: 'Length: 1-20 characters!',
            checkMsg_8: 'Length: 1-100 characters!',
            checkMsg_CodeRepeat: 'Duplicated permission code',
            confirmEnable: 'Are you sure to enable this permission?',
            confirmDisable: 'Are you sure to disable this permission?',
            messageError1:
                'The service you purchased has expired. Please renew it in time',
            messageError2:
                'You have not purchased the service, please try after purchase',
            jobNumberMsg:
                'Please re-enter the employee ID (consisting of letters, numbers, etc.)'
        },
        bgReturnError: {
            111111: 'Operation failed',
            990001: 'Data parsing error',
            950204: 'Message mode cannot be empty',
            950201: 'Message ID error',
            991313: "Only supervisors can modify the user's mobile phone number",
            994006: 'The input box contains illegal characters. Please contact the administrator if you have any questions',
            991103: 'Incorrect organization ID',
            991101: 'Failed to add organization',
            991102: 'Organization ID cannot be empty',
            991104: 'Failed to modify the organization',
            991105: 'Failed to query the organization',
            991106: 'Failed to delete the organization',
            991107: 'The organization cannot be deleted because that there are users under it',
            991108: 'Failed to get the organization tree',
            991109: 'Failed to drag the organization',
            991110: 'Organization name is repeated',
            991111: 'Failed to unbind the relationship between user and organization ',
            991112: 'Please select the users to be exported !',
            991113: 'No operation permission',
            990301: 'User name can not be empty',
            990302: 'User phone number cannot be empty',
            990303: 'User email address cannot be empty',
            990304: 'User information is empty, please operate after logging on to the system',
            990305: 'User ID cannot be empty',
            990306: "User's department cannot be empty",
            990307: 'User ID cannot be empty',
            990308: 'User ID already exists',
            990309: 'User ID can be up to 20 characters',
            993301: 'User phone number format is incorrect',
            993302: 'User phone number can not exceed 16 digits',
            993303: 'User email address format is incorrect',
            993304: 'User email address is up to 64 characters',
            991301: 'User name already exists',
            991302: 'Associated personnel are disabled',
            991303: 'Incorrect user ID',
            991304: 'User does not exist',
            991305: 'Invalid user status',
            991306: 'Only one file must be uploaded at a time',
            991307: 'Only excel files can be uploaded',
            991308: 'User ID already exists',
            991309: 'User phone number already exists',
            991310: 'User email already exists',
            991311: 'User phone number detection failed',
            991312: 'There is no user under this phone number, it is unable to change the administrator.',
            990313: 'The verification code you entered is incorrect or expired, please try again',
            991314: 'The new administrator phone number you entered is the same as the original phone number, please try again',
            990315: 'The verification code has been entered for too many times, please re-send the verification code',
            990316: 'This number has been bound to a user, please change to another mobile phone number',
            991317: 'Phone number is not registered',
            991318: "The user's mobile number cannot be modified without exceeding the control",
            991708: 'Configuration code already exists',
            990504: 'Please delete the child node before deleting',
            990502: 'Unbind role permission Association before deleting',
            991502: 'Duplicate data code',
            990503: 'Please delete the data permission configuration before deleting',
            991401: 'Role name already exists',
            991406: 'Super administrator cannot be disabled',
            991407: 'The super administrator role cannot add or remove users',
            991408: 'The user cannot be assigned the super administrator role',
            991903: 'Dictionary type number already exists',
            991901: 'Dictionary value encoding or dictionary value already exists',
            991801: 'The region number already exists',
            991803: 'Region number cannot be 0',
            991501: 'Add up to 10 top menus',
            991503: 'Navigation menu code already exists',
            950208: 'The announcement has been made',
            920101: 'User Id error',
            920102: 'File path error',
            920103: 'File error or empty file',
            920104: 'File suffix error, unable to upload this type of file',
            920105: 'Cannot upload this type of file',
            920301: 'Upload frequency too high',
            920302: 'Download frequency too high',
            920303: 'The file is too large to upload',
            920304: 'File format error',
            920305: 'File already exists',
            920306: 'File does not exist',
            920307: 'Breakpoint continuation error',
            950213: 'Template number, message channel, and sending platform already exist',
            950209: 'The message template ID cannot be empty',
            950215: 'The message type is illegal',
            950216: 'The message type is illega',
            950217: 'The sending platform is illegal',
            950218: 'The message template operator cannot be empty',
            950223: 'Each variable in the template mapping parameter must be included in the template content',
            990209: 'The message template ID cannot be empty',
            950210: 'The message template ID does not exist',
            950211: 'The status of the message template is illegal',
            950220: 'You must and can only upload one file at a time',
            950219: 'The message template operator ID cannot be empty',
            950221: 'Only Excel files can be uploaded',
            990102: 'Product code already exists',
            991115: 'There are roles under this organization and cannot be deleted'
        },
        permission: {
            permissionName: 'Permission name',
            routingUrl: 'Routing address',
            permissionIcon: 'Icon',
            permissionCode: 'Permission code',
            permissionType: 'Type',
            enableFlag: 'Enable or not',
            operation: 'Operation',
            parentName: 'Parent permission',
            system: 'System',
            menu: 'Menu',
            button: 'Button',
            interface: 'Interface',
            enable: 'Enabled',
            disabled: 'Not enabled',
            permissionList: 'Permission list',
            scopeSetting: 'Perm config',
            searchPlaceholder: 'Enter the keyword',
            dialog: {
                addTitle: 'Newly added permission',
                updateTitle: 'Edit permissions',
                detailTitle: 'View permissions',
                chooseIcon: 'Select the icon',
                remarks: 'Remarks',
                scopeFieldPlaceholder: 'Enter the visible fields',
                remarksPlaceholder: 'Enter the note information',
                scopeClassPlaceholder: 'Enter the permission class name',
                dataScopeTitle: '[System Management] data permission',
                dataScopeName: 'Permission name',
                dataScopeCode: 'Permission code',
                dataScopeColumn: 'Permission field',
                dataScopeType: 'Rule type',
                dataScopeField: 'Visible fields',
                dataScopeClass: 'Permission class name',
                dataScopeType_0: 'Customize',
                dataScopeType_1: 'Visible only to yourself',
                dataScopeType_2: 'Your organization is visible',
                dataScopeType_3:
                    'Your organization and sub-organizations are visible',
                dataScopeType_4: 'All visible'
            },
            placeholder: {
                msg1: 'Please enter permission name',
                msg2: 'Please enter the permission code',
                msg3: 'Please enter the routing address',
                msg4: 'Please enter the remarks',
                msg5: 'Please enter the permission field'
            }
        },
        configuration: {
            paramName: 'Parameter name',
            placeholderParamName: 'Enter the parameter name',
            paramTypeName: 'Parameter type value',
            paramCode: 'Parameter number',
            paramValue: 'Parameter value',
            pleaseInput: 'Please enter',
            paramTypeCode: 'Parameter type number',
            createTime: 'Create time',
            addParam: 'Newly added parameters',
            editParam: 'Edit parameters',
            content: 'Content',
            inputParamName: 'Enter the parameter name',
            inputParamCode: 'Enter the parameter number',
            inputParamValue: 'Enter the parameter value',
            inputParamTypeCode: 'Enter the parameter type number',
            selectParamTypeName: 'Select the parameter type value',
            paramCodeRule:
                'Please enter letters and numbers, and the maximum length is 45',
            message: {
                deleteTipTitle: 'Delete prompt',
                deleteTypeTip: 'Are you sure to delete this item?',
                deleteValueTip: 'Are you sure to delete this data?',
                deleteSuccess: 'Deleted successfully!',
                deleteFailure: 'Failed to delete!'
            }
        },
        area: {
            addRegion: 'Add regions',
            regionName: 'Region name',
            regionCode: 'Region number',
            importArea: 'Import region',
            downloadTemplate: 'Download template',
            describe: 'Description',
            addArea: 'Newly added region',
            fatherName: 'Parent name',
            editArea: 'Edit region',
            inputRegionName: 'Please enter the region name',
            inputRegionCode: 'Please enter the region number',
            message: {
                deleteTipTitle: 'Delete prompt',
                deleteTypeTip:
                    'If delete this region, its sub-regions will be deleted together, are you sure?',
                deleteSuccess: 'Deleted successfully!',
                deleteFailure: 'Failed to delete!'
            },
            placeholder: {
                msg1: 'Please enter the region name',
                msg2: 'Please enter the region number',
                msg3: 'Please enter the description',
                msg4: 'Please enter the parent name'
            }
        },
        dictionary: {
            search: 'Search ',
            view: 'View ',
            add: 'Add',
            addValue: 'Newly added dictionary value ',
            addType: 'Newly added dictionary type',
            edit: 'Edit',
            editValue: 'Edit dictionary value',
            editType: 'Edit dictionary type',
            delete: 'Delete ',
            back: 'Back',
            save: 'Save',
            cancel: 'Cancel ',
            exportAll: 'Export all',
            reset: 'Reset',
            listData: 'List data',
            dictTitle: 'Dictionary value information',
            typeTitle: 'Dictionary type information',
            title: 'Dictionary management',
            columnName: {
                sequence: 'Serial number',
                operate: 'Operation',
                type: 'Dictionary type',
                typeName: 'Dictionary type name',
                typeCode: 'Dictionary type code',
                value: 'Dictionary value',
                valueName: 'Dictionary value name',
                valueParentName: 'Parent dictionary',
                valueCode: 'Dictionary value coding',
                valueDesc: 'Dictionary value description'
            },
            placeholder: {
                valueParent: 'Select',
                msg1: 'Please enter dictionary type',
                msg2: 'Please enter the dictionary value',
                msg3: 'Please enter the dictionary value coding'
            },
            message: {
                placeholderPre: 'Enter',
                selectDefaultText: 'Select',
                ruleName:
                    'Enter Chinese characters, letters, numbers and underscores. Maximum length is 20.',
                ruleCode:
                    'Enter Chinese characters, letters, numbers and underscores. Maximum length is 40.',
                ruleCodeValue:
                    'Please enter characters other than spaces, the maximum length is 50',
                ruleNoPass: 'Please fill in the input items correctly!',
                saveSuccess: 'Save successfully',
                saveFailure: 'Save failed',
                queryListFailure: 'Failed to query list data',
                deleteTipTitle: 'Delete the prompts',
                deleteTypeTip:
                    'Are you sure to delete this data? If there are dictionary values, they will also be deleted!',
                deleteValueTip: 'Are you sure to delete this data?',
                deleteSuccess: 'Delete succeeded!',
                deleteFailure: 'Deletion failed!',
                exportExcelFailure: 'Export log failed',
                noMoreData: 'Has reached the bottom, no more data!'
            }
        },
        logger: {
            querySearch: 'Query',
            username: 'User name',
            clientType: 'Client type',
            organization: 'Organization',
            loginIp: 'Login IP',
            timeRange: 'Time frame',
            operateModule: 'Operation module',
            operateType: 'Operation type',
            query: 'Query',
            reset: 'Reset',
            queryList: 'Query list',
            more: 'More',
            downloadPage: 'Export current page',
            downloadAll: 'Export log',
            noData: 'No data',
            startDate: 'Start time',
            to: 'To',
            endDate: 'End time',
            logExcelName: 'System log sheet',
            columnName: {
                logTime: 'Log time',
                userId: 'User ID',
                username: 'User name',
                organ: 'Organization',
                tenantName: 'Merchant name',
                loginIp: 'Login IP',
                operateModule: 'Module',
                operateType: 'Operation type',
                content: 'Content',
                remarks: 'Remark',
                clientType: 'Client Type',
                browser: 'Browser and version'
            },
            message: {
                inputUsername: 'Enter user name',
                inputClientType: 'Select the type of merchant',
                inputOrgan: 'Please enter the merchant name',
                inputLoginIp: 'Please enter the login IP',
                inputOperateModule: 'Enter the operation module',
                inputOperateType: 'Enter the operation type',
                loginIp: 'Please enter login ip',
                queryListFailure: 'Failed to query list data',
                exportExcelFailure: 'Export log failed',
                export1: 'Please export the log in less than',
                export2: 'day',
                export3: 'The quantity of logs to be exported exceeds',
                export4: ', please modify the inquiry conditions.'
            }
        },
        role: {
            search: 'Search',
            add: 'Newly added',
            edit: 'Edit',
            delete: 'Delete',
            back: 'Back',
            save: 'Save',
            cancel: 'Cancel',
            addRole: 'Create role',
            addUser: 'Add user',
            removeUser: 'Remove the user',
            assignRole: 'Assign role',
            roleList: 'Role list',
            waitAssignUser: 'Users to be assigned',
            roleTitle: 'Role information',
            enable: 'Enable',
            disable: 'Disable',
            permissionSetting: 'Permission settings',
            expand: 'Expand/Collapse',
            allSelect: 'Select All/Select None',
            linkage: 'linkage',
            roleNameTip:
                'The role name format is incorrect. Please re-enter the role name (limited to 10 Chinese characters or 20 characters)',
            defaultPermissionPrompt:
                'Are you sure to reset the role permissions?',
            reSetSuccess: 'Operation succeeded',
            columnName: {
                sequence: 'sequence',
                userName: 'Full name',
                phone: 'Phone number',
                orgName: 'Department',
                isLock: 'Status',
                jobNumber: 'Employee ID',
                roleId: 'Role number',
                roleName: 'Name',
                roleDesc: 'Description',
                department: 'Organization',
                permissionMenu: 'Permission type',
                permissionFunc: 'Permission items',
                permissionData: 'Data permission range'
            },
            placeholder: {
                userFilter: 'Enter user name/phone number',
                msg1: 'Enter the assignable role name',
                msg2: 'Enter the name / employee ID'
            },
            message: {
                placeholderPre: 'Enter',
                placeholderRoleName: 'Enter role name',
                placeholderRoleDesc: 'Enter role description',
                maxLenTip: 'The maximum length cannot exceed {0} characters!',
                saveSuccess: 'Save successfully',
                saveFailure: 'Save failed',
                queryListFailure: 'Failed to query list data',
                deleteTipTitle: 'Delete the prompts',
                deleteValueTip: 'Are you sure to delete this data?',
                deleteSuccess: 'Delete succeeded!',
                deleteFailure: 'Deletion failed!',
                removeSuccess: 'Remove successfully!',
                removeFailure: 'Failed to remove!',
                userListEmptySelect:
                    'Please click and select the user information!',
                removeUserTip:
                    'Are you sure to remove [{0}] from the role [{1}]?',
                deleteRoleTip: 'Are you sure to delete the role [{0}]?',
                assignRoleEmptySelect: 'Select the roles to be assigned',
                addUserEmptySelect: 'Select the user to be added',
                currentSelectUserCount: 'Currently select {0} people',
                currentSelectRoleCount: 'Selected: {0}',
                pleaseUntie: 'Unbound the role related user and delete them'
            },
            permissionTable: {
                permissionsLabel: 'Permissions',
                authorityLabel: 'Data permission',
                radioMine: 'Own',
                radioOrg: 'Organization',
                radioOrgSub: 'Organization and sub-organizations',
                radioAll: 'All'
            },
            tooltip: {
                look: 'View role permissions',
                edit: 'Edit role permissions',
                delete: 'Delete role permissions',
                reset: 'Reset role permissions'
            }
        },
        tenant: {
            search: 'Search',
            reset: 'Reset',
            add: 'Newly added',
            edit: 'Edit',
            save: 'Save',
            cancel: 'Cancel',
            title: 'Merchant',
            detail: 'Detail',
            columnName: {
                tenantCode: 'Merchant code',
                tenantName: 'Merchant name',
                contactName: 'Contact person',
                contactPhone: 'Contact telephone',
                channelName: 'Connection channel',
                dataStatus: 'Enable or not',
                contactAddress: 'Your Region',
                address: 'Detailed address',
                industryInfo: 'Industry information',
                serviceLevel: 'Service level',
                csdName: 'Customer service staff',
                busManName: 'Salesman',
                tenantTags: 'Merchant label',
                remark: 'Remarks',
                createTime: 'Registration time',
                operate: 'Operation'
            },
            placeholder: {
                timeRange: 'Select the time range',
                startDate: 'Start time',
                endDate: 'End time',
                tenantName: 'Enter the merchant name',
                contactName: 'Enter the contact person',
                contactPhone: 'Enter the contact telephone',
                channel: 'Select the connection channel',
                contactAddress: 'Select the region',
                address: 'Enter the detailed address',
                industryInfo: 'Select the industry information',
                serviceLevel: 'Select the service level',
                csdName: 'Select the customer service staff',
                busManName: 'Select the salesman',
                tenantTags: 'Select the merchant label',
                remark: 'Enter the note information'
            },
            message: {
                maxLenTip: 'The maximum length cannot exceed {0} characters!',
                queryListFailure: 'Failed to query list data',
                deleteTipTitle: 'Delete prompt',
                updateStatusTipTitle: 'Confirmation prompt',
                updateStatusTipMsg:
                    'Are you sure to change merchant {1} to [{0}]?',
                updateStatusSuccess: 'Setup succeeded',
                updateStatusFailure: 'Setup failed',
                tenantExport: 'Merchant log record form'
            },
            tenantDetail: {
                tenantNo: 'Merchant ID:',
                contacts: 'Contact person:',
                telephone: 'Contact num:',
                area: 'Location:',
                address: 'Address:',
                industryInfo: 'Industry Info:',
                serviceLevel: 'Service level:',
                customer: 'Customer service:',
                business: 'Businessers:',
                label: 'Customer label:',
                remark: 'Remarks:',
                registerTime: 'Regist time:',
                extension: 'Extension',
                productServices: 'Product Services',
                dialogTitle: 'Extend service time',
                extensionTime: 'Service expiration time',
                extensionRemark: 'Remark',
                inputExtensionTime:
                    'Please fill in the service expiration date',
                inputRemark: 'Please enter a comment',
                column: {
                    serviceNo: 'Service Number',
                    serviceName: 'Service name',
                    serviceType: 'Service Type',
                    serviceStatus: 'State',
                    createTime: 'Creation time',
                    expireTime: 'Expiration time'
                },
                msg: {
                    inputDueTime: 'Please enter the service expiration time',
                    timeTip:
                        'The extension time cannot be less than the expiration time of the current service'
                }
            }
        },
        messageNotice: {
            title: 'Title',
            content: 'Content',
            delete: 'Delete',
            deleteAll: 'Delete all',
            setRead: 'Mark it as read',
            setAllRead: 'All read',
            messageType: 'Message type',
            msgStatus: 'Message status',
            placeholderMessageContent: 'Enter the message content',
            columnName: {
                messageTitle: 'Message title',
                sendTime: 'Notification time',
                messageType: 'Message type'
            },
            messageListEmptySelect: 'Please click and select the message !',
            allMessages: 'All messages',
            inputPlaceholder: 'Enter the message title/content',
            back: 'Back',
            previous: 'Last item',
            next: 'Next item',
            messageNotification: 'Message notification',
            allMsg: 'All messages',
            unreadMessage: 'Unread message',
            deleteConfirmTip: 'Delete prompt',
            deleteConfirmMessage:
                'This operation will permanently delete the selected message, Continue?',
            readMessage: 'Message already read',
            drawer: {
                title: 'Notification Center',
                allRead: 'All read',
                timeFilter: 'Time filter',
                statusFilter: 'Status filter',
                noMoreData: 'No more data',
                serviceMsg: 'Service message',
                all: 'All',
                cancel: 'Cancelled',
                tipTitle: 'Confirmation prompt',
                tipMsg1:
                    'This operation marks all unread messages as read. Do you want to continue?',
                tipMsg2:
                    'This operation marks this class of unread messages as read, do you want to continue?'
            }
        },
        organization: {
            departmentHead: 'Department manager:',
            telephoneNumber: 'Phone number:',
            memberInformation: 'User',
            placeholder: {
                msg1: 'Enter user name/phone number'
            },
            addUser: 'Add user',
            removeUser: 'Remove the user',
            adjustmentDepartment: 'Adjust the departments',
            moreMenu: 'More',
            enterUser: 'Import user',
            outputUser: 'Export user',
            exportUser: 'Export all',
            table: {
                name: 'Full name',
                number: 'Employee ID',
                phone: 'Phone number',
                mail: 'E-mail',
                depart: 'Department',
                role: 'Role',
                status: 'Status'
            },
            dialog: {
                add: 'Add ',
                edit: 'Edit ',
                user: 'User',
                name: 'Full name',
                number: 'Employee ID',
                phone: 'Phone number',
                telPhone: 'Phone number:',
                mail: 'E-mail',
                depart: 'Department',
                role: 'Role',
                isEnable: 'Enable',
                LDAPUser: 'LDAP user',
                placeholder: {
                    msg1: 'Enter the name',
                    msg2: 'Enter the employee ID',
                    msg3: 'Enter phone number',
                    msg4: 'Enter the email',
                    msg5: 'Select the department',
                    msg6: 'Select the role',
                    msg7: 'Enter the organization name',
                    msg8: 'Enter the assignable role name',
                    msg9: 'Enter the department names',
                    msg10: 'Enter the department name',
                    msg11: 'Enter the department description',
                    msg12: 'Select',
                    msg13: 'Please enter name'
                },
                deleteOrgTitle: 'Tips',
                deleteOrgTip: 'Are you sure to remove the department【{0}】?',
                removeUserMsg:
                    'Are you sure to remove the user from the current department?',
                tips: 'Tips',
                organization: 'Organization',
                assignRoles: 'Assign roles',
                roleList: 'Role list',
                departName: 'Name',
                superiorDepartment: 'Superior ',
                departmentDescription: 'Description',
                departmentHead: 'Manager',
                rules: {
                    msg1: 'Enter the organization name',
                    msg2: 'Enter the parent organization',
                    msg3: 'Enter the organization description',
                    msg4: 'The phone number can only be made up of 5-16 digits',
                    msg5: 'Enter the full name',
                    msg6: 'Enter the employee ID',
                    msg7: 'Enter phone number',
                    msg8: 'The phone number can only be made up of 5-16 digits',
                    msg9: 'The phone number can only be made up of 5-16 digits',
                    msg10: 'The business phone number can only be made up of 5-16 digits',
                    msg11: 'The registered landline phone can only be made up of 5-16 digits',
                    msg12: 'Phone number of new administrator can  only be made up of 5-16 digits'
                },
                importUser: 'Import user',
                pleaseDownload: 'Please download it first',
                importDocument: 'Import template document',
                finishedImport: ',Upload and import after filling in.',
                finishedNotice:
                    'After the import is completed, you will receive a system message notification.',
                finishedWarning:
                    'Please note that a single import can support up to 1000 pieces of data',
                exceedMax:
                    'The amount of user data you imported is greater than 1000, please adjust it again',
                exceedMaxTip:
                    'Import the user data in the template before importing.',
                importServicet: 'Import services',
                clickUpload: 'Click upload',
                userList: 'User list',
                allUser: 'All user',
                userListResult: 'User list import results',
                tipMsg: {
                    msg1: 'All user information imported successfully',
                    msg2: 'Select the department information!',
                    msg3: 'There are sub departments, which will be deleted together. Are you sure to delete them?',
                    msg4: 'Confirm to delete Department',
                    msg5: 'is it?',
                    msg6: 'Confirm prompt',
                    importTip: 'Import prompt'
                },
                orgError: 'Organization cannot be empty',
                mailMsg: 'Enter the correct email address'
            },
            message: {
                success: 'Operation succeeded',
                failture: 'Operation failed',
                repeatName: 'User name already exists',
                exportTip: 'Export Tips',
                exportMsgOne:
                    'Are you sure you want to export the selected user data?',
                exportMsgTwo:
                    'After the export is completed, a system notification will be sent to you to export the data file',
                exportMsgThree: 'Please download in [Export Record]'
            }
        },
        dictionaryManagement: {
            type: {
                industryField: 'Industry field',
                enterpriseScale: 'Enterprise scale',
                createChannel: 'Tenant creation channel',
                param_type: 'Parameter type',
                userWorkStatus: 'User status',
                tenantTags: 'Tenant tag',
                serviceLevel: 'Service level',
                ability_control_switch: 'Capability control switch',
                apply_type: 'Apply type',
                charge_type: 'Charge type',
                charge_type_unit: 'Payment type and unit',
                contract_product_code: 'Code of product gifted by the contract',
                grade_unit: 'Grade unit',
                invoice_carrier: 'Invoice medium',
                invoice_status: 'Invoice status',
                invoice_type: 'Invoice type',
                is_default: 'Set as default or not',
                is_renew: 'To renew or not',
                logistics_company: 'Logistics company',
                log_type: 'Log type',
                notice_type: 'Notice type',
                paytimeoutmin: 'Payment due time',
                pay_type: 'Payment type',
                product_category: 'Service category',
                product_status: 'Service status',
                product_type: 'Service type',
                purchase_channel: 'Payment method',
                purchase_status: 'Order status',
                refund_type: 'Refund type',
                send_mail: 'Role mailbox gifted by the contract',
                message_send_time: 'Message sending time',
                message_status: 'Message status',
                message_type: 'Message type',
                dbType: 'dbType',
                logTarget: 'Operation module',
                logOpration: 'Operation Type'
            },
            value: {
                Working: 'Working',
                VIPCustomer: 'VIP customer',
                Major: 'Major customer',
                Ordinary: 'Ordinary customer',
                Small: 'Small customer',
                VIPService: 'VIP service',
                ClassA: 'Class A service',
                ClassB: 'Class B service',
                ClassC: 'Class C service',
                Applet: 'Applet',
                Wechat: 'WeChat',
                Logistics: 'Logistics industry',
                Financial: 'Financial industry',
                NewRetai: 'New retail industry',
                Media: 'Media / Advertising / Exhibition industry',
                Internet: 'Internet / Information technology',
                Professional: 'Professional service industry',
                Manufacturing: 'Manufacturing industry',
                Others: 'Others',
                People50: '0-50 people',
                People100: '50-100 people',
                People500: '100-500 people',
                People1000: '500-1000 people',
                People5000: '1000-5000 people',
                Background: 'Platform entry',
                ThreeSync: 'Three-party synchronization',
                ApplicationService: 'Application service',
                CustomizedService: 'Customized service',
                StandardEdition: 'Standard edition',
                EnterpriseEdition: 'Enterprise edition',
                UltimateEdition: 'Ultimate edition',
                ProductPackage: 'Product package',
                ThirdPartyService: 'Third party service',
                UnReleased: 'To be released',
                Released: 'Released',
                Disabled: 'Disabled',
                Removed: 'Removed',
                ChargeDay: 'Charge by the day',
                ChargeMonth: 'Charge by the month',
                ChargeQuarter: 'Charge by the quarter',
                ChargeYear: 'Charge by the year',
                OfflinePay: 'Offline pay',
                WechatPay: 'WeChat pay',
                ToBePaid: 'To be paid',
                Paid: 'Paid',
                PaidSuccess: 'Purchase successfully',
                Cancelled: 'Cancelled',
                Refunded: 'Refunded',
                Applied: 'Applied',
                NewlyBuy: 'Newly purchased',
                Renew: 'Renew',
                Alipay: 'Alipay',
                WeChat: 'WeChat',
                ReleasedNotice: 'Service release notice',
                ExpirationNotice: 'Service expiration notice',
                AboutToExpire: 'Service is about to expire',
                ServiceDisabled: 'Service is disabled',
                SeviceOffine: 'Service is offline',
                ServiceEnabled: 'Service is enabled',
                ServiceOnline: 'Service is online',
                Online: 'Online',
                Offline: 'Offline',
                Day: 'Day',
                Month: 'Month',
                Season: 'Season',
                Year: 'Year',
                NonDefault: 'Non-default',
                Default: 'Default',
                Personal: 'Personal',
                Enterprise: 'Enterprise',
                OrdinaryInvoice: 'VAT ordinary invoice',
                SpecialInvoice: 'VAT special invoice',
                PaperInvoice: 'Paper invoice',
                ElectronicInvoice: 'Electronic invoice',
                Invoiced: 'Invoiced',
                Notification: 'Notification',
                Authorize: 'Authorize',
                SFExpress: 'S.F. Express',
                JDLogistics: 'JD Logistics',
                EMS: 'EMS',
                ZTOExpress: 'ZTO Express',
                STOExpress: 'STO Express',
                YTOExpress: 'YTO Express',
                BestExpress: 'Best Express',
                YundaExpress: 'Yunda Express',
                Try: 'Try',
                RemittanceConfirmed: 'Remittance to be confirmed',
                Timeout: 'Timeout',
                PayByAlipay: 'Pay by Alipay',
                ServiceMessage: 'Service message',
                OperationMessage: 'Operation message',
                ProductMessage: 'Product message',
                FailureMessage: 'Failure message',
                SystemMessage: 'System message',
                NoticeMessage: 'Announcement message',
                Unread: 'Unread',
                HaveRead: 'Have read',
                Today: 'Today',
                Yesterday: 'Yesterday',
                Last7Days: 'Last 7 days',
                Last30Days: 'Last 30 days',
                MYSQL: 'MYSQL',
                ORACLE: 'ORACLE',
                org: 'Organization',
                userManage: 'User Management',
                dictManage: 'Dictionary Management',
                login: 'Login',
                confManage: 'Configuration Management',
                permissionManage: 'Permission Management',
                regionManage: 'Region Management',
                roleManage: 'Role Management',
                tenantManage: 'Tenant Management',
                logManage: 'Log Management',
                messageCenter: 'Message Center',
                enterprise: 'Enterprise Information',
                invoiceManage: 'Invoice management',
                payConfig: 'Payment configuration',
                productFunctionDefin: 'Product Function Definition',
                productServiceManage: 'Product Service Management',
                merchantMonthlyReport: 'Merchant Monthly Report',
                orderService: 'Service Market',
                exportFile: 'export file',
                addOrg: 'Adding Organization',
                modifyOrg: 'Modifying Organization',
                delOrg: 'Deleting Organization',
                rankOrg: 'Ranking Organization',
                changPwd: 'Changing Password',
                forgetPwd: 'Forget Password',
                modifyUser: 'Modifying User',
                addUser: 'Adding User',
                adjustOrg: 'Adjust Organization',
                importExportUser: 'Import/Export Users',
                changeAdmin: 'Change Administrator',
                modifyDictType: 'Modifying Dictionary Type',
                addDictType: 'Adding Dictionary Type',
                delDictType: 'Deleting Dictionary Type',
                addDictValue: 'Adding Dictionary Value',
                modifyDictValue: 'Modifying Dictionary Value',
                delDictValue: 'Deleting Dictionary Value',
                userLogin: 'User Login',
                userLogout: 'User logout',
                loginLock: 'Login Lock',
                unbindThreeLogin: 'Unbind three-party login',
                addParam: 'Adding Parameters',
                ModifyParam: 'Modifying Parameters',
                delParam: 'Deleting Parameter',
                permissionSort: 'Permission sort',
                changPermissionStatus: 'Changing Permission Status',
                delPermission: 'Deleting Permission',
                modifyPermission: 'Modifying Permission',
                addPermission: 'Adding Permission',
                addDataPermission: 'Adding Data Permission',
                modifyDataPermission: 'Modifying Data Permission',
                delDataPermission: 'Deleting Data Permission',
                addRegion: 'Adding Region',
                modifyRegion: 'Modifying Region',
                delRegion: 'Deleting Region',
                modifyRole: 'Modifying Role',
                addRole: 'Adding Role',
                delRole: 'Deleting Role',
                roleAssocUser: 'Role Associated User',
                resetRole: 'Resetting Role',
                addMerchant: 'Adding Merchant',
                modifyMerchant: 'Modifying Merchant',
                changeMerchantStatus: 'Change Merchant status',
                exportLog: 'Exporting Logs',
                delMessage: 'Deleting message',
                changeEnterInfo: 'Changing Enterprise Information',
                exportInvoice: 'Exporting Invoice Records',
                issueInvoice: 'Issue an invoice',
                applyInvoice: 'Apply for invoice',
                modifyPayConfig: 'Modifying Payment Configuration',
                functionSort: 'Product function Ranking',
                changeFunctionStatus: 'Changing Function Status',
                delFunction: 'Deleting Product function',
                modifyFunction: 'Modifying Product function',
                addFunction: 'Adding Product function',
                addProduct: 'Adding Product Service',
                modifyProduct: 'Modifying Product Service',
                publishProduct: 'Publish Product Service',
                modifyPublishedProduct: 'Modify published product service',
                delProduct: 'Deleting Products and Services',
                downloadMonthlyRep: 'Download the Merchant Monthly report',
                confirmPayment: 'Confirm merchant payment',
                canceOrder: 'Cancellation of order',
                purchaseProduct: 'Purchase product services',
                productRenewal: 'Product service renewal fee',
                fillRremittance: 'Fill in the remittance return form',
                deleteFile: 'Delete File',
                notice: 'Notice',
                announcement: 'Announcement',
                unpublished: 'Unpublished',
                published: 'Published',
                CancelRelease: 'Version cancellation',
                ToBeReleased: 'To be released',
                Release: 'Published',
                HaveCompleted: 'Completed',
                Locked: 'Locked',
                RolledBack: 'Rolled back',
                PartialRollback: 'Partial rollback',
                sys: 'sys',
                email: 'email',
                sms: 'sms',
                wechat: 'wechat',
                dingding: 'dingding',
                aliyun: 'aliyun',
                huyiwuxian: 'Yiyi Wireless',
                tencent: 'tencent',
                enable: 'Enable',
                disable: 'Disable',
                Running: 'Running',
                TrialPeriod: 'TrialPeriod',
                Expired: 'Expired',
                addExtendRecord: 'Extend service duration'
            }
        },
        exportRecords: {
            fileName: 'Document name',
            operator: 'Operator',
            timeScope: 'time frame',
            refresh: 'Refresh',
            table: {
                fileName: 'Document name',
                status: 'state',
                operator: 'Operator',
                createTime: 'Creation time',
                operation: 'operation',
                downLoad: 'download'
            },
            msg: {
                msg1: 'The files exported by users will be temporarily stored on the server after generation, and will be automatically deleted 30 days later',
                msg2: 'Please enter the file name',
                msg3: 'Please enter the operator',
                msg4: 'The files exported by users will be temporarily stored on the server after being generated,',
                msg5: 'will be automatically deleted in days'
            }
        },
        topMenu: {
            addMenu: 'New Menu',
            editMenu: 'Edit Menu',
            pleaseInput: 'Please enter',
            pleaseSelect: 'Please select',
            isEmpty: 'Cannot be empty',
            addSuccess: 'Successfully added',
            editSuccess: 'Edit succeeded',
            deleteAllMsg: 'Are you sure you want to delete these menu records?',
            deleteMsg: 'Are you sure you want to delete this menu record?',
            selectItem: 'Please select delete item',
            listControl: 'Add up to 10 pieces of data',
            setMenu: 'Configuration Menu',
            setSuccess: 'Setting succeeded',
            column: {
                name: 'Menu Name',
                code: 'Menu Number',
                icon: 'Icon',
                sort: 'sort',
                releaseTime: 'Release time',
                order: 'Menu Order'
            },
            message: {
                requireNum: 'Menu order must be numeric',
                enterName: 'Please enter a menu name',
                enterNumber: 'Please enter the menu number',
                enterIcon: 'Please select icon',
                enterOrder: 'Please enter menu sort'
            }
        },
        informationAnnounce: {
            title: 'title',
            content: 'content',
            publisher: 'Publisher',
            timeRange: 'time frame',
            publishStatus: 'Publishing status',
            startDate: 'start time',
            to: 'to',
            endDate: 'End time',
            add: 'newly added',
            delete: 'delete',
            selectDept: 'select',
            message: {
                inputTheme: 'Please enter a title',
                inputContent: 'Please enter the content',
                inputPublisher: 'Please enter the publisher',
                inputPublishStatus: 'Please select the publishing status',
                titleMax: 'The maximum length of the title is 50',
                selectClass: 'Please select a category',
                deleteTitle: 'Delete Prompt',
                deleteItem:
                    'Are you sure you want to delete this announcement information?',
                deleteItems:
                    'Are you sure you want to delete these announcement information?',
                toDeleteItems: 'Please select the item to delete',
                deleteSuccess: 'Deletion succeeded',
                releaseTitle: 'Publishing Tips',
                releaseBefore: 'Are you sure you want to publish【',
                releaseBack: '】?',
                releaseSuccess: 'Publish successfully',
                fileMax:
                    'The maximum upload size of a single attachment is 20M',
                fileFormat:
                    'Support pdf, ppt, jpg, png, jpeg, excel, word and other file formats.',
                addSuccess: 'Successfully added',
                editSuccess: 'Edit succeeded',
                publishSuccess: 'Publish successfully',
                alreadyDelete:
                    'The announcement (notice) you are viewing has been deleted by the publisher!',
                searchPerson: 'Please enter your name/mobile number to query',
                noticeNoEmpty: 'The notification range cannot be empty'
            },
            columnName: {
                number: 'Serial No',
                title: 'title',
                publisher: 'Publisher',
                class: 'category',
                publishTime: 'Release time',
                readTimes: 'Reading times',
                status: 'state',
                operateType: 'operation'
            },
            operate: {
                edit: 'edit',
                publish: 'release',
                delete: 'delete',
                preview: 'preview'
            },
            addPage: {
                title: 'title',
                class: 'category',
                noticeInfo: 'Announcement information',
                addFile: 'Add attachments',
                noticeRange: 'Scope of notification',
                attachmentsList: 'List of attachments',
                back: '@:common.return',
                addUser: 'Please select the push department or person',
                btn: {
                    preview: 'preview',
                    save: 'preservation',
                    publish: 'release'
                },
                selectedPart: 'Selected department',
                selectedPerson: 'Selected Personnel'
            }
        },
        messageTemplate: {
            templateNumber: 'Template number',
            templateName: 'Template Name',
            templateType: 'Message type',
            messageSendType: 'Message channels',
            sendingPlatform: 'Sending platform',
            platformId: 'Platform corress ID',
            templateStatus: 'Template Status',
            createTime: 'Creation time',
            startTime: 'time-on',
            endTime: 'End Time',
            search: 'Query Search',
            templateImport: 'Template Import',
            templateDescOne: 'Please download first',
            messageTemplate: ' Message Template ',
            templateDescTwo: 'Then click the upload button to upload the file',
            inportService: 'Import Service',
            clickUpload: 'Click to upload',
            reMark: 'Note: The file cannot exceed 1000 entries (supports xlsx, xls formats)',
            disabledTemplate: 'Are you sure you want to disable this template?',
            endbleTemplate: 'Are you sure to enable this template?',
            statusTipTitle: 'confirmation prompt',
            exceedMax:
                'The amount data you imported is more than 1000, please readjust',
            exceedMaxTip: 'Import the data in the template before importing.',
            importTip: 'Import prompt',
            column: {
                number: 'NO.',
                templateContent: 'Template Content',
                templateParamMap: 'Template parameter',
                operate: 'operate'
            },
            placeholder: {
                number: 'Enter the template number',
                name: 'Enter a template name',
                templateType: 'Select a message type',
                messageType: 'Select a message channel',
                platform: 'Select the sending platform',
                platformId: 'Enter platform corresponding ID',
                tempplateStatus: 'Select the template status',
                templateContent: 'Enter the template content',
                templateParamMap: 'Enter parameters for mapping',
                deleteFailure: 'Delete failed,',
                templateIdUnexcixt: 'Template ID does not exist'
            },
            operation: {
                reset: 'resetting',
                search: 'query',
                add: 'Add',
                export: 'Import',
                batchDelete: 'Batch Delete',
                columnOptional: 'Column optiona',
                edit: 'Edit',
                detail: 'Details',
                delete: 'Delete',
                success: 'Operation successful',
                toDeleteItems: 'Please select a deletion item',
                confirmDelete:
                    'Are you sure you want to delete message templates?',
                deleteTitle: 'Delete prompt',
                operateSuccess: 'Operation successful',
                operateFailure: 'operation failed'
            }
        }
    }
};
