// 校验规则
const shortLengthLimit = {
    min: 0,
    max: 50,
    message: '最多输入50个字符',
    trigger: 'change'
};
const longLengthLimit = {
    min: 0,
    max: 500,
    message: '最多输入500个字符',
    trigger: 'change'
};
const projectFormRules = {
    time: [
        { required: true, message: '请选择项目启动时间', trigger: 'change' }
    ],
    projectNumber: [
        { required: true, message: '请输入项目编号', trigger: 'change' },
        { ...shortLengthLimit }
    ],
    demandImportSubject: [
        { required: true, message: '请选择需求导入主体', trigger: 'change' }
    ],
    assMarketStoryImportId: [shortLengthLimit],
    OAProjectTaskId: [shortLengthLimit],
    productModel: [shortLengthLimit]
};
const marketFormRules = {
    projectName: [
        { required: true, message: '请输入项目名称', trigger: 'change' },
        { ...shortLengthLimit }
    ],
    productLine: [
        { required: true, message: '请选择产品线', trigger: 'change' }
    ],
    subProductLine: [
        { required: true, message: '请选择细分产品线', trigger: 'change' }
    ],
    productCategory: [
        { required: true, message: '请选择产品类别', trigger: 'change' }
    ],
    marketPositioning: [
        { required: true, message: '请选择市场定位', trigger: 'change' }
    ],
    approvalType: [
        { required: true, message: '请选择立项类型', trigger: 'change' }
    ],
    queueNotStartedReason: [{ ...longLengthLimit }],
    marketDemandImportID: [{ ...shortLengthLimit }],
    projectCancelReason: [{ ...longLengthLimit }],
    projectManagerLoginName: [
        { required: false, message: '请输入项目经理', trigger: 'change' }
    ]
};

// 表单默认值
const defaultMarketForm = () => {
    return {
        // 禅道项目ID
        marketDemandImportID: '',
        // 产品类别
        productCategory: '',
        // 市场定位
        marketPositioning: '',
        // 所属产品线
        productLine: '',
        // 细分产品线
        subProductLine: '',
        // 立项类型
        approvalType: '',
        // 需求类别
        demandCategory: '',
        // 产品类型
        productType: '',
        // 排队未启动原因
        queueNotStartedReason: '',
        // 项目名称
        projectName: '',
        // 项目经理，这里不能传空字符串，某些人的域账号为空字符串，避免错误回显
        projectManagerLoginName: null
    };
};
const defaultProjectForm = () => {
    return {
        // OA项目任务书ID
        OAProjectTaskId: '',
        // 项目编号
        projectNumber: '',
        // 项目控制等级
        projectControlLevel: '',
        // 项目技术难度
        technicalComplexity: '',
        // 开发流程
        developmentProcess: '',
        // 项目归属
        projectOwnership: '',
        // 生产归属
        productionOwnership: '',
        // 产品型号
        productModel: '',
        // 需求导入主体
        demandImportSubject: '',
        // 禅道项目ID
        zendaoProjectId: null,
        // 产品经理，不能传空字符串，理由同项目经理
        productManagerLoginName: null,
        // 项目起止时间
        time: ''
    };
};
// 各状态下对应的项目状态选项
const pendingArray = [
    { label: '市场待立项', value: '市场待立项' },
    { label: '排队中', value: '排队中' },
    { label: '进行中', value: '进行中' },
    { label: '已取消', value: '已取消' }
];
const queuedArray = [
    { label: '排队中', value: '排队中' },
    { label: '进行中', value: '进行中' }
];
const onGoingArray = [
    { label: '进行中', value: '进行中' },
    { label: '已暂停', value: '已暂停' },
    { label: '已终止', value: '已终止' },
    { label: '已结项', value: '已结项' }
];
const pasedArray = [
    { label: '已暂停', value: '已暂停' },
    { label: '进行中', value: '进行中' },
    { label: '已终止', value: '已终止' }
];

export {
    projectFormRules,
    marketFormRules,
    defaultMarketForm,
    defaultProjectForm,
    pendingArray,
    queuedArray,
    onGoingArray,
    pasedArray
};
