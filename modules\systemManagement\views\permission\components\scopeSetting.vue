<template>
  <div class="container">
    <el-dialog
      :title="$t('systemManagement.permission.dialog.dataScopeTitle')"
      :visible.sync="tableDialogVisible"
      custom-class="scope-list"
      @close="closeWindowHandler"
      @open="openWindowHandler"
    >
      <div class="header">
        <div class="header__btns">
          <el-button v-permission="['permission_settings_add']" type="primary" icon="fa fa-plus-square-o" @click="tableListAddHandler">
            {{ $t('common.add') }}
          </el-button>
          <el-button v-permission="['permission_settings_delete']" icon="fa fa-trash-o" plain @click="tableListDeleteHandler">
            {{ $t('common.delete') }}
          </el-button>
        </div>
      </div>
      <el-table :key="tableKey" :data="tableSourceList" border fit highlight-current-row style="width: 100%;" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column :label="$t('systemManagement.permission.dialog.dataScopeName')" width="150px" align="center" prop="scopeName">
          <template slot-scope="{row}">
            <span class="link-type">{{ row.scopeName }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('systemManagement.permission.dialog.dataScopeCode')" width="150px" prop="scopeCode">
          <template slot-scope="{row}">
            <span class="link-type">{{ row.scopeCode }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('systemManagement.permission.dialog.dataScopeColumn')" width="150px" prop="scopeColumn">
          <template slot-scope="{row}">
            <span class="link-type">{{ row.scopeColumn }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('systemManagement.permission.dialog.dataScopeType')" align="center" width="120px" prop="scopeType">
          <template slot-scope="{row}">
            <span class="link-type">{{ row.scopeType | typeFilter($i18n) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('common.handle')" align="center" min-width="300" class-name="small-padding fixed-width">
          <template slot-scope="{row, $index}">
            <el-button type="primary" class="margin-left" size="mini" icon="fa fa-eye" @click="tableItemDetailHandler(row, $index)">{{ $t('common.detail') }}</el-button>
            <el-button type="primary" class="margin-left" size="mini" icon="fa fa-pencil" @click="tableItemUpdateHandler(row, $index)">{{ $t('common.edit') }}</el-button>
            <el-button size="mini" class="margin-left" icon="fa fa-trash-o" plain @click="tableItemDeleteHandler(row, $index)">{{ $t('common.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <scope-dialog
      :show-window.sync="scopeDialogVisible"
      :dialog-status="scopeDialogStatus"
      :processing-scope-item="processingScopeItem"
      :permission-id="permissionId"
      @scopeNeedUpdateEvent="scopeNeedUpdateEventHandler"
    />
  </div>
</template>

<script>
import ScopeDialog from './scopeDialog';

export default {
    name: 'ScopeSetting',
    components: { ScopeDialog },
    filters: {
        typeFilter(type, i18n) {
            // 权限类型  0：自定义 1：本人可见 2：所在机构可见 3：所在机构及以下可见 3：全部可见
            const typeMap = {
                0: i18n.t('systemManagement.permission.dialog.dataScopeType_0'),
                1: i18n.t('systemManagement.permission.dialog.dataScopeType_1'),
                2: i18n.t('systemManagement.permission.dialog.dataScopeType_2'),
                3: i18n.t('systemManagement.permission.dialog.dataScopeType_3'),
                4: i18n.t('systemManagement.permission.dialog.dataScopeType_4')
            };
            return typeMap[type];
        }
    },
    props: {
        // 是否显示弹窗
        showWindow: {
            type: Boolean,
            default: false
        },
        // 上个页面列表行数据的permissionId
        permissionId: {
            type: String,
            default: '-1'
        },
        processingTableItem: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            // 已经选中的table列表数据
            tableSelectedList: {
                scopeIds: [],
                scopeNames: [],
                scopeCodes: [],
                permissionName: '',
                permissionCode: ''
            },
            // tableloading
            tableLoading: true,
            // table数据源
            tableSourceList: [],
            // table绑定的key
            tableKey: 0,
            // 数据权限页面是否弹窗变量
            tableDialogVisible: false,
            // 是否显示新增，更新，详情的弹窗
            scopeDialogVisible: false,
            // 标识符，弹窗是新增，更新，详情中的一种情况
            scopeDialogStatus: 'create',
            // 当前正在处理的数据对象
            processingScopeItem: null
        };
    },
    watch: {
        showWindow(val) {
            if (val) {
                this.tableDialogVisible = true;
            } else {
                this.tableDialogVisible = false;
            }
        }
    },
    methods: {
        // 打开弹窗时，查询数据
        openWindowHandler() {
            this.tableSourceList = [];
            if (this.permissionId !== -1) {
                this.tableLoading = true;
                this.$service.systemManagement
                    .getDataScopeList({
                        permissionId: this.permissionId
                    })
                    .then((response) => {
                        if (response.head.code === '000000') {
                            this.tableSourceList = response.body;
                        }
                        this.tableLoading = false;
                    });
            }
        },
        // 关闭窗口处理
        closeWindowHandler() {
            this.tableDialogVisible = false;
            this.$emit('update:showWindow', false);
        },
        // table列表数据选中状态改变时触发，把permissionId记录到tableSelectedList数组中，用于接口传值
        handleSelectionChange(selectedList) {
            this.tableSelectedList = {
                scopeIds: [],
                scopeNames: [],
                scopeCodes: [],
                permissionName: '',
                permissionCode: ''
            };
            selectedList.forEach((element) => {
                this.tableSelectedList.scopeIds.push(element.scopeId);
                this.tableSelectedList.scopeNames.push(element.scopeName);
                this.tableSelectedList.scopeCodes.push(element.scopeCode);
            });
            this.tableSelectedList.permissionName = this.processingTableItem.permissionName;
            this.tableSelectedList.permissionCode = this.processingTableItem.permissionCode;
        },
        // 列表上方新增按钮事件   添加按钮类型和接口类型权限数据
        tableListAddHandler() {
            this.processingScopeItem = {};
            this.scopeDialogStatus = 'create';
            this.processingScopeItem.permissionName = this.processingTableItem.permissionName;
            this.processingScopeItem.permissionCode = this.processingTableItem.permissionCode;
            this.scopeDialogVisible = true;
        },
        // 列表上方删除按钮事件   删除权限数据
        tableListDeleteHandler() {
            if (this.tableSelectedList.scopeIds.length === 0) {
                this.$message({
                    type: 'warning',
                    message: this.$t('systemManagement.msg.tableWarningMsg_1')
                });
                return;
            }
            // 删除数据
            this.deletePermissionData(this.tableSelectedList);
        },
        // 列表中点击某一行 查看  时触发
        tableItemDetailHandler(row, $index) {
            this.processingScopeItem = row;
            this.scopeDialogStatus = 'detail';
            this.scopeDialogVisible = true;
        },
        // 列表中点击某一行 编辑  时触发
        tableItemUpdateHandler(row, $index) {
            this.processingScopeItem = row;
            this.processingScopeItem.permissionName = this.processingTableItem.permissionName;
            this.processingScopeItem.permissionCode = this.processingTableItem.permissionCode;
            this.processingScopeItem.oldParam = JSON.parse(JSON.stringify(row));
            this.scopeDialogStatus = 'update';
            this.scopeDialogVisible = true;
        },
        // 列表中点击某一行 编辑  时触发
        tableItemDeleteHandler(row, $index) {
            const param = {
                scopeIds: [row.scopeId],
                scopeNames: [row.scopeName],
                scopeCodes: [row.scopeCode],
                permissionName: this.processingTableItem.permissionName,
                permissionCode: this.processingTableItem.permissionCode
            };
            this.deletePermissionData(param);
        },
        // 删除数据方法，会多次调用，参数是数据权限id集合
        deletePermissionData(dataList) {
            this.$confirm(this.$t('systemManagement.msg.deleteDataWarningMsg_1'), this.$t('common.tips'), {
                confirmButtonText: this.$t('common.done'),
                cancelButtonText: this.$t('common.cancel'),
                type: 'warning'
            }).then(() => {
                this.$service.systemManagement
                    .deleteDataScope(dataList)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            // 刷新列表数据
                            this.openWindowHandler();
                            this.$message({
                                type: 'success',
                                message: this.$t('systemManagement.msg.deleteSuccess')
                            });
                        } else {
                            this.$message({
                                type: 'error',
                                message: this.$t('systemManagement.msg.deleteFailed')
                            });
                        }
                    });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: this.$t('systemManagement.msg.canceled')
                });
            });
        },
        // 弹窗修改或新增完毕后，通知本页面刷新数据
        scopeNeedUpdateEventHandler() {
            this.openWindowHandler();
        }
    }
};
</script>

<style lang="scss" scoped>
.container{
    ::v-deep .scope-list{
        width:52%;
    }
	.header__btns {
		::v-deep .el-button:first-child {
			margin-left: 0;
		}
	}
}
</style>
