<template>
    <div class="annual-spend-summary" ref="overallChartBox">
        <div class="flex info-container">
            <el-date-picker
                v-model="year"
                value-format="yyyy"
                type="year"
                placeholder="选择年"
                format="yyyy自然年"
                :picker-options="{
                    disabledDate(time) {
                        return (
                            time.getTime() < new Date('2024-01-01').getTime()
                        );
                    }
                }"
                :clearable="false"
                @input="handleYearChange"
            >
            </el-date-picker>
        </div>
        <el-card class="chart-container">
            <div class="chart-box-title">超出项目总预算项目数量统计</div>
            <div ref="overBudgetChart" class="chart"></div>
        </el-card>
        <div class="flex selector-group">
            <el-select
                v-model="queryConditions.productLine"
                placeholder="产品线"
                class="selector"
                @input="handleProductLineChange"
                clearable
            >
                <el-option
                    v-for="item in queryOptions.productLine"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="queryConditions.subProductLine"
                placeholder="细分产品线"
                class="selector"
                clearable
            >
                <el-option
                    v-for="item in queryOptions.subProductLine"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <form @submit.prevent="handleTableQuery" class="flex projectQuery">
                <el-input
                    v-model="queryConditions.projectName"
                    placeholder="请输入项目名称关键字"
                    style="max-width: 300px; min-width: 200px"
                ></el-input>
                <el-button
                    @click="handleTableQuery"
                    type="primary"
                    class="button"
                    >搜索</el-button
                >
            </form>
            <el-button
                @click="handleTableReset"
                type="primary"
                class="reset-button"
                >重置</el-button
            >
        </div>
        <SnbcBaseTable
            class="table"
            ref="tableRef"
            :table-config="tableConfig"
            :showTableHeader="false"
        >
            <template #budgetAmount="{ row }">
                ￥{{
                    parseFloat(row.budgetAmount).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
            <template #costAmount="{ row }">
                ￥{{
                    parseFloat(row.costAmount).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
            <template #overExpendAmount="{ row }">
                ￥{{
                    parseFloat(row.overExpendAmount).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
        </SnbcBaseTable>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import { getTableConfig } from './tableConfig.js';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import { getOverBudgetChartOption } from './bugetChartOptions';

export default {
    name: 'DevProjectBudgetMonitor',
    components: { SnbcBaseTable },
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            year: new Date().getFullYear().toString(),
            // 图表缓存
            charts: {},
            optionsMap: {},
            chartType: 'month',
            chartShowType: 'bar',
            // 是否展示费用科目
            showFeeSubject: false,
            // 列表各项配置
            tableConfig: getTableConfig(this),
            // 查询条件
            queryConditions: {
                // 产品线
                productLine: '',
                // 细分产品线
                subProductLine: '',
                // 项目名称
                projectName: ''
            },
            // 查询条件的选项
            queryOptions: {
                // 产品线
                productLine: this.$store.state.project.productLine.map((i) => {
                    return { value: i.label, label: i.label };
                }),
                // 细分产品线
                subProductLine: []
            }
        };
    },
    watch: {
        activeName(newVal) {
            if (newVal === 'devProjectBudgetMonitor') {
                this.handleYearChange();
            }
        }
    },
    created() {
        this.getQueryOptions('细分产品线');
    },
    mounted() {
        this.handleYearChange();
    },
    activated() {
        if (this.activeName === 'devProjectBudgetMonitor') {
            this.handleYearChange();
        }
    },
    methods: {
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const { overallChartBox } = this.$refs;
            const observer = new ResizeObserver(
                debounce((entries) => {
                    this.activeName === 'devProjectBudgetMonitor' &&
                        myChart.resize();
                }, 100)
            );
            observer.observe(overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 年份变更处理
         */
        handleYearChange() {
            this.setOverBudgetChart();
            this.handleTableQuery();
        },
        /**
         * 表格查询
         */
        handleTableQuery() {
            this.tableConfig.queryParams = {
                year: this.year,
                ...this.queryConditions
            };
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 获取下拉列表选项
         * @param {String} type 哪种option
         * @param {String} [value=''] 参数
         */
        async getQueryOptions(type, value = '') {
            try {
                const api = this.$service.project.finance.getSelectOptions;
                const res = await api({ paramName: type, paramType: value });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 处理选项并去重
                const options = res.body
                    .map((i) => {
                        return {
                            value: i.paramName,
                            label: i.paramName
                        };
                    })
                    .filter(
                        (item, index, self) =>
                            index ===
                            self.findIndex((t) => t.value === item.value)
                    );
                if (type === '细分产品线') {
                    this.queryConditions.subProductLine = '';
                    this.queryOptions.subProductLine = options;
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 处理不同产品线对应的细分产品线
         * @param {String} value 产品线
         */
        handleProductLineChange(value) {
            this.getQueryOptions('细分产品线', value);
        },
        /**
         * 排序前的hook
         * @param {Object} params 参数
         */
        sortChangeHook(params) {
            const { prop: sortKey, order: sortOrder } = params;
            this.tableConfig.sortParams.sortKey = sortKey;
            this.tableConfig.sortParams.sortOrder =
                sortOrder === 'ascending' ? 'ASC' : 'DESC';
        },
        /**
         * 超出项目总预算数量统计
         */
        async setOverBudgetChart() {
            try {
                const api =
                    this.$service.reportForm.finance.getOverBudgetCostChart;
                const params = {
                    year: this.year
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.overBudget = getOverBudgetChartOption(res.body);
                this.initChart('overBudget');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 重置查询
         */
        handleTableReset() {
            this.queryConditions = {
                // 汇总/分项表
                tabType: 1,
                // 月份
                month: '',
                // 产品线
                productLine: '',
                // 细分产品线
                subProductLine: '',
                // 对应费用科目
                costSubject: '',
                // 项目名称
                projectName: ''
            };
            this.handleTableQuery();
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
.annual-spend-summary {
    .info-container {
        width: 100%;
        .info {
            line-height: 30px;
            margin-left: 20px;
            font-weight: 600;
            .fee {
                color: #0054ca;
                font-size: 18px;
                margin-right: 2px;
            }
        }
    }
    .chart-container {
        margin-top: 10px;
        width: 100%;
        height: 300px;
        padding: 10px;
        height: 400px;
        .chart-box-title {
            width: 180px;
            height: 30px;
            background-color: #3370ff;
            color: #fff;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            padding: 8px;
            border-radius: 10px;
        }
        .radio-group {
            margin-left: auto;
            line-height: 30px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-top: 20px;
        }
    }
    .select-icon {
        height: 15px;
        width: 15px;
    }

    .selector-group {
        // 修改placeholder颜色
        ::v-deep .el-input__inner::placeholder {
            color: rgba(0, 0, 0, 0.685) !important;
        }
        .projectQuery {
            margin-top: 10px;
            .button {
                margin-left: 5px;
                height: 28px;
            }
        }
        .selector {
            margin: 10px 15px 10px 0;
        }
        .table-type {
            width: 250px;
        }
    }
    .reset-button {
        margin-left: 5px;
        margin-top: 10px;
        height: 28px;
        justify-self: flex-end;
    }
}
</style>
