<template>
    <div class="more-scroll" :key="count">
        <div class="more-top">
            <div class="more-word" v-if="usageStatuses.length !== 0">
                <div class="more-btn" @click="toggleDropdown">
                    <span>使用情况</span>
                    <span class="triangle" :class="{ rotate: dropdownOpen }"
                        >&#9658;</span
                    >
                </div>
            </div>
            <div class="flex pr-20">
                <scene-product-chart-vue
                    v-for="(item, index) in productsData"
                    :key="index"
                    :keyModuleName="item.keyModuleName"
                    :iconName="item.iconName"
                    :calculateFieldVo="item.calculateFieldVo"
                    :style="{
                        width: `${unitWidth}px`
                    }"
                ></scene-product-chart-vue>
            </div>
        </div>
        <div class="more-bottom" v-if="dropdownOpen">
            <div
                v-for="(item, index) in usageStatuses"
                :key="index"
                class="status-line"
                :style="{
                    width: `${tableWidth}px`
                }"
            >
                <div class="status-content">{{ item.productType }}</div>
                <div
                    v-for="(detail, key) in item.keyModuleUsageVoList"
                    :key="key"
                    class="status-symbol"
                    :style="{
                        width: `${unitWidth}px`
                    }"
                >
                    <img
                        v-if="detail.flag === 1"
                        src="../../../../scene/assets/check.png"
                        alt="Checkmark"
                        class="check-mark"
                    />
                    <span v-else class="check-mark"></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import SceneProductChartVue from './SceneProductChart.vue';

export default {
    components: {
        SceneProductChartVue
    },
    props: {
        productsData: {
            type: Array,
            default: () => []
        },
        usageStatuses: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dropdownOpen: false,
            count: 0,
            tableWidth: 0
        };
    },
    computed: {
        // 单个图表的宽度，根据productsData计算,最外层宽度已经固定为1250px,
        // 全部图表的宽度小于1230px（留出20px空白），充满屏幕；大于1230px，出现滚动条
        // 150为左侧按钮的宽度，20为右侧的padding
        unitWidth() {
            const wholeWidth = this.productsData.length * 115 + 150 + 20;
            if (wholeWidth < 1230) {
                const width = Math.floor(
                    (1230 - 150 - 20) / this.productsData.length
                );
                return width;
            }
            return 115;
        }
    },
    watch: {
        productsData: {
            handler(newVal) {
                this.count += 1;
            },
            deep: true
        }
    },
    methods: {
        // 点击使用情况的回调
        toggleDropdown() {
            this.dropdownOpen = !this.dropdownOpen;
            // 展开后的宽度，用来和上方对齐
            this.$nextTick(() => {
                this.tableWidth =
                    this.unitWidth * this.productsData.length + 150;
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.pr-20 {
    padding-right: 20px;
}
.more-scroll {
    width: 100%;
    overflow: auto;
    margin: 20px;
}
.rotate {
    transform: rotate(90deg);
    transition: transform 0.3s ease;
}
// 更多表格
.more-top {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    .more-word {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .more-btn {
        width: 150px;
        height: 40px;
        border-radius: 48px;
        background: linear-gradient(
            180deg,
            rgba(92, 138, 255, 1) 0%,
            rgba(158, 189, 255, 1) 100%
        );
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: clamp(0.7rem, 0.489rem + 1.05vw, 1rem);
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        letter-spacing: 1px;
        cursor: pointer;
        .triangle {
            margin-left: 5px;
        }
    }
}
.more-bottom {
    width: 100%;
    height: calc(100%-170px);
}
.status-ilne {
    width: 100%;
    display: flex;
}
.status-content {
    left: 0;
    position: sticky;
    width: 150px;
    height: 35px;
    flex-shrink: 0;
    background-color: #3760e4;
    background-image: linear-gradient(0deg, #3760e4, #91a8f1);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: clamp(0.7rem, 0.489rem + 1.05vw, 1.1rem);
    font-weight: 500;
    border-bottom: 1px solid #d7e2ff;
}
.status-symbol {
    height: 35px;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #d7e2ff;
}
.check-mark {
    width: 20px;
    height: 20px;
}
.status-line {
    display: flex;
}
</style>
