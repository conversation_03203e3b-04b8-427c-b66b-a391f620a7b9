<template>
    <div class="card-content">
        <div class="card-layout">
            <div
                class="card-item"
                v-for="(item, index) in cardArray"
                :key="index"
                :class="'card-item-' + index"
            >
                <span class="card-circle"></span>
                <span>{{ item.cardName }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            cardArray: [
                { cardName: '规划' },
                { cardName: '在研' },
                { cardName: '发布' }
            ]
        };
    }
};
</script>

<style lang="scss" scoped>
.card-content {
    width: 200px;
    height: 36px;
    display: flex;
    justify-content: center;
    .card-layout {
        width: 100%;
        display: flex;
        border-radius: 138px;
        background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0) 100%
        );
        border: 1px solid rgba(255, 255, 255, 1);
    }
    .card-item {
        width: 33.3%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: clamp(0.7rem, 0.489rem + 1.05vw, 1.3rem);
        font-weight: 500;
        &:nth-child(1) .card-circle {
            background: rgba(255, 141, 26, 1);
        }
        &:nth-child(2) .card-circle {
            background: rgba(63, 205, 123, 1);
        }

        &:nth-child(3) .card-circle {
            background: rgba(42, 130, 228, 1);
        }
    }
    .card-circle {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 10px;
    }
}
</style>
