<template>
  <div class="view">
    <div class="container">
{{#if isSingleQuery}}
      <div class="header">
        <span class="header__title">\{{ $t("common.queryList") }}</span>
        <span class="header__body">
          <el-input
            v-model="listQuery.{{queryFields}}"
            placeholder="请输入名称搜索"
            @keyup.enter.native="getList"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="getList"
            />
          </el-input>
        </span>
        <span class="header__btns">
          {{#if isAdd}}
          <el-button
            v-waves
            type="primary"
            icon="el-icon-plus"
            @click="showAdd"
          >
            \{{ $t("common.add") }}
          </el-button>
          {{/if}}
          <el-button
            @click="handleExportCmd"
          >
            \{{ $t("common.downloadT") }}
          </el-button>
        </span>
      </div>
{{else}}
      <div class="filter">
        <div class="header">\{{ $t("common.queryList") }}</div>
        <el-form>
{{#each queryFields}}
          <el-form-item :label="$t('{{ @root.moduleName }}.{{ @root.viewName }}.columnName.{{this}}')">
            <el-input
              v-model="listQuery.{{this}}"
              :placeholder="$t('{{ @root.moduleName }}.{{ @root.viewName }}.placeholder.{{this}}')"
              @keyup.enter.native="getList"
            />
          </el-form-item>
{{/each}}
          <div class="filter__btns">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="getList"
            >
              \{{ $t("common.searchBtn") }}
            </el-button>
            <el-button
              v-waves
              icon="el-icon-refresh-left"
              @click="queryReset"
            >
              \{{ $t("common.resetBtn") }}
            </el-button>
          </div>

        </el-form>
      </div>
      <div class="header">
        <span class="header__title">\{{  $t("common.queryList") }}</span>
        <span class="header__btns">
{{#if isAdd}}
          <el-button
            v-waves
            type="primary"
            icon="el-icon-plus"
            @click="showAdd"
          >
            \{{ $t("common.add") }}
          </el-button>
{{/if}}
          <el-button
            @click="handleExportCmd"
          >
          \{{ $t("common.downloadT") }}
          </el-button>
        </span>
      </div>
{{/if}}
      <el-table
        :data="list"
        border
      >
{{#each fieldList}}
        <el-table-column
          :label="$t('{{ @root.moduleName }}.{{ @root.viewName }}.columnName.{{this}}')"
          prop="{{this}}"
        />
{{/each}}
{{! 编辑或删除只要有一个可用，则显示编辑列}}
{{#ifcond isEdit '||' isDelete }}
        <el-table-column
          :label="$t('common.handle')"
          width="200px"
          align="center"
        >
          <template slot-scope="{ row }">
    {{~#if isEdit ~}}
            <el-button
              type="primary"
              icon="el-icon-edit"
              size="mini"
              @click="showEdit(row)"
            >
              \{{ $t("common.edit") }}
            </el-button>
    {{~/if~}}
    {{~#if isDelete~}}
            <el-button
              icon="el-icon-delete"
              size="mini"
              @click="showDelete(row)"
            >
              \{{ $t("common.delete") }}
            </el-button>
    {{~/if~}}
          </template>
        </el-table-column>
{{/ifcond}}
      </el-table>

      <el-pagination
        background
        :current-page.sync="listQuery.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size.sync="listQuery.pageSize"
        layout="total, prev, pager, next,sizes,  jumper"
        :total="totalCount"
        @size-change="getList"
        @current-change="getList"
      />
    </div>

{{! 新增、编辑只要有1个可以用，此次则需要生成代码}}
{{#ifcond isAdd '||' isEdit}}
    <!-- 数据列表新增、编辑弹窗 -->
    <list-edit-info
      v-if="editInfo.isShow"
      :show.sync="editInfo.isShow"
      :edit="editInfo.isEdit"
      :form-data="editInfo.formData"
      @view-save-finish="saveFinish"
    />
{{/ifcond}}
  </div>
</template>

<script>
{{#ifcond isAdd '||' isEdit}}
import ListEditInfo from './components/ListEditInfo.vue';
{{/ifcond}}
import List from '../../mixins/list';
import ExcelExport from '../../mixins/excel-export';
export default {
{{#ifcond isAdd '||' isEdit}}
    components: { ListEditInfo },
{{/ifcond}}
    mixins: [List, ExcelExport],
    data() {
        return {
            // 查询、分页表单
            listQuery: {
{{#if isSingleQuery}}
                {{queryFields}}: '',
{{else}}
    {{#each queryFields}}
                {{this}}:'',
    {{/each}}
{{/if}}
                pageSize: 10,
                pageNum: 1
            },
            // 总记录数
            totalCount: 0,
            // 表格数据
            list: [],
            // 编辑操作相关数据
            editInfo: {
                // 编辑数据
                formData: {
{{#each fieldList}}
                    {{this}}: ''
    {{~#unless @last~}}
                        ,
    {{/unless~}}
{{/each}}
                }
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        // 获取列表数据
        getList() {
            this.$service.{{ moduleName }}
                .get{{pascalCase viewName}}List(this.listQuery)
                .then(response => {
                    if (response.head.code === '000000') {
                        const resultObj = response.body;
                        this.list = resultObj.list;
                        this.totalCount = resultObj.total;
                        this.listQuery.pageNum = resultObj.pageNum;
                        this.listQuery.pageSize = resultObj.pageSize;
                    } else {
                        this.$message.error(response.head.message || this.$t('{{ moduleName }}.{{ viewName }}.message.queryListFailure'));
                    }
                });
        },
{{#if isDelete}}
        // 调用接口删除数据
        postDelete(typeCode) {
            this.$service.{{ moduleName }}
                .post{{pascalCase viewName}}Remove({
                    typeCode: typeCode
                })
                .then(response => {
                    if (response.head.code === '000000') {
                        this.$message.success(this.$t('{{ moduleName }}.{{ viewName }}.message.deleteSuccess'));
                        this.getList();
                    } else {
                        this.$message.error(response.head.message ||this.$t('{{ moduleName }}.{{ viewName }}.message.deleteFailure'));
                    }
                });
        },
        // 显示删除确认提示
        showDelete(row) {
            this.$confirm(
                this.$t('{{ moduleName }}.{{ viewName }}.message.deleteTip'),
                this.$t('{{ moduleName }}.{{ viewName }}.message.deleteTipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.postDelete(row.typeCode);
            });
        },
{{/if}}
        // 重置查询条件
        queryReset() {
            // 清空数据
            Object.assign(this.listQuery, this.$options.data().listQuery);
        },
        // 导出数据列表
        handleExportCmd() {
            this.$service.{{ moduleName }}
                .get{{pascalCase viewName}}Export()
                .then(response => {
                    // 调用下载excel方法
                    this.download(
                        response,
                        `${this.$tools.parseTime(
                            new Date(),
                            '{y}{m}{d}{h}{i}{s}'
                        )}.xlsx`
                    );
                });
        }
    }
};
</script>

<style lang="scss" scoped>
</style>
