<template>
    <div class="product-box">
        <div v-if="calculateFieldVo" class="product-count-box">
            <div class="count1">{{ calculateFieldVo.strategyNum }}战</div>
            <div class="count2">{{ calculateFieldVo.noStrategyNum }}非</div>
        </div>
        <div class="scene-product">
            <div
                ref="chartRef"
                class="scene-product-chart"
                v-popover:SceneProductChartPopover
            ></div>
            <div class="product-text">
                <div class="product-name">{{ keyModuleName }}</div>
                <div class="product-count">在研{{ personCount }}人月</div>
            </div>
        </div>
        <el-popover
            ref="SceneProductChartPopover"
            width="450"
            trigger="click"
            popper-class="resources-product-chart--popper"
            @show="getProjectData"
            ><div
                :style="{
                    padding: projectList.length > 0 ? '15px' : 0
                }"
                class="project-detail"
                id="SceneProductNodePopoverId"
            >
                <div v-for="i in projectList" :key="i.projectName">
                    <el-badge
                        :value="getBadgeValue(i.projectStatus, i.hrCost)"
                        class="area-badge"
                        :style="{
                            '--project-status-color': i.projectStatusColor
                        }"
                    >
                        <div
                            style="display: flex; width: 100%; padding: 0 10px"
                        >
                            <span
                                class="title-prefix"
                                :style="{
                                    backgroundColor: i.strategyColor
                                }"
                            >
                                {{ i.strategy }} </span
                            ><el-link
                                style="height: 20px; line-height: 20px"
                                type="primary"
                                @click="gotoProjectDetail(i.projectId)"
                                >{{ i.projectName }}</el-link
                            >
                        </div>
                    </el-badge>
                </div>
            </div>
        </el-popover>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { getDeviceImg } from 'scene/views/scene/scene-tools.js';

export default {
    name: 'SceneProductChart',
    props: {
        keyModuleName: {
            type: String,
            required: true
        },
        iconName: {
            type: String,
            required: true
        },

        calculateFieldVo: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            // 图表实例
            chart: null,
            personCount: '',
            projectList: []
        };
    },
    mounted() {
        this.initChart();
        this.personCount = this.calculateFieldVo.hrCost;
    },
    beforeDestroy() {
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    },
    deactivated() {
        this.$refs.SceneProductChartPopover.doClose();
    },
    methods: {
        // 初始化图表
        initChart() {
            const target = this.$refs.chartRef;
            this.chart = echarts.init(target);
            const { planProjectNum, doingProjectNum, publishProjectNum } =
                this.calculateFieldVo;
            const data = [
                { color: '#FF8D1A', value: planProjectNum, name: '规划' },
                { color: '#3FCD7B', value: doingProjectNum, name: '在研' },
                { color: '#1C86FF', value: publishProjectNum, name: '发布' }
            ].filter((item) => item.value > 0);
            const option = {
                tooltip: { show: false },
                legend: { show: false },
                series: [
                    {
                        type: 'pie',
                        radius: ['30px', '35px'],
                        center: ['50%', '50%'],
                        color: data.map((item) => item.color),
                        data,
                        label: { show: true, formatter: '{c}' },
                        labelLine: { show: false, length: 6, length2: 0 },
                        silent: true
                    }
                ],
                graphic: {
                    elements: [
                        {
                            type: 'circle',
                            shape: { cx: 35, cy: 35, r: 30 },
                            style: {
                                fill: 'rgba(25,227,193,0.16)',
                                stroke: null
                            },
                            left: 'center',
                            top: 'center'
                        },
                        {
                            type: 'image',
                            style: {
                                image: getDeviceImg(this.iconName),
                                width: 36,
                                height: 36
                            },
                            left: 'center',
                            top: 'center'
                        }
                    ]
                }
            };
            this.chart.setOption(option);
        },
        async getProjectData() {
            try {
                const params = {
                    productType: this.keyModuleName,
                    productSet: ['核心模块'],
                    businessUnit: '金融机具'
                };
                const api = this.$service.scene.getProjectList;
                const res = await api(params);
                if (res.head.code === '000000') {
                    if (res.body.length === 0) {
                        this.projectList = [];
                        this.$refs.SceneProductChartPopover.doClose();
                        this.$message.warning('暂无数据');
                        return;
                    }
                    this.projectList = res.body.map((i) => {
                        i.strategy = i.strategy[0];
                        i.strategyColor =
                            i.strategy === '战' ? '#00baad' : '#2a82e4';
                        if (i.projectStatus === '在研') {
                            i.projectStatusColor = '#3fcd7b';
                        } else if (i.projectStatus === '发布') {
                            i.projectStatusColor = '#2a82e4';
                        } else {
                            i.projectStatusColor = '#ff8d1a';
                        }
                        return i;
                    });
                    this.$nextTick(() => {
                        this.$refs.SceneProductChartPopover.updatePopper();
                    });
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },

        /**
         * 不显示除在研之外状态的人力
         * @param {String} projectStatus 项目状态
         * @param {String} hrCost 人力
         * @returns {String} 描述
         */
        getBadgeValue(projectStatus, hrCost) {
            if (projectStatus !== '在研') {
                return projectStatus;
            }
            return `${projectStatus}，投入${hrCost}人月`;
        },
        async gotoProjectDetail(projectId) {
            const valid = projectId;
            if (!valid) {
                this.$message.warning('该项目暂未录入系统');
                return;
            }
            await this.$refs.SceneProductChartPopover.doClose();
            this.$router.push({
                path: '/project/baseInfo',
                query: {
                    id: projectId,
                    from: 'scene'
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.product-box {
    width: 110px;
    height: 170px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px dashed #d7e2ff;
    .product-count-box {
        width: 100%;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        .count1,
        .count2 {
            width: 35px;
            height: 25px;
            line-height: 24px;
            text-align: center;
            font-size: clamp(0.7rem, 0.489rem + 1.05vw, 1rem);
        }
        .count1 {
            background-color: #00baad;
            color: #fff;
            border-radius: 15px 0px 0px 15px;
        }
        .count2 {
            background-color: #2a82e4;
            color: #fff;
            border-radius: 0px 15px 15px 0px;
        }
    }
    .scene-product {
        width: 100%;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        .scene-product-chart {
            width: 100%;
            height: 100%;
        }
        .product-text {
            width: 100%;
            height: 20%;
            text-align: center;
            font-size: clamp(0.7rem, 0.489rem + 1.05vw, 1rem);
            .product-name,
            .product-count {
                font-size: clamp(0.7rem, 0.489rem + 1.05vw, 1rem);
                white-space: nowrap;
            }
            .product-name {
                font-weight: 600;
                margin-bottom: 4px;
            }
        }
    }
}
.area-badge {
    font-size: 12px;
    margin-top: 8px;

    // 修改职称背景颜色
    ::v-deep .el-badge__content {
        background-color: var(--project-status-color);
    }
}
.title-prefix {
    width: 22px;
    height: 22px;
    color: white;
    margin-right: 10px;
    border-radius: 50%;
    line-height: 22px;
    text-align: center;
}

.project-detail {
    max-height: 360px;
    overflow: auto;
    padding: 1px 0;
}
</style>
<style lang="scss">
// 弹窗样式，与app同级，只能写在这里
.el-popper.el-popover.resources-product-chart--popper {
    max-height: 360px;
    padding: 0;
    border: 0;
}
</style>
