<!-- eslint-disable max-lines-per-function -->
<template>
    <table class="table" border="0" cellspacing="1" cellpadding="0">
        <thead>
            <tr>
                <th class="role-permissions">
                    <el-checkbox v-model="isCheckedAllMenu" :disabled="isView?true:false" @change="checkAllMenu" />
                    <b class="role-permissions-label">{{
            $t("systemManagement.role.permissionTable.permissionsLabel")
          }}</b>
                </th>
                <th class="scope-authority">
                    <div class="scope-authority-label">{{
            $t("systemManagement.role.permissionTable.authorityLabel")
          }}</div>
                    <template>
                        <el-radio-group v-model="radio" @change="allRadioChange">
                            <el-radio :label="1" :disabled="isView?true:false">{{
                $t("systemManagement.role.permissionTable.radioMine")
              }}</el-radio>
                            <el-radio :label="2" :disabled="isView?true:false">{{
                $t("systemManagement.role.permissionTable.radioOrg")
              }}</el-radio>
                            <el-radio :label="3" :disabled="isView?true:false">{{
                $t("systemManagement.role.permissionTable.radioOrgSub")
              }}</el-radio>
                            <el-radio :label="4" :disabled="isView?true:false">{{
                $t("systemManagement.role.permissionTable.radioAll")
              }}</el-radio>
                        </el-radio-group>
                    </template>
                </th>
            </tr>
        </thead>
        <tbody>
            <template v-for="(menu, index) in viewPermissionList">
                <tr :key="menu.id">
                    <td class="table__cell--menu">
                        <div class="parent-permission permission-val">
                            <el-checkbox v-model="menu.checked" :disabled="isView?true:false" :label="menu.label" @change="menuChange(menu)" />
                        </div>
                        <!-- 用父级的一个属性来控制展开、折叠挺好的 -->
                        <template v-if="menu.open">
                            <div v-for="func in menu.children" :key="func.id" class="child-permission permission-val">
                                <el-checkbox v-model="func.checked" :label="func.label" :disabled="isView?true:false" @change="funcChange(func, index)" />
                            </div>
                        </template>
                    </td>
                    <td class="scope-authority-val">
                        <div class="authority-val authority-parent-val" :class="menu.open?'show-border':'hide-border'">
                            <el-radio-group v-show="menu.showRadioGroup" v-model="menu.radioChecked" @change="menuRadioChange(menu,$event)">
                                <el-radio :label="1" :disabled="isView?true:false">{{
                  $t("systemManagement.role.permissionTable.radioMine")
                }}</el-radio>
                                <el-radio :label="2" :disabled="isView?true:false">{{
                  $t("systemManagement.role.permissionTable.radioOrg")
                }}</el-radio>
                                <el-radio :label="3" :disabled="isView?true:false">{{
                  $t("systemManagement.role.permissionTable.radioOrgSub")
                }}</el-radio>
                                <el-radio :label="4" :disabled="isView?true:false">{{
                  $t("systemManagement.role.permissionTable.radioAll")
                }}</el-radio>
                            </el-radio-group>
                            <template v-if="menu.children && menu.children.length > 0">
                                <span v-if="menu.open" class="operation-icon-box" @click="putAway(index)"><i class="el-icon-caret-bottom operation-icon" /></span>
                                <span v-else class="operation-icon-box" @click="open(index)"><i class="el-icon-caret-right operation-icon" /></span>
                            </template>
                        </div>
                        <!-- 用父级的一个属性来控制展开、折叠挺好的 -->
                        <template v-if="menu.open">
                            <div v-for="func in menu.children" :key="func.id" class="authority-val authority-child-val">
                                <template v-if="func.children && func.children.length > 0">
                                    <el-radio-group v-model="func.radioChecked" @change="funcRadioChange(menu,$event)">
                                        <el-radio v-for="item of func.children" :key="item.id" :label="item.scopeType" :class="item.hidden?'placeholder-hiding':''" :disabled="isView?true:false">
                                            <!-- 解决子级radio国际化无法显示英文的问题 -->
                                            <span v-if="item.scopeType === 4">{{ $t("systemManagement.role.permissionTable.radioAll") }}</span>
                                            <span v-else-if="item.scopeType === 1">{{ $t("systemManagement.role.permissionTable.radioMine") }}</span>
                                            <span v-else-if="item.scopeType === 2">{{ $t("systemManagement.role.permissionTable.radioOrg") }}</span>
                                            <span v-else-if="item.scopeType === 3">{{ $t("systemManagement.role.permissionTable.radioOrgSub") }}</span>
                                        </el-radio>
                                    </el-radio-group>
                                </template>
                            </div>
                        </template>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
export default {
    props: {
        // 角色编码
        roleId: {
            type: String,
            default: ''
        },
        // 是否编辑模式
        edit: {
            type: Boolean,
            default: false
        },
        // 是否查看
        isView: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 用于展示的数据列表
            viewPermissionList: [],
            // 当前用户所有权限
            dataPermissionAll: [],
            // 角色对应权限
            dataPermissionRole: [],
            // 第一行radio
            radio: 0,
            // 第一行第一列是否选中
            isCheckedAllMenu: false
        };
    },
    computed: {
        // 当前选中的角色
        curRoleId() {
            return this.roleId;
        },
        // 是否编辑模式
        isEdit() {
            return this.edit;
        },
        // 合并的所有的func列表
        viewPermissionFuncList() {
            let funcList = [];
            this.viewPermissionList.forEach((item) => {
                funcList = funcList.concat(item.children);
            });
            return funcList;
        }
    },
    created() {
        this.getAllPermissionList();
    },
    methods: {
        // 获取所有权限信息
        getAllPermissionList() {
            this.$service.systemManagement
                .getRoleAllPermissionList()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.dataPermissionAll = response.body;
                        if (this.isEdit || this.isView) {
                            // 如果是编辑，获取当前角色权限，并进行反显
                            this.getPermissionList();
                        } else {
                            // 如果是增加，直接显示功能权限即可
                            this.parseViewDataPermissionList();
                        }
                    } else {
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.role.message.queryListFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 获取本人权限信息
        getPermissionList() {
            this.$service.systemManagement
                .getRolePermissionList({
                    roleId: this.curRoleId
                })
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.dataPermissionRole =
                            (response.body || {}).permissionInfo || [];
                        // 绘制权限列表
                        this.parseViewDataPermissionList();
                        this.$eventBus.emit('orgInfo',response.body.orgInfo);
                    } else {
                        this.$message({
                            message: response.head.message,
                            type: 'error'
                        });
                    }
                });
        },
        // eslint-disable-next-line max-lines-per-function
        parseViewDataPermissionList() {
            // eslint-disable-next-line max-lines-per-function
            this.dataPermissionAll.forEach((menuItem) => {
                // 获取角色对应的菜单权限信息
                const roleMenuItem = this.dataPermissionRole.filter((item) => {
                    return item.permissionId === menuItem.permissionId;
                });

                const tempMenuItem = {
                    // 响应式属性
                    radioChecked: 0,
                    // 响应式属性
                    open: true,
                    id: menuItem.permissionId,
                    label: menuItem.permissionName,
                    checked: roleMenuItem.length >= 1,
                    children: [],
                    showRadioGroup: true
                };
                let parentScopeType = 0;
                // 第二层遍历功能权限，组装功能权限
                // eslint-disable-next-line max-lines-per-function
                menuItem.nodes.forEach((funcItem) => {
                    const roleFuncItem = this.dataPermissionRole.filter(
                        (item) => {
                            return item.permissionId === funcItem.permissionId;
                        }
                    );

                    const tempFuncItem = {
                        // 响应式属性
                        radioChecked: 0,
                        id: funcItem.permissionId,
                        label: funcItem.permissionName,
                        checked: roleFuncItem.length >= 1,
                        dataId: '',
                        children: []
                    };

                    // 第三次遍历数据权限，组织数据权限
                    if (funcItem.scopes && funcItem.scopes.length > 0) {
                        const scopes = [];
                        const tempAllObj = {
                            scopeType: 4,
                            hidden: true
                        };
                        const tempMineObj = {
                            scopeType: 1,
                            hidden: true
                        };
                        const tempOrgObj = {
                            scopeType: 2,
                            hidden: true
                        };
                        const tempOrgSubObj = {
                            scopeType: 3,
                            hidden: true
                        };
                        funcItem.scopes.forEach((v) => {
                            scopes.push(v.scopeType);
                        });
                        // includes 无法校验多个
                        if (funcItem.scopes.length === 1) {
                            // 没有的话前端补全
                            if (scopes.includes(1)) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempOrgObj,
                                    tempOrgSubObj,
                                    tempAllObj
                                );
                            } else if (scopes.includes(2)) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempAllObj,
                                    tempMineObj,
                                    tempOrgSubObj
                                );
                            } else if (scopes.includes(3)) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempAllObj,
                                    tempMineObj,
                                    tempOrgObj
                                );
                            } else if (scopes.includes(4)) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempMineObj,
                                    tempOrgObj,
                                    tempOrgSubObj
                                );
                            }
                        } else if (funcItem.scopes.length === 2) {
                            if (scopes.includes(1) && scopes.includes(2)) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempAllObj,
                                    tempOrgSubObj
                                );
                            } else if (
                                scopes.includes(1) &&
                                scopes.includes(3)
                            ) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempAllObj,
                                    tempOrgObj
                                );
                            } else if (
                                scopes.includes(1) &&
                                scopes.includes(4)
                            ) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempOrgObj,
                                    tempOrgSubObj
                                );
                            } else if (
                                scopes.includes(2) &&
                                scopes.includes(3)
                            ) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempMineObj,
                                    tempAllObj
                                );
                            } else if (
                                scopes.includes(2) &&
                                scopes.includes(4)
                            ) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempMineObj,
                                    tempOrgSubObj
                                );
                            } else if (
                                scopes.includes(3) &&
                                scopes.includes(4)
                            ) {
                                funcItem.scopes.splice(
                                    0,
                                    0,
                                    tempMineObj,
                                    tempOrgObj
                                );
                            }
                        } else if (funcItem.scopes.length === 3) {
                            if (!scopes.includes(1)) {
                                funcItem.scopes.splice(0, 0, tempMineObj);
                            } else if (!scopes.includes(2)) {
                                funcItem.scopes.splice(0, 0, tempOrgObj);
                            } else if (!scopes.includes(3)) {
                                funcItem.scopes.splice(0, 0, tempOrgSubObj);
                            } else if (!scopes.includes(4)) {
                                funcItem.scopes.splice(0, 0, tempAllObj);
                            }
                        }
                        // 前端做排序
                        funcItem.scopes.sort(this.scopeSort('scopeType'));
                        // 编辑状态下，回显选中的子级radio
                        if (this.isEdit || this.isView) {
                            this.dataPermissionRole.forEach((v) => {
                                if (v.scopes && v.scopes.length > 0) {
                                    if (
                                        funcItem.permissionId === v.permissionId
                                    ) {
                                        tempFuncItem.radioChecked =
                                            // eslint-disable-next-line no-multi-assign
                                            parentScopeType =
                                                v.scopes[0].scopeType;
                                    }
                                }
                            });
                        }
                        funcItem.scopes.forEach((dataItem) => {
                            const tempDataItem = {
                                hidden: dataItem.hidden,
                                scopeType: dataItem.scopeType,
                                id: dataItem.scopeId,
                                label: dataItem.scopeName,
                                checked:
                                    tempFuncItem.checked &&
                                    roleFuncItem[0].scopes.some(
                                        (item) =>
                                            item.scopeId === dataItem.scopeId
                                    )
                            };
                            // 设置默认选中的权限id
                            if (tempDataItem.checked) {
                                tempFuncItem.dataId = tempDataItem.id;
                            }
                            tempFuncItem.children.push(tempDataItem);
                        });
                    }
                    tempMenuItem.children.push(tempFuncItem);
                });
                const allNoScope = tempMenuItem.children.every((v) => {
                    return v.children.length === 0;
                });
                if (allNoScope) {
                    tempMenuItem.showRadioGroup = false;
                }
                if (this.isEdit || this.isView) {
                    // 编辑情况下，回显父级单选框状态
                    this.updateParentRadioStatus(tempMenuItem, parentScopeType);
                }
                this.viewPermissionList.push(tempMenuItem);
            });
            if (this.isEdit || this.isView) {
                // 编辑情况下，回显表头复选框、单选框状态
                // 解决新增的子级不选中，父级选中状态不取消、表头选中状态不取消问题
                this.viewPermissionList.forEach((v) => {
                    if (v.children && v.children.length > 0) {
                        const itemIsAllChecked = v.children.every((val) => {
                            return val.checked === true;
                        });
                        v.checked = itemIsAllChecked;
                    }
                });
                this.updateAllCheckedStatus();
                this.updateThRadioStatus();
            }
        },
        // 让权限数组按照scopeType递增的顺序排序
        scopeSort(data) {
            return function (obj1, obj2) {
                const value1 = obj1[data];
                const value2 = obj2[data];
                if (value2 < value1) {
                    return 1;
                } else if (value2 > value1) {
                    return -1;
                }
                return 0;
            };
        },
        // 表头全选与取消全选
        checkAllMenu(status) {
            this.viewPermissionList.forEach((v) => {
                v.checked = status;
                if (v.children && v.children.length > 0) {
                    // 兼容异常数据
                    v.children.forEach((val) => {
                        val.checked = status;
                    });
                }
            });
        },
        // 父级菜单选中与取消选中
        menuChange(menu) {
            if (menu.children && menu.children.length > 0) {
                // 兼容异常数据
                menu.children.forEach((val) => {
                    val.checked = menu.checked;
                });
            }
            this.updateAllCheckedStatus();
        },
        // 更新表头复选框全选与取消全选状态
        updateAllCheckedStatus() {
            this.isCheckedAllMenu = this.viewPermissionList.every((v) => {
                return v.checked === true;
            });
        },
        // 子级功能选中与取消选中
        funcChange(funcItem, index) {
            const currMenu = this.viewPermissionList[index];
            if (funcItem.checked) {
                const isChecked = currMenu.children.every((v) => {
                    return v.checked === true;
                });
                currMenu.checked = isChecked;
            } else {
                currMenu.checked = false;
            }
            this.updateAllCheckedStatus();
        },
        // 获取选中的权限信息列表
        getCheckedPermission() {
            const menuIds = [];
            const funcItems = [];
            // 组装菜单权限的id信息
            this.viewPermissionList.forEach((item) => {
                if (item.checked) {
                    menuIds.push(item.id);
                } else if (item.children && item.children.length > 0) {
                    const childHaveSelect = item.children.some((v) => {
                        return v.checked === true;
                    });
                    if (childHaveSelect) {
                        menuIds.push(item.id);
                    }
                }
            });
            // 组装已经选中的功能权限信息id，格式等同于接口请求参数
            this.viewPermissionFuncList.forEach((item) => {
                if (!item.checked) {
                    return;
                }
                const dataIds = [];
                if (item.children && item.children.length > 0) {
                    item.children.forEach((v) => {
                        if (v.scopeType === item.radioChecked) {
                            dataIds.push(v.id);
                        }
                    });
                }
                funcItems.push({
                    permissionId: item.id,
                    dataScopeIds: dataIds
                });
            });
            return {
                permissionIds: menuIds,
                dataPermission: funcItems
            };
        },
        // 收起当前菜单
        putAway(index) {
            this.menuOperationFun(index, false);
        },
        // 打开当前菜单
        open(index) {
            this.menuOperationFun(index, true);
        },
        // 收起当前菜单、打开当前菜单封装成一个方法
        menuOperationFun(index, flag) {
            const currItem = this.viewPermissionList[index];
            currItem.open = flag;
            this.viewPermissionList.splice(index, 1, currItem);
        },
        // 表头radio change事件监听
        allRadioChange(val) {
            this.viewPermissionList.forEach((v) => {
                v.radioChecked = val;
                if (v.children && v.children.length > 0) {
                    // 兼容异常数据
                    v.children.forEach((item) => {
                        item.radioChecked = val;
                    });
                }
            });
        },
        // 菜单radio change监听
        menuRadioChange(v, checkedVal) {
            if (v.children && v.children.length > 0) {
                // 兼容异常数据
                v.children.forEach((item) => {
                    item.radioChecked = checkedVal;
                });
            }
            this.updateThRadioStatus();
        },
        // 功能radio change监听
        funcRadioChange(menu, checkedVal) {
            this.updateParentRadioStatus(menu, checkedVal);
            this.updateThRadioStatus();
        },
        // 更新父级单选框状态
        updateParentRadioStatus(menu, checkedVal) {
            // 把空的、还有不含有当前点击的这种权限的过滤出去
            const tempArr = [];
            const finalArr = [];
            menu.children.forEach((v) => {
                let tempObj = {};
                if (v.children && v.children.length > 0) {
                    v.children.forEach((val) => {
                        if (!val.hidden && val.scopeType === checkedVal) {
                            tempObj = val;
                            tempObj.radioChecked = v.radioChecked;
                        }
                    });
                }
                tempArr.push(tempObj);
            });
            tempArr.forEach((v) => {
                if (JSON.stringify(v) !== '{}') {
                    finalArr.push(v);
                }
            });
            // 更新父级菜单单选框的选中与取消选中状态
            const isAllChecked = finalArr.every((v) => {
                return v.radioChecked === checkedVal;
            });
            if (isAllChecked) {
                menu.radioChecked = checkedVal;
            } else {
                menu.radioChecked = 0;
            }
        },
        // 更新表头单选框状态
        updateThRadioStatus() {
            let count = 0;
            const radioCheckedArr = [];
            this.viewPermissionList.forEach((v) => {
                radioCheckedArr.push(v.radioChecked);
            });
            // 判断数据里面的值是否都相同
            for (let i = 0; i < radioCheckedArr.length; i++) {
                if (radioCheckedArr[i] !== radioCheckedArr[0]) {
                    // eslint-disable-next-line no-plusplus
                    count++;
                }
            }
            if (count === 0) {
                this.radio = radioCheckedArr[0];
            } else {
                // 避免数据残留
                this.radio = 0;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
$border-color: #eaeaea;
$font-color: #909399;

.table {
    border: 1px solid $border-color;
    border-right: 0px;
    border-bottom: 0px;
    margin: 0px 0px -2px 0px;
    width: 100%;
    border-collapse: collapse;
    background-color: #F8F8F8;
}
.table td,
th {
    border: 1px solid $border-color;
    border-top: 0px;
    border-left: 0px;
    margin: 0px;
    height: 50px;
}
.table th {
    padding: 10px;
}
.table td {
    padding: 0;
}
.table th {
    background: #f5f6fa;
}
.table__cell--menu {
    width: 25%;
}
.table__cell--func {
    width: 25%;
}
.table__cell--data {
    width: 50%;
}
.scope-authority {
    display: flex;
    align-items: center;
    padding: 10px 0 !important;
    .scope-authority-label {
        width: 110px;
        margin-right: 10px;
    }
    ::v-deep .el-radio-group {
        flex: 1;
        width: 100%;
        display: flex;
        text-align: left;
        .el-radio {
            flex: 1;
        }
    }
}
.role-permissions {
    text-align: left;
    .role-permissions-label {
        margin-left: 10px;
    }
}
.parent-permission,
.child-permission {
    width: 100%;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid $border-color;
}
::v-deep .parent-permission {
    padding-left: 10px;
    background: #fff;
    .el-checkbox__label{
        color:#000000 !important;
    }
}
::v-deep .child-permission {
    padding: 0 10px;
    .el-checkbox__label{
        color:#3370FF;
    }
}
.permission-val:last-child {
    border-bottom: none;
}
.table {
    .scope-authority-val {
        .authority-val {
            position: relative;
            width: 100%;
            height: 50px;
            padding-left: 120px;
            ::v-deep .el-radio-group {
                height: 100%;
                width: 100%;
                display: flex;
                align-items: center;
                .el-radio {
                    flex: 1;
                }
            }
        }
        .show-border {
            border-bottom: 1px solid $border-color;
        }
        .hide-border {
            border-bottom: none;
        }
        .authority-parent-val {
            line-height: 60px;
            background: #fff;
        }
        .authority-child-val {
            line-height: 50px;
            border-bottom: 1px solid $border-color;
            background-color: #F8F8F8;
        }
        .authority-val:last-child {
            border-bottom: 0;
        }
    }
    .operation-icon-box {
        position: absolute;
        display: block;
        width: 50px;
        height: 100%;
        text-align: center;
        right: 100%;
        top: 0;
        cursor: pointer;
        .operation-icon {
            font-size: 16px;
            color: #fff;
            width: 18px;
            height: 18px;
            background: #3370FF;
            border-radius: 50%;
        }
    }
    /*占位隐藏*/
    .placeholder-hiding {
        visibility: hidden;
    }
    /*重置禁用时的样式*/
    ::v-deep .el-radio__input.is-disabled + span.el-radio__label,
    ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
        color: #606266;
    }
    ::v-deep .el-radio__input.is-checked + span.el-radio__label,
    ::v-deep .el-checkbox__input.is-checked + span.el-checkbox__label {
        color: #3370ff;
    }
}
</style>
