<template>
    <div>
        <MaintenanceSelector
            @input="handleChange"
            :key="projectSelectorKey"
            :year="year"
        ></MaintenanceSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="仪表盘" name="dashboard" :lazy="true">
                    <MaintenanceDashboard
                        :activeName="activeName"
                        :productLine="productLine"
                        :subProductLine="subProductLine"
                        :projectManager="projectManager"
                        @year-change="yearChange"
                    ></MaintenanceDashboard>
                </el-tab-pane>
                <el-tab-pane
                    label="支出明细"
                    name="expenseDetails"
                    :lazy="true"
                >
                    <ExpenseDetails
                        :activeName="activeName"
                        :productLine="productLine"
                        :subProductLine="subProductLine"
                        :projectManager="projectManager"
                        @year-change="yearChange"
                    ></ExpenseDetails>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import MaintenanceSelector from 'maintenanceProject/components/MaintenanceSelector';
import MaintenanceDashboard from './components/MaintenanceDashboard';
import ExpenseDetails from './components/ExpenseDetails';

export default {
    name: 'MaintenanceFinance',
    components: {
        MaintenanceSelector,
        MaintenanceDashboard,
        ExpenseDetails
    },
    data() {
        return {
            // 当前年份
            year: new Date().getFullYear().toString(),
            activeName: 'dashboard',
            // 产品线
            productLine: '',
            // 细分产品线
            subProductLine: '',
            // 项目经理
            projectManager: '',
            // 顶部级联组件key
            projectSelectorKey: 0
        };
    },
    activated() {
        // 因为做了缓存，其他页面改变项目之后本页面的级联选择器不会同步，改变key令其同步
        this.projectSelectorKey += 1;
    },
    methods: {
        // 调用组件值
        handleChange(value) {
            // 注意这里所有的值都要取，不能只取最后一个
            const [productLine, subProductLine = null, projectManager = null] =
                value;
            this.productLine = productLine;
            this.subProductLine = subProductLine;
            this.projectManager = projectManager;
        },
        /**
         * 年份选择变更
         * @param {String} year 年份
         */
        yearChange(year) {
            this.year = year;
            this.projectSelectorKey += 1;
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 10px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
.resourceLoad {
    margin-top: 20px;
}
</style>
