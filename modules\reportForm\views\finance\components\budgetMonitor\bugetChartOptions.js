const formatTooltip = (params) => {
    let tooltipContent = '';
    params.forEach((param) => {
        tooltipContent += `${param.marker}${param.seriesName}：${param.data}${
            param.seriesName === '比率' ? '%' : '个'
        }</br>`;
    });
    return tooltipContent;
};

// 按月份：柱状图
export const getOverBudgetChartOption = (data) => {
    return {
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params);
            },
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['执行预算管理项目数量', '超预算项目数量', '比率']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: data.map((i) => i.subProductLine),
                axisTick: {
                    alignWithLabel: true
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '数量(个)',
                position: 'left',
                axisLabel: {
                    formatter: '{value} '
                }
            },
            {
                type: 'value',
                name: '比率',
                position: 'right',
                splitLine: {
                    show: false
                },
                axisLabel: {
                    formatter: (value) => {
                        return `${value.toFixed(1)}%`;
                    }
                }
            }
        ],
        series: [
            {
                name: '执行预算管理项目数量',
                type: 'bar',
                data: data.map((i) => i.projectTotal)
            },
            {
                name: '超预算项目数量',
                type: 'bar',
                data: data.map((i) => i.overBudgetProjectCount)
            },
            {
                name: '比率',
                type: 'line',
                yAxisIndex: 1,
                data: data.map((i) => i.proportion),
                label: {
                    show: true,
                    formatter: (params) => {
                        return `${Number(params.value).toFixed(1)}%`;
                    }
                }
            }
        ]
    };
};
