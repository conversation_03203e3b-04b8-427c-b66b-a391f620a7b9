<template>
  <div class="container">
    <el-input
      v-model="filterText"
      type="text"
      suffix-icon="el-icon-search"
      placeholder="输入名称进行过滤"
      autocomplete="text"
    />
    <el-tree
      ref="treeObj"
      :data="treeData"
      :props="defaultProps"
      :expand-on-click-node="false"
      :current-node-key="currentNodeKey"
      default-expand-all
      highlight-current
      node-key="treeId"
{{#if isTreeSort}}
      draggable
      :allow-drag="allowDrag"
{{/if}}
      :filter-node-method="filterTree"
      @node-click="handleNodeClick"
{{#if isTreeSort}}
      @node-drop="handleDrop"
{{/if}}
    >
      <el-tooltip slot-scope="{ node, data }" :content="node.label" placement="right">
        <span class="block">
          <span class="block__text">\{{ node.label }}</span>
          <span class="block__btns">
{{#if isTreeAdd}}
            <el-button
              type="text"
              size="mini"
              icon="el-icon-circle-plus"
              @click.stop="addTree(data)"
            />
{{/if}}
{{#if isTreeEdit}}
            <el-button
              type="text"
              size="mini"
              icon="el-icon-edit"
              @click.stop="editTree(data)"
            />
{{/if}}
{{#if isTreeDelete}}
            <el-button
              type="text"
              size="mini"
              icon="el-icon-delete"
              @click.stop="delTree(node, data)"
            />
{{/if}}
          </span>
        </span>
      </el-tooltip>
    </el-tree>

{{! 新增、编辑只要有1个可以用，此次则需要生成代码}}
{{#ifcond isTreeAdd '||' isTreeEdit}}
    <!-- tree数据新增、编辑弹窗 -->
    <tree-edit-info
      v-if="editInfo.isShow"
      :show.sync="editInfo.isShow"
      :edit="editInfo.isEdit"
      :form-data="editInfo.formData"
      @view-save-finish="saveFinish"
    />
{{/ifcond}}
  </div>
</template>

<script>
{{#ifcond isTreeAdd '||' isTreeEdit}}
import TreeEditInfo from './TreeEditInfo.vue';
{{/ifcond}}

// 事件名称定义
const constEventName = {
    TREE_NODE_CLICK: 'tree-node-click',
    TREE_EDIT_FINISH: 'tree-edit-finish'
};

export default {
{{#ifcond isTreeAdd '||' isTreeEdit}}
    components: { TreeEditInfo },
{{/ifcond}}
    data() {
        return {
            // 树结构数据
            treeData: [],
            // 树节点绑定属性
            defaultProps: {
                children: 'children',
                label: 'treeName'
            },
{{#if isTreeSort}}
            // 用于树拖拽还原
            treeDataBackup: [],
            // 拖拽Tree节点数据对象
            dragTreeItem: {
                draggingNode: null,
                dropNode: null,
                dropType: ''
            },
{{/if}}
            // 用于tree过滤
            filterText: '',
            // 当前选中的key
            currentNodeKey: '',
{{#ifcond isTreeAdd '||' isTreeEdit}}
            // 编辑操作相关数据
            editInfo: {
                // 是否显示窗口
                isShow: false,
                // 窗口模式，true：编辑，false：新增
                isEdit: false,
                // 编辑数据,一般由组件进行重写
                formData: {
                    treeId: '',
                    treeName: '',
                    parentId: '',
                    parentName: ''
                }
            }
{{/ifcond}}
        };
    },
    watch: {
        filterText(val) {
            this.$refs.treeObj.filter(val);
        }
    },
    created() {
        this.getTreeList();
    },
    methods: {
        // 获取数据
        getTreeList() {
            const data = {};
            this.$service.{{ moduleName }}
                .get{{pascalCase viewName}}TreeList(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.treeData = res.body;
{{#if isTreeSort}}
                        // 深度copy数据
                        this.treeDataBackup = JSON.parse(
                            JSON.stringify(this.treeData)
                        );
{{/if}}

                        // 如果列表有数据，并且没有选中，则默认选中第一条
                        if (!this.currentNodeKey && this.treeData && this.treeData.length > 0) {
                            this.currentNodeKey = this.treeData[0].treeId || '';
                            this.$emit(
                                constEventName.TREE_NODE_CLICK,
                                this.treeData[0]
                            );
                        }
                        this.$nextTick(() => {
                            // 设置选中节点
                            this.$refs.treeObj.setCurrentKey(this.currentNodeKey);
                        });
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
{{#if isTreeSort}}
        // 拖拽树节点 - 确认
        postTreeSort() {
            const params = {
                thisNodeId: this.dragTreeItem.draggingNode.data.treeId,
                targetNodeId: this.dragTreeItem.dropNode.data.treeId,
                parentId: this.dragTreeItem.dropNode.data.parentId,
                sortType: this.dragTreeItem.dropType
            };
            this.$service.{{ moduleName }}
                .post{{pascalCase viewName}}TreeSort(params)
                .then((res) => {
                    if (res.head.code !== '000000') {
                        this.$message.error(res.head.message);
                    }
                    this.getTreeList();
                })
                .catch((e) => {
                    this.treeDataRestore();
                });
        },
{{/if}}
{{#if isTreeDelete}}
        // 删除 - 确认
        postTreeDelete(treeId) {
            this.$service.{{ moduleName }}
                .post{{pascalCase viewName}}TreeRemove({ treeId })
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.getTreeList();
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
{{/if}}
        // 过滤树列表
        filterTree(value, data) {
            if (!value) {
                return true;
            }
            return (data.treeName || '').includes(value.trim());
        },
        // 获取树节点下的 - 列表数据
        handleNodeClick(node, nodeValue, obj) {
            // 获取该树节点下的列表数据
            this.currentNodeKey = node.treeId;
            this.$emit(constEventName.TREE_NODE_CLICK, node);
        },
{{#if isTreeSort}}
        // 根据拖动类型，获取拖动类型中文
        getDropTypeName(val) {
            let name;
            switch (val) {
                case 'before':
                    name = '前面';
                    break;
                case 'inner':
                    name = '里面';
                    break;
                default:
                    name = '后面';
                    break;
            }
            return name;
        },
        // 判断节点能否被拖拽
        allowDrag(draggingNode) {
            // 根节点是不允许被拖拽的
            // draggingNode.level!==1;
            return true;
        },
        // 恢复拖拽数据
        treeDataRestore() {
            this.treeData = JSON.parse(JSON.stringify(this.treeDataBackup));
        },
        // 拖拽成功完成时触发的事件
        handleDrop(draggingNode, dropNode, dropType, ev) {
            this.dragTreeItem = {
                draggingNode: draggingNode,
                dropNode: dropNode,
                dropType: dropType
            };
            this.$confirm(`【${draggingNode.label}】移动到【${dropNode.label}】的[${this.getDropTypeName(dropType)}]`, this.$t('common.tips'), { type: 'warning' }).then(() => {
                this.postTreeSort(this.dragTreeItem);
            }).catch(() => {
                // 拖拽取消事件处理
                this.treeDataRestore();
            });
        },
{{/if}}
{{#if isTreeAdd}}
        // 增加
        addTree(data) {
            // 清空上次数据
            Object.assign(
                this.editInfo.formData,
                this.$options.data().editInfo.formData
            );
            this.editInfo.formData.parentId = data.treeId;
            this.editInfo.formData.parentName = data.treeName;

            this.editInfo.isEdit = false;
            this.editInfo.isShow = true;
        },
{{/if}}
{{#if isTreeEdit}}
        // 编辑
        editTree(data) {
            // 组装数据
            Object.assign(this.editInfo.formData, data);

            this.editInfo.isEdit = true;
            this.editInfo.isShow = true;
        },
{{/if}}
{{#if isTreeDelete}}
        // 删除节点数据
        delTree(node, data) {
            let msg = '';
            // 判断是否有子节点，提示删除所以子节点
            if (data && data.children && data.children.length > 0) {
                msg = `【${data.treeName}】有子节点，将会一并删除，确定删除吗？`;
            } else {
                msg = `确定删除节点【${data.treeName}】吗？`;
            }
            this.$confirm(msg, '确定提示', {
                type: 'warning'
            }).then(() => {
                this.currentNodeKey = data.parentId;
                this.postTreeDelete(data.treeId);
            });
        },
{{/if}}
{{#ifcond isTreeAdd '||' isTreeEdit}}
        // 编辑窗口保持回调事件
        saveFinish() {
            this.getTreeList();
        }
{{/ifcond}}
    }
};
</script>

<style lang="scss" scoped>

</style>
