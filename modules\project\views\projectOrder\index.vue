<template>
    <div>
        <ProjectSelector
            @input="handleChange"
            :key="projectSelectorKey"
        ></ProjectSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="订单列表" name="orderList" :lazy="true">
                    <OrderList
                        :activeName="activeName"
                        :projectId="projectId"
                        :projectName="projectName"
                        :proProjectId="proProjectId"
                        projectType="开发"
                    ></OrderList>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector.vue';
import OrderList from 'maintenanceProject/views/maintenanceOrder/components/OrderList';

export default {
    name: 'ProjectRisk',
    components: {
        ProjectSelector,
        OrderList
    },
    data() {
        return {
            activeName: 'orderList',
            // 顶部级联组件key
            projectSelectorKey: 0,
            projectId: '',
            projectName: '',
            proProjectId: ''
        };
    },
    async created() {
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    activated() {
        this.projectSelectorKey += 1;
    },
    methods: {
        /**
         * 选择后的回调
         * @param {Array} value  选中的值
         */
        handleChange(value) {
            this.projectId = value[value.length - 1];
            const { proProjectId, projectName } =
                this.$store.state.project.projectInfo;
            this.proProjectId = proProjectId;
            this.projectName = projectName;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 0px 20px;
    background-color: #ffffff;
    overflow: auto;
}
</style>
