<template>
    <div class="change-task-container">
        <el-tooltip
            effect="dark"
            content="该禅道任务下已存在消耗工时，无法变更"
            placement="top"
            :disabled="!hasTaskHour"
        >
            <el-button
                v-permission="['changeZentaoTaskButton']"
                type="primary"
                @click="hanldeTaskChange"
                :disabled="!!hasTaskHour"
                >变更任务</el-button
            >
        </el-tooltip>

        <el-dialog
            title="变更任务"
            :visible.sync="dialogVisible"
            :before-close="reset"
            width="500px"
            append-to-body
        >
            <el-form ref="form" :model="form" class="form" size="large">
                <el-form-item
                    label="关联禅道项目"
                    prop="proProjectId"
                    :rules="required"
                >
                    <el-select
                        @input="getZentaoTaskList"
                        class="w-100"
                        v-model="form.proProjectId"
                        placeholder="请选择关联禅道项目"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="item in zentaoProjectList"
                            :label="item.name"
                            :key="item.id"
                            :value="item.id"
                        ></el-option
                    ></el-select>
                </el-form-item>
                <el-form-item label="关联禅道任务" prop="proTaskId">
                    <el-select
                        class="w-100"
                        v-model="form.proTaskId"
                        placeholder="请选择关联禅道任务"
                        popper-class="truncate-option-dropdown"
                        filterable
                    >
                        <el-option
                            v-for="item in zentaoTaskList"
                            :label="`ID${item.id}：${item.name}`"
                            :key="item.id"
                            :value="item.id"
                            :title="`ID${item.id}：${item.name}`"
                        ></el-option
                    ></el-select>
                </el-form-item>
            </el-form>
            <div class="footer" slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// 变更禅道任务的弹窗
export default {
    name: 'ChangeTaskDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 是否有禅道工时
        hasTaskHour: {
            type: Boolean,
            default: true
        },
        // 是否有禅道工时
        zentaoProjectList: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            dialogVisible: false,
            form: {
                proProjectId: '',
                proTaskId: ''
            },
            required: {
                required: true,
                message: ' ',
                trigger: ['change', 'blur']
            },
            zentaoTaskList: []
        };
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update');
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            if (!this.form.proTaskId) {
                this.form.proTaskId = 0;
            }
            this.$emit('save', this.form);
            this.$refs.form.resetFields();
            this.closeDialog();
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 关闭弹窗前的回调
         * @param {Function} done 关闭弹窗的函数
         */
        reset(done) {
            this.$refs.form.resetFields();
            done();
        },
        /**
         * 变更任务
         */
        hanldeTaskChange() {
            this.dialogVisible = true;
        },
        /**
         * 根据项目获取子任务接口
         * @param {String} value id
         */
        async getZentaoTaskList(value) {
            const api =
                this.$service.maintenanceProject.zentao.getZentaoTaskList;

            try {
                const res = await api({ proProjectId: value });
                if (res.head.code === '000000') {
                    this.zentaoTaskList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.w-100 {
    width: 100%;
}
.change-task-container {
    display: grid;
    place-items: center;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
}
</style>

<style>
/* 全局样式，用于自定义下拉菜单选项 */
.truncate-option-dropdown .el-select-dropdown__item {
    max-width: 320px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
