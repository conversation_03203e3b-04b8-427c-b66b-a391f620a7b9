<template>
  <div class="container">
    <el-dialog
      v-if="tableDialogVisible"
      :title="dialogStatus==='create'?$t('systemManagement.permission.dialog.addTitle'):$t('systemManagement.permission.dialog.updateTitle')"
      :visible.sync="tableDialogVisible"
      @close="closeWindowHandler"
    >
      <el-form ref="tableForm" :rules="tableFormRules" :model="tableDialogFormData" label-position="right" label-width="140px">
        <el-form-item :label="$t('systemManagement.permission.permissionName')" prop="permissionName">
          <el-input v-model="tableDialogFormData.permissionName" :placeholder="$t('systemManagement.permission.placeholder.msg1')" />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.permission.permissionCode')" prop="permissionCode">
          <el-input v-model="tableDialogFormData.permissionCode" :validate-event="false" :placeholder="$t('systemManagement.permission.placeholder.msg2')" />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.permission.routingUrl')" prop="routingUrl">
          <el-input v-model="tableDialogFormData.routingUrl" :placeholder="$t('systemManagement.permission.placeholder.msg3')" />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.permission.parentName')" prop="parentName">
          <el-input v-model="tableDialogFormData.parentName" disabled />
        </el-form-item>
        <el-form-item v-if="+tableDialogFormData.permissionType === 2" :label="$t('systemManagement.permission.permissionIcon')" prop="permissionIcon">
          <el-input v-model="tableDialogFormData.permissionIcon" readonly class="choose-icon-input" @click.native="chooseIconClickHandler">
            <div slot="append" class="choose-icon-show">
              <i v-if="selectedIcon.isElementIcon" :class="selectedIcon.className" />
              <svg-icon v-else :icon-class="selectedIcon.className" class-name="disabled" />
            </div>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('systemManagement.permission.permissionType')" prop="permissionType">
          <el-radio v-if="dialogStatus === 'update'" v-model="tableDialogFormData.permissionType" :disabled="dialogStatus === 'update'" label="1">{{ $t('systemManagement.permission.system') }}</el-radio>
          <el-radio v-if="dialogStatus === 'update'" v-model="tableDialogFormData.permissionType" :disabled="dialogStatus === 'update'" label="2">{{ $t('systemManagement.permission.menu') }}</el-radio>
          <el-radio v-model="tableDialogFormData.permissionType" :disabled="dialogStatus === 'update'" label="3">{{ $t('systemManagement.permission.button') }}</el-radio>
          <el-radio v-model="tableDialogFormData.permissionType" :disabled="dialogStatus === 'update'" label="4">{{ $t('systemManagement.permission.interface') }}</el-radio>
        </el-form-item>
        <el-form-item :label="$t('systemManagement.permission.enableFlag')">
          <el-switch v-model="tableDialogFormData.hasEnable" />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.permission.dialog.remarks')" prop="remarks">
          <el-input v-model="tableDialogFormData.remarks" :autosize="{ minRows: 4, maxRows: 4}" maxlength="50" type="textarea" :placeholder="$t('systemManagement.permission.placeholder.msg4')" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="closeWindowHandler">
          {{ $t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?tableCreateData():tableUpdateData()">
          {{ $t('common.done') }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :title="$t('systemManagement.permission.dialog.chooseIcon')" width="50%" :visible.sync="chooseIconDialogVisible">
      <icons ref="iconComponent" :selected-icon-name="tableDialogFormData.permissionIcon" @iconSelectedEventByClick="iconSelectedEventByClickHandler" @iconSelectedEventByDBClick="iconSelectedEventByDBClickHandler" />
      <div slot="footer">
        <el-button type="primary" @click="chooseIconDialogVisible = false">
          {{ $t('common.done') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Icons from '../../../components/Icons';
import { getIconItem } from '../../../components/Icons/icons.js';

export default {
    name: 'TableDialog',
    components: { Icons },
    props: {
        // 是否显示弹窗
        showWindow: {
            type: Boolean,
            default: false
        },
        // 标识符，弹窗是新增权限还是编辑权限，新增--'create' 更新--'update'
        dialogStatus: {
            type: String,
            default: ''
        },
        // 当前正在处理的节点对象
        processingTableItem: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 新增权限页面formdata对象
            tableDialogFormData: {
                // 父页面传递过来的原始数据
                dataSource: {},
                // 权限id
                permissionId: '',
                // 权限名称
                permissionName: '',
                // 路由地址
                routingUrl: '',
                // 权限编码
                permissionCode: '',
                // 父级权限名称
                parentName: '',
                // 父级权限id
                parentId: '',
                // 权限图标
                permissionIcon: '',
                // 权限类型 0--无类型（根节点使用） 1--系统 2--菜单 3--按钮 4--接口
                permissionType: '1',
                // 是否启用
                hasEnable: false,
                // 排序
                orderBy: 0,
                // 层级
                level: 0,
                // 备注
                remarks: '',
                oldParam: {},
                // reset方法
                reSet() {
                    this.permissionName = '';
                    this.permissionCode = '';
                    this.routingUrl = '';
                    this.orderBy = 0;
                    this.level = 0;
                    this.parentName = '';
                    this.parentId = '';
                    this.permissionIcon = '';
                    this.permissionType = 1;
                    this.hasEnable = true;
                    this.remarks = '';
                    this.oldParam = '';
                },
                initAddData(dataObject) {
                    if (dataObject) {
                        this.parentName = dataObject.permissionName || '';
                        this.parentId = dataObject.permissionId || '';
                        this.permissionType = '3';
                    }
                },
                initUpdateData(dataObject) {
                    if (dataObject) {
                        this.dataSource = dataObject;
                        this.permissionId = dataObject.permissionId || '';
                        this.permissionName = dataObject.permissionName || '';
                        this.permissionCode = dataObject.permissionCode || '';
                        this.routingUrl = dataObject.routingUrl || '';
                        this.parentName = dataObject.parentName || '';
                        this.parentId = dataObject.parentId || '';
                        this.permissionIcon = dataObject.permissionIcon || '';
                        this.permissionType = dataObject.permissionType || 1;
                        this.hasEnable = Boolean(+dataObject.hasEnable);
                        this.orderBy = dataObject.orderBy || 0;
                        this.level = dataObject.level || 0;
                        this.remarks = dataObject.remarks || '';
                        this.oldParam = dataObject.oldParam || '';
                    }
                },
                getData() {
                    return {
                        permissionId: this.permissionId,
                        permissionName: this.permissionName,
                        permissionCode: this.permissionCode,
                        routingUrl: this.routingUrl,
                        parentName: this.parentName,
                        parentId: this.parentId,
                        permissionIcon: this.permissionIcon,
                        permissionType: this.permissionType,
                        hasEnable: Number(this.hasEnable),
                        remarks: this.remarks,
                        oldParam: this.oldParam
                    };
                }
            },
            // 新增权限页面是否弹窗变量
            tableDialogVisible: false,
            // 选择图标页面是否弹窗变量
            chooseIconDialogVisible: false,
            // 已经选中的图标对象
            selectedIcon: {
                iconName: '',
                className: '',
                isChecked: false,
                isElementIcon: true
            }
        };
    },
    computed: {
        // 新增权限页面校验规则
        tableFormRules() {
            // 校验函数，不允许输入中文
            const checkNoChineseData = (rule, value, callback) => {
                // 未修改时不进行校验
                if (value === this.tableDialogFormData.dataSource.permissionCode) {
                    return callback();
                }
                if (!value) {
                    return callback(new Error(this.$t('systemManagement.msg.required')));
                }
                if (value.length > 45) {
                    return callback(new Error(this.$t('systemManagement.msg.checkMsg_5')));
                }
                const regBox = {
                    regWords: /[\u4E00-\u9FA5]/g
                };
                const result = regBox.regWords.test(value);
                if (!result) {
                    this.dialogLoading = true;
                    this.$service.systemManagement
                        .postPermissionCodeCheck(
                            { permissionCode: value }
                        )
                        .then((response) => {
                            this.dialogLoading = false;
                            if (response.head.code === '000000') {
                                if (response.body === 'false') {
                                    return callback();
                                } 
                                    return callback(new Error(this.$t('systemManagement.msg.checkMsg_CodeRepeat')));
                                
                            } 
                                const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                return callback(new Error(this.$t(msg) || this.$t('common.interfaceFailed')));
                            
                        }).catch(() => {
                            this.dialogLoading = false;
                            return callback(new Error(this.$t('systemManagement.msg.serviceFailed')));
                        });
                } else {
                    return callback(new Error(this.$t('systemManagement.msg.checkMsg_1')));
                }
            };
            return {
                permissionName: [
                    {
                        required: true,
                        message: this.$t('systemManagement.msg.required'),
                        trigger: ['blur', 'change']
                    },
                    {
                        min: 1,
                        max: 10,
                        message: this.$t('systemManagement.msg.checkMsg_2'),
                        trigger: ['blur', 'change']
                    }
                ],
                permissionCode: [
                    {
                        required: true,
                        validator: checkNoChineseData
                    }
                ],
                routingUrl: [
                    {
                        min: 0,
                        max: 255,
                        message: this.$t('systemManagement.msg.checkMsg_6'),
                        trigger: ['blur', 'change']
                    }
                ],
                permissionIcon: [
                    {
                        required: true,
                        message: this.$t('systemManagement.msg.checkMsg_4'),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    watch: {
        showWindow(val) {
            if (val) {
                this.tableDialogFormData.reSet();
                if (this.dialogStatus === 'create') {
                    this.tableDialogFormData.initAddData(this.processingTableItem);
                }
                if (this.dialogStatus === 'update') {
                    this.tableDialogFormData.initUpdateData(this.processingTableItem);
                    const iconItem = getIconItem(this.tableDialogFormData.permissionIcon);
                    if (iconItem) {
                        this.selectedIcon = iconItem;
                    }
                }
                this.tableDialogVisible = true;
            } else {
                this.tableDialogVisible = false;
            }
        }
    },
    methods: {
        // 抛出事件让父页面的树刷新数据
        parentNeedUpdate(flag) {
            this.$emit('tableNeedUpdateEvent', flag);
        },
        // 新增权限弹窗，新增权限，点击确定按钮后，先校验，后保存数据
        tableCreateData() {
            this.$refs['tableForm'].validate((valid) => {
                if (valid) {
                    this.$service.systemManagement
                        .postPermissionCreateData(
                            this.tableDialogFormData.getData()
                        )
                        .then((response) => {
                            if (response.head.code === '000000') {
                                this.$message({
                                    title: this.$t('common.success'),
                                    message: this.$t('systemManagement.msg.createSuccess'),
                                    type: 'success',
                                    duration: 2000
                                });
                                // 刷新树节点
                                this.parentNeedUpdate('create');
                                // 关闭窗口
                                this.closeWindowHandler();
                            } else {
                                const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                this.$message({
                                    title: this.$t('systemManagement.msg.failed'),
                                    message: this.$t(msg),
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                }
            });
        },
        // 编辑权限弹窗，更新权限，点击确定按钮后，先校验，后保存数据
        tableUpdateData() {
            this.$refs['tableForm'].validate((valid) => {
                if (valid) {
                    this.$service.systemManagement
                        .putPermissionUpdateData(this.tableDialogFormData.getData())
                        .then((response) => {
                            if (response.head.code === '000000') {
                                this.$message({
                                    title: this.$t('common.success'),
                                    message: this.$t('systemManagement.msg.editSuccess'),
                                    type: 'success',
                                    duration: 2000
                                });
                                // 刷新树节点
                                this.parentNeedUpdate('update');
                                // 关闭窗口
                                this.closeWindowHandler();
                            } else {
                                const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                this.$message({
                                    title: this.$t('systemManagement.msg.failed'),
                                    message: this.$t(msg),
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                }
            });
        },
        // 关闭窗口处理
        closeWindowHandler() {
            this.tableDialogVisible = false;
            this.$emit('update:showWindow', false);
        },
        // 点击图标input框，在弹窗中选择icon
        chooseIconClickHandler() {
            this.chooseIconDialogVisible = true;
        },
        // 单击选中图标后，触发的事件，用于记录选中的图标
        iconSelectedEventByClickHandler(item) {
            this.selectedIcon = item;
            if (this.selectedIcon) {
                this.tableDialogFormData.permissionIcon = this.selectedIcon.className;
            }
        },
        // 双击选中图标后，触发的事件，用于记录选中的图标
        iconSelectedEventByDBClickHandler(item) {
            this.selectedIcon = item;
            if (this.selectedIcon) {
                this.tableDialogFormData.permissionIcon = this.selectedIcon.className;
                // 双击选中图标后，直接关闭窗口
                this.chooseIconDialogVisible = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>

</style>
