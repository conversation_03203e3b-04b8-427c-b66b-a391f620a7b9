<!-- 用于创建/编辑会议 -->
<template>
    <div>
        <el-dialog
            title="会议效果评价"
            :visible.sync="dialogVisible"
            width="85%"
        >
            <HeaderInfo
                :meetingInfo="headerInfo.meetingInfo"
                :meetingPartRelateList="headerInfo.meetingPartRelateList"
            ></HeaderInfo>
            <el-form ref="form" :model="form" class="quality-form">
                <el-table
                    :data="form.tableData"
                    class="quality-form-table"
                    :row-style="{ background: '#fff' }"
                    :span-method="objectSpanMethod"
                    empty-text="无会议效果评价"
                >
                    <el-table-column
                        prop="prop"
                        label="会议效果评价"
                        align="center"
                    >
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="evaluationItems"
                            label="评价项"
                            width="80"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="evaluationName"
                            label="评价人"
                            width="90"
                        >
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            prop="evaluationScore"
                        >
                            <template #header>
                                <RedStar class="required" />评价结论
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.evaluationScore`"
                                    :rules="required"
                                >
                                    <div class="flex">
                                        <el-radio-group
                                            v-model="scoped.row.evaluationScore"
                                            v-if="
                                                scoped.row.evaluationItems ===
                                                '评委选择合理性'
                                            "
                                            :disabled="
                                                user !==
                                                    scoped.row
                                                        .evaluationAccount &&
                                                !combinedPermission
                                            "
                                            class="radio-group"
                                        >
                                            <el-radio
                                                v-for="item in CONSTANTS.REVIEWER_REASONABLENESS"
                                                :label="item.value"
                                                :key="item.value"
                                            >
                                                {{ item.label }}</el-radio
                                            >
                                        </el-radio-group>
                                        <el-radio-group
                                            v-model="scoped.row.evaluationScore"
                                            v-else-if="
                                                scoped.row.evaluationItems ===
                                                '预审质量'
                                            "
                                            class="radio-group"
                                            :disabled="
                                                (user !==
                                                    scoped.row
                                                        .evaluationAccount &&
                                                    !combinedPermission) ||
                                                isOnline
                                            "
                                        >
                                            <el-radio
                                                v-for="item in CONSTANTS.PREVIEW_QUALITY"
                                                :label="item.value"
                                                :key="item.value"
                                            >
                                                {{ item.label }}</el-radio
                                            >
                                        </el-radio-group>
                                        <el-radio-group
                                            v-model="scoped.row.evaluationScore"
                                            v-else-if="
                                                scoped.row.evaluationItems ===
                                                '评审质量'
                                            "
                                            class="radio-group"
                                            :disabled="
                                                user !==
                                                    scoped.row
                                                        .evaluationAccount &&
                                                !combinedPermission
                                            "
                                        >
                                            <el-radio
                                                v-for="item in CONSTANTS.REVIEW_QUALITY"
                                                :label="item.value"
                                                :key="item.value"
                                                >{{ item.label }}</el-radio
                                            >
                                        </el-radio-group>
                                    </div>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="thingsToImprove"
                            width="240"
                        >
                            <template #header>
                                <RedStar class="required" />待改善事项
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.thingsToImprove`"
                                    :rules="required"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="500"
                                        v-model="scoped.row.thingsToImprove"
                                        :disabled="
                                            (!combinedPermission &&
                                                user !==
                                                    scoped.row
                                                        .evaluationAccount) ||
                                            (isOnline &&
                                                scoped.row.evaluationItems ===
                                                    '预审质量')
                                        "
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-form>
            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import HeaderInfo from './HeaderInfo.vue';
import {
    getUserAccount,
    getUuid,
    isOrganizerOrWriter
} from '../../commonFunction';
import RedStar from 'feature/components/redStar';

export default {
    name: 'MeetingQuality',
    components: { HeaderInfo, RedStar },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        meetingId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            CONSTANTS,
            headerInfo: {},
            form: {
                tableData: []
            },
            required: {
                required: true,
                // 注意这里必填提示是一个空格，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            },
            user: getUserAccount(this),
            // 会议数据管理员专有的编辑权限
            dataManagerPermission:
                this.$store.state.permission.btnDatas.includes(
                    'MeetingDataManagerEditButton'
                ),
            isFinish: true
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        isOnline() {
            return this.headerInfo?.meetingInfo?.organizeForm === '线上';
        },
        // 组织者或编写人是否有编辑权限（在恰当的时机）
        isOrganizerOrWriterHasPermission() {
            if (
                this.headerInfo.meetingInfo?.meetingStatus === '已取消' ||
                (this.headerInfo.meetingInfo?.meetingStatus === '结束' &&
                    this.headerInfo.meetingInfo?.minutesStatus === '无纪要')
            ) {
                return false;
            }
            const bool = isOrganizerOrWriter(
                this.headerInfo.meetingPartRelateList
            );
            return bool;
        },
        combinedPermission() {
            return (
                (this.isOrganizerOrWriterHasPermission && this.isFinish) ||
                this.dataManagerPermission
            );
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.update();
            }
        }
    },
    methods: {
        closeDialog() {
            this.dialogVisible = false;
        },
        save() {
            let data = [];
            if (this.combinedPermission) {
                data = this.form.tableData;
            } else {
                data = this.form.tableData.filter(
                    (i) => i.evaluationAccount === this.user
                );
            }

            let valid = true;
            let validData = this.$tools.cloneDeep(data);
            if (this.isOnline) {
                validData = data.filter(
                    (i) => i.evaluationItems !== '预审质量'
                );
            }
            validData.forEach((i) => {
                if (i.evaluationScore === null || !i.thingsToImprove) {
                    valid = false;
                }
            });
            if (!valid) {
                this.$message.warning('请填写评价结论与待改善事项');
                return;
            }
            this.editMeetingQualityInfo(data);
        },
        /**
         * 获取会议效果评价信息
         */
        async getMeetingQualityInfo() {
            const api = this.$service.feature.meetingQuality.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.form.tableData = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 编辑会议效果评价信息
         * @param {Array} data 已填写的数据
         */
        async editMeetingQualityInfo(data) {
            const api = this.$service.feature.meetingQuality.edit;
            try {
                const res = await api({
                    meetingId: this.meetingId,
                    meetingEvaluationList: data
                });
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取会议信息
         */
        async getMeetingInfo() {
            const api = this.$service.feature.meeting.getMeetingInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.headerInfo = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 根据会议信息生成表格数据，用于用户填写
         */
        getTableData() {
            const reviewerList = this.headerInfo.meetingPartRelateList.filter(
                (i) => {
                    if (this.form.tableData.length === 0) {
                        return i.userRole === '评审决策人';
                    }
                    // 目前有的评委清单
                    const evaList = this.form.tableData.map(
                        (j) => j.evaluationAccount
                    );
                    return (
                        !evaList.includes(i.userAccount) &&
                        i.userRole === '评审决策人'
                    );
                }
            );
            const evaluationItemsList = [
                '评委选择合理性',
                '预审质量',
                '评审质量'
            ];
            for (let i = 0; i < 3; i++) {
                const res = reviewerList.map((item) => {
                    let evaluationScore = null;
                    // 线上会议没有预审，所以默认赋值0
                    if (this.isOnline && i === 1) {
                        evaluationScore = 0;
                    }
                    return {
                        evaluationAccount: item.userAccount,
                        evaluationItems: evaluationItemsList[i],
                        evaluationName: item.userName,
                        evaluationScore,
                        id: getUuid(),
                        thingsToImprove: ''
                    };
                });
                this.form.tableData.push(...res);
                this.isFinish = this.getIsQualityFinish();
            }
        },
        async update() {
            try {
                // 使用 Promise.all 并行执行两个异步函数
                await Promise.all([
                    this.getMeetingQualityInfo(),
                    this.getMeetingInfo()
                ]);
                this.getTableData();
            } catch (error) {
                console.error(error);
            }
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只在“评价项”这一列进行合并, 其他保持现状
            if (columnIndex !== 0) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }
            // 如果当前行的“项”与上一行的“项”相同，则合并
            if (
                rowIndex > 0 &&
                row.evaluationItems ===
                    this.form.tableData[rowIndex - 1].evaluationItems
            ) {
                return {
                    // 隐藏当前行的单元格
                    rowspan: 0,
                    colspan: 0
                };
            }
            // 计算当前“项”需要合并的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.form.tableData.length; i++) {
                if (
                    row.evaluationItems ===
                    this.form.tableData[i].evaluationItems
                ) {
                    rowspan += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan,
                colspan: 1
            };
        },
        // 会议效果评价有没有全部完成(没全部完成可编辑)
        getIsQualityFinish() {
            const data = this.form.tableData;
            if (data.length === 0) {
                return true;
            }
            let res = false;
            data.forEach((i) => {
                if (!i.evaluationScore) {
                    res = true;
                }
            });
            return res;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.wrap {
    flex-wrap: wrap;
}
.meeting-material {
    margin-bottom: 12px;
}

.meeting-conclusion {
    background-color: white;
    width: 100%;
    color: black;
    padding: 15px 0;
}

.add-minutes {
    float: right;
    margin-top: 15px;
    margin-bottom: 5px;
}

.quality-form {
    margin-top: 15px;
    ::v-deep .el-table__row > td {
        padding: 5px;
    }
    ::v-deep .cell {
        padding: 0 !important;
    }
    .el-form-item {
        margin-bottom: 0px;
    }
    ::v-deep .el-form-item__error {
        top: calc(50% - 10px);
        left: 16px;
    }
    ::v-deep .el-form-item__content {
        margin: 0 !important;
    }

    // 令输入框无边框
    ::v-deep .el-textarea__inner {
        border: none;
        resize: none;
    }
    // 令鼠标移入之后不变色
    ::v-deep .el-table tbody tr:hover > td {
        background-color: #fff !important;
    }

    .quality-form-table {
        border: 1px solid #8c8c8c !important;
    }
}

.date-picker {
    width: 100%;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.required {
    margin-right: 2px;
}
::v-deep .meeting-conclusion .el-form-item__label {
    font-weight: bold;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
    padding-right: 10px;
    width: fit-content;
}
.review-form {
    margin-left: 40px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
