<!-- eslint-disable vue/no-v-html -->
<template>
    <div>
        <div v-show="show" class="message" @click="gotoMessage">
            <el-badge :value="unreadCount > 0 ? unreadCount : ''">
                <i class="el-icon-bell bell" />
            </el-badge>
        </div>
        <el-drawer :modal="false" :visible.sync="visibleType" custom-class="type-list-box" :title="`${$t(
        'systemManagement.messageNotice.drawer.title'
      )}`" :append-to-body="true" :show-close="false" @open="getMessageType">
            <div class="header">
                <div class="header_title" />
                <div class="header__btns">
                    <el-link :underline="false" :disabled="unreadCount === 0" @click="unreadCount >0 && setMsgTypeAllRead()"><i class="el-icon-s-open fa-rotate-180" />{{ $t('systemManagement.messageNotice.drawer.allRead') }}</el-link>
                </div>
            </div>
            <div>
                <div v-for="item in messageTypeList" :key="item.messageType" class="msg-group-card">
                    <div v-if="item.hasMessageType" class="msg-group-box" :class="{'is-active':(curMsgTypeItem.messageType===item.messageType)}" @click="loadMessageList(item)">
                        <img class="msg-group-card__img" :src="item.image.img">
                        <div class="msg-group-card__body">
                            <div class="block">
                                <div class="block__text">{{ $t('systemManagement.dictionaryManagement.value.'+item.messageTypeName) }}</div>
                                <div class="block__btns">
                                    {{ item.lastMessageSendTime }}
                                </div>
                            </div>
                            <div class="block">
                                <div class="block__text">{{ item.messageTitle }}</div>
                                <div class="block__btns">
                                    <div v-show="item.messageNum>0" class="badge-num">{{ item.messageNum }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <el-drawer ref="msgList" :modal="false" :visible.sync="visibleMessage" custom-class="msg-list-box" :show-close="false" :append-to-body="true" @close="visibleType = false">
                <template #title>
                    <div class="msg-filter">
                        <div class="header">
                            <div class="header__title">{{ $t('systemManagement.messageNotice.drawer.timeFilter') }}</div>
                            <div class="header__btns">
                                <el-link :underline="false" :disabled="unreadCount === 0" @click="unreadCount >0 && setMsgAllRead()"><i class="el-icon-s-open fa-rotate-180" />{{ $t('systemManagement.messageNotice.drawer.allRead') }}</el-link>
                            </div>
                        </div>
                        <div>
                            <el-radio-group v-model="msgFilterSendTimeName" size="small" @change="loadMessageListFilter">
                                <el-radio-button :label="$t('systemManagement.messageNotice.drawer.all')" />
                                <el-radio-button v-for="item in msgFilterSendTimeList" :key="item.valueCode" :label="item.valueName">
                                    {{ $t('systemManagement.dictionaryManagement.value.'+item.valueName) }}
                                </el-radio-button>
                            </el-radio-group>
                        </div>
                        <div class="header">
                            <div class="header__title">{{ $t('systemManagement.messageNotice.drawer.statusFilter') }}</div>
                        </div>
                        <div>
                            <el-radio-group v-model="msgFilterStatusName" size="small" @change="loadMessageListFilter">
                                <el-radio-button :label="$t('systemManagement.messageNotice.drawer.all')" />
                                <el-radio-button v-for="item in msgFilterStatusList" :key="item.valueCode" :label="item.valueName">
                                    {{ $t('systemManagement.dictionaryManagement.value.'+item.valueName) }}
                                </el-radio-button>
                            </el-radio-group>
                        </div>
                    </div>
                </template>
                <template>
                    <div v-infinite-scroll="scrolLoadMessageList" :infinite-scroll-disabled="messageListNoMore">
                        <div v-for="msg in messageList" :key="msg.msgId" class="msg-card" @click="gotoMessageDetail(msg)">
                            <div class="msg-card__header" :class="{'msg-badge':msg.readMsg===0}">{{ msg.messageTitle }}</div>
                            <div class="msg-card__body" v-html="msg.messageContent" />
                            <div class="msg-card__footer">
                                <div class="block">
                                    <span class="block__text">{{ $t('systemManagement.dictionaryManagement.value.'+curMsgTypeItem.messageTypeName) }}</span>
                                    <span class="block__btns">{{ msg.sendTime }}</span>
                                </div>
                            </div>
                        </div>
                        <el-divider v-if="messageListNoMore">{{ $t('systemManagement.messageNotice.drawer.noMoreData') }}</el-divider>
                    </div>
                </template>
            </el-drawer>
        </el-drawer>

    </div>
</template>
<script>
import service from '../assets/notice/service.png';
import operate from '../assets/notice/operate.png';
import product from '../assets/notice/product.png';
import fault from '../assets/notice/fault.png';
import system from '../assets/notice/system.png';
import getUnreadCount from '../mixins/getUnreadCount';
// localStorage加密存储
import storage from 'wtf-core-vue/src/methods/storage';

// 消息类型数据
const MESSAGE_TYPE = [
    {
        messageType: '2',
        messageTypeName: '服务消息',
        image: { img: service, icon: 'el-icon-service', backColor: '#658EF8' }
    },
    {
        messageType: '4',
        messageTypeName: '运营消息',
        image: { img: operate, icon: 'el-icon-data-line', backColor: '#4FCB73' }
    },
    {
        messageType: '3',
        messageTypeName: '产品消息',
        image: { img: product, icon: 'el-icon-s-goods', backColor: '#2DC8E6' }
    },
    {
        messageType: '5',
        messageTypeName: '故障消息',
        image: {
            img: fault,
            icon: 'el-icon-message-solid',
            backColor: '#FF7475'
        }
    },
    {
        messageType: '1',
        messageTypeName: '系统消息',
        image: { img: system, icon: 'el-icon-s-platform', backColor: '#FEAA24' }
    },
    {
        messageType: '6',
        messageTypeName: '公告消息',
        image: { img: operate, icon: 'el-icon-s-ticket', backColor: '#4FCB73' }
    }
];

export default {
    name: 'MessageManagement',
    mixins: [getUnreadCount],
    data() {
        const _this = this;
        return {
            tenantId: '',
            // 是否展示铃铛
            show: false,
            // 消息类型的抽屉层显示控制
            visibleType: false,
            // 消息列表的抽屉层显示控制
            visibleMessage: false,
            // 消息类型列表
            messageTypeList: [],
            // 时间过滤条件
            msgFilterSendTimeList: [],
            // 状态过滤条件
            msgFilterStatusList: [],
            // 消息列表
            messageList: [],
            // 用于是否有更多的价值
            messageListNoMore: false,
            // 消息列表- 时间过滤条件选项
            msgFilterSendTimeName: '',
            // 消息列表- 时间过滤条件选项
            msgFilterStatusName: '',
            // 消息列表-过滤条件
            msgFilter: {
                // 消息类型编码
                messageType: '',
                // 消息时间筛选编码
                messageSendTime: '',
                // 消息读取状态编码
                messageHasRead: '',
                // 每次查询记录树，用于滚动加载
                pageSize: 20,
                // 记录第几次加载
                pageNum: 1
            },
            // 当前选择的消息类型
            curMsgTypeItem: {
                messageTypeName: ''
            },
            // 避免重复连接
            lockReconnect: false,
            ws: '',
            tt: null,
            // token过期的标志
            overdue: false,
            heartCheck: {
                timeout: 180000,
                timeoutObj: null,
                serverTimeoutObj: null,
                start() {
                    const self = this;
                    this.timeoutObj && clearTimeout(this.timeoutObj);
                    this.timeoutObj = setTimeout(() => {
                        // 处于连接状态的时候发送心跳，后端收到后，返回一个消息，
                        if (_this.ws.readyState === 1) {
                            _this.ws.send('heart');
                        }
                    }, self.timeout);
                }
            },
            token: '',
            hasPermission: false
        };
    },
    watch: {
        // 监控时间范围查询，消息值
        'msgFilterSendTimeName': function (val) {
            const tempItem = this.msgFilterSendTimeList.find(
                (v) => v.valueName === val
            );
            this.msgFilter.messageSendTime = (tempItem || {}).valueCode || '';
        },
        // 监控消息状态值
        'msgFilterStatusName': function (val) {
            const tempItem = this.msgFilterStatusList.find(
                (v) => v.valueName === val
            );
            this.msgFilter.messageHasRead = (tempItem || {}).valueCode || '';
        },
        // 解决切换语言，重新选中问题
        '$i18n.locale': function () {
            this.msgFilterSendTimeName = this.$t(
                'systemManagement.messageNotice.drawer.all'
            );
            this.msgFilterStatusName = this.$t(
                'systemManagement.messageNotice.drawer.all'
            );
        }
    },
    mounted() {
        // 获取所有路由判断有没有消息通知权限
        const menuList = this.$store.getters.permission_routes;
        for (let i = 0; i < menuList.length; i++) {
            if (menuList[i].children && menuList[i].children.length > 0) {
                for (let j = 0; j < menuList[i].children.length; j++) {
                    // eslint-disable-next-line max-depth
                    if (menuList[i].children[j].path === 'messageNotice') {
                        this.show = true;
                        this.getUnreadCount();
                        this.hasPermission = true;
                        this.getDictMessageType();
                        this.getDictMessageSendTime();
                        this.getDictMessageStatus();
                    }
                }
            }
        }
        if (this.hasPermission) {
            const _this = this;
            if ('WebSocket' in window) {
                // 先调一下下面的接口，当状态是401或403时不去重连
                _this.$service.systemManagement
                    .getUnReadCount({})
                    .then((res) => {
                        // 创建websocket
                        this.createWebSocket();
                    })
                    .catch((error) => {
                        if (
                            error.request.status === 401 ||
                            error.request.status === 403
                        ) {
                            _this.overdue = true;
                        } else {
                            // 创建websocket
                            this.createWebSocket();
                        }
                    });
            } else {
                _this.$message(
                    '您的浏览器不支持websocket 请更换浏览器后重试！'
                );
            }
        }
    },
    // 组件销毁时，关闭连接
    beforeDestroy() {
        if (this.ws) {
            //  主动断开连接，清掉定时器
            this.overdue = true;
            this.ws.close();
            clearTimeout(this.heartCheck.timeoutObj);
        }
    },
    methods: {
        /** *websocket 开始 */
        createWebSocket() {
            const wsUrl = `${
                this.$service.systemManagement.getWebsocket() +
                this.$tools.getToken()
            }&userId=${storage.getLocalStorage('userId')}`;
            const _this = this;
            if (!this.overdue) {
                // 先调一下下面的接口，当状态是401或403时不去重连
                _this.$service.systemManagement
                    .getUnReadCount({})
                    .then((res) => {
                        try {
                            this.ws = new WebSocket(wsUrl);
                            this.websocketInit();
                        } catch (e) {
                            this.websocketReconnect();
                        }
                    })
                    .catch((error) => {
                        if (
                            error.request.status === 401 ||
                            error.request.status === 403
                        ) {
                            _this.overdue = true;
                        } else {
                            try {
                                this.ws = new WebSocket(wsUrl);
                                this.websocketInit();
                            } catch (e) {
                                this.websocketReconnect();
                            }
                        }
                    });
            }
        },
        websocketInit() {
            // 建立 web socket 连接成功触发事件
            const _this = this;
            this.ws.onopen = function (evt) {
                _this.onOpen(evt);
            };
            // 断开 web socket 连接成功触发事件
            this.ws.onclose = function (evt) {
                _this.websocketReconnect();
                _this.onClose(evt);
            };
            // 接收服务端数据时触发事件
            this.ws.onmessage = function (evt) {
                _this.onMessage(evt);
            };
            // 通信发生错误时触发
            this.ws.onerror = function (evt) {
                _this.websocketReconnect();
                _this.onError(evt);
            };
        },
        onOpen(evt) {
            // 心跳检测重置
            this.heartCheck.start();
        },
        onClose(evt) {
            // 关闭连接
        },
        onError(evt) {
            // 连接报错
        },

        websocketReconnect() {
            //  如果退出登录了或者token过期了或者已经重连了，则直接返回
            if (!this.$tools.getToken() || this.overdue || this.lockReconnect) {
                // 是否已经执行重连
                return;
            }
            this.lockReconnect = true;
            // 没连接上会一直重连，设置延迟避免请求过多
            this.tt && clearTimeout(this.tt);
            const _this = this;
            this.tt = setTimeout(() => {
                _this.createWebSocket();
                _this.lockReconnect = false;
            }, 3000);
        },
        onMessage(evt) {
            //  如果接收到的消息不是live，则替换铃铛消息数
            if (evt.data) {
                if (evt.data !== 'live') {
                    this.$store.commit(
                        'systemManagement/updateUnreadCount',
                        evt.data
                    );
                } else {
                    //  如果token不存在或已过期，说明退出了，则断开连接
                    if (!this.$tools.getToken() || this.overdue) {
                        //  断开连接，关闭定时器
                        this.ws.close();
                        clearTimeout(this.heartCheck.timeoutObj);
                        return;
                    }
                    //  否则重新执行心跳检测
                    this.heartCheck.start();
                }
            }
        },
        /** ***websocket结束***** */
        /**
         * 获取字典-消息类型
         */
        getDictMessageType() {
            this.$service.systemManagement.getMessageDictType().then((res) => {
                if (res.head.code === '000000') {
                    this.messageTypeList = (res.body || []).map((item) => {
                        const tempItem = MESSAGE_TYPE.find(
                            (v) => v.messageType === item.valueCode
                        );
                        return Object.assign({}, tempItem, {
                            messageType: item.valueCode,
                            messageTypeName: item.valueName
                        });
                    });
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        /**
         * 获取字典-时间类型
         */
        getDictMessageSendTime() {
            this.$service.systemManagement
                .getMessageDictSendTime()
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.msgFilterSendTimeList = res.body || [];
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        /**
         * 获取字典-消息状态类型
         */
        getDictMessageStatus() {
            this.$service.systemManagement
                .getMessageDictStatus()
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.msgFilterStatusList = res.body || [];
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 获取今天的日期
        getToday() {
            const now = new Date();
            const year = now.getFullYear();
            let month = now.getMonth() + 1;
            let day = now.getDate();
            if (month < 10) {
                month = `0${month}`;
            }
            if (day < 10) {
                day = `0${day}`;
            }
            return `${year}-${month}-${day}`;
        },
        /**
         * 获取消息类型数量
         */
        getMessageType() {
            this.messageTypeList = [];
            this.$service.systemManagement.getMessageGrouping().then((res) => {
                if (res.head.code === '000000') {
                    const tempList = res.body;
                    if (tempList && tempList.length > 0) {
                        tempList.forEach((msgTypeItem) => {
                            const temp = MESSAGE_TYPE.find(
                                (v) => v.messageType === msgTypeItem.messageType
                            );
                            msgTypeItem.lastMessageSendTime = (
                                msgTypeItem.lastMessageSendTime || ''
                            ).replace('T', ' ');
                            const timeArr =
                                msgTypeItem.lastMessageSendTime.split(' ');
                            if (this.getToday() === timeArr[0]) {
                                msgTypeItem.lastMessageSendTime = timeArr[1];
                            } else {
                                msgTypeItem.lastMessageSendTime = timeArr[0];
                            }
                            this.messageTypeList.push(
                                Object.assign({}, temp, msgTypeItem)
                            );
                        });
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        /**
         * 根据消息类型，获取消息列表
         */
        getMessageList() {
            this.$service.systemManagement
                .getMessageGroupingList(this.msgFilter)
                .then((res) => {
                    if (res.head.code === '000000') {
                        // 处理res.body=null 情况
                        const list = (res.body || {}).list || [];
                        if (list.length > 0) {
                            // 处理时间
                            list.forEach((item) => {
                                // 对时间进行处理，今天的消息展示时分秒，其他的展示年月日
                                item.sendTime = (item.sendTime || '').replace(
                                    'T',
                                    ' '
                                );
                                const timeArr = item.sendTime.split(' ');
                                if (this.getToday() === timeArr[0]) {
                                    item.sendTime = timeArr[1];
                                } else {
                                    item.sendTime = timeArr[0];
                                }
                            });
                        }
                        this.messageList = this.messageList.concat(list);
                        /**
                         * 三种情况，判断没有更多数据
                         * 1.body返回为null
                         * 2.body返回的空数组
                         * 3.body返回的数据长度，比要查询的记录数少（一般最后一页存在这情况）
                         * 4.总记录数，等于当前页数*每页记录数(最好一页刚好等于pageSize情况)
                         */
                        this.messageListNoMore =
                            !res.body ||
                            list.length === 0 ||
                            list.length < this.msgFilter.pageSize ||
                            res.body.total ===
                                this.msgFilter.pageSize *
                                    this.msgFilter.pageNum;
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        /**
         * 弹出抽屉
         */
        gotoMessage() {
            this.curMsgTypeItem = this.$options.data().curMsgTypeItem;
            this.visibleType = true;
        },
        // 根据消息类型，加载消息列表
        loadMessageList(msgTypeItem, forceLoad) {
            if (!msgTypeItem) {
                return;
            }
            this.$refs.msgList.$el.style.marginRight = '398px';
            //  如果点击的不是同一个，重置条件并刷新列表
            if (
                forceLoad === true ||
                this.curMsgTypeItem.messageTypeName !==
                    msgTypeItem.messageTypeName
            ) {
                this.curMsgTypeItem = msgTypeItem;
                this.messageListNoMore = true;
                // 重置默认参数
                this.messageList = [];
                Object.assign(this.msgFilter, this.$options.data().msgFilter);
                this.msgFilterSendTimeName = this.$t(
                    'systemManagement.messageNotice.drawer.all'
                );
                this.msgFilterStatusName = this.$t(
                    'systemManagement.messageNotice.drawer.all'
                );
                this.msgFilter.messageType = msgTypeItem.messageType;
                if (this.msgFilter.messageType === undefined) {
                    this.msgFilter.messageType = '';
                }
                this.getMessageList();
            }
            this.visibleMessage = true;
        },
        /**
         * 消息列表过滤，查询
         */
        loadMessageListFilter() {
            // 重置默认参数
            this.messageList = [];
            this.msgFilter.pageNum = this.$options.data().msgFilter.pageNum;
            this.msgFilter.pageSize = this.$options.data().msgFilter.pageSize;

            this.getMessageList();
        },
        /**
         * 滚动加载消息列表
         */
        scrolLoadMessageList() {
            // eslint-disable-next-line no-plusplus
            this.msgFilter.pageNum++;
            this.getMessageList();
        },
        /**
         * 设置消息类型全部已读
         */
        setMsgTypeAllRead() {
            this.$confirm(
                this.$t('systemManagement.messageNotice.drawer.tipMsg1'),
                this.$t('systemManagement.messageNotice.drawer.tipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.$service.systemManagement
                    .postMessageSetAllRead()
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.getUnreadCount();
                            this.getMessageType();
                            if (this.visibleMessage === true) {
                                this.loadMessageList(this.curMsgTypeItem, true);
                            }
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        },
        /**
         * 设置消息全部已读
         */
        setMsgAllRead() {
            this.$confirm(
                this.$t('systemManagement.messageNotice.drawer.tipMsg1'),
                this.$t('systemManagement.messageNotice.drawer.tipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                const data = {
                    messageType: this.msgFilter.messageType
                };
                this.$service.systemManagement
                    .postMessageSetAllRead(data)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.getUnreadCount();
                            this.getMessageType();
                            this.loadMessageList(this.curMsgTypeItem, true);
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        },
        // 消息点击事件
        gotoMessageDetail(msgItem) {
            //  关闭抽屉
            this.visibleMessage = false;
            //  跳转到详情页
            this.$router.push({
                path: '/systemManagement/messageNotice',
                query: {
                    readMsg: msgItem.readMsg,
                    msgId: msgItem.msgId,
                    // 解决从首页进去，切换左侧导航，再仓首页点击当前消息，不"显示详情页问题
                    time: new Date().getTime()
                }
            });
        }
    }
};
</script>
<style lang="scss">
.el-message-box__wrapper {
    .el-message-box {
        .el-message-box__header {
            .el-message-box__title {
                font-size: 14px;
                font-weight: 700;
                color: #000000;
            }
        }
    }
}
</style>
<style scoped lang="scss">
.message {
    position: relative;
    .bell {
        color: #fff;
    }
}

.badge-num {
    min-width: 20px;
    height: 20px;
    background: red;
    border-radius: 20px;
    display: inline-block;
    color: #fff;
    text-align: center;
    font-weight: bold;
    padding: 0px 5px;
}
.el-drawer__wrapper {
    margin-top: 105px;
    height: calc(100vh - 105px);
    ::v-deep .el-drawer__title {
        margin-bottom: 0px;
    }

    ::v-deep .el-drawer__body {
        padding: 20px;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    ::v-deep .el-drawer__header {
        background: #3271fe;
        height: 60px;
        font-size: 1.6rem;
        color: #ffffff;
        font-weight: bold;
        text-align: center;
        padding: 10px 10px;
        margin-bottom: 0px;
    }
    // 消息类型的样式列表
    ::v-deep .type-list-box {
        width: 398px !important;
        background: #fcfcfc;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        .msg-group-card {
            .msg-group-box {
                padding: 20px;
                border-radius: 10px;
                width: 100%;
                height: 90px;
                border: 1px solid transparent;
                display: flex;
                flex-flow: row nowrap;
                justify-content: space-between;
                margin-bottom: 20px;
                background: #fff;
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                cursor: pointer;
                &.is-active {
                    border: 1px solid #0086ff;
                }

                .msg-group-card__img {
                    width: 50px;
                    height: 50px;
                    border-radius: 50px;
                    font-size: 30px;
                    color: #fff;
                    margin-right: 1.2rem;
                }
                .msg-group-card__body {
                    flex-grow: 1;
                    font-size: 1.4rem;
                    padding: 5px;

                    .block {
                        height: 20px;
                        line-height: 20px;
                        color: #000000;
                        &:first-child {
                            font-weight: bold;
                            .block__btns {
                                font-weight: normal;
                                color: #979797;
                            }
                        }
                        &:last-child {
                            margin-top: 6px;
                        }

                        .block__text {
                            width: 0px;
                        }

                        & > * {
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }

    ::v-deep .msg-list-box {
        width: 398px !important;
        background: #f6f6f6;
        .el-drawer__header {
            height: auto;
            background: #fff;
            text-align: left;
            color: #000;
            font-weight: normal;
            padding: 20px;

            .header__title {
                font-weight: normal;
                line-height: normal;
                height: 20px;
            }
            .header__btns {
                line-height: normal;
                height: 20px;
            }

            .el-radio-button__inner {
                border: 1px solid #e6ebf5;
                border-radius: 5px;
                margin: 0px 10px;
                &:first-of-type {
                    margin-left: 0px;
                }
            }
            .el-radio-button__orig-radio:checked + .el-radio-button__inner {
                border: 1px solid #3370ff;
            }
            .el-radio-button:focus:not(.is-focus):not(:active):not(
                    .is-disabled
                ) {
                box-shadow: none;
            }
        }

        .el-divider__text {
            background: #f6f6f6;
            color: #979797;
        }

        .msg-card {
            background: #ffffff;
            border-radius: 10px;
            border: 1px solid transparent;
            padding: 20px;
            font-size: 1.4rem;
            margin-bottom: 20px;
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-between;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            cursor: pointer;

            .msg-card__header {
                font-weight: bold;
            }

            .msg-badge {
                &::before {
                    width: 8px;
                    height: 8px;
                    content: ' ';
                    background: red;
                    border-radius: 8px;
                    display: inline-block;
                    margin: 0px 5px 0px -13px;
                }
            }

            .msg-card__body {
                flex-grow: 1;
                color: #979797;
                margin: 14px 0 15px;
                width: 100%;
                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
                a {
                    color: #3370ff;
                }
            }
            .msg-card__footer {
                border-top: 1px solid #e6ebf5;
                .block {
                    color: #979797;
                }
            }
        }
    }
}
</style>
