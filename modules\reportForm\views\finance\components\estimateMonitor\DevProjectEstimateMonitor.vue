<template>
    <div class="annual-spend-summary" ref="overallChartBox">
        <div class="flex info-container">
            <el-date-picker
                v-model="year"
                @input="handleYearChange"
                value-format="yyyy"
                format="yyyy年启动项目"
                type="year"
                placeholder="选择年"
                :picker-options="{
                    disabledDate(time) {
                        return (
                            time.getTime() < new Date('2024-01-01').getTime()
                        );
                    }
                }"
                :clearable="false"
            >
            </el-date-picker>
            <div class="info">
                共启动项目
                <span class="numbers">{{ startupProjectTotal }}</span>
                个，已完成预算审批项目
                <span class="numbers">{{ finishBudgetProjectTotal }}</span
                >个，与概算的对齐情况如下。
            </div>
        </div>
        <div class="flex">
            <el-card class="chart-container">
                <div class="chart-box-title">预算超概算项目占比</div>
                <div ref="overEstimateChart" class="chart"></div>
            </el-card>
            <el-card class="chart-container right">
                <div class="chart-box-title">超概算项目分布占比</div>
                <div ref="overEstimateDistributionChart" class="chart"></div>
            </el-card>
        </div>
        <SnbcBaseTable
            class="table"
            ref="tableRef"
            :table-config="tableConfig"
            :showTableHeader="false"
        >
            <template v-slot:estimate="{ row }">
                ￥{{
                    parseFloat(row.estAmount).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
            <template v-slot:budget="{ row }">
                ￥{{
                    parseFloat(row.budgetAmount).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
        </SnbcBaseTable>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import { getTableConfig } from './tableConfig';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import {
    getOverEstimateChartoption,
    getOverEstimateDistributionChartoption
} from './estimateChartOptions.js';

export default {
    name: 'DevProjectEstimateMonitor',
    components: { SnbcBaseTable },
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            year: new Date().getFullYear().toString(),
            // 图表缓存
            charts: {},
            optionsMap: {},
            chartType: 'month',
            chartShowType: 'bar',
            // 启动项目
            startupProjectTotal: 0,
            // 完成预算审批项目
            finishBudgetProjectTotal: 0,
            // 是否展示费用科目
            showFeeSubject: false,
            // 列表各项配置
            tableConfig: getTableConfig(this)
        };
    },
    watch: {
        activeName(newVal) {
            if (newVal === 'devProjectEstimateMonitor') {
                this.handleYearChange();
            }
        }
    },
    mounted() {
        this.handleYearChange();
    },
    activated() {
        if (this.activeName === 'devProjectEstimateMonitor') {
            this.handleYearChange();
        }
    },
    methods: {
        /**
         * 获取顶部信息
         */
        async getTopInfo() {
            try {
                const api = this.$service.reportForm.finance.getTopInfo;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.startupProjectTotal = res.body.startupProjectTotal;
                this.finishBudgetProjectTotal =
                    res.body.finishBudgetProjectTotal;
                this.optionsMap.overEstimate = getOverEstimateChartoption(
                    res.body
                );
                this.initChart('overEstimate');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const { overallChartBox } = this.$refs;
            const observer = new ResizeObserver(
                debounce((entries) => {
                    this.activeName === 'devProjectEstimateMonitor' &&
                        myChart.resize();
                }, 100)
            );
            observer.observe(overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 表格查询
         */
        handleTableQuery() {
            this.tableConfig.queryParams = {
                year: this.year
            };
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 年变更的选项
         */
        handleYearChange() {
            this.getTopInfo();
            this.setChartBySubjectOptions();
            this.handleTableQuery();
        },
        /**
         * 超概算项目分布占比
         */
        async setChartBySubjectOptions() {
            try {
                const api =
                    this.$service.reportForm.finance.getOverEstimateCostChart;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.overEstimateDistribution =
                    getOverEstimateDistributionChartoption(
                        res.body,
                        this.$store.state.project.productLine
                    );
                this.initChart('overEstimateDistribution');
            } catch (err) {
                console.error('Error:', err);
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
.annual-spend-summary {
    .info-container {
        width: 100%;
        .info {
            line-height: 30px;
            margin-left: 20px;
            font-weight: 600;
            .numbers {
                color: #0054ca;
                font-size: 18px;
                margin-right: 2px;
            }
        }
    }
    .chart-container {
        margin-top: 10px;
        width: 49%;
        padding: 10px;
        height: 400px;
        .chart-box-title {
            width: 150px;
            height: 30px;
            background-color: #3370ff;
            color: #fff;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            padding: 8px;
            border-radius: 10px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-top: 20px;
        }
    }
    .select-icon {
        height: 15px;
        width: 15px;
    }

    .selector-group {
        // 修改placeholder颜色
        ::v-deep .el-input__inner::placeholder {
            color: rgba(0, 0, 0, 0.685) !important;
        }
        .projectQuery {
            margin-top: 10px;
            .button {
                margin-left: 5px;
                height: 28px;
            }
        }
        .selector {
            margin: 10px 15px 10px 0;
        }
        .table-type {
            width: 250px;
        }
    }
    .table {
        margin-top: 15px;
    }
    .right {
        margin-left: auto;
    }
}
</style>
