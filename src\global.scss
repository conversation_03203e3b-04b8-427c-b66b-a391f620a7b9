// @import "../node_modules/wtf-core-vue/src/styles/index.scss";
@import '../node_modules/wtf-core-vue/src/styles/element-variables.scss';
//@import "../node_modules/wtf-core-vue/src/styles/variables.scss";

$theme-color: $--color-primary;
html,
body {
    overflow: hidden;
}

// 修改左侧导航栏样式
.el-submenu__title {
    height: 40px !important;
    line-height: 40px !important;
    padding-left: 5px !important;
    font-size: 12px !important;
}
.el-menu .el-menu-item:not(.el-menu-item.submenu-title-noDropdown) {
    height: 40px !important;
    line-height: 40px !important;
    padding-left: 15px !important;
    font-size: 12px !important;
}
.el-menu-item.submenu-title-noDropdown {
    height: 40px !important;
    line-height: 40px !important;
    padding-left: 5px !important;
    font-size: 12px !important;
}

.el-dialog .el-dialog__body .el-form .el-form-item__label {
    font-weight: bold;
}

.el-table .el-table__row {
    height: auto !important;
}
.el-table th {
    background-color: #3370ff !important;
    border-bottom: 1px solid #8c8c8c !important;
    border-right: 1px solid #8c8c8c !important;
}
.el-table th > .cell {
    color: #fff !important;
}
.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
