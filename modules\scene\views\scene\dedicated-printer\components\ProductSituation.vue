<template>
    <!-- 产品情况 -->
    <div class="flex">
        <div ref="coreChartRef" class="chart-box core" />
    </div>
</template>
<script>
import * as echarts from 'echarts';
import ProjectList from './ProjectList.vue';
import Vue from 'vue';

export default {
    name: 'ProductSituation',
    components: { ProjectList },
    data() {
        return {
            // 图标示例
            sceneChart: null,
            // 图表数据
            echartsData: {
                sceneType: [],
                xAxisLabel: [],
                noStrategyList: [],
                strategyList: [],
                hrCost: [],
                strategyHrCost: [],
                noStrategyHrCost: [],
                strategyIsShow: true,
                noStrategyIsShow: true
            },
            sceneChartData: [],
            coreChartData: []
        };
    },
    computed: {},
    mounted() {
        this.coreChart = echarts.init(this.$refs.coreChartRef);
        this.selectLegend('coreChart');
    },
    beforeDestroy() {
        this.coreChart.dispose();
        this.coreChart = null;
    },
    methods: {
        // 清除图表
        clearChart(chart) {
            this[chart].clear();
        },
        /**
         * 对产品情况数据进行处理，用于展示图表
         * @param {Array} list 产品情况列表
         * @returns {Object} 图表数据
         */
        handleData(list, chart) {
            const echartsData = {
                scenexAxisLabelType: [],
                xAxisLabel: [],
                sceneType: [],
                noStrategyList: [],
                strategyList: [],
                hrCost: [],
                strategyHrCost: [],
                noStrategyHrCost: [],
                strategyIsShow: true,
                noStrategyIsShow: true
            };
            list.forEach((item, index) => {
                echartsData.sceneType.push(item.sceneType);
                echartsData.xAxisLabel = [
                    ...echartsData.xAxisLabel,
                    ...['发布', '在研', '规划']
                ];
                echartsData.noStrategyList = [
                    ...echartsData.noStrategyList,
                    ...[
                        item.publishStatusVos.noStrategyNum || '',
                        item.doingStatusVos.noStrategyNum || '',
                        item.planStatusVos.noStrategyNum || ''
                    ]
                ];
                echartsData.strategyList = [
                    ...echartsData.strategyList,
                    ...[
                        item.publishStatusVos.strategyNum || '',
                        item.doingStatusVos.strategyNum || '',
                        item.planStatusVos.strategyNum || ''
                    ]
                ];
                echartsData.hrCost.push({
                    value: `${item.doingStatusVos.hrCost}人月`,
                    xAxis:
                        chart === 'sceneChart'
                            ? 5 * (index + 1) - 2
                            : index + 1,
                    yAxis:
                        item.doingStatusVos.noStrategyNum +
                        item.doingStatusVos.strategyNum
                });
                echartsData.strategyHrCost.push({
                    value: `${item.doingStatusVos.hrCost}人月`,
                    xAxis:
                        chart === 'sceneChart'
                            ? 5 * (index + 1) - 2
                            : index + 1,
                    yAxis: item.doingStatusVos.strategyNum
                });
                echartsData.noStrategyHrCost.push({
                    value: `${item.doingStatusVos.hrCost}人月`,
                    xAxis:
                        chart === 'sceneChart'
                            ? 5 * (index + 1) - 2
                            : index + 1,
                    yAxis: item.doingStatusVos.noStrategyNum
                });
            });
            return echartsData;
        },
        drawChart(data, chart) {
            const result = this.handleData(data, chart);
            this[`${chart}data`] = this.$tools.cloneDeep(result);
            this.createChart(result, chart);
        },
        // eslint-disable-next-line max-lines-per-function
        /**
         * 处理option，利用隐藏x轴选项与控制数据达到柱状图分组的效果
         * @param {Object} result 结果
         */
        createChart(result, chart) {
            this.clearChart(chart);
            // 数据前面补''
            function addEmptyItems(data) {
                let result = [];
                let emptyArr = Array.from({ length: 2 }, () => '');
                for (let i = 0; i < data.length; i += 3) {
                    result = result.concat(emptyArr, data.slice(i, i + 3));
                }
                result = result.concat(emptyArr);
                return result;
            }
            const xAxisLabel = addEmptyItems(result.xAxisLabel);
            let yAxisLabel = [];
            for (let i = 0; i < result.sceneType.length; i += 1) {
                yAxisLabel = yAxisLabel.concat(
                    [''],
                    result.sceneType.slice(i, i + 1)
                );
            }
            yAxisLabel = yAxisLabel.concat(['']);
            // 空格占位，令两个图表下端对齐
            const coreYAxisLabel = [` `];
            const noStrategyList = addEmptyItems(result.noStrategyList);
            const strategyList = addEmptyItems(result.strategyList);
            // 用于获取项目列表
            const api = this.$service.scene.getProjectList;
            const option = {
                title: {
                    text: '专用打印机',
                    textStyle: {
                        color: '#022152',
                        fontSize: 28,
                        padding: 20
                    },
                    itemGap: 20,
                    subtext: '项目',
                    subtextStyle: {
                        color: '#959ead',
                        fontSize: 24,
                        padding: 20
                    }
                },
                legend: {
                    x: 'right',
                    data: ['战略', '非战略'],
                    itemWidth: 53,
                    itemHeight: 37,
                    textStyle: {
                        color: '#0a0a0a',
                        fontSize: 24
                    },
                    selected: {
                        战略: result.strategyIsShow,
                        非战略: result.noStrategyIsShow
                    }
                },
                grid: {
                    left: '1%',
                    right: '1%',
                    top: '15%',
                    bottom: '2%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#a1b8e6'
                            }
                        },
                        axisLabel: {
                            color: '#022152',
                            fontSize: 24
                        },
                        data:
                            chart === 'sceneChart'
                                ? xAxisLabel
                                : result.xAxisLabel
                    },
                    {
                        type: 'category',
                        position: 'bottom',
                        offset: 40,
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        },
                        axisLabel: {
                            color: '#022152',
                            fontSize: 24,
                            fontWeight: 'bold'
                        },
                        data:
                            chart === 'sceneChart' ? yAxisLabel : coreYAxisLabel
                    }
                ],
                yAxis: {
                    type: 'value',
                    show: true,
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#a1b8e6',
                            width: 2
                        }
                    },
                    axisLabel: {
                        color: '#022152',
                        fontSize: 24
                    },
                    // 内部分隔线
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed'
                        }
                    }
                },
                tooltip: {
                    confine: true,
                    trigger: 'item',
                    triggerOn: 'click',
                    // 让鼠标可以移入tooltip中，否则tooltip无法滚动
                    enterable: true,
                    show: true,
                    formatter: function (chartParams, ticket, callback) {
                        // 创建一个新的Vue实例，并挂载组件以拿到真实dom
                        const getRenderedHTML = (projectList) => {
                            return new Promise((resolve) => {
                                const vueInstance = new Vue({
                                    render: (h) =>
                                        h(ProjectList, {
                                            props: {
                                                projectList
                                            }
                                        })
                                });
                                vueInstance.$mount();
                                vueInstance.$nextTick(() => {
                                    resolve(vueInstance.$el);
                                });
                            });
                        };
                        const params = {
                            strategy: chartParams.seriesName,
                            projectStatus: chartParams.name,
                            businessUnit: '专用打印机'
                        };
                        api(params)
                            .then((res) => {
                                if (res.head.code === '000000') {
                                    if (res.body.length === 0) {
                                        const str =
                                            '<div style="height:300px;font-size:24px;display:grid;place-items:center">暂无数据<div>';
                                        callback(ticket, str);
                                        return;
                                    }
                                    const projectList = res.body.map((i) => {
                                        i.strategy = i.strategy[0];
                                        i.strategyColor =
                                            i.strategy === '战'
                                                ? '#00baad'
                                                : '#2a82e4';
                                        if (i.projectStatus === '在研') {
                                            i.projectStatusColor = '#3fcd7b';
                                        } else if (i.projectStatus === '发布') {
                                            i.projectStatusColor = '#2a82e4';
                                        } else {
                                            i.projectStatusColor = '#ff8d1a';
                                        }
                                        return i;
                                    });
                                    getRenderedHTML(projectList).then(
                                        (html) => {
                                            callback(ticket, html);
                                        }
                                    );
                                } else {
                                    this.$message.error(res.head.message);
                                }
                            })
                            .catch((err) => {
                                console.error('Error:', err);
                            });
                        return '<div style="width:600px;height:200px"></div>';
                    },
                    backgroundColor: '#fff',
                    extraCssText:
                        'width: 800px;box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);color: #333;'
                },
                series: [
                    {
                        name: '非战略',
                        type: 'bar',
                        stack: 'total',
                        barWidth: 66,
                        label: {
                            show: true,
                            position: 'insideTop',
                            color: '#000000',
                            fontSize: 20
                        },
                        itemStyle: {
                            // 设置柱状图的颜色
                            color: '#2a82e4'
                        },
                        emphasis: {
                            focus: 'item'
                        },
                        data:
                            chart === 'sceneChart'
                                ? noStrategyList
                                : result.noStrategyList,
                        markPoint: {
                            symbol: 'pin',
                            data: result.hrCost,
                            symbolSize: (value) =>
                                value.length > 4 ? value.length * 20 : 88,
                            label: {
                                fontSize: 16
                            },
                            itemStyle: {
                                color: '#2168e6'
                            },
                            silent: true
                        }
                    },
                    {
                        name: '战略',
                        type: 'bar',
                        barWidth: '66',
                        barCategoryGap: '40%',
                        stack: 'total',
                        label: {
                            show: true,
                            position: 'insideTop',
                            color: '#000000',
                            fontSize: 20
                        },
                        itemStyle: {
                            // 设置柱状图的颜色
                            color: '#43cf7c'
                        },
                        emphasis: {
                            focus: 'item'
                        },
                        data:
                            chart === 'sceneChart'
                                ? strategyList
                                : result.strategyList
                    }
                ]
            };
            this[chart].setOption(option);
        },
        selectLegend(chart) {
            this[chart].on('legendselectchanged', (obj) => {
                const strategySelected = obj.selected['战略'];
                const noStrategySelected = obj.selected['非战略'];
                const echartsData = this.$tools.cloneDeep(this[`${chart}data`]);
                echartsData.strategyIsShow = strategySelected;
                echartsData.noStrategyIsShow = noStrategySelected;
                if (strategySelected && !noStrategySelected) {
                    this.$set(
                        echartsData,
                        'hrCost',
                        echartsData.strategyHrCost
                    );
                }
                if (!strategySelected && noStrategySelected) {
                    this.$set(
                        echartsData,
                        'hrCost',
                        echartsData.noStrategyHrCost
                    );
                }
                if (!strategySelected && !noStrategySelected) {
                    echartsData.hrCost = echartsData.hrCost.map((item) => {
                        item.yAxis = 0;
                        return item;
                    });
                }
                this.createChart(echartsData, chart);
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.chart-box {
    height: 778px;
    padding: 16px 50px 18px 8px;
    background: #f7f9ff;
    border-radius: 4px;
    width: 100%;
    margin: auto;
}
.flex {
    display: flex;
}
</style>
