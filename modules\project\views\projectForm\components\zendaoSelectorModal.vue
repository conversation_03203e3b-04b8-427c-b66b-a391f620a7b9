<template>
    <div class="zandaoModal">
        <el-dialog
            title="禅道执行关联"
            :visible.sync="dialogVisible"
            width="60%"
            ref="dialog"
        >
            <form @submit.prevent="search" class="flex">
                <el-input
                    class="projectSearch"
                    v-model="projectName"
                    placeholder="请输入项目名称"
                    size="medium"
                >
                </el-input>
                <el-button
                    icon="el-icon-search"
                    @click="search"
                    type="primary"
                    style="height: 40px; width: 80px"
                ></el-button>
            </form>
            <p style="color: red; margin: 5 0px">
                根据项目开始时间倒序，最多显示100条，请根据项目名称进行过滤
            </p>
            <SnbcBaseTable
                class="projectTable"
                ref="tableRef"
                :table-config="tableConfig"
                :showTableHeader="false"
            >
            </SnbcBaseTable>
        </el-dialog>
    </div>
</template>
<script>
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';

export default {
    name: 'ZendaoSelectorModal',
    components: { SnbcBaseTable },
    data() {
        return {
            dialogVisible: false,
            // 筛选条件
            tableConfig: {
                // 查询api配置
                queryApi:
                    this.$service.project.projectInfo.getZentaoProjectList,
                // 列表各列配置
                elTableColumns: this.initColumn(),
                operations: [
                    {
                        name: '确认',
                        type: 'primary',
                        handleClick: this.confirm,
                        handleShow: () => true
                    }
                ],
                hasPage: false,
                pageParams: {},
                sortParams: {}
            },
            projectName: ''
        };
    },
    methods: {
        /**
         * 初始化列选项
         * @return {Array} 列各项配置
         */
        initColumn() {
            return [
                {
                    label: '项目名称',
                    prop: 'projectName',
                    show: true,
                    elTableColumnAttrs: {
                        'align': 'left',
                        'resizable': false,
                        'header-align': 'center'
                    },
                    width: 500
                }
            ];
        },
        /**
         * 查询
         */
        search() {
            this.tableConfig.queryParams = {
                proProjectName: this.projectName,
                productLineCode: this.productLineCode
            };
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 控制弹框打开时的操作
         */
        openZendaoSelectorModal() {
            this.productLineCode = '';
            this.dialogVisible = true;
            // 等待表格实例挂载完成
            this.$nextTick(() => {
                this.search();
            });
        },
        /**
         * 确认
         * @param {Object} raw 每行对应的项目
         */
        confirm(raw) {
            this.projectId = raw.projectId;
            this.$emit('getProjectInfo', {
                projectId: this.projectId,
                projectName: raw.projectName
            });
            this.dialogVisible = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.el-aside {
    padding: 0px;
    height: 100%;
    .el-card {
        padding: 10px;
    }
    border: 1px solid #d9d9d9;
}

.el-main {
    padding: 0 10px;
    height: 100%;
}
.projectTable {
    overflow: auto;
    height: 52vh !important;
    // 去除表格上部显示
    ::v-deep .header,
    .table-header {
        display: none;
    }
}

// 修改弹窗大小
.zandaoModal {
    ::v-deep .el-dialog {
        margin: 5% auto !important;
        width: 55% !important;
    }
}

// 搜索框按钮样式
::v-deep .el-input-group__append {
    background-color: #3370ff;
    color: white;
}
// 搜索框
.projectSearch {
    height: 30px;
    min-width: 200px;
}
.searchBtn {
    width: 80px;
    background-color: #3370ff;
}
.searchBtn:hover {
    background-color: #3370ff;
}

.searchBtn:focus {
    background-color: #3370ff;
}
</style>
