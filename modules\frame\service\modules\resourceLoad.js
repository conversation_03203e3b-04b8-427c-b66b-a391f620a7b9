/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import httpErrorHandle from '../../common/httpErrorHandle.js';
import httpInterceptors from '../../common/httpInterceptors.js';
import { basePathInit } from '../../../../src/envConst.js';

export default (Vue) => {
    const http = Vue.prototype.$http;
    // 进行请求拦截处理
    httpInterceptors(Vue, http);
    // 响应结果处理
    http.responseHandle(httpErrorHandle);
    // 根服务对象
    const basePath = basePathInit();

    const service = {
        presonalResourceLoad: {
            // 个人资源负载-日视图查询-图
            getChartDataByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_day_view_employee_charts',
                    method: 'post',
                    data
                });
            },
            // 个人资源负载-月视图查询-图
            getChartDataByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_month_view_employee_charts',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 个人资源负载-日视图-任务详情-图
            getTaskDetailChartDataByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_day_view_task_info_employee_charts',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 个人资源负载-月视图-任务详情-图
            getTaskDetailChartDataByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_month_view_task_info_employee_charts',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 个人资源负载-日视图查询-表格-任务类型列表
            getTaskListTableByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_day_view_employee_task_type',
                    method: 'post',
                    data
                });
            },
            // 个人资源负载-月视图查询-表格-任务类型列表
            getTaskListTableByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_month_view_employee_task_type',
                    method: 'post',
                    data
                });
            },
            // 个人资源负载-日视图查询-表格-任务类型下的项目列表
            getProjectListByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_day_view_employee_project',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 个人资源负载-月视图查询-表格-任务类型下的项目列表
            getProjectListByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_month_view_employee_project',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 个人资源负载-日视图查询-任务详情
            getTaskDetailByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_day_view_employee_task_info',
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 个人资源负载-月视图查询-任务详情
            getTaskDetailByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/employee_resource_load/resource_month_view_employee_task_info',
                    method: 'post',
                    loading: false,
                    data
                });
            }
        }
    };

    return service;
};
