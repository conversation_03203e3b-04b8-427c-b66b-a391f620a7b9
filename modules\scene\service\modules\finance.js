/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        finance: {
            // 获取产品情况图表(仅限金融场景与物流分拣的概况使用)
            getProductOverviewList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project_finance/get_product_overview_list',
                    method: 'post',
                    data
                });
            },
            // 获取剩余所有图表
            getProductOverviewWholeList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project_finance/get_product_overview_key_list',
                    method: 'post',
                    data
                });
            },
            // 获取产品情况列表(金融机具与物流分拣)
            getProductSetListInFineneceScene(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project_finance/get_product_set_list',
                    method: 'post',
                    data
                });
            },
            // 获取产品情况列表（关键基础零部件/专用打印机）
            getProductSetListInWholeScene(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project/get_product_set_list',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
