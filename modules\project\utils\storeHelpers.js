/**
 * 存储 state 到 localStorage 的工具函数
 */

// 需要持久化的 project store 字段列表
const PERSISTED_KEYS = [
    'projectStore',
    'projectInfo',
    'productLine'
    // 添加其他需要持久化的字段
];

// 存储前缀，避免与其他 localStorage 键冲突
const STORAGE_KEY_PREFIX = 'si-pmis-project-';

/**
 * 将指定的 state 数据保存到 localStorage
 * @param {Object} state Vuex state 对象
 */
export const saveStateToStorage = (state) => {
    PERSISTED_KEYS.forEach((key) => {
        try {
            const value = state[key];
            // 只有当值存在且不为空时才保存
            if (value !== undefined && value !== null) {
                const storageKey = `${STORAGE_KEY_PREFIX}${key}`;
                const valueToStore = JSON.stringify(value);
                localStorage.setItem(storageKey, valueToStore);
            }
        } catch (error) {
            console.error(
                `Error saving state key "${key}" to localStorage:`,
                error
            );
        }
    });
};

/**
 * 从 localStorage 加载状态到 store
 * @param {Object} store Vuex store 对象
 * @returns {Boolean} 是否成功返回store
 */
export const loadStateFromStorage = (store) => {
    let dataLoaded = false;

    const actionMap = {
        projectStore: 'project/changeProject',
        projectInfo: 'project/changeProjectInfo',
        productLine: 'project/changeProductLine'
    };

    PERSISTED_KEYS.forEach((key) => {
        try {
            const storageKey = `${STORAGE_KEY_PREFIX}${key}`;
            const value = localStorage.getItem(storageKey);

            if (value === null) return;

            const parsedValue = JSON.parse(value);
            // 确保值不为空数组或空对象
            const isEmpty =
                (Array.isArray(parsedValue) && parsedValue.length === 0) ||
                (typeof parsedValue === 'object' &&
                    !Array.isArray(parsedValue) &&
                    Object.keys(parsedValue).length === 0);

            // 如果值为空，不处理
            if (isEmpty) return;

            // 使用预定义的 action 映射
            const actionName = actionMap[key];
            if (!actionName) {
                console.warn(`No action mapping found for key: ${key}`);
                return;
            }

            // 使用 store 中定义的 action 来更新状态
            store.dispatch(actionName, parsedValue);
            dataLoaded = true;
        } catch (error) {
            console.error(
                `Error loading state key "${key}" from localStorage:`,
                error
            );
        }
    });
    // 返回是否成功加载了数据
    return dataLoaded;
};

/**
 * 清除所有持久化的状态
 */
export const clearPersistedState = () => {
    PERSISTED_KEYS.forEach((key) => {
        try {
            const storageKey = `${STORAGE_KEY_PREFIX}${key}`;
            localStorage.removeItem(storageKey);
        } catch (error) {
            console.error(
                `Error clearing state key "${key}" from localStorage:`,
                error
            );
        }
    });
};

/**
 * 确保在用户关闭页面前保存状态
 * @param {Object} store Vuex store 对象
 */
export const setupBeforeUnloadHandler = (store) => {
    window.addEventListener('beforeunload', () => {
        saveStateToStorage(store.state.project);
    });
};
