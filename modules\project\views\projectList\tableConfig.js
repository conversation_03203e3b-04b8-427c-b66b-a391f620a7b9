import { getStatusArray } from './tableHeader';

const getTableConfig = (scope) => {
    return {
        // 查询api配置
        queryApi: scope.$service.project.projectInfo.getProjectList,
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: getStatusArray('2', scope),
        // 头部按钮
        headerButtons: [
            {
                name: '新增',
                type: 'primary',
                handleClick: scope.handleAdd,
                handleShow: () => true,
                permissionCode: 'add_project'
            }
        ],
        // 操作列
        operationColumnWidth: 80,
        operations: [
            {
                name: '编辑',
                type: 'primary',
                handleClick: scope.handleEdit,
                elTableOprationColumnAttrs: {
                    resizable: false
                }
            },
            {
                name: '删除',
                type: 'danger',
                handleClick: scope.handleDelete,
                handleShow: scope.showDeleteButton,
                elTableOprationColumnAttrs: {
                    resizable: false
                }
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        // 固定表头
        elTableAttrs: {
            'height': 'calc(100vh - 160px)',
            'header-cell-style': '{text-align:center}'
        },
        hooks: {
            sortChangeHook: scope.sortChangeHook
        }
    };
};
export { getTableConfig };
