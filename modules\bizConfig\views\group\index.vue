<template>
    <div class="view">
        <el-container class="container-box">
            <el-aside class="aside-box">
                <org-tree
                    ref="orgTree"
                    @level-one-click="handleLevelOneClick"
                    @other-level-click="handleOtherLevelClick"
                    @pass-level-click="passLevelClick"
                    @pass-data="onPassData"
                    @delete-false="deleteRefsh"
                />
            </el-aside>
            <div class="main">
                <div v-if="showGroupInfo" class="main-show">
                    <el-form
                        ref="dataForm"
                        label-position="right"
                        label-width="120px"
                        :model="userEditDataInfo"
                        class="groupFrom"
                    >
                        <el-form-item
                            label="小组名称"
                            prop="teamName"
                            class="groupitem"
                        >
                            <el-input
                                v-model="userEditDataInfo.teamName"
                                placeholder="请输入小组名称"
                                autocomplete="text"
                                maxlength="20"
                                disabled
                            />
                        </el-form-item>
                        <el-form-item
                            label="组长姓名"
                            prop="teamLeaderId"
                            class="groupitem"
                        >
                            <el-select
                                v-model="userEditDataInfo.teamLeaderId"
                                placeholder="请选择组长姓名"
                                disabled
                            >
                                <el-option
                                    v-for="item in groupLeaderData"
                                    :key="item.loginName"
                                    :label="item.employeeName"
                                    :value="item.loginName"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="组成员"
                            prop="members"
                            class="groupitem"
                        >
                            <el-select
                                v-model="userEditDataInfo.members"
                                placeholder="请选择组成员"
                                filterable
                                multiple
                                ref="refElSelect"
                                class="custom-select"
                                disabled
                            >
                                <el-option
                                    v-for="item in groupMembersData"
                                    :key="item.loginName"
                                    :label="item.employeeName"
                                    :value="item.loginName"
                                    class="custom-option"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <div v-else class="main-text">
                    说明: 选择一级部门后可添加，删除，修改小组信息
                </div>
            </div>
        </el-container>
    </div>
</template>

<script>
import OrgTree from './components/OrgTree.vue';

export default {
    name: 'Organization',
    components: { OrgTree },
    data() {
        return {
            showGroupInfo: false,
            userEditDataInfo: {
                teamName: '',
                teamLeaderId: '',
                members: []
            },
            groupLeaderData: [],
            groupMembersData: []
        };
    },
    methods: {
        handleLevelOneClick() {
            this.showGroupInfo = false;
        },
        handleOtherLevelClick() {
            this.showGroupInfo = true;
        },
        passLevelClick(data) {
            this.departNameLevel(data).then(this.setData(data));
        },
        onPassData(data) {
            this.departNameLevel(data).then(this.setData(data));
        },
        departNameLevel(data) {
            const params = {
                orgCode: data.departmentCode,
                isAll: 0
            };
            return new Promise((resolve, reject) => {
                this.$service.department.group
                    .getDepartName(params)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.groupLeaderData = res.body || [];
                            this.groupMembersData = res.body || [];
                            resolve();
                        } else {
                            this.$message.error(res.head.message);
                            reject();
                        }
                    });
            });
        },
        deleteRefsh() {
            this.showGroupInfo = false;
        },
        setData(data) {
            this.userEditDataInfo.teamName = data.teamName;
            this.userEditDataInfo.teamLeaderId = data.teamLeaderId;
            this.userEditDataInfo.members = data.members;
        }
    }
};
</script>

<style lang="scss" scoped>
.main {
    width: 65%;
    height: 100%;
    margin-left: 10px;
    padding: 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.main-show,
.main-text {
    width: 100%;
    height: 100%;
    font-size: 24px;
    font-weight: bolder;
    display: flex;
    justify-content: center;
    align-items: center;
}
.main-text {
    color: #00c1de;
    font-family: fangsong;
}
.groupFrom {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.groupitem {
    width: 80%;
    margin-bottom: 40px;
}
::v-deep .el-select {
    height: auto !important;
}
::v-deep .el-input {
    height: auto !important;
}
.container-box {
    padding-top: 5px;
}
.aside-box {
    width: 35% !important;
}
</style>
