<template>
    <div
        :style="{
            height: isCurrent ? '170px' : '150px'
        }"
    >
        <el-empty
            description="暂无数据"
            :image-size="30"
            v-if="milestoneData.projectDetailDashboardDetailList.length === 0"
        ></el-empty>
        <div class="milestone-bar" ref="milestoneRef" v-else>
            <div class="milestone-base-version">
                <div class="flex">
                    <span>
                        {{ milestoneData.projectDetailVersion }}
                    </span>
                    <el-popover
                        ref="changeReasonRef"
                        placement="top-start"
                        width="850"
                        trigger="click"
                        popper-class="base-info-change-reason-popover"
                    >
                        <el-table
                            :data="changeReasonList"
                            style="border: 1px solid #8c8c8c !important"
                            :cell-style="{
                                border: '1px solid #8c8c8c!important'
                            }"
                            :header-cell-style="{ border: '1px solid #8c8c8c' }"
                            max-height="500"
                        >
                            <el-table-column
                                width="150"
                                property="delayOneLevelReason"
                                label="一级原因"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                width="150"
                                property="delayTwoLevelReason"
                                label="二级原因"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                property="detailsReason"
                                label="计划变更具体原因描述"
                                header-align="center"
                                align="left"
                            ></el-table-column>
                            <el-table-column
                                width="80"
                                property="delayDays"
                                label="影响工期"
                                align="center"
                            ></el-table-column>
                        </el-table>
                        <svg-icon
                            slot="reference"
                            @click="getChangeReasonList"
                            icon-class="milestone-flag"
                            :style="{
                                visibility: showChangeFlag
                                    ? 'initial'
                                    : 'hidden'
                            }"
                            class="change-flag"
                        ></svg-icon>
                    </el-popover>
                </div>
                <div v-if="isCurrent" style="position: absolute; bottom: 20px">
                    （当前）
                </div>
            </div>
            <div class="milestone-info">
                <div v-if="isLast">
                    <div style="margin-top: 8px">启动时间：</div>
                    <div>{{ milestoneData.startDate }}</div>
                </div>
                <div v-else>
                    <div style="margin-top: 8px">变更时间：</div>
                    <div>{{ milestoneData.effectiveDate }}</div>
                </div>
                <div>
                    <div class="milestone-info-title"><b>里程碑</b></div>
                    <div class="milestone-info-plan-time">计划完成时间</div>
                    <div v-if="isCurrent" class="milestone-info-actual-time">
                        实际完成时间
                    </div>
                </div>
            </div>
            <div
                v-for="(
                    data, index
                ) in milestoneData.projectDetailDashboardDetailList"
                :key="index"
                :style="{
                    'min-width': `${maxWidth[index]}px`
                }"
            >
                <MilestoneBar
                    :style="{
                        width: `${wholeWidth[index] || 180}px`
                    }"
                    v-show="data.projectDetailName"
                    :data="data"
                    :isCurrent="isCurrent"
                    :wholeWidth="wholeWidth[index]"
                    :progressWidth="progressWidth[index]"
                    :isDelay="
                        wholeWidth[index] !== progressWidth[index] && !isLast
                    "
                    :version="version"
                    :isAfterBaseline="isAfterBaseline"
                ></MilestoneBar>
                <div v-show="!data.projectDetailName" class="placeholder-arrow">
                    <div
                        v-for="(n, index) in 3"
                        class="arrow-container"
                        :key="index"
                    >
                        <div class="arrow"></div>
                    </div>
                </div>
            </div>
            <div v-if="!isLast" class="milestone-base-delay">
                延长<b class="milestone-base-delay-days">{{
                    milestoneData.versionDelayDays
                }}</b
                >天
            </div>
        </div>
    </div>
</template>
<script>
import MilestoneBar from './MilestoneBar.vue';
import moment from 'moment';

export default {
    name: 'Milestone',
    components: { MilestoneBar },
    props: {
        milestoneData: {
            type: Object,
            default: () => {}
        },
        // 是否是当前基线
        isCurrent: {
            type: Boolean,
            default: false
        },
        // 是否是最早的历史基线
        isLast: {
            type: Boolean,
            default: false
        },
        // 最大宽度，即最长的里程碑的宽度
        maxWidth: {
            type: Array,
            default: () => []
        },
        // 整体宽度
        wholeWidth: {
            type: Array,
            default: () => []
        },
        // 进度条宽度
        progressWidth: {
            type: Array,
            default: () => []
        },
        // 版本
        version: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            changeReasonList: []
        };
    },
    computed: {
        showChangeFlag() {
            return !this.isLast && this.isAfterBaseline;
        },
        isAfterBaseline() {
            // 基线时间，超过这个时间里程碑变更原因变成整体显示
            const BASELINE = '2025-05-19';
            return moment(this.milestoneData.effectiveDate) >= moment(BASELINE);
        }
    },
    methods: {
        async getChangeReasonList() {
            const params = {
                oaFlowId: this.milestoneData.oaFlowId
            };
            const api =
                this.$service.project.dashboard.getWholeMilestoneChangeReason;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.changeReasonList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.milestone-bar {
    display: flex;
    .milestone-base-version {
        font-weight: 600;
        font-size: 16px;
        min-width: 70px;
        max-width: 70px;
        text-align: center;
        display: grid;
        place-items: center;
        position: relative;
    }
    .milestone-base-delay {
        display: flex;
        margin-left: 10px;
        margin-top: 45px;
        min-width: 120px;
        max-width: 120px;
        height: 120px;
        font-size: 14px;
        .milestone-base-delay-days {
            line-height: 12px;
            color: #0054bb;
            font-size: 24px;
            margin: 0 3px;
        }
    }
    .milestone-info {
        min-width: 140px;
        max-width: 140px;
        height: 120px;
        padding-left: 15px;
        position: relative;
        margin-top: 20px;
        .milestone-info-title {
            margin-top: 13px;
        }
        .milestone-info-plan-time {
            position: absolute;
            bottom: 4px;
        }
        .milestone-info-actual-time {
            position: absolute;
            bottom: -18px;
        }
    }
    .placeholder-arrow {
        width: 100%;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 600;
        margin-top: 47px;
        // 超出的隐藏，让箭头上部打平
        overflow: hidden;
        .arrow-container {
            width: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .arrow {
            width: 18px;
            height: 18px;
            border-top: 7px solid #999;
            border-right: 7px solid #999;
            // 这里箭头实际上用的两条直角边旋转得出
            transform: rotate(45deg);
        }
    }
    .change-flag {
        width: 20px;
        height: 20px;
        fill: red;
        &:hover {
            cursor: pointer;
            fill: #0064f0;
            scale: 1.2;
        }
    }
}
</style>
