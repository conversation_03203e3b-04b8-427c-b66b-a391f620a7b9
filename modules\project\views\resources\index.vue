<template>
    <div>
        <project-selector
            @input="handleDepartmentChange"
            :key="projectSelectorKey"
        ></project-selector>
        <div class="box-main">
            <el-tabs v-model="activeName" @tab-click="tabChange">
                <el-tab-pane
                    label="项目资源负载"
                    name="projectResource"
                    :lazy="true"
                >
                    <resource-load
                        :projectId="projectId"
                        class="resourceLoad"
                        :key="resourceLoadKey"
                    ></resource-load>
                </el-tab-pane>
                <el-tab-pane label="项目团队" name="ProjectOrgChart">
                    <ProjectOrgChart
                        :chartData="orgChartData"
                    ></ProjectOrgChart>
                </el-tab-pane>
                <el-tab-pane
                    label="项目团队配置"
                    name="projectGroupSet"
                    v-if="hasGroupPermission"
                >
                    <project-team
                        :personnelData="projectTeamData"
                        :otherpersonnel.sync="projectTeamOtherData"
                        @handle-personnel-change="teamChange"
                        :showNoDataMessage="isShowform"
                        @handle-leader-change="leaderChange"
                        @save-change="handleSaveChange"
                        @synchro-cd="handlesynchroChange"
                        :otherDepartmentOptions="otherDepartmentOptions"
                    >
                    </project-team>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import projectSelector from 'project/components/projectSelector';
import projectTeam from './components/projectTeam.vue';
import ResourceLoad from './components/resourceLoad.vue';
import ProjectOrgChart from './components/ProjectOrgChart.vue';

export default {
    name: 'Resources',
    components: {
        projectSelector,
        projectTeam,
        ResourceLoad,
        ProjectOrgChart
    },
    data() {
        return {
            activeName: 'projectResource',
            selectValue: '',
            projectTeamData: [],
            projectTeamOtherData: [],
            isShowform: true,
            orgChartData: [],
            // 项目ID
            projectId: '',
            // 顶部级联组件key
            projectSelectorKey: 0,
            // 资源负载key
            resourceLoadKey: 0,
            // 其他资源部门人员选项
            otherDepartmentOptions: []
        };
    },
    computed: {
        hasGroupPermission() {
            return this.$store.state.permission.btnDatas.includes(
                'projectGroupTab'
            );
        }
    },
    activated() {
        // 因为做了缓存，其他页面改变项目之后本页面的级联选择器不会同步，改变key令其同步
        this.projectSelectorKey += 1;
    },
    methods: {
        // 调用组件值
        handleDepartmentChange(value) {
            this.selectValue = value[value.length - 1];
            this.projectId = this.selectValue;
            if (this.activeName === 'projectGroupSet') {
                this.ProjectTeam(this.selectValue);
            } else if (this.activeName === 'ProjectOrgChart') {
                this.getGroupInfoForOrgChart(this.selectValue);
            }
        },
        // 获取项目团队配置信息
        async ProjectTeam(value) {
            try {
                const params = {
                    projectId: value
                };
                const res = await this.$service.project.schedule.getProjectTeam(
                    params
                );
                if (res.head.code === '000000') {
                    res.body.projectTeams[0].leader =
                        res.body.projectTeams[0].leader.length === 0
                            ? ''
                            : res.body.projectTeams[0].leader[0];
                    this.projectTeamData = res.body.projectTeams || [];
                    this.projectTeamOtherData =
                        res.body.projectTeamOtherData || [];
                } else {
                    this.$message.error(res.head.message);
                }
                this.getOtherDepartmentOptions();
            } catch (err) {
                console.error('Error:', err);
            }
        },
        // 获取项目团队组织机构图信息
        async getGroupInfoForOrgChart(value) {
            try {
                const params = {
                    projectId: value
                };
                const res =
                    await this.$service.project.schedule.getGroupInfoForOrgChart(
                        params
                    );
                if (res.head.code === '000000') {
                    this.orgChartData = res.body.projectTeams || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },
        // 组员选择事件
        teamChange(data, value) {
            this.projectTeamData = data;
            this.projectTeamOtherData = this.projectTeamOtherData.filter(
                (person) => !value.personnel.includes(person)
            );
            this.getOtherDepartmentOptions();
        },
        // 项目负责人选择事件
        leaderChange(data, value) {
            this.projectTeamData = data;
            this.projectTeamOtherData = this.projectTeamOtherData.filter(
                (person) => !value.leader.includes(person)
            );
            this.getOtherDepartmentOptions();
        },
        // 保存
        handleSaveChange() {
            let projectTeamData = this.$tools.cloneDeep(this.projectTeamData);
            projectTeamData[0].leader = projectTeamData[0].leader
                ? [projectTeamData[0].leader]
                : [];
            const params = {
                projectId: this.selectValue,
                projectTeams: projectTeamData,
                projectTeamOtherData: this.projectTeamOtherData
            };
            this.$service.project.schedule
                .editProjectTeam(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message.success(res.head.message);
                        this.ProjectTeam(this.selectValue);
                        // 保存后资源负载界面也会更新
                        this.resourceLoadKey += 1;
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        /**
         * 同步禅道
         */
        handlesynchroChange() {
            const params = {
                projectId: this.selectValue
            };
            this.$service.project.schedule.synchroZentao(params).then((res) => {
                if (res.head.code === '000000') {
                    this.ProjectTeam(this.selectValue);
                    this.$message.success(res.head.message);
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        /**
         * 切换页签的处理
         */
        tabChange() {
            if (!this.projectId) {
                Message.closeAll();
                this.$message.warning('请选择项目后查询');
                return;
            }
            // 切换页签后查询
            if (this.activeName === 'projectGroupSet') {
                this.ProjectTeam(this.selectValue);
            } else if (this.activeName === 'ProjectOrgChart') {
                this.getGroupInfoForOrgChart(this.selectValue);
            }
        },
        /**
         * 固定其他资源人员对应的option，令其无法新增
         */
        getOtherDepartmentOptions() {
            if (this.projectTeamOtherData.length === 0) {
                this.otherDepartmentOptions = [];
                return;
            }
            const allPersonData = this.$store.state.project.currentEmployeeList;
            this.otherDepartmentOptions = allPersonData.filter((item) => {
                return this.projectTeamOtherData.includes(item.loginName);
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 10px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
.resourceLoad {
    margin-top: 20px;
}
</style>
