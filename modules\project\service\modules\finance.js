/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 通过产品线获取项目信息
        finance: {
            // 获取查询条件的选项（公共接口）
            getSelectOptions(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/common/getParamCodeValues',
                    method: 'get',
                    params: data,
                    loading: false
                });
            },
            // 查询项目财务总体执行情况
            getOverallChartInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project-finance/getProjectFinanceOverall',
                    method: 'get',
                    params: data
                });
            },
            // 查询项目财务按阶段执行情况
            getPhaseChartInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project-finance/getProjectFinanceByStage',
                    method: 'get',
                    params: data
                });
            },
            // 查询项目财务按科目执行情况
            getSubjectChartInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project-finance/getProjectFinanceBySubject',
                    method: 'get',
                    params: data
                });
            },
            // 根据查询条件获取支出明细列表
            getCostList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/get_project_pay_detail_list',
                    method: 'post',
                    data
                });
            },
            // 查询项目财务按科目执行情况
            getEmpolyeeCostByWeek(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/get_project_week_hr_cost_detail',
                    method: 'get',
                    params: data
                });
            },
            // 确认项目周人力成本
            confirmEmoloyeeCost(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/confirm_project_week_hr_cost',
                    method: 'post',
                    data
                });
            },
            // 查询周人力成本任务明细列表
            getEmoloyeeTaskList(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/get_project_week_hr_cost_task_info',
                    method: 'post',
                    data
                });
            },
            // 确认内部样机扣减费用
            confirmSampleCost(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/confirm_project_deduct_cost',
                    method: 'post',
                    data
                });
            },
            // 查询内部样机扣减信息
            getSampleCostInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/get_project_folw_deduct_info',
                    method: 'get',
                    params: data
                });
            },
            // 人工费导出
            exportEmployeeFee(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_pay_detail/laborCostImport',
                    method: 'get',
                    responseType: 'blob',
                    params: data
                });
            }
        }
    };

    return service;
};
