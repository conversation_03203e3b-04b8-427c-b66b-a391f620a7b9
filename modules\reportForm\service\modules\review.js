/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 审核
        review: {
            // 获取未填写日志人员占比
            getMissingLogStaffRatio(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/getNotFillInLogPersonProp',
                    method: 'post',
                    data
                });
            },
            // 获取未填写日志人员占比（明细）
            getMissingLogStaffRatioDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/notFillInLogDetailExport',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            // 获取下周工时负载分布占比
            getWorkloadHoursRatio(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/getWorkHourLoadProp',
                    method: 'post',
                    data
                });
            },
            // 获取下周工时负载分布占比（明细）
            getWorkloadHoursRatioDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/workHourLoadDetailExport',
                    responseType: 'blob',
                    method: 'post',
                    data
                });
            },
            // 获取未分配预计工时数量
            getUnassignedProjectedTimeNumbers(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/getUnallocatedEstimatedHourCount',
                    method: 'post',
                    data
                });
            },
            // 获取未关闭风控数量
            getUnClosedRiskTaskNumbers(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/getNotCloseRiskControlCount',
                    method: 'post',
                    data
                });
            },
            // 获取产品线未完成人工费用核算条目数量
            getUnFinishedFeeNumbers(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/getNotCalculateLaborCostCount',
                    method: 'post',
                    data
                });
            },
            // 获取产品线未完成人工费用核算条目数量(明细)
            getUnFinishedFeeNumbersDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/platformExecution/notCalculateLaborDetailExport',
                    responseType: 'blob',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
