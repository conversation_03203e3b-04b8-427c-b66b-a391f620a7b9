<template>
    <div>
        <el-button
            type="primary"
            @click="dialogVisible = true"
            class="add-button"
            v-permission="['businessSectorProjectUpdate']"
            >新增项目</el-button
        >
        <el-dialog
            title="新增项目"
            :visible.sync="dialogVisible"
            :before-close="dialogBeforeClose"
            class="add-project-dialog"
        >
            <el-form :model="form" ref="form">
                <el-form-item label="业务板块">
                    {{ businessUnit }}
                </el-form-item>
                <el-form-item label="产品系列" prop="productSet">
                    <el-select
                        v-model="form.productSet"
                        placeholder="请选择产品系列"
                        :disabled="true"
                    >
                        <el-option
                            v-for="item in productTypeOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="产品分类"
                    prop="productType"
                    :rules="required"
                    filterable
                >
                    <el-select
                        v-model="form.productType"
                        placeholder="请选择产品分类"
                    >
                        <el-option
                            v-for="item in productClassOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="战略标志"
                    prop="strategy"
                    :rules="required"
                >
                    <el-select
                        v-model="form.strategy"
                        placeholder="请选择战略标志"
                    >
                        <el-option
                            v-for="item in CONSTANTS.STRATEGY"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="项目"
                    prop="projectNumber"
                    :rules="required"
                >
                    <el-select
                        v-model="form.projectNumber"
                        placeholder="请选择项目"
                        filterable
                    >
                        <el-option
                            v-for="item in projectList"
                            :key="item.projectNumber"
                            :label="item.projectName"
                            :value="item.projectNumber"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="handleCancel">取 消</el-button>
                <el-button type="primary" @click="addProject">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';

const required = [
    {
        required: true,
        message: ' ',
        trigger: ['change', 'blur']
    }
];
const form = {
    productType: '',
    strategy: '',
    projectNumber: ''
};
export default {
    name: 'AddNewRetails',
    components: {},
    props: {
        businessUnit: {
            type: String,
            default: ''
        },
        isScene: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            CONSTANTS,
            dialogVisible: false,
            form: this.$tools.cloneDeep(form),
            projectList: [],
            required,
            projectListMap: [],
            // 产品分类
            productClassOptions: [],
            productTypeOptions: []
        };
    },
    watch: {
        businessUnit(newVal) {
            this.updateProductClassOptions(newVal);
        },
        dialogVisible(newVal) {
            if (newVal) {
                this.getproductLine();
                this.getRestProjectList();
            }
        }
    },
    methods: {
        // 查询产品系列
        async getproductLine() {
            try {
                const params = {
                    paramName: '产品系列',
                    paramType: this.businessUnit
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productTypeOptions = res.body || [];
                    if (this.productTypeOptions.length > 0) {
                        // 默认选中第一个产品系列
                        this.form.productSet =
                            this.productTypeOptions[0].paramCode;
                        this.getproductType();
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        // 产品分类
        async getproductType() {
            try {
                const params = {
                    paramName: this.businessUnit,
                    paramType: this.form.productSet
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productClassOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        /**
         * 关闭弹窗前的处理
         * @param {Function} done 完成后的函数
         */
        dialogBeforeClose(done) {
            this.resetForm();
            this.dialogVisible = false;
            done();
        },
        /**
         * 获取项目列表（已经过滤调已经存在的项目）
         */
        async getRestProjectList() {
            const api = this.$service.scene.getRestProjectList;
            try {
                const res = await api();
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.projectList = res.body;
                // 创建对象映射
                this.projectList.forEach((item) => {
                    this.projectListMap[item.projectNumber] = item;
                });
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 新增项目
         */
        async addProject() {
            const valid = this.validateForm();
            if (!valid) {
                this.$message.error('请输入所有必填项');
                return;
            }
            const api = this.$service.scene.addProject;
            try {
                const params = {
                    ...this.form,
                    ...this.projectListMap[this.form.projectNumber],
                    businessUnit: this.businessUnit
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.$emit('update');
                this.resetForm();
                this.dialogVisible = false;
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 取消时的操作
         */
        handleCancel() {
            this.resetForm();
            this.dialogVisible = false;
        },
        /**
         * 表单验证
         * @returns {Boolean} 是否通过校验
         */
        validateForm() {
            let res = false;
            this.$refs.form.validate((valid) => {
                if (valid) {
                    res = true;
                }
            });
            return res;
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.$refs.form.resetFields();
        }
    }
};
</script>

<style lang="scss" scoped>
.add-button {
    position: absolute;
    z-index: 100;
    top: 15px;
    right: 170px;
}
.add-project-dialog {
    ::v-deep .el-form-item__label {
        font-weight: 700;
    }
    ::v-deep .el-select {
        width: 100%;
    }
}
</style>
