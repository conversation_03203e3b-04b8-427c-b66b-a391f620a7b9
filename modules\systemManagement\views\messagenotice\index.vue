<!-- eslint-disable vue/no-v-html -->
<template>
    <div class="view">
        <el-container>
            <el-aside width="290px">
                <div class="container">
                    <el-tree ref="msgTree" :data="treeData" :props="defaultProps" :expand-on-click-node="false" :current-node-key="currentNodeKey" default-expand-all highlight-current node-key="msgId" @node-click="handleNodeClick">
                        <el-tooltip slot-scope="{ node }" :content="node.label" placement="right">
                            <span class="block">
                                <span class="block__text">{{
                  node.label
                }}</span>
                            </span>
                        </el-tooltip>
                    </el-tree>
                </div>
            </el-aside>
            <el-main v-show="showList" class="custom-main">
                <el-tabs ref="tabs" v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane :label="$t('systemManagement.messageNotice.allMessages')" name="0">
                        {{ $t('systemManagement.messageNotice.allMessages') }}</el-tab-pane>
                    <el-tab-pane v-for="item of tabsArr" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :name="item.valueCode">{{ $t('systemManagement.dictionaryManagement.value.'+item.valueName) }}</el-tab-pane>
                </el-tabs>
                <div class="header">
                    <span class="header__body">
                        <el-input v-model="queryParam" type="text" clearable :placeholder="$t('systemManagement.messageNotice.inputPlaceholder')" @keyup.enter.native="getMessageNoticeList">
                            <i slot="suffix" class="fa fa-search" @click="getMessageNoticeList" />
                        </el-input>
                    </span>
                    <div class="header__btns">
                        <el-button v-permission="['message_manage_delete']" icon="fa fa-trash-o" size="middle" type="primary" :disabled="multipleSelection.length === 0" @click="deleteMessage">
                            {{ $t('systemManagement.messageNotice.delete') }}
                        </el-button>
                        <el-button v-if="currentNodeKey !=='1'" v-permission="['message_manage_mark_read']" type="primary" size="middle" class="button table-inner-button" :disabled="multipleSelection.length === 0" @click="setMessageRead">
                            <i class="fa fa-flag-o" aria-hidden="true" />
                            {{ $t('systemManagement.messageNotice.setRead') }}
                        </el-button>
                        <el-button v-if="currentNodeKey !=='1'" v-permission="['message_manage_read_all']" icon="fa fa-th-large" size="middle" type="primary" :disabled="messageTableData.length === 0" @click="setAllMessageRead">
                            {{ $t('systemManagement.messageNotice.setAllRead') }}
                        </el-button>
                        <el-button v-permission="['message_manage_delete_all']" icon="fa fa-trash-o" size="middle" type="primary" :disabled="messageTableData.length === 0" @click="deleteAllMessage">
                            {{ $t('systemManagement.messageNotice.deleteAll') }}
                        </el-button>

                    </div>
                </div>
                <el-table :header-cell-style="{ background: '#F5F6FA' }" :data="messageTableData" border @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column :label="$t('systemManagement.messageNotice.columnName.messageTitle')">
                        <template slot-scope="{ row }">
                            <div class="title-box" @click="getMessageDetail(row)">
                                <span v-if="row.readMsg === 0" class="red-dot" />
                                <span :class="row.readMsg === 0?'weight':''" class="msg-title">
                                    {{ row.messageTitle }}
                                </span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('systemManagement.messageNotice.columnName.sendTime')">
                        <template slot-scope="{ row }">
                            <span>{{
                row.sendTime.split("T").join(" ")
              }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="messageTypeName" :label="$t('systemManagement.messageNotice.messageType')" width="200">
                        <template slot-scope="{ row }">
                            <span>{{ $t('systemManagement.dictionaryManagement.value.'+row.messageTypeName) }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background for="pagination" :current-page.sync="pager.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size.sync="pager.pageSize" layout="total, prev, pager, next,sizes,  jumper" :total="pager.total" class="right" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </el-main>
            <el-main v-show="!showList" class="custom-main">
                <div class="header detail-header">
                    <div class="header-left back">
                        <el-button type="text" icon="fa fa-arrow-left" class="back" size="medium" @click="goBack">
                            {{ $t('systemManagement.messageNotice.back') }}
                        </el-button>
                        <el-divider direction="vertical" />
                        <span>{{ activeName==='0'? $t('systemManagement.messageNotice.allMessages') : $t('systemManagement.dictionaryManagement.value.'+activeText) }}</span>
                    </div>
                    <div class="header-right">
                        <span v-if="prePageMsg.msgId" @click="prevClick">{{ $t('systemManagement.messageNotice.previous') }}</span>
                        <el-divider v-if="prePageMsg.msgId && nextPageMsg.msgId" direction="vertical" />
                        <span v-if="nextPageMsg.msgId" @click="nextClick">{{ $t('systemManagement.messageNotice.next') }}</span>
                    </div>
                </div>
                <div class="line" />
                <div v-if="releaseType === '2'" class="msg-content-box">
                    <div class="detail-title">{{ messageTitle }}</div>
                    <div class="msg-time">{{ messageSendTime }}</div>
                    <div class="deleted-msg">{{ $t('systemManagement.informationAnnounce.message.alreadyDelete') }}</div>
                </div>
                <div v-else class="msg-content-box">
                    <div class="detail-title">{{ messageTitle }}</div>
                    <div class="msg-time">{{ messageSendTime }}</div>
                    <div class="msg-content" v-html="msgContent" />
                    <div class="msg-file">
                        <el-tag v-for="(tag,index) in fileList" :key="tag.name + index" type="info">
                            <a :href="tag.url" class="tutorial" target="_blank">
                                <i v-if="tag.icon === 'pdf'" class="fa fa-file-pdf-o" aria-hidden="true" />
                                <i v-else-if="tag.icon === 'png' || tag.icon === 'jpg'|| tag.icon === 'jpeg' " class="fa fa-file-image-o" aria-hidden="true" />
                                <i v-else-if="tag.icon === 'doc' || tag.icon === 'docx' " class="fa fa fa-file-word-o" aria-hidden="true" />
                                <i v-else-if="tag.icon === 'xls' || tag.icon === 'xlsx' " class="fa fa-file-excel-o" aria-hidden="true" />
                                <i v-else class="fa fa-file-powerpoint-o" aria-hidden="true" />
                                <span>{{ tag.name }}</span>
                            </a>

                        </el-tag>
                    </div>
                </div>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import getUnreadCount from '../../mixins/getUnreadCount';

export default {
    name: 'Messagenotice',
    mixins: [getUnreadCount],
    data() {
        return {
            // 查询、分页表单
            pager: {
                pageNum: 1,
                pageSize: 10,
                total: 0
            },
            // 表格数据
            messageTableData: [],
            // 已选择的表格数据
            multipleSelection: [],
            // 左侧树数据
            treeData: [
                {
                    magName: '',
                    msgId: '00',
                    children: [
                        {
                            magName: '',
                            msgId: ''
                        },
                        {
                            magName: '',
                            msgId: '0'
                        },
                        {
                            magName: '',
                            msgId: '1'
                        }
                    ]
                }
            ],
            // 默认参数
            defaultProps: {
                children: 'children',
                label: 'magName'
            },
            // 左侧树默认选中哪个
            currentNodeKey: '',
            // 右边导航选中的值
            activeName: '0',
            activeIndex: '0',
            activeText: '',
            // 右侧导航
            tabsArr: [],
            // 搜索框内容
            queryParam: '',
            // 展示列表
            showList: true,
            // 消息内容
            msgContent: '',
            // 上一条消息
            prePageMsg: {},
            // 下一条消息
            nextPageMsg: {},
            // 当前列表的搜索条件
            currQueryParam: '',
            // 详情消息标题
            messageTitle: '',
            // 详情消息时间
            messageSendTime: '',
            // 判断消息是否已被删除
            releaseType: '',
            // 附件列表
            fileList: []
        };
    },
    watch: {
        '$route': {
            // 解决多次从首页消息进入，页面不刷新问题
            handler(to) {
                if (to.path === '/systemManagement/messageNotice') {
                    const { query } = this.$route;
                    if (query && query.msgId) {
                        // 直接显示详情
                        this.showList = false;
                        this.activeName = '0';
                        this.activeIndex = '0';
                        this.queryParam = '';
                        this.getMessageDetail(query);
                    }
                }
            },
            // 解决第一次从首页过来不进详情的问题
            immediate: true
        },
        'currentNodeKey': function (id) {
            // 解决从首页进入详情，点击返回，左侧树无法重置的问题
            this.$refs.msgTree.setCurrentKey(id);
        },
        // 解决切换语言，左侧树国际化不更新的问题
        '$i18n.locale': function () {
            this.internationTreeData();
        }
    },
    created() {
        this.internationTreeData();
        const { query } = this.$route;
        if (!query.msgId) {
            this.getMessageNoticeList();
        }
        // 减少请求
        this.getMessageTypeList();
    },
    methods: {
        // 获取参数类型值
        getMessageTypeList() {
            this.$service.systemManagement.getMessageDictType().then((res) => {
                if (res.head.code === '000000') {
                    this.tabsArr = res.body;
                }
            });
        },
        // 获取表格列表
        getMessageNoticeList() {
            const data = {
                pageNum: this.pager.pageNum,
                pageSize: this.pager.pageSize,
                searchParam: this.queryParam,
                messageHasRead: this.currentNodeKey,
                messageType: this.activeName === '0' ? '' : this.activeName
            };
            this.$service.systemManagement
                .getMessageNoticeList(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        // 解决列表没刷新，但搜索条件变了，此时再去详情、点击全部标记已读、全部删除报错的问题
                        this.currQueryParam = this.queryParam;
                        this.messageTableData = res.body.list;
                        this.pager.total = res.body.total;
                        this.pager.pageNum = res.body.pageNum;
                        this.pager.pageSize = res.body.pageSize;
                    } else if (res.head.code === '99000001') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.msg.messageError1'
                            ),
                            type: 'error'
                        });
                    } else if (res.head.code === '99000004') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.msg.messageError2'
                            ),
                            type: 'error'
                        });
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 用户列表check选择
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 左侧树点击
        handleNodeClick(node) {
            if (this.showList) {
                // 当前显示列表
                if (node.msgId !== '00' && this.currentNodeKey !== node.msgId) {
                    // this.currentNodeKey !== node.msgId 避免重复请求数据
                    this.currentNodeKey = node.msgId;
                    this.queryParam = '';
                    this.pager.pageNum = 1;
                    this.getMessageNoticeList();
                }
            } else {
                // 当前显示详情
                // eslint-disable-next-line no-lonely-if
                if (node.msgId !== '00') {
                    if (this.currentNodeKey !== node.msgId) {
                        // 解决在详情页点击左侧导航回不到列表页的问题
                        this.currentNodeKey = node.msgId;
                        this.queryParam = '';
                        this.pager.pageNum = 1;
                        this.getMessageNoticeList();
                        this.showList = true;
                    } else {
                        // 解决在详情页点击当前选择中的导航回不到列表页的问题
                        this.showList = true;
                    }
                }
            }
        },
        // 右边导航点击
        handleClick(tab) {
            if (tab.index !== this.activeIndex) {
                if (tab.index !== '0') {
                    const currentTabData = this.tabsArr[tab.index - 1];
                    this.activeText = currentTabData.valueName;
                }
                // 避免重复请求数据
                this.activeIndex = tab.index;
                this.queryParam = '';
                this.pager.pageNum = 1;
                this.getMessageNoticeList();
            }
        },
        // 删除选择的消息
        deleteMessage() {
            this.$confirm(
                this.$t('systemManagement.messageNotice.deleteConfirmMessage'),
                this.$t('systemManagement.messageNotice.deleteConfirmTip'),
                {
                    type: 'warning'
                }
            ).then(() => {
                const msgIdSelect = this.getSelectMeaasgeListIds();
                const msgIdTitle = this.getSelectMeaasgeListTitles();
                const params = {
                    msgIds: msgIdSelect.join(','),
                    messageTitle: msgIdTitle.join(',')
                };
                this.$service.systemManagement
                    .deleteMessage(params)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.getMessageNoticeList();
                            this.getUnreadCount();
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        },
        // 获取表格选中的消息列表id
        getSelectMeaasgeListIds() {
            const msgIdSelect = this.multipleSelection.map((item) => {
                return item.msgId;
            });

            return msgIdSelect;
        },
        // 获取表格选中的消息列表title
        getSelectMeaasgeListTitles() {
            const msgTitleSelect = this.multipleSelection.map((item) => {
                return item.messageTitle;
            });
            return msgTitleSelect;
        },
        // 标记已读:参数msgIds
        setMessageRead() {
            const msgIdSelect = this.getSelectMeaasgeListIds();
            const params = {
                msgIds: msgIdSelect.join(',')
            };
            this.$service.systemManagement
                .setMessageRead(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.getMessageNoticeList();
                        this.getUnreadCount();
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 标记列表数据全部已读:
        setAllMessageRead() {
            const data = {
                messageType: this.activeName === '0' ? '' : this.activeName,
                searchParam: this.currQueryParam
            };
            this.$service.systemManagement
                .setAllMessageRead(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.getMessageNoticeList();
                        this.getUnreadCount();
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 删除所有列表消息
        deleteAllMessage() {
            this.$confirm(
                this.$t('systemManagement.messageNotice.deleteConfirmMessage'),
                this.$t('systemManagement.messageNotice.deleteConfirmTip'),
                {
                    type: 'warning'
                }
            ).then(() => {
                const data = {
                    messageType: this.activeName === '0' ? '' : this.activeName,
                    searchParam: this.currQueryParam
                };
                this.$service.systemManagement
                    .deleteAllMessage(data)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.getMessageNoticeList();
                            this.getUnreadCount();
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        },
        // 每页多少条发生改变
        handleSizeChange(val) {
            this.pager.pageSize = val;
            this.getMessageNoticeList();
        },
        // 翻页
        handleCurrentChange(val) {
            this.pager.pageNum = val;
            this.getMessageNoticeList();
        },
        // 返回列表
        goBack() {
            const { query } = this.$route;
            if (query && query.msgId) {
                // 从首页进来
                this.currentNodeKey = '';
                this.pager = {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0
                };
            }
            // 重新请求数据
            this.getMessageNoticeList();
            this.showList = true;
        },
        // 获取消息详情
        getMessageDetail(row) {
            const params = {
                msgId: row.msgId,
                messageType: this.activeName === '0' ? '' : this.activeName,
                searchParam: this.currQueryParam,
                messageHasRead: this.currentNodeKey
            };
            this.$service.systemManagement
                .getMessageDetail(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        const { body } = res;
                        this.fileList = [];
                        this.nextPageMsg = {
                            msgId: body.nextPageMsgId,
                            readMsg: body.nextPageMsgHasRead
                        };
                        this.prePageMsg = {
                            msgId: body.prePageMsgId,
                            readMsg: body.prePageMsgHasRead
                        };
                        this.releaseType = body.releaseType;
                        this.messageTitle = body.messageTitle;
                        this.messageSendTime = body.messageSendTime;
                        this.msgContent = body.messageContent;
                        const files = body.msgFiles;
                        if (files.length > 0) {
                            files.forEach((item) => {
                                const fileNameIndex = item.lastIndexOf('/');
                                const name = item.slice(fileNameIndex + 15);
                                const suffixIndex = item.lastIndexOf('.');
                                const suffix = item.slice(suffixIndex + 1);
                                this.fileList.push({
                                    name,
                                    icon: suffix,
                                    url: item
                                });
                            });
                        }
                        // 相当于如果是未读进来的，异步标记已读，不影响正常进入详情
                        this.showList = false;
                        if (row.readMsg === 0) {
                            // eslint-disable-next-line no-shadow
                            const params = {
                                msgIds: row.msgId
                            };
                            this.$service.systemManagement
                                .setMessageRead(params)
                                // eslint-disable-next-line no-shadow
                                .then((res) => {
                                    if (res.head.code !== '000000') {
                                        this.$message.error(res.head.message);
                                    } else {
                                        this.getUnreadCount();
                                    }
                                });
                        }
                    } else if (res.head.code === '99000001') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.msg.messageError1'
                            ),
                            type: 'error'
                        });
                    } else if (res.head.code === '99000004') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.msg.messageError2'
                            ),
                            type: 'error'
                        });
                    }
                });
        },
        // 上一条
        prevClick() {
            this.getMessageDetail(this.prePageMsg);
        },
        // 下一条
        nextClick() {
            this.getMessageDetail(this.nextPageMsg);
        },
        // 国际化处理左侧树
        internationTreeData() {
            const currObj = {
                magName: this.$t(
                    'systemManagement.messageNotice.messageNotification'
                ),
                msgId: '00',
                children: [
                    {
                        magName: this.$t(
                            'systemManagement.messageNotice.allMsg'
                        ),
                        msgId: ''
                    },
                    {
                        magName: this.$t(
                            'systemManagement.messageNotice.unreadMessage'
                        ),
                        msgId: '0'
                    },
                    {
                        magName: this.$t(
                            'systemManagement.messageNotice.readMessage'
                        ),
                        msgId: '1'
                    }
                ]
            };
            this.treeData.splice(0, 1, currObj);
        }
    }
};
</script>
<style lang="scss">
/* 给带链接的a标签添加样式 */
.msg-content {
    a {
        color: #3370ff;
    }
}
</style>
<style lang="scss" scoped>
::v-deep .el-tabs__content {
    display: none; /*隐藏右侧头部选中的tab*/
}
::v-deep .el-tabs {
    height: 54px;
    line-height: 54px;
    ::v-deep .el-tabs__nav-wrap {
        height: 100%;
        ::v-deep .el-tabs__nav-scroll {
            height: 100%;
        }
    }
}
::v-deep .custom-main {
    padding-top: 3px !important;
}
.header {
    .header__body {
        text-align: left;
    }
    .header-left {
        ::v-deep .el-button {
            padding: 0;
        }
    }
    .header-right {
        span {
            font-weight: 400;
            color: #5584fb;
            cursor: pointer;
        }
    }
}
.detail-header {
    margin-bottom: 0;
    align-items: center;
}
.fa-flag-o {
    margin-right: 5px;
}
.weight {
    font-weight: bold;
}
.red-dot {
    width: 8px;
    height: 8px;
    display: inline-block;
    background-color: red;
    border-radius: 50%;
    margin-right: 5px;
}
.title-box {
    min-height: 23px;
    cursor: pointer;
}
.msg-title:hover {
    text-decoration: underline;
    color: #3370ff;
}
.msg-content-box {
    margin-top: 20px;
    .detail-title,
    .msg-time {
        text-align: center;
        font-weight: bold;
    }
    .msg-time {
        margin: 5px 0 16px 0;
    }
    .msg-content {
        background: #ffffff;
        border: 1px solid #dfdfdf;
        padding: 20px;
        margin-bottom: 20px;
    }
    .msg-file {
        .el-tag {
            cursor: pointer;
            color: #3370ff;
        }
    }
    .deleted-msg {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
    }
}
</style>
