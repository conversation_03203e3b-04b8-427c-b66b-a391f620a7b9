<template>
    <div class="risk-detail-container">
        <div class="flex">
            <h3>风险详情</h3>
            <div>
                <el-button
                    v-permission="['maintenanceProjectRiskUpdate']"
                    type="primary"
                    @click="handleEdit"
                    >编辑</el-button
                >
                <el-button type="primary" @click="goBack">返回</el-button>
            </div>
        </div>

        <!-- 基本信息 -->
        <el-divider>基本信息</el-divider>
        <el-descriptions :column="3" border>
            <el-descriptions-item label="ID">{{
                riskData.riskId
            }}</el-descriptions-item>
            <el-descriptions-item label="风险标题" :span="2">{{
                riskData.riskTitle
            }}</el-descriptions-item>
            <el-descriptions-item label="风险状态" :span="3">{{
                riskData.riskStatus
            }}</el-descriptions-item>
            <el-descriptions-item label="风险关联对象">{{
                riskData.riskAssObjectType
            }}</el-descriptions-item>
            <el-descriptions-item
                v-if="riskData.riskAssObjectType === '维护项目任务'"
                label="项目"
                >{{ riskData.assProjectName }}</el-descriptions-item
            >
            <el-descriptions-item
                v-if="
                    riskData.riskAssObjectType === '维护项目任务' ||
                    riskData.riskAssObjectType === '开发项目任务'
                "
                label="任务"
            >
                <el-button
                    type="text"
                    class="main-task"
                    @click="handleRoutingJump"
                >
                    {{ riskData.assTaskName }}
                </el-button>
            </el-descriptions-item>
            <el-descriptions-item
                v-if="riskData.riskAssObjectType === '维护项目缺陷'"
                label="缺陷"
                :span="2"
            >
                <el-button
                    type="text"
                    class="main-task"
                    @click="handleObjectRoutingJump"
                >
                    {{ riskData.assObjectName }}
                </el-button>
            </el-descriptions-item>
            <el-descriptions-item
                v-if="riskData.riskAssObjectType === '维护项目需求'"
                label="需求"
                :span="2"
            >
                <el-button
                    type="text"
                    class="main-task"
                    @click="handleObjectRoutingJump"
                >
                    {{ riskData.assObjectName }}
                </el-button></el-descriptions-item
            >
            <el-descriptions-item
                v-if="
                    riskData.riskAssObjectType === '订单' ||
                    riskData.riskAssObjectType === '开发项目订单'
                "
                label="订单"
                :span="2"
            >
                <el-button
                    type="text"
                    class="main-task"
                    @click="handleObjectRoutingJump"
                >
                    {{ riskData.assObjectName }}
                </el-button></el-descriptions-item
            >

            <el-descriptions-item label="产品型号">{{
                riskData.productModel
            }}</el-descriptions-item>
            <el-descriptions-item label="产品线">{{
                riskData.productLine
            }}</el-descriptions-item>
            <el-descriptions-item label="细分产品线">{{
                riskData.subProductLine
            }}</el-descriptions-item>
        </el-descriptions>

        <!-- 风险识别与分析 -->
        <el-divider>风险识别与分析</el-divider>
        <el-descriptions :column="3" border class="mt-20">
            <el-descriptions-item label="风险识别日期">{{
                riskData.riskIdentifyDate
            }}</el-descriptions-item>
            <el-descriptions-item label="风险等级">{{
                riskData.riskLevel
            }}</el-descriptions-item>
            <el-descriptions-item label="风险类型">{{
                riskData.riskType
            }}</el-descriptions-item>

            <el-descriptions-item label="风险描述及影响" :span="3">
                <div class="long-text">{{ riskData.riskDesc }}</div>
            </el-descriptions-item>
        </el-descriptions>

        <!-- 风险应对 -->
        <el-divider>风险应对</el-divider>
        <el-descriptions :column="3" border class="mt-20">
            <el-descriptions-item label="应对措施" :span="3">
                <div class="long-text">{{ riskData.solutionMeasures }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="计划完成日期">{{
                riskData.planFinishDate
            }}</el-descriptions-item>
            <el-descriptions-item label="责任人" :span="2">{{
                riskData.responsiblePersonList &&
                riskData.responsiblePersonList.join(',')
            }}</el-descriptions-item>
        </el-descriptions>

        <!-- 风险应对计划 -->
        <template v-if="riskData.proProjectId">
            <el-divider>风险应对计划</el-divider>
            <ZentaoTaskList
                :row="riskData"
                class="mt-10"
                :show="isShowZentaoTaskList"
                :proProjectId="riskData.proProjectId"
                :proTaskId="riskData.proTaskId"
                :projectManagerAccount="projectManagerAccount"
            ></ZentaoTaskList>
        </template>

        <!-- 风险应对需支持事项 -->
        <div
            v-show="
                riskData.riskSupportList &&
                riskData.riskSupportList.length !== 0
            "
        >
            <el-divider>风险应对需支持事项</el-divider>
            <el-table
                :data="riskData.riskSupportList"
                style="width: 100%"
                class="risk-support"
            >
                <el-table-column
                    prop="supportItem"
                    label="需支持的事项和目标"
                    width="width"
                    header-align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="expectedDate"
                    label="期望达成日期"
                    header-align="center"
                    align="center"
                    width="120"
                >
                </el-table-column>
                <el-table-column label="提供支持责任人" header-align="center">
                    <template slot-scope="scope">
                        {{
                            scope.row.responsiblePersonList &&
                            scope.row.responsiblePersonList.join('，')
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="提供支持责任部门"
                    header-align="center"
                    width="width"
                >
                    <template slot-scope="scope">
                        {{
                            scope.row.responsibleOrgShortList &&
                            scope.row.responsibleOrgShortList.join('，')
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="supportStatus"
                    label="状态"
                    header-align="center"
                    align="center"
                    width="90"
                >
                </el-table-column>
            </el-table>
        </div>

        <!-- 风险控制 -->
        <el-divider>风险控制</el-divider>
        <el-descriptions :column="3" border class="mt-20">
            <el-descriptions-item label="风险最终落实情况" :span="3">
                {{ riskData.riskImplementInfo }}
            </el-descriptions-item>
            <el-descriptions-item label="实际完成日期">
                {{ riskData.finishDate }}
            </el-descriptions-item>
        </el-descriptions>

        <RiskUpdateDialog
            type="edit"
            :id="id"
            @success="getDetail"
            :visible.sync="riskUpdateDialogVisible"
            :projectType="projectType"
        ></RiskUpdateDialog>
    </div>
</template>

<script>
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskUpdateDialog from 'maintenanceProject/components/RiskUpdateDialog';

export default {
    name: 'MaintenanceRiskDetail',
    components: { ZentaoTaskList, RiskUpdateDialog },
    data() {
        return {
            riskData: {},
            // 风险ID
            id: '',
            // 禅道项目ID
            proProjectId: null,
            // 项目经理域账号
            projectManagerAccount: '',
            riskUpdateDialogVisible: false,
            isShowZentaoTaskList: false
        };
    },
    computed: {
        projectType() {
            return this.riskData.riskStageType === '开发'
                ? 'develop'
                : 'maintenance';
        }
    },
    created() {
        const { query = null } = this.$route;
        if (query) {
            this.id = query.risk_id;
            this.getDetail();
        }
        this.closeAllPopovers();
    },
    methods: {
        /**
         * 跳转到禅道任务
         * @param {Object} row 行数据
         */
        handleRoutingJump() {
            const url = `http://192.168.28.67/pro/task-view-${this.riskData.assTaskId}.html`;
            this.riskData.assTaskId && window.open(url);
        },
        /**
         * 关闭所有弹窗
         */
        closeAllPopovers() {
            this.$root.$emit('close-popovers');

            const popovers = document.querySelectorAll('.el-popover');
            popovers.forEach((popover) => {
                if (popover.style.display !== 'none') {
                    document.body.click();
                    popover.style.display = 'none';
                }
            });

            const tooltipPopovers = document.querySelectorAll(
                '.el-tooltip__popper'
            );
            tooltipPopovers.forEach((tooltipPopover) => {
                if (tooltipPopover.style.display !== 'none') {
                    document.body.click();
                    tooltipPopover.style.display = 'none';
                }
            });
        },
        goBack() {
            this.$router.go(-1);
        },
        handleEdit() {
            this.riskUpdateDialogVisible = true;
        },
        /**
         * 获取风险详情
         */
        async getDetail() {
            if (!this.id) return;
            const api = this.$service.maintenanceProject.risk.getDetail;
            const params = {
                riskId: this.id
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.riskData = res.body;
                    this.projectManagerAccount = this.riskData.projectManager;
                    this.proProjectId = this.riskData.proProjectId;
                    this.isShowZentaoTaskList = true;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleObjectRoutingJump() {
            if (this.riskData.riskAssObjectType === '维护项目缺陷') {
                this.$router.push({
                    path: 'maintenanceDefectDetail',
                    query: {
                        defect_id: this.riskData.assObjectId
                    }
                });
            } else if (this.riskData.riskAssObjectType === '维护项目需求') {
                this.$router.push({
                    path: 'maintenanceDemandDetail',
                    query: {
                        demand_id: this.riskData.assObjectId
                    }
                });
            } else if (this.riskData.riskAssObjectType === '订单') {
                this.$router.push({
                    path: 'maintenanceOrderDetail',
                    query: {
                        order_id: this.riskData.assObjectId
                    }
                });
            } else if (this.riskData.riskAssObjectType === '开发项目订单') {
                this.$router.push({
                    path: 'projectOrderDetail',
                    query: {
                        order_id: this.riskData.assObjectId
                    }
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.risk-support {
    border: 1px solid #8c8c8c !important;
}
.risk-detail-container {
    padding: 20px;
    height: calc(100vh - 20px);
    overflow: auto;
}

.el-descriptions {
    margin-bottom: 20px;
}

.long-text {
    white-space: pre-line;
    line-height: 1.6;
}

.mt-20 {
    margin-top: 20px;
}

::v-deep .el-descriptions-item__label {
    width: 120px;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep .el-table th {
    background-color: #3370ff !important;
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
