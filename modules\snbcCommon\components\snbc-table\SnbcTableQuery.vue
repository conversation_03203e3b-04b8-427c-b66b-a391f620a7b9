<template>
    <div class="header table-header query-area-form">
        <div class="header-title">
            <span class="header__title">{{ queryTitle }}</span>
            <div class="header-title-btns">
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                <el-button v-if="resetHidden" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button
                    v-if="queryConfig.seniorShow"
                    :icon="seniorShow ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
                    size="mini"
                    @click="seniorHandler"
                    >高级查询</el-button
                >
                <el-button v-if="queryConfig.importShow" icon="el-icon-upload2" size="mini" @click="handleImport"
                    >导入</el-button
                >
            </div>
        </div>
        <snbc-form :form="queryParams" :config="snbcFormConfig">
            <template slot="form-body">
                <el-col
                    v-for="(item, index) in queryItems"
                    v-show="index < 3 || !seniorShow"
                    :key="index"
                    class="form-item-height"
                    :span="8"
                >
                    <snbc-form-item :config="item" />
                </el-col>
            </template>
        </snbc-form>
    </div>
</template>
<script>
import SnbcForm from 'snbcCommon/components/snbc-form/SnbcForm.vue';
import SnbcFormItem from 'snbcCommon/components/snbc-form/SnbcFormItem.vue';

export default {
    name: 'SnbcTableQuery',
    components: {
        SnbcForm,
        SnbcFormItem
    },
    props: {
        /**
         * 查询参数对象
         */
        queryParams: {
            type: Object,
            default() {
                return {};
            }
        },
        /**
         * 查询区域表单配置
         */
        queryConfig: {
            type: Object,
            default() {
                return {
                    elFormAttrs: {},
                    items: [],
                    resetHidden: false,
                    seniorShow: false
                };
            }
        },
        /**
         * 查询区域标题
         */
        queryTitle: {
            type: String,
            default: '查询条件'
        }
    },
    data() {
        return {
            // el-form组件默认属性设置
            defaultElFromAttrs: {
                'size': 'small',
                'inline': true,
                'label-width': '100px'
            },
            // 高级查询判断
            seniorShow: false
        };
    },
    computed: {
        // SnbcForm组件配置
        snbcFormConfig() {
            return {
                elFormAttrs: this.elFormAttrs
            };
        },
        // ElForm组件应用属性
        elFormAttrs() {
            return {
                ...this.defaultElFromAttrs,
                ...(this.queryConfig.elFormAttrs || {})
            };
        },
        // 查询条件的表单项
        queryItems() {
            return (this.queryConfig.items || []).map((item, index) => {
                // 扩展表单项的配置
                return this.expandItem(item);
            });
        },
        // 是否隐藏 reset
        resetHidden() {
            return !this.queryConfig.resetHidden;
        }
    },
    watch: {
        'queryConfig.seniorShow': {
            handler: 'seniorShowHandler',
            immediate: true
        }
    },
    methods: {
        // 查询按钮操作
        handleQuery() {
            this.$emit('query');
        },
        // 重置按钮操作
        resetQuery() {
            this.$emit('reset');
        },
        // 不同表单项执行不同的配置扩展
        expandItem(item) {
            switch (item.component) {
                case 'SnbcFormInput':
                    return this.expandSnbcFormInput(item);
                default:
                    return this.expandSnbcFormCommon(item);
            }
        },
        // 扩展SnbcFormInput组件属性
        expandSnbcFormInput(item) {
            return {
                modelObj: this.queryParams,
                elFormItemAttrs: {
                    label: item.name,
                    ...(item.elFormItemAttrs || {})
                },
                enterHandler: this.handleQuery,
                ...item
            };
        },
        // 扩展组件属性
        expandSnbcFormCommon(item) {
            return {
                modelObj: this.queryParams,
                elFormItemAttrs: {
                    label: item.name,
                    ...(item.elFormItemAttrs || {})
                },
                label: item.name,
                ...item
            };
        },
        // 高级查询
        seniorHandler() {
            this.seniorShow = !this.seniorShow;
        },
        // 初始化 seniorShow
        seniorShowHandler(newVal) {
            this.seniorShow = newVal;
        },
        // 导入按钮操作
        handleImport() {
            this.$emit('import');
        }
    }
};
</script>
<style lang="scss" scoped>
.header {
    display: flex;
    margin: 0;
    flex-direction: column;
    align-items: normal;
}
.header-title {
    display: flex;
    flex-direction: row;
    align-items: center;
    .header__title {
        flex: 1;
    }
}
.header-title-btns {
    flex-shrink: 0;
}
.header__title {
    display: flex !important;
    align-items: center;
    &::before {
        display: inline-block;
        content: '';
        height: 14px;
        padding-left: 8px;
        border-left: 3px solid #3370ff;
    }
}
.query-area-form {
    ::v-deep .el-form-item {
        display: inline-flex;
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;
        .el-form-item__label {
            flex-shrink: 0;
        }
        .el-form-item__content {
            flex: 1;
            padding-right: 15px;
        }
        .el-date-editor {
            width: 100%;
        }
    }
    form {
        transition: all 1s ease;
    }
}
.form-item-height {
    height: 42px;
}
</style>
