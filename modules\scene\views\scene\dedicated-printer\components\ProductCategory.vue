<template>
    <!-- 产品分类图 -->
    <div class="product-category">
        <div
            v-for="(item, index) in config"
            :key="`category${index}`"
            :style="item.style"
            class="product-category-item"
        >
            <el-image :src="item.img" class="category-icon">
                <div slot="error"></div
            ></el-image>
            <span>{{ item.name }}</span>
            <el-image :src="arrowDownImg" class="arrow-down"></el-image>
            <span class="circle">
                <span></span>
            </span>
            <span class="left-arrow-tip" v-if="index === 0"></span>
            <span
                class="right-arrow-tip"
                v-if="index === config.length - 1"
            ></span>
        </div>
        <span class="arrow-icon left-arrow"></span>
        <span class="arrow-icon right-arrow"></span>
    </div>
</template>
<script>
import arrowDownImg from 'scene/assets/arrow.png';

export default {
    name: 'ProductCategory',
    props: {
        config: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            arrowDownImg
        };
    }
};
</script>
<style lang="scss" scoped>
.product-category {
    position: relative;
    display: flex;
    z-index: 1;
    margin: 40px 0 10px 0;
    .product-category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 30px;
        .category-icon {
            height: 60px;
            margin-bottom: 20px;
        }
        .arrow-down {
            margin-top: -10px;
        }
        .circle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: #185ae1;
            border-radius: 50%;
            span {
                width: 30px;
                height: 30px;
                background-color: #ffffff;
                border-radius: 50%;
            }
        }
        .left-arrow-tip {
            width: 100%;
            text-align: left;
            height: 44px;
            margin-left: -20px;
        }
        .right-arrow-tip {
            width: 100%;
            text-align: right;
            height: 44px;

            margin-right: -20px;
        }
    }
}
.product-category::before {
    position: absolute;
    bottom: 62px;
    content: '';
    width: 99%;
    height: 18px;
    background: linear-gradient(
        90deg,
        rgba(179, 204, 255, 0.29) 0%,
        rgba(89, 167, 255, 1) 46.1%,
        rgba(14, 83, 222, 1) 100%
    );
    z-index: -1;
}
.arrow-icon {
    position: absolute;
    bottom: 46px;
    height: 50px;
    width: 50px;
    background-color: #185ae1;
}
.arrow-icon.left-arrow {
    left: -30px;
    clip-path: polygon(100% 0%, 50% 50%, 100% 100%);
}
.arrow-icon.right-arrow {
    right: -25px;
    clip-path: polygon(0% 0%, 50% 50%, 0% 100%);
}
</style>
