<template>
    <div class="zandaoModal">
        <el-dialog :title="title" width="60%" :visible.sync="dialogVisible">
            <el-form
                ref="form"
                :model="form"
                label-width="140px"
                :rules="formRules"
            >
                <el-form-item label="项目名称">{{
                    form.projectName
                }}</el-form-item>
                <el-form-item label="当前流程金额"
                    >￥{{
                        form.cacluteAmount &&
                        form.cacluteAmount.toLocaleString()
                    }}</el-form-item
                >
                <el-form-item
                    :label="
                        title === '样机试制备料申请'
                            ? '转销售的样机成本'
                            : '关联流程金额'
                    "
                    prop="associationFlowAmount"
                    ><el-input
                        v-model="form.associationFlowAmount"
                        v-if="!form.associationFlowId"
                    >
                        <div slot="prefix" class="money-prefix">
                            ￥
                        </div></el-input
                    >
                    <div v-else>
                        ￥{{
                            form.associationFlowAmount &&
                            form.associationFlowAmount.toLocaleString()
                        }}
                    </div>
                </el-form-item>
                <el-form-item label="备注" prop="notes">
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 5 }"
                        v-model="form.notes"
                        v-if="!form.associationFlowId"
                    >
                    </el-input>
                    <div v-else>{{ form.notes }}</div>
                </el-form-item>
            </el-form>
            <div
                class="confirm-button"
                v-if="!form.associationFlowId && hasSampleCostCheckPermission"
            >
                <el-button type="primary" @click="confirm">确认扣减</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
// 校验规则
const longLengthLimit = {
    min: 0,
    max: 500,
    message: '最多输入500个字符',
    trigger: 'change'
};
const validateAmount = (rule, value, callback) => {
    const regex = /^\d{1,8}(\.\d{1,2})?$/;
    if (value === '' || value === null || value === undefined) {
        callback(new Error('请输入转销售的样机成本'));
    } else if (!regex.test(value) || parseFloat(value) <= 0) {
        callback(new Error('请输入有效的正数金额，最多8位整数与两位小数'));
    } else {
        callback();
    }
};
const formRules = {
    associationFlowAmount: [{ validator: validateAmount, trigger: 'blur' }],
    notes: [{ ...longLengthLimit }]
};
export default {
    name: 'SampleCostDetail',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            form: {},
            formRules
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        title() {
            return this.data.title;
        },
        hasSampleCostCheckPermission() {
            return this.$store.state.permission.btnDatas.includes(
                'sampleCostCheck'
            );
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                // 清除校验
                this.$refs.form && this.$refs.form.clearValidate();
                this.form = this.data;
            }
        }
    },
    methods: {
        confirm() {
            let formValid = false;
            this.$refs.form.validate((valid) => {
                formValid = valid;
            });
            if (!formValid) {
                return;
            }

            const params = {
                cacluteAmount: Number(this.form.associationFlowAmount),
                flowId: this.form.flowId,
                flowName: '内部样机转销售扣减',
                notes: this.form.notes,
                projectManager: this.form?.projectManager,
                projectNumber: this.form?.projectNumber,
                productLine: this.form.productLine,
                subProductLine: this.form.subProductLine,
                costType: this.form.costType,
                projectStage: this.form.projectStage
            };
            this.$emit('sample-cost-confirm', params);
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.info {
    justify-content: space-between;
    margin-bottom: 20px;
}

.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
}
.money-prefix {
    height: 100%;
    display: grid;
    place-items: center;
    font-size: 16px;
}
::v-deep .el-form-item__label {
    font-weight: 600 !important;
}
</style>
