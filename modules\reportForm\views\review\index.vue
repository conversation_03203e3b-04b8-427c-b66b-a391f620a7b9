<template>
    <div class="review-container">
        <el-tabs class="tabs" v-model="activeName">
            <el-tab-pane
                label="信息平台执行"
                name="infoPlatform"
                v-if="infoPlatformPermission"
            >
                <InfoPlatform :activeName="activeName"></InfoPlatform>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import InfoPlatform from './components/InfoPlatform';

export default {
    name: 'ReportFormReview',
    components: {
        InfoPlatform
    },
    data() {
        return {
            activeName: 'infoPlatform',
            infoPlatformPermission:
                this.$store.state.permission.btnDatas.includes(
                    'ReviewInfoPlatformTab'
                )
        };
    }
};
</script>

<style scoped lang="scss">
.review-container {
    height: 100vh;
    overflow-y: auto;
    padding: 10px 16px;
    .tabs {
        height: 100%;
    }
}
</style>
