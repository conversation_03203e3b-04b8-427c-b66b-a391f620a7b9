<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-date-picker
            v-model="config.modelObj[config.modelKey]"
            v-bind="elDatePickerAttrs"
        />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormDatePicker',
    props: {
        /**
         * SnbcFormDatePicker组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elDatePickerAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-date-picker组件默认属性设置
            defaultElDatePickerAttrs: {
                'type': 'date',
                'placeholder': '请选择',
                'value-format': 'yyyy-MM-dd HH:mm:ss'
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-date-picker组件应用属性
        elDatePickerAttrs() {
            return {
                ...this.defaultElDatePickerAttrs,
                ...(this.config.elDatePickerAttrs || {})
            };
        }
    },
    methods: {}
};
</script>
