/* eslint-disable max-lines-per-function */

const getDetail = (scope) => {
    return (row) => {
        scope.$router.push({
            path: '/project/finance',
            query: {
                id: row.projectId,
                productLine: row.productLine
            }
        });
    };
};
const elButtonFn = () => {
    // 去除button的padding，设置max-width，否则tooltip与省略号不会正常显示
    return {
        type: 'text',
        style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align:left; padding:0px!important; max-width:100%;'
    };
};
/**
 * 获取维护项目与开发项目不同部分的配置
 * @param {*} scope this
 * @param {Boolean} isMaintain 是否为维护项目
 * @returns {Array} 配置
 */
const getDiffConfig = (scope, isMaintain) => {
    let config;
    if (isMaintain) {
        config = [
            {
                label: '产品线',
                prop: 'productLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '细分产品线',
                prop: 'subProductLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目经理',
                prop: 'projectManager',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            }
        ];
    } else {
        config = [
            {
                label: '项目名称',
                prop: 'projectName',
                show: true,
                minWidth: 180,
                renderMode: 'button',
                elButtonAttrsFn: elButtonFn,
                handleClick: getDetail(scope),
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '项目编号',
                prop: 'projectNo',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '产品线',
                prop: 'productLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '细分产品线',
                prop: 'subProductLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            }
        ];
    }
    return config;
};

// 项目月度费用汇总表
const summary = (scope, isMaintain) => {
    return [
        {
            label: '核算月份',
            prop: 'month',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center'
            }
        },
        ...getDiffConfig(scope, isMaintain),
        {
            label: '费用',
            prop: 'costAmount',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                'sortable': 'custom',
                'resizable': false,
                'header-align': 'center',
                'align': 'right'
            },
            slot: 'cost'
        }
    ];
};
// 项目月度费用科目分项表
const subject = (scope, isMaintain) => {
    return [
        {
            label: '核算月份',
            prop: 'month',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center'
            }
        },
        ...getDiffConfig(scope, isMaintain),
        {
            label: '费用科目',
            prop: 'costSubject',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '费用',
            prop: 'costAmount',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                'sortable': 'custom',
                'resizable': false,
                'header-align': 'center',
                'align': 'right'
            },
            slot: 'cost'
        }
    ];
};
/**
 * 获取表头数据
 * @param {Number} index 0:项目月度费用汇总表 1:项目月度费用科目分项表
 * @param {*} scope this
 * @param {Boolean} isMaintain 是否是维护项目
 * @returns {Array} 表头配置
 */
const getStatusArray = (index, scope, isMaintain = false) => {
    const statusMap = [summary(scope, isMaintain), subject(scope, isMaintain)];
    return statusMap[index];
};
export { getStatusArray };
