// 校验规则
const shortLengthLimit = {
    min: 0,
    max: 145,
    message: '最多输入145个字符',
    trigger: ['change', 'blur']
};
const longLengthLimit = {
    min: 0,
    max: 2000,
    message: '最多输入2000个字符',
    trigger: ['change', 'blur']
};

const required = {
    required: true,
    // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
    message: ' ',
    trigger: ['change', 'blur']
};
// 表单校验配置
export const rules = {
    // 短文本字段 (使用required和shortLengthLimit)
    riskTitle: [required, shortLengthLimit],
    riskStatus: [required],
    projectManager: [required],
    riskAssObjectType: [required],
    assProjectId: [required],
    assObjectId: [required],
    productModel: [required, shortLengthLimit],
    customerName: [required, shortLengthLimit],
    productLine: [required],
    subProductLine: [required],
    riskIdentifyDate: [required],
    riskLevel: [required],
    riskType: [required],
    planFinishDate: [required],
    responsiblePersonAccountList: [required],
    riskPlanType: [required],
    riskSupportType: [required],
    finishDate: [required],

    // 长文本字段 (使用required和longLengthLimit)
    riskDesc: [required, longLengthLimit],
    solutionMeasures: [required, longLengthLimit]
};
