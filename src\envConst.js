/* eslint-disable no-shadow */

// 网关列表 包含该网关下的服务列表。使用时根据网关+服务来使用
const getWayList = [
    {
        // key值，使用时key用这个
        getWayKey: 'bossapi',
        // 具体的值
        getWayValue: 'api',
        services: {
            system: 'bbpfsystem',
            pmService: 'pmis',
            messagecenter: 'messagecenter',
            reportService: 'report'
        }
    }
];
// 服务根url对象，包含：根域名+网关+服务
let basePath = null;
// mock服务地址
const mockPath = 'http://dev-pmis.xtjc.net';
// const mockPath = 'http://whpmis-test.xtjc.net';

// 初始化服务路径，规则是取得当前url路径，解析出host，然后拼接网关，并存储到对象里。获取时：xxx.网关名称.服务名称
const initBaseUrl = (basePaths) => {
    const { hostname } = window.location;
    const { origin } = window.location;
    const basePathsObj = {};
    // eslint-disable-next-line array-callback-return
    basePaths.map((basePath) => {
        basePathsObj[basePath.getWayKey] = {};
        for (const serviceName in basePath.services) {
            if (hostname === '127.0.0.1' || hostname === 'localhost') {
                basePathsObj[basePath.getWayKey][
                    serviceName
                ] = `${mockPath}/${basePath.getWayValue}/${basePath.services[serviceName]}`;
            } else {
                basePathsObj[basePath.getWayKey][
                    serviceName
                ] = `${origin}/${basePath.getWayValue}/${basePath.services[serviceName]}`;
            }
        }
    });
    basePath = basePathsObj;
    return basePath;
};

// 构造函数，参数可以传getWay数组进来，会追加到变量getWayList里
const basePathInit = (list) => {
    if (list && Array.isArray(list) && list.length > 0) {
        for (let index = 0; index < list.length; index++) {
            const element = list[index];
            if (element) {
                getWayList.push(element);
            }
        }
    }
    return initBaseUrl(getWayList);
};

export { basePathInit };
