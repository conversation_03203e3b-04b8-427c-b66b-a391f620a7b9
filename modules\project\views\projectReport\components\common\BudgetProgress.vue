<template>
    <div class="budget-progress">
        <el-progress
            :percentage="computedPercentage"
            :stroke-width="strokeWidth"
            :text-inside="true"
            :text-color="textColor"
            :color="color"
            :format="format"
            :style="{ width: widthValue }"
        ></el-progress>
    </div>
</template>

<script>
export default {
    name: 'BudgetProgress',
    props: {
        percentage: {
            type: Number,
            default: 0,
            validator: (value) => value >= 0
        },
        strokeWidth: {
            type: Number,
            default: 16
        },
        color: {
            type: String,
            default: '#13ce66'
        },
        width: {
            type: [Number, String],
            default: 120
        }
    },
    computed: {
        textColor() {
            return this.percentage <= 20 ? '#000' : '#fff';
        },
        computedPercentage() {
            return this.percentage > 100 ? 100 : this.percentage;
        },
        widthValue() {
            if (typeof this.width === 'number') {
                return `${this.width}px`;
            }
            return this.width;
        }
    },
    methods: {
        format() {
            return `${this.percentage}%`;
        }
    }
};
</script>

<style lang="scss" scoped>
.budget-progress {
    display: flex;
    align-items: center;
}
</style>
