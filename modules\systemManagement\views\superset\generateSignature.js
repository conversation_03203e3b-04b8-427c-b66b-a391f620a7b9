import {
    randomStr,
    randomTime,
    objKeySort
} from 'wtf-core-vue/src/methods/signature';
import { encryptByMd5 } from 'wtf-core-vue/src/utils/crypto';
/**
 * 生成url参数前面字符串
 * @param {object} config axois 请求配置信息
 * @returns {string} 生成的前面字符串
 */
function generateSignature(config) {
    //  在headers 中添加验签
    const objStr = {
        // 13位随机数字或字母
        randomChart: randomStr(13),
        // 13位时间戳
        randomTimeStr: randomTime(),
        // 固定字符串
        fixedStr: 'axeonmc',
        // 请求方式
        method: config.method.toUpperCase(),
        // 请求的接口
        url: config.url.split('?')[0]
    };
    /** * 获取url中的参数 ** */
    const currentUrl = config.url;
    const urlParams = {};
    //  如果url中带参数,则转为对象形式
    const index = currentUrl.indexOf('?');
    if (index !== -1) {
        const urlParamsStr = currentUrl.substring(index + 1);
        const urlParamsArr = urlParamsStr.split('&');
        urlParamsArr.forEach((item) => {
            const arr = item.split('=');
            const key = arr.shift();
            const value = arr.join('=');
            urlParams[key] = value;
        });
    }
    // 对请求的参数进行排序
    let params = '';
    if (config.params) {
        params = objKeySort(urlParams, config.params);
    } else if (config.data) {
        params = objKeySort(urlParams, config.data);
    } else if (Object.keys(urlParams).length > 0) {
        params = objKeySort({}, urlParams);
    }
    // 请求的参数拼接出的字符串
    objStr.paramsStr = '';
    if (params) {
        for (const key in params) {
            if (!Object.prototype.hasOwnProperty.call(params, key)) {
                continue;
            }

            // 如果value值是数组，则转为json串
            if (
                Object.prototype.toString.call(params[key]) ===
                    '[object Array]' ||
                Object.prototype.toString.call(params[key]) ===
                    '[object Object]'
            ) {
                params[key] = JSON.stringify(params[key]);
            }
            objStr.paramsStr = `${objStr.paramsStr}&${key}=${params[key]}`;
        }
    }

    // 最终拼接出的字符串
    const finalStr = `${objStr.randomChart}&${objStr.randomTimeStr}${objStr.fixedStr}${objStr.paramsStr}&${objStr.url}&${objStr.method}`;
    const signature =
        objStr.randomChart + objStr.randomTimeStr + encryptByMd5(finalStr);
    return signature;
}
export default generateSignature;
