<template>
    <table>
        <tbody>
            <tr>
                <td
                    :colspan="
                        datasource.children && datasource.children.length
                            ? datasource.children.length * 2
                            : null
                    "
                >
                    <div
                        class="node"
                        :id="datasource.id"
                        @click.stop="handleClick(datasource)"
                    >
                        <slot :node-data="datasource">
                            <div
                                class="title"
                                :style="{
                                    'background-color':
                                        datasource.backgroundColor ||
                                        'rgba(217, 83, 79, 0.8)',
                                    'color': datasource.color || '#fff',
                                    'border': datasource.borderColor
                                        ? `1px solid ${datasource.borderColor}`
                                        : 'none'
                                }"
                            >
                                <i
                                    v-if="
                                        datasource.showIcon === false
                                            ? false
                                            : true
                                    "
                                    class="fa fa-solid fa-user symbol"
                                    :style="{
                                        color: datasource.iconColor || 'inherit'
                                    }"
                                ></i>
                                <div
                                    :style="{
                                        'margin-right':
                                            datasource.showIcon === false
                                                ? 0
                                                : '18px'
                                    }"
                                >
                                    {{ datasource.name }}
                                </div>
                            </div>
                        </slot>
                    </div>
                </td>
            </tr>
            <template v-if="datasource.children && datasource.children.length">
                <tr class="lines">
                    <td
                        v-if="datasource.children.length > 1"
                        :colspan="datasource.children.length * 2"
                    >
                        <div class="downLine"></div>
                    </td>
                </tr>
                <tr class="lines">
                    <td class="rightLine"></td>
                    <template v-for="n in datasource.children.length - 1">
                        <td class="leftLine topLine"></td>
                        <td class="rightLine topLine"></td>
                    </template>
                    <td class="leftLine"></td>
                </tr>
                <tr class="nodes">
                    <td
                        colspan="2"
                        v-for="child in datasource.children"
                        :key="child.id"
                    >
                        <node :datasource="child" :handle-click="handleClick">
                            <template
                                v-for="slot in Object.keys($scopedSlots)"
                                :slot="slot"
                                slot-scope="scope"
                            >
                                <slot :name="slot" v-bind="scope" />
                            </template>
                        </node>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
export default {
    name: 'node',
    props: {
        datasource: Object,
        handleClick: Function
    },
    methods: {}
};
</script>

<style></style>
