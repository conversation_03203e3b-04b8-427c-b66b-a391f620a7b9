<template>
    <div>
        <el-descriptions v-bind="elDescriptionsAttrs">
            <el-descriptions-item
                v-for="(item, index) in items"
                :key="index"
                :label="item.label"
                v-bind="item.handleElDescItemLabelAttrs ? item.handleElDescItemLabelAttrs(item) : {}"
            >
                <template slot="label">
                    {{ item.label }}
                    <el-tooltip v-if="item.tip" class="item" placement="top">
                        <i class="el-icon-question" style="color: #3370ff"></i>
                        <div slot="content">
                            <p>{{ item.tip }}</p>
                        </div>
                    </el-tooltip>
                </template>
                <el-link v-if="item.type === 'image'" type="primary" @click="handleViewImage(item.value)">
                    {{ item.value ? '查看图片' : '' }}
                </el-link>
                <template v-else>
                    <span class="item-content" :title="item.value">{{ item.value }}</span>
                </template>
            </el-descriptions-item>
        </el-descriptions>
        <snbc-image-viewer ref="snbcImageViewerRef" />
    </div>
</template>
<script>
import SnbcImageViewer from 'snbcCommon/components/snbc-image-viewer/SnbcImageViewer.vue';

export default {
    name: 'SnbcDescriptions',
    components: {
        SnbcImageViewer
    },
    props: {
        items: {
            type: Array,
            default() {
                return [];
            }
        },
        config: {
            type: Object,
            default() {
                return {
                    elDescriptionsAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // 默认配置
            defaultElDescriptionsAttrs: {
                title: '',
                size: 'small',
                border: true,
                column: 2
            }
        };
    },
    computed: {
        // 组件属性
        elDescriptionsAttrs() {
            return {
                ...this.defaultElDescriptionsAttrs,
                ...(this.config.elDescriptionsAttrs || {})
            };
        }
    },
    methods: {
        // 图片预览
        handleViewImage(value) {
            if (value) {
                const srcList = value.split(',');
                this.$refs.snbcImageViewerRef.show(srcList[0], srcList);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.item-content {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2; //控制行数
    -webkit-box-orient: vertical;
}
</style>
