<template>
    <div class="container">
        <div class="container-top">
            <el-select
                v-model="firstDepartvalue"
                filterable
                placeholder="请选择一级部门"
                @change="handleFirstDepartChange"
                class="onelevel"
            >
                <el-option
                    v-for="item in firstDepart"
                    :key="item.orgCode"
                    :label="item.orgName"
                    :value="item.orgCode"
                    filterable
                >
                </el-option>
            </el-select>
        </div>
        <el-tree
            ref="orgTree"
            for="orgTree"
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            :current-node-key="currentNodeKey"
            default-expand-all
            highlight-current
            node-key="orgId"
            draggable
            :empty-text="$t('systemManagement.logger.noData')"
            @node-click="handleNodeClick"
        >
        </el-tree>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';

const { TECH_CENTER_CODE } = CONSTANTS;
// 事件名称定义
const constEventName = {
    TREE_NODE_CLICK: 'org-node-click',
    TREE_EDIT_FINISH: 'org-edit-finish'
};

export default {
    props: {
        isEnable: {
            type: Boolean,
            dafault: false
        }
    },
    data() {
        return {
            firstDepart: [],
            firstDepartvalue: '',
            // 树结构数据
            treeData: [],
            groupLeaderData: [],
            groupMembersData: [],
            // 部门ID
            departmentCode: '',
            passData: {},
            editType: '',
            teamId: '',
            isSave: false,
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            // 当前选中的key
            currentNodeKey: ''
        };
    },
    created() {
        this.getfirstDepart();
    },
    methods: {
        // 获取一级部门下拉列表
        getfirstDepart() {
            const data = {
                orgCode: TECH_CENTER_CODE
            };
            this.$service.department.group.getSubDepartment(data).then((res) => {
                if (res.head.code === '000000') {
                    this.firstDepart = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        // 选择一级部门
        handleFirstDepartChange() {
            this.getOrgTree(this.firstDepartvalue);
            this.$emit('depart-change-click');
        },
        // 获取机构树
        getOrgTree(value) {
            const params = {
                orgCode: value
            };
            this.$service.department.group.getSubDepartment(params).then((res) => {
                if (res.head.code === '000000') {
                    this.treeData = res.body;
                    if (!this.currentNodeKey && this.treeData && this.treeData.length > 0) {
                        this.currentNodeKey = this.treeData[0].orgId || '';
                        this.$emit(constEventName.TREE_NODE_CLICK, this.treeData[0]);
                    }
                    this.$nextTick(() => {
                        this.$refs.orgTree.setCurrentKey(this.currentNodeKey);
                        const curNode = this.$refs.orgTree.getCurrentNode();
                        this.$emit(constEventName.TREE_EDIT_FINISH, curNode);
                    });
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        async handleNodeClick(node, nodeValue) {
            try {
                if (nodeValue.level === 1) {
                    this.departNmaeLevel(node);
                    this.$emit('level-one-click');
                } else {
                    this.$emit('level-two-click', node);
                }
            } catch (error) {
                console.error('操作失败', error);
            }
        },
        // 获取部门下成员信息
        departNmaeLevel(data) {
            const params = {
                orgCode: data.orgCode,
                isAll: 0
            };
            this.$service.department.group.getDepartName(params).then((res) => {
                if (res.head.code === '000000') {
                    const modifiedData = res.body.map((item) => ({
                        orgCode: item.loginName,
                        orgName: item.employeeName
                    }));
                    // 获取树节点对象
                    const target = this.treeData.find((item) => item.orgCode === data.orgCode);
                    this.$set(target, 'children', modifiedData);
                } else {
                    this.$message.error(res.head.message);
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.container-top {
    width: 100%;
    height: 45px;
    margin-bottom: 5px;
}
.onelevel {
    width: 100%;
    height: 100%;
}
</style>
