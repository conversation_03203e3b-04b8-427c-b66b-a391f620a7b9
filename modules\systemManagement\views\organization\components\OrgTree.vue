<template>
    <div class="container">
        <el-input v-model="filterText" for="ms9" type="text" suffix-icon="fa fa-search" :placeholder="$t('systemManagement.organization.dialog.placeholder.msg9')" autocomplete="text" />
        <el-tree v-show="showTree" ref="orgTree" for="orgTree" :data="treeData" :props="defaultProps" :expand-on-click-node="false" :current-node-key="currentNodeKey" default-expand-all highlight-current node-key="orgId" draggable :allow-drag="allowDrag" :filter-node-method="filterOrg" :empty-text="$t('systemManagement.logger.noData')" @node-click="handleNodeClick" @node-drop="handleDrop">
            <el-tooltip slot-scope="{ node, data }" :content="node.label" placement="right">
                <span class="block">
                    <span class="block__text">{{
            node.label
          }}</span>
                    <span class="block__btns">
                        <el-button v-permission="['org_new_department']" type="text" size="mini" icon="fa fa-plus-circle" @click.stop="addOrg(data)" />
                        <el-button v-if="node.key !== '0'" v-permission="['org_edit_department']" type="text" size="mini" icon="fa fa-pencil" @click.stop="editOrg(data)" />
                        <el-button v-if="node.key !== '0'" v-permission="['org_delete_department']" type="text" size="mini" icon="fa fa-trash-o" @click.stop="delOrg(node, data)" />
                    </span>
                </span>
            </el-tooltip>
        </el-tree>
        <!-- 拖拽部门 -->
        <el-dialog :title="$t('common.tips')" :visible.sync="dragOrgDialogVisible" width="30%">
            <span><b>{{ dragOrgItem.draggingNode.label }}</b> 移动到
                <b>{{ dragOrgItem.dropNode.label }}</b> 的
                <b>{{ dragOrgItem.dropType | dropTypeName }}</b>，请确认是否更改？</span>
            <span slot="footer">
                <el-button @click="handleDropCancel">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="postOrgSort">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>
        <org-tree-edit-info v-if="editInfo.isShow" :show.sync="editInfo.isShow" :edit="editInfo.isEdit" :form-data="editInfo.formData" @view-save-finish="saveFinish" />
    </div>
</template>

<script>
import OrgTreeEditInfo from './OrgTreeEditInfo.vue';

// 事件名称定义
const constEventName = {
    TREE_NODE_CLICK: 'org-node-click',
    TREE_EDIT_FINISH: 'org-edit-finish'
};

export default {
    components: { OrgTreeEditInfo },
    filters: {
        dropTypeName(val) {
            let name;
            switch (val) {
                case 'before':
                    name = '前面';
                    break;
                case 'inner':
                    name = '里面';
                    break;
                default:
                    name = '后面';
                    break;
            }
            return name;
        }
    },
    props: {
        // 组织机构源数据,用于给其他组件提供数据源。本组件不使用此数据
        orgSourceData: {
            type: Array,
            default() {
                return [];
            }
        },
        isEnable: {
            type: Boolean,
            dafault: false
        }
    },
    data() {
        return {
            // 树结构数据
            treeData: [],
            // 用于树拖拽还原
            treeDataBackup: [],
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            // 拖拽确认提示窗口，显示标识
            dragOrgDialogVisible: false,
            dragOrgItem: {
                draggingNode: '',
                dropNode: '',
                dropType: ''
            },
            filterText: '',
            // 当前选中的key
            currentNodeKey: '',
            // 编辑操作相关数据
            editInfo: {
                // 是否显示窗口
                isShow: false,
                // 窗口模式，true：编辑，false：新增
                isEdit: false,
                // 编辑数据,一般由组件进行重写
                formData: {
                    orgName: '',
                    parentId: '',
                    orgDesc: '',
                    supervisor: '',
                    supervisorPhone: '',
                    orgId: '',
                    oldParam: {}
                }
            },
            // 权限树loading
            treeLoading: false,
            showTree: false
        };
    },
    watch: {
        filterText(val) {
            this.$refs.orgTree.filter(val);
        },
        isEnable: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.showTree = true;
                }
            }
        }
    },
    created() {
        this.getOrgTree();
    },
    methods: {
        // 获取机构树
        getOrgTree() {
            this.treeLoading = true;
            const data = {
                // 返回的组织机构层级 0：全部 1：根节点下一级 2：根节点下两级
                orgLevel: 0 
            };
            this.$service.systemManagement
                .getOrgTree(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.treeLoading = false;
                        this.treeData = res.body;
                        // 深度copy数据
                        this.treeDataBackup = JSON.parse(
                            JSON.stringify(this.treeData)
                        );
                        this.$emit(
                            'update:orgSourceData',
                            JSON.parse(JSON.stringify(this.treeData))
                        );
                        if (
                            !this.currentNodeKey &&
                            this.treeData &&
                            this.treeData.length > 0
                        ) {
                            this.currentNodeKey = this.treeData[0].orgId || '';
                            this.$emit(
                                constEventName.TREE_NODE_CLICK,
                                this.treeData[0]
                            );
                        }
                        this.$nextTick(() => {
                            this.$refs.orgTree.setCurrentKey(
                                this.currentNodeKey
                            );

                            const curNode = this.$refs.orgTree.getCurrentNode();
                            this.$emit(
                                constEventName.TREE_EDIT_FINISH,
                                curNode,
                                this.editInfo.isEdit
                            );
                            this.$refs.orgTree.filter(this.filterText);
                        });
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch(() => {
                    this.treeLoading = false;
                });
        },
        // 拖拽组织机构 - 确认
        postOrgSort() {
            const params = {
                thisNodeId: this.dragOrgItem.draggingNode.data.orgId,
                targetNodeId: this.dragOrgItem.dropNode.data.orgId,
                parentId: this.dragOrgItem.dropNode.data.parentId,
                sortType: this.transferName(this.dragOrgItem.dropType)
            };
            this.treeLoading = true;
            this.$service.systemManagement
                .putOrgSort(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.dragOrgDialogVisible = false;
                        this.getOrgTree();
                    } else {
                        this.$message.error(res.head.message);
                        this.getOrgTree();
                    }
                    this.treeLoading = false;
                })
                .catch((e) => {
                    this.dragOrgDialogVisible = false;
                    this.treeOrgRestore();
                });
        },
        // 删除组织机构 - 确认
        postDeleteOrg(data) {
            this.$service.systemManagement.deleteOrg(data).then((res) => {
                if (res.head.code === '000000') {
                    this.getOrgTree();

                    // 解决bug：55448，删除后列表记录的id未和对应选择项关联
                } else if(res.head.code === '991107') {
                    this.$message({
                        type: 'error',
                        message: this.$t('systemManagement.msg.deleteFailed_1')
                    });
                }else{
                    const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                }
            });
        },
        filterOrg(value, data) {
            if (!value) {
                return true;
            }
            return (data.orgName || '').includes(value.trim());
        },
        // 判断节点能否被拖拽
        allowDrag(draggingNode) {
            // 根节点是不允许被拖拽的
            return draggingNode.label.indexOf('组织机构') === -1;
        },
        // 恢复拖拽数据
        treeOrgRestore() {
            this.treeData = JSON.parse(JSON.stringify(this.treeDataBackup));
        },
        // 获取机构组织下的 - 用户列表数据
        handleNodeClick(node, nodeValue, obj) {
            // 获取该组织机构下的用户列表
            this.currentNodeKey = node.orgId;
            this.$emit(constEventName.TREE_NODE_CLICK, node);
        },
        // 拖拽成功完成时触发的事件
        handleDrop(draggingNode, dropNode, dropType, ev) {
            this.dragOrgItem = {
                draggingNode,
                dropNode,
                dropType
            };
            this.dragOrgDialogVisible = true;
        },
        // 拖拽取消事件
        handleDropCancel() {
            this.dragOrgDialogVisible = false;
            this.treeOrgRestore();
        },

        // 转换位置关系 before、inner、after <==> prev、inner、next
        transferName(name) {
            let transfer;
            switch (name) {
                case 'before':
                    transfer = 'prev';
                    break;
                case 'inner':
                    transfer = name;
                    break;
                case 'after':
                    transfer = 'next';
                    break;
                default:
                    break;
            }
            return transfer;
        },
        // 增加部门
        addOrg(data) {
            // 清空上次数据
            Object.assign(
                this.editInfo.formData,
                this.$options.data().editInfo.formData
            );
            this.editInfo.formData.parentId = data.orgId;
            this.editInfo.formData.parentOrgName = data.orgName;

            this.editInfo.isEdit = false;
            this.editInfo.isShow = true;
        },
        // 编辑部门
        editOrg(data) {
            // 组装数据
            Object.keys(data).forEach((item) => {
                //  如果有相同的属性，则赋值
                if (Object.keys(this.editInfo.formData).indexOf(item) > -1) {
                    this.editInfo.formData[item] = data[item];
                }
            });
            this.editInfo.formData.supervisorName = data.supervisorName;
            this.editInfo.formData.oldParam = JSON.parse(
                JSON.stringify(this.editInfo.formData)
            );
            this.editInfo.isEdit = true;
            this.editInfo.isShow = true;
        },
        // 删除部门
        delOrg(node, data) {
            let msg = '';
            if (data && data.children && data.children.length > 0) {
                msg = `【${data.orgName}】${this.$t(
                    'systemManagement.organization.dialog.tipMsg.msg3'
                )}`;
            } else {
                msg = `${this.$t(
                    'systemManagement.organization.dialog.tipMsg.msg4'
                )}【${data.orgName}】${this.$t(
                    'systemManagement.organization.dialog.tipMsg.msg5'
                )}`;
            }
            this.$confirm(
                msg,
                this.$t('systemManagement.organization.dialog.tipMsg.msg6'),
                {
                    type: 'warning'
                }
            ).then(() => {
                const param = {
                    orgId: data.orgId,
                    orgName: data.orgName
                };
                this.currentNodeKey = data.parentId;
                this.postDeleteOrg(param);
            });
        },
        saveFinish() {
            this.getOrgTree();
        }
    }
};
</script>

<style lang="scss" scoped>
</style>
