<template>
    <div>
        <el-input ref="txtOrgName" v-model="treeSelectNode.orgName" v-bind="$attrs" readonly class="input" @input="inputChanage" @click.native="showDialog">
            <!-- <el-button slot="append" icon="el-icon-search" @click="showDialog" /> -->
            <i v-if="treeSelectNode.orgName !== ''" slot="suffix" class="el-icon-circle-close el-input__icon input__icon" @click.stop="inputClear" />
        </el-input>

        <el-dialog v-if="treeDialog.isShow" :title="$t('systemManagement.components.treeSelectOrg.title')" :visible.sync="treeDialog.isShow" class="tee-dialog">
            <span>
                <el-input v-model="filterText" :placeholder="
            $t(
              'systemManagement.components.treeSelectOrg.filterPlaceholder'
            )
          ">
                    <i slot="suffix" class="el-icon-search el-input__icon" />
                </el-input>

                <el-scrollbar class="tree-scrollbar" wrap-class="tree-scrollbar__wrap">
                    <el-tree ref="tree" :data="treeData" :filter-node-method="filterNode" :props="treeProps" node-key="orgId" current-node-key="selectOrgId" default-expand-all class="filter-tree" @node-click="treeNodeClick" />
                </el-scrollbar>
            </span>
            <span slot="footer">
                <el-button @click="closeDialog">{{
          $t("systemManagement.components.treeSelectOrg.cancel")
        }}</el-button>
                <el-button type="primary">{{
          $t("systemManagement.components.treeSelectOrg.confirm")
        }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'TreeSelectOrg',
    inheritAttrs: false,
    props: {
        // 当前选中的组织机构编号,双向绑定，用于默认选中
        orgId: {
            type: String,
            default: ''
        },
        // 当前选中组织机构名称,双向绑定，用于默认选中
        orgName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 搜索文本
            filterText: '',
            treeData: [],
            treeProps: {
                label: 'orgName',
                children: 'children'
            },
            // 组织机构树选中节点
            treeSelectNode: {
                orgId: '',
                orgName: '',
                orgCode: '',
                parentId: null
            },
            treeOrgName: '',
            treeDialog: {
                // 窗口是否显示
                isShow: false
            }
        };
    },
    computed: {
        // 选择组织机构Id
        selectOrgId: {
            get() {
                return this.orgId;
            },
            set(val) {
                this.$emit('update:orgId', val);
            }
        },
        // 选择组织机构名称
        selectOrgName: {
            get() {
                return this.orgName;
            },
            set(val) {
                this.$emit('update:orgName', val);
            }
        }
    },
    watch: {
        // 监控树过滤条件字符串
        filterText(val) {
            this.$refs.tree.filter((val || '').trim());
        }
    },
    methods: {
        // 获取组织机构数据
        getOrgTreeList() {
            this.$service.systemManagement
                // .getRoleOrgTree({ orgLevel: 0, orgId: "" })
                .getLogOrgTree()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.treeData = response.body || [];
                    } else {
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.role.message.queryListFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 输入框input回调事件，v-model绑定必须数据
        inputChanage({ target }) {
            this.$emit('input', target.checked);
        },
        // 情况选择的相关数据信息
        inputClear() {
            this.selectOrgId = '';
            this.selectOrgName = '';

            this.treeSelectNode.orgId = '';
            this.treeSelectNode.orgName = '';
        },
        // 点击输入框，弹出组织机构弹出
        showDialog() {
            this.treeDialog.isShow = true;
            this.getOrgTreeList();
        },
        // 关闭窗口
        closeDialog() {
            this.treeDialog.isShow = false;
        },
        // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏
        filterNode(value, treedata) {
            if (!value) return true;
            return treedata.orgName.indexOf(value) !== -1;
        },
        // tree 节点选择事件
        treeNodeClick(nodeData) {
            this.treeDialog.isShow = false;

            // 双向绑定属性赋值
            this.selectOrgId = nodeData.orgId;
            this.selectOrgName = nodeData.orgName;
            // copy当前选中的节点内容
            Object.assign(this.treeSelectNode, nodeData);

            // 当前组件点击的事件
            this.$emit('org-node-click', nodeData);
        }
    }
};
</script>

<style lang="scss" scoped>
.input {
    width: 100%;
}
.input__icon {
    cursor: pointer;
}

.tree-scrollbar {
    height: 300px;
    margin-top: 10px;
}
</style>
<style lang="scss">
.tree-scrollbar__wrap {
    overflow-x: hidden;
}
// .tee-dialog {
//     .el-dialog__body {
//         padding-top: 10px;
//     }
// }
</style>
