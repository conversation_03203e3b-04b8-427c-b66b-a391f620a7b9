<template>
    <div class="defect-list-container">
        <div class="flex">
            <CollapsibleSearchPanel
                :queryParams="queryParams"
                :queryConfig="queryConfig"
                :isDot="isDot"
                :navItems="navItems"
                v-model="navActiveName"
                @navChange="handleNavChange"
                @search="handleSearch"
                @reset="handleReset"
            >
            </CollapsibleSearchPanel>
        </div>
        <div class="content-area">
            <el-table :data="tableData" height="calc(100vh - 230px)">
                <el-table-column
                    label="序号"
                    prop="index"
                    align="center"
                    width="50"
                ></el-table-column>
                <el-table-column
                    prop="demandName"
                    label="需求名称"
                    align="center"
                    min-width="120"
                >
                    <template slot-scope="scope">
                        <el-tooltip
                            effect="dark"
                            :content="scope.row.demandName"
                            placement="top"
                            popper-class="maintananceProject-tooltip-width"
                        >
                            <a
                                @click="handleRoutingJump(scope.row)"
                                class="demandName-ellipsis"
                            >
                                {{ scope.row.demandName }}
                            </a>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                    label="OA发起时间"
                    align="center"
                    prop="applyTime"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="flowName"
                    label="OA流程"
                    align="center"
                    min-width="70"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    prop="customerName"
                    label="客户"
                    align="center"
                    min-width="70"
                    show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                    prop="productModel"
                    label="型号"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column
                    prop="projectManagerName"
                    label="项目经理"
                    align="center"
                    width="90"
                ></el-table-column>
                <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                        <div
                            class="flex flex-center gap-5"
                            style="height: 31px"
                        >
                            <!-- 关联禅道项目 -->
                            <el-popover
                                v-if="scope.row.proTaskId"
                                class="mt-5"
                                width="700"
                                trigger="hover"
                                @show="showZentaoTaskList(scope.row)"
                                @hide="hideZentaoTaskList"
                                :open-delay="300"
                            >
                                <ZentaoTaskList
                                    :row="scope.row"
                                    :show="isShowZentaoTaskList"
                                    :proProjectId="proProjectId"
                                    :proTaskId="proTaskId"
                                    :projectManagerAccount="
                                        projectManagerAccount
                                    "
                                ></ZentaoTaskList>
                                <svg-icon
                                    slot="reference"
                                    icon-class="maintenanceProject-creat-task"
                                    class="task-icon blue"
                                    @click="handleCreateTask(scope.row)"
                                />
                            </el-popover>
                            <el-tooltip
                                v-if="!scope.row.proTaskId"
                                effect="dark"
                                content="无关联禅道任务"
                                placement="top"
                            >
                                <svg-icon
                                    icon-class="maintenanceProject-creat-task"
                                    class="task-icon gray"
                                    @click="handleCreateTask(scope.row)"
                                />
                            </el-tooltip>
                            <!-- 新建风险 -->
                            <el-popover
                                v-if="scope.row.hasRisk === '是'"
                                class="mt-5"
                                width="700"
                                trigger="hover"
                                @show="() => riskListCounter++"
                            >
                                <RiskList
                                    :assObjectId="scope.row.id"
                                    :riskListCounter="riskListCounter"
                                    relatedObject="维护项目需求"
                                ></RiskList>
                                <svg-icon
                                    slot="reference"
                                    icon-class="maintenanceProject-creat-risk"
                                    class="risk-icon blue"
                                />
                            </el-popover>
                            <el-tooltip
                                v-if="scope.row.hasRisk === '否'"
                                effect="dark"
                                content="无风险"
                                placement="top"
                            >
                                <svg-icon
                                    icon-class="maintenanceProject-creat-risk"
                                    class="risk-icon gray"
                                    @click="handleCreateRisk(scope.row)"
                                />
                            </el-tooltip>
                            <!-- 编辑需求 -->
                            <svg-icon
                                v-permission="[
                                    'maintenanceProjectDemandUpdate'
                                ]"
                                icon-class="maintenanceProject-edit"
                                class="edit-icon"
                                @click="handleDemandEdit(scope.row)"
                            />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                class="pagination"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @pagination="queryParamsSelect"
            />
        </div>
        <!-- 新建/编辑需求弹窗 -->
        <DefectUpdateDialog
            :visible.sync="demandUpdateDialogVisible"
            :id="id"
            @success="queryParamsSelect"
        ></DefectUpdateDialog>
        <!-- 建立禅道任务弹窗 -->
        <TargetZentaoTaskDialog
            :visible.sync="targetZentaoTaskDialogVisible"
            :projectManager="projectManagerAccount"
            @save="createZentaoTask"
        ></TargetZentaoTaskDialog>
        <RiskUpdateDialog
            type="add"
            relatedObject="维护项目需求"
            :visible.sync="riskUpdateDialogVisible"
            :relatedObjectId="id"
            @success="queryParamsSelect"
        ></RiskUpdateDialog>
    </div>
</template>

<script>
import CollapsibleSearchPanel from 'Components/CollapsibleSearchPanel.vue';
import DefectUpdateDialog from './DemandUpdateDialog.vue';
import TargetZentaoTaskDialog from 'maintenanceProject/components/TargetZentaoTaskDialog/index.vue';
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskList from 'maintenanceProject/components/RiskList';
import RiskUpdateDialog from 'maintenanceProject/components/RiskUpdateDialog';
import { queryParams, queryConfig, navItems } from './formInit.js';

const oriQureryParams = {
    flowStatus: '',
    priorityLevel: '',
    customerName: '',
    productModel: '',
    hasRisk: '',
    demandName: ''
};

export default {
    name: 'DemandList',
    components: {
        CollapsibleSearchPanel,
        DefectUpdateDialog,
        TargetZentaoTaskDialog,
        RiskUpdateDialog,
        ZentaoTaskList,
        RiskList
    },
    props: {
        // 当前页签
        activeName: {
            type: String,
            default: ''
        },
        productLine: {
            type: String,
            default: ''
        },
        subProductLine: {
            type: String,
            default: ''
        },
        projectManager: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            total: 0,
            page: 1,
            limit: 50,
            tableData: [],
            navActiveName: 'OA流程未关闭的',
            navItems,
            // 顶部级联组件key
            projectSelectorKey: 0,
            // 建立禅道任务弹窗
            targetZentaoTaskDialogVisible: false,
            // 编辑需求弹窗
            demandUpdateDialogVisible: false,
            // 新建风险弹窗
            riskUpdateDialogVisible: false,
            // 新建/编辑需求以及建立禅道任务这三个按钮权限
            showDemandUpdatePermission:
                this.$store.state.permission.btnDatas.includes(
                    'maintenanceProjectDemandUpdate'
                ),
            // 新建风险按钮权限
            showRiskPermission: this.$store.state.permission.btnDatas.includes(
                'maintenanceProjectRiskUpdate'
            ),
            queryParams,
            queryConfig,
            // 是否正在查询
            isQuerying: false,
            // 当前的需求ID
            id: '',
            // 是否展示禅道任务列表
            isShowZentaoTaskList: false,
            // 项目经理域账号
            projectManagerAccount: '',
            // 当前选择的行的项
            rowData: {},
            riskListCounter: 0,
            proProjectId: '',
            proTaskId: ''
        };
    },
    computed: {
        isDot() {
            return (
                Object.values(this.queryParams).findIndex((i) => i !== '') !==
                -1
            );
        },
        // 产品型号下拉选项
        productModelOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.productModel) {
                    res.push(i.productModel);
                }
            });
            return [...new Set(res)].map((i) => ({
                value: i,
                label: i
            }));
        },
        // 客户名称下拉选项
        customerNameOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.customerName) {
                    res.push(i.customerName);
                }
            });
            return [...new Set(res)].map((i) => ({
                value: i,
                label: i
            }));
        }
    },
    watch: {
        productLine(newVal) {
            if (this.activeName === 'demandList') {
                this.queryParamsSelect();
            }
        },
        subProductLine(newVal) {
            if (this.activeName === 'demandList') {
                this.queryParamsSelect();
            }
        },
        projectManager(newVal) {
            if (this.activeName === 'demandList') {
                this.queryParamsSelect();
            }
        }
    },
    methods: {
        /**
         * 执行查询操作
         * @param {Object} form - 查询表单参数，默认使用this.queryParams
         */
        async handleQuery(form = this.queryParams) {
            if (!this.productLine) {
                this.$message.warning('请选择产品线或项目经理后查询');
                return;
            }
            if (this.isQuerying) return;
            this.isQuerying = true;
            let params = this.$tools.cloneDeep(form);
            if (!Array.isArray(params.faultLevelList)) {
                params.faultLevelList = params.faultLevelList
                    ? [params.faultLevelList]
                    : [];
            }
            params = {
                ...params,
                productLine: this.productLine,
                subProductLine: this.subProductLine,
                projectManager: this.projectManager,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件',
                pageSize: this.limit,
                currentPage: this.page
            };
            try {
                const api =
                    this.$service.maintenanceProject.demand.getDemandList;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                const { startRow, total } = res.body;
                this.total = total;
                this.tableData = res.body.list.map((item, index) => {
                    if (startRow) {
                        item.index = startRow + index;
                    }
                    return item;
                });
                this.queryConfig.items.find(
                    (i) => i.name === '产品型号'
                ).elOptions = this.productModelOptions;
                this.queryConfig.items.find(
                    (i) => i.name === '客户名称'
                ).elOptions = this.customerNameOptions;
            } catch (error) {
                console.error(error, 'error');
            } finally {
                this.isQuerying = false;
            }
        },
        /**
         * 根据当前选中的导航项执行查询
         */
        queryParamsSelect() {
            if (this.navActiveName) {
                const item = this.navItems.find(
                    (i) => i.name === this.navActiveName
                );
                this.handleNavChange(item);
            } else {
                this.handleQuery();
            }
        },
        /**
         * 跳转到详情页
         * @param  {Object} row 行数据
         */
        handleRoutingJump(row) {
            this.$router.push({
                path: 'maintenanceDemandDetail',
                query: {
                    demand_id: row.id
                }
            });
        },
        /**
         * 建立禅道任务
         * @param  {Object} row 行数据
         */
        handleCreateTask(row) {
            if (row.proProjectId || !this.showDemandUpdatePermission) return;
            this.rowData = row;
            this.id = row.id;
            this.projectManagerAccount = row.projectManager;
            this.targetZentaoTaskDialogVisible = true;
        },
        /**
         * 新建风险
         * @param  {Object} row 行数据
         */
        handleCreateRisk(row) {
            if (row.hasRisk === '是' || !this.showRiskPermission) return;
            this.id = row.id;
            this.riskUpdateDialogVisible = true;
        },
        /**
         * 编辑需求
         * @param {Object} row - 行数据
         */
        handleDemandEdit(row) {
            this.id = row.id;
            this.proTaskId = row.proTaskId;
            this.demandUpdateDialogVisible = true;
        },
        /**
         * 处理导航切换
         * @param {Object} item - 导航项数据
         */
        handleNavChange(item) {
            const form = this.$tools.cloneDeep(oriQureryParams);
            const { queryField, field } = item;
            form[queryField] = field;
            this.handleQuery(form);
        },
        /**
         * 处理搜索按钮点击
         */
        handleSearch() {
            this.navActiveName = '';
            this.handleQuery();
        },
        /**
         * 处理重置按钮点击
         */
        handleReset() {
            this.navActiveName = '';
            this.queryParams = this.$tools.cloneDeep(oriQureryParams);
            this.handleQuery();
        },
        /**
         * 创建禅道任务
         * @param {string} value - 项目ID
         */
        async createZentaoTask(value) {
            const api =
                this.$service.maintenanceProject.demand.createZentaoTask;
            const params = {
                ...this.rowData,
                proProjectId: value
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('创建成功');
                this.targetZentaoTaskDialogVisible = false;
                this.queryParamsSelect();
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 显示禅道任务列表
         * @param {Object} row - 行数据
         */
        showZentaoTaskList(row) {
            this.proProjectId = row.proProjectId;
            this.proTaskId = row.proTaskId;
            this.projectManagerAccount = row.projectManager;
            this.isShowZentaoTaskList = true;
        },
        /**
         * 隐藏禅道任务列表
         */
        hideZentaoTaskList() {
            this.isShowZentaoTaskList = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.flex-center {
    align-items: center;
    justify-content: center;
}
.gap-5 {
    gap: 5px;
}
.mt-5 {
    margin-top: 5px;
}
.gray {
    color: #bfbfbf;
}
.blue {
    color: #18a8f8;
}
.risk-icon {
    width: 22px;
    height: 22px;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.task-icon {
    width: 22px;
    height: 22px;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.edit-icon {
    width: 22px;
    height: 22px;
    color: #18a8f8;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.demandName-ellipsis {
    color: #3370ff;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-align: left;
}
.content-area {
    margin-top: 10px;
    .pagination {
        padding: 0px;
        margin-top: 10px;
    }
}

::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
<style>
.maintananceProject-tooltip-width.el-tooltip__popper {
    max-width: 500px;
}
</style>
