<template>
  <div class="container">
    <div class="view">
      <el-container>
        <el-aside>
          <permission-tree ref="treeObject" @nodeSelectedEvent="nodeSelectedHandler" @addNodeEvent="addNodeHandler" @updateNodeEvent="updateNodeHandler" @deleteNodeEvent="deleteNodeHandler" />
        </el-aside>
        <el-main>
          <div class="header">
            <div class="header__title">{{ $t('systemManagement.permission.permissionList') }}</div>
            <div class="header__btns">
              <el-button v-permission="['permission_manage_add']" icon="fa fa-plus-square-o" type="primary" @click="tableListAddHandler">
                {{ $t('common.add') }}
              </el-button>
              <el-button v-permission="['permission_manage_delete_multiple']" icon="fa fa-trash-o" @click="tableListDeleteHandler">
                {{ $t('common.delete') }}
              </el-button>
            </div>
          </div>
          <el-table
            id="permissionTable"
            :key="tableKey"
            :data="tableSourceList"
            border
            :header-cell-style="{background:'#F5F6FA'}"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="45" align="center" />
            <el-table-column :label="$t('systemManagement.permission.permissionName')" min-width="120px" prop="permissionName">
              <template slot-scope="{row}">
                <span>{{ row.permissionName }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('systemManagement.permission.permissionCode')" min-width="120px" prop="permissionCode">
              <template slot-scope="{row}">
                <span>{{ row.permissionCode }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('systemManagement.permission.routingUrl')" min-width="160px" prop="permissionCode">
              <template slot-scope="{row}">
                <span>{{ row.routingUrl }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('systemManagement.permission.permissionIcon')" width="100px">
              <template slot-scope="{row}">
                <i v-if="generateIconCode(row.permissionIcon) === 'el' || generateIconCode(row.permissionIcon) === 'fa'" :class="row.permissionIcon" class="table-content-icon" />
                <svg-icon v-if="generateIconCode(row.permissionIcon) === 'svg'" :icon-class="row.permissionIcon" class="table-content-icon" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('systemManagement.permission.permissionType')" width="100" prop="permissionType">
              <template slot-scope="{row}">
                <span>{{ row.permissionType | typeFilter($i18n) }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('systemManagement.permission.enableFlag')" class-name="status-col" width="130" prop="hasEnable">
              <template slot-scope="{row}">
                <el-switch v-model="row.hasEnable" v-permission="['permission_manage_enable']" class="captcha-img" :active-value="1" :inactive-value="0" @click.native="tableHasEnableChangeHandler(row)" />
              </template>
            </el-table-column>
            <el-table-column :label="$t('common.handle')" width="310" class-name="small-padding fixed-width" fixed="right">
              <template slot-scope="{row, $index}">
                <el-button v-permission="['permission_manage_edit']" icon="fa fa-pencil" class="margin-left" size="mini" type="primary" @click="tableItemUpdateHandler(row, $index)">{{ $t('common.edit') }}</el-button>
                <el-button v-permission="['permission_manage_delete']" icon="fa fa-trash-o" class="margin-left" size="mini" @click="tableItemDeleteHandler(row, $index)">{{ $t('common.delete') }}</el-button>
                <el-button v-show="+row.permissionType !== 1" v-permission="['permission_manage_permission_set']" class="margin-left" icon="fa fa-support" size="mini" type="primary" @click="tableItemSettingHandler(row, $index)">{{ $t('systemManagement.permission.scopeSetting') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-main>
      </el-container>
    </div>
    <tree-dialog :show-window.sync="treeDialogVisible" :dialog-status="treeDialogStatus" :processing-tree-item="processingTreeItem" @treeNeedUpdateEvent="updateAll" />
    <table-dialog :show-window.sync="tableDialogVisible" :dialog-status="tableDialogStatus" :processing-table-item="processingTableItem" @tableNeedUpdateEvent="updateAll" />
    <scope-setting :show-window.sync="scopeSettingVisible" :permission-id="processingTableItem.permissionId" :processing-table-item="processingTableItem" />
  </div>
</template>

<script>
import PermissionTree from './components/permissionTree';
import TreeDialog from './components/treeDialog';
import TableDialog from './components/tableDialog';
import ScopeSetting from './components/scopeSetting';

export default {
    name: 'Permission',
    components: { PermissionTree, TreeDialog, TableDialog, ScopeSetting },
    filters: {
        // 菜单类型，支持多语言
        typeFilter(type, i18n) {
            const typeMap = {
                1: i18n.t('systemManagement.permission.system'),
                2: i18n.t('systemManagement.permission.menu'),
                3: i18n.t('systemManagement.permission.button'),
                4: i18n.t('systemManagement.permission.interface')
            };
            return typeMap[type];
        }
    },
    data() {
        return {
            // 选中的树节点对象
            selectedItem: null,
            // 已经选中的table列表数据
            tableSelectedList: {
                permissionIds: [],
                permissionNames: [],
                permissionCodes: []
            },
            // table数据源
            tableSourceList: [],
            // table的key值
            tableKey: 0,
            // 正在处理中的树节点对象，弹窗前赋值
            processingTreeItem: {},
            // 新增和编辑权限页面是否弹窗变量
            treeDialogVisible: false,
            // 标识符，弹窗是新增权限还是编辑权限
            treeDialogStatus: '',
            // 正在处理中的列表行对象，弹窗前赋值
            processingTableItem: {},
            // 新增和编辑权限页面是否弹窗变量
            tableDialogVisible: false,
            // 标识符，弹窗是新增权限还是编辑权限
            tableDialogStatus: '',
            // 数据权限页面是否弹窗变量
            scopeSettingVisible: false
        };
    },
    methods: {
        // 树选中的节点变化事件，需要根据选中的节点查询列表
        nodeSelectedHandler(node) {
            this.selectedItem = node;
            // 据选中的节点查询列表
            this.getTableList(this.selectedItem);
        },
        // 树节点  点击新增按钮事件
        addNodeHandler(node) {
            this.processingTreeItem = node.data;
            this.treeDialogStatus = 'create';
            this.treeDialogVisible = true;
        },
        // 树节点  点击编辑按钮事件
        updateNodeHandler(node) {
            this.processingTreeItem = node.data;
            this.processingTreeItem.oldParam = JSON.parse(JSON.stringify(this.processingTreeItem));
            this.treeDialogStatus = 'update';
            this.treeDialogVisible = true;
        },
        // 树节点  点击删除按钮事件
        deleteNodeHandler(node) {
            const param = {
                permissionIds: [node.data.permissionId],
                permissionNames: [node.data.permissionName],
                permissionCodes: [node.data.permissionCode]
            };
            // 删除数据
            this.deletePermissionData(param, 'tree');
        },
        // icon图标格式判断，框架中有3类图标，需要分开判断
        generateIconCode(iconName) {
            if (!iconName) {
                return 'noData';
            } else if (iconName.indexOf('el-icon') !== -1) {
                // 处理icon，如果是element-ui的图标，则
                return 'el';
            } else if (iconName.indexOf('fa-') !== -1) {
                // 处理icon，如果是fontawsome的图标，则
                return 'fa';
            } 
                return 'svg';
            
        },
        // 查询table列表数据
        getTableList(item) {
            const params = {
                parentId: ''
            };
            // 如果传值则按照传值来查询，不传值则查询全部
            if (item && item.permissionId) {
                params.permissionId = item.permissionId;
            }
            // 查询权限相关数据
            this.$service.systemManagement
                .getPermissionTableList(params)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.tableSourceList = response.body;
                        // 重置选中数据的数组
                        this.tableSelectedList = {
                            permissionIds: [],
                            permissionNames: [],
                            permissionCodes: []
                        };
                    }
                });
        },
        // 列表中，点击是否启用switch开关后触发，调用接口，修改状态
        tableHasEnableChangeHandler(row) {
            this.$confirm(
                +row.hasEnable === 1 ? this.$t('systemManagement.msg.confirmEnable') : this.$t('systemManagement.msg.confirmDisable'),
                this.$t('common.tips'),
                {
                    confirmButtonText: this.$t('common.done'),
                    cancelButtonText: this.$t('common.cancel'),
                    type: 'warning'
                }
            ).then(() => {
                const params = {
                    permissionId: row.permissionId,
                    hasEnable: row.hasEnable,
                    permissionName: row.permissionName,
                    permissionCode: row.permissionCode
                };
                this.$service.systemManagement
                    .postPermissionChangeStatus(params)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            this.$message({
                                title: this.$t('systemManagement.msg.success'),
                                message: this.$t(
                                    'systemManagement.msg.editSuccess'
                                ),
                                type: 'success',
                                duration: 2000
                            });
                            // 刷新树节点
                            this.updateAll('update');
                            this.getTableList();
                        } else {
                            this.$message({
                                title: this.$t('systemManagement.msg.failed'),
                                // message: this.$t('systemManagement.msg.editFailed'),
                                message: response.head.message,
                                type: 'error',
                                duration: 2000
                            });
                        }
                    });
            }).catch(() => {
                row.hasEnable = row.hasEnable === 0 ? 1 : 0;
                this.$message({
                    type: 'info',
                    message: this.$t('systemManagement.msg.canceled')
                });
            });
        },
        // 列表上方新增按钮事件   添加按钮类型和接口类型权限数据
        tableListAddHandler() {
            // 判断是否选中菜单节点
            if (!this.selectedItem) {
                this.$message({
                    type: 'warning',
                    message: this.$t('systemManagement.msg.treeWarningMsg_1')
                });
                return;
            }
            // 权限类型转换为数字，并判断是否为菜单类型，非菜单类型不支持。
            if (+this.selectedItem.permissionType !== 2) {
                this.$message({
                    type: 'warning',
                    message: this.$t('systemManagement.msg.treeWarningMsg_2')
                });
                return;
            }
            // 给当前添加数据对象赋值
            this.processingTableItem = this.selectedItem;
            this.tableDialogStatus = 'create';
            this.tableDialogVisible = true;
        },
        // 列表中点击某一行 编辑  时触发
        tableItemUpdateHandler(row, $index) {
            this.processingTableItem = row;
            this.processingTableItem.oldParam = JSON.parse(JSON.stringify(row));
            this.tableDialogStatus = 'update';
            this.tableDialogVisible = true;
        },
        // 列表上方删除按钮事件   删除权限数据
        tableListDeleteHandler() {
            // 判断是否选中了节点数据
            if (
                this.tableSelectedList.permissionIds.length === 0
            ) {
                this.$message({
                    type: 'warning',
                    message: this.$t('systemManagement.msg.tableWarningMsg_1')
                });
                return;
            }
            // 删除数据
            this.deletePermissionData(this.tableSelectedList, 'table');
        },
        // 列表中点击某一行 删除  时触发
        tableItemDeleteHandler(row, $index) {
            const param = {
                permissionIds: [row.permissionId],
                permissionNames: [row.permissionName],
                permissionCodes: [row.permissionCode]
            };
            // 删除数据
            this.deletePermissionData(param, 'table');
        },
        // 列表中点击某一行 权限配置  时触发
        tableItemSettingHandler(row, $index) {
            this.processingTableItem = row;
            this.scopeSettingVisible = true;
        },
        // table列表数据选中状态改变时触发，把permissionId记录到tableSelectedList数组中，用于接口传值
        handleSelectionChange(selectedList) {
            this.tableSelectedList = {
                permissionIds: [],
                permissionNames: [],
                permissionCodes: []
            };
            selectedList.forEach((element) => {
                this.tableSelectedList.permissionIds.push(element.permissionId);
                this.tableSelectedList.permissionNames.push(element.permissionName);
                this.tableSelectedList.permissionCodes.push(element.permissionCode);
            });
        },
        // 删除权限数据方法，会多次调用，参数是permissionId数组，flag两个值'tree'和'table'用于回调刷新数据调用不同的方法
        deletePermissionData(dataList, flag) {
            this.$confirm(
                this.$t('systemManagement.msg.deleteDataWarningMsg_1'),
                this.$t('common.tips'),
                {
                    confirmButtonText: this.$t('common.done'),
                    cancelButtonText: this.$t('common.cancel'),
                    type: 'warning'
                }
            ).then(() => {
                this.$service.systemManagement
                    .deletePermissionData(dataList)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            // 刷新树节点
                            this.updateAll('delete', dataList);

                            this.$message({
                                type: 'success',
                                message: this.$t(
                                    'systemManagement.msg.deleteSuccess'
                                )
                            });
                        } else {
                            const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                            this.$message({
                                message: this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: this.$t('systemManagement.msg.canceled')
                });
            });
        },
        // 在列表中数据删除和更新后，需要刷新权限树和列表
        updateAll(flag, deleteList) {
            if (flag === 'delete') {
                // 删除后，刷新tree数据
                this.$refs.treeObject.deleteSuccessHandler(deleteList);
            } else {
                // 新增和修改，刷新权限树数据
                this.$refs.treeObject.getPermissionTreeData();
            }
        }
    }
};
</script>
<style lang="scss" scoped>
	::v-deep .margin-left.el-button{
	span{
		margin-left:5px;
	}
}
</style>
