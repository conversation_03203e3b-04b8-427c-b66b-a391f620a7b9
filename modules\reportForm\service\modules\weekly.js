/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 审核
        weekly: {
            // 获取页头数据
            getHeaderData(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getReportPullDownList',
                    method: 'get',
                    params: data
                });
            },
            // 获取开发周报页头
            getDevelopHeaderData(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getDevReportPullDownList',
                    method: 'get',
                    params: data
                });
            },
            // 获取维护周报页头
            getMaintainHeaderData(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getMaintainReportPullDownList',
                    method: 'get',
                    params: data
                });
            },
            // 获取产品线周报对应的产品线列表
            getProductLineList(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getProductLineReport',
                    method: 'get',
                    params: data
                });
            },
            // 获取部门/产品线下支持事项数量
            getSupplymentList(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getReportSupportNo',
                    method: 'post',
                    data
                });
            },
            // 获取开发项目列表
            getDevelopProjectList(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getDevWeekProjectList',
                    method: 'post',
                    data
                });
            },
            // 获取开发项目各状态数量
            getDevelopProjectStatusNumbers(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getProjectStatusGroupNo',
                    method: 'post',
                    data
                });
            },
            // 点击对应的产品线/部门，获取弹窗展示支持事项列表
            getSupplymentDetailList(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/getReportSupportInfo',
                    method: 'post',
                    data
                });
            },
            // 维护周报导出
            exportMaintainWeekly(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/maintainWeekReportExport',
                    method: 'post',
                    responseType: 'blob',
                    timeout: 60000,
                    data
                });
            },
            // 开发周报导出
            exportDevWeekly(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/weekReport/devWeekReportExport',
                    method: 'post',
                    responseType: 'blob',
                    timeout: 60000,
                    data
                });
            }
        }
    };

    return service;
};
