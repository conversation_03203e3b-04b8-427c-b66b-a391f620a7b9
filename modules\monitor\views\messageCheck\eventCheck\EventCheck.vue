<template>
    <div>
        <SnbcBaseTable class="table" ref="tableRef" :table-config="tableConfig">
        </SnbcBaseTable>
    </div>
</template>

<script>
import { getTableConfig } from './tableConfig';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';

export default {
    name: 'EventCheck',
    components: { SnbcBaseTable },
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableConfig: getTableConfig(this)
        };
    },
    watch: {
        activeName(newVal) {
            if (newVal === 'eventCheck') {
                this.handleQuery();
            }
        }
    },
    mounted() {
        this.handleQuery();
    },
    methods: {
        /**
         * 查询
         */
        handleQuery() {
            this.$nextTick(() => {
                this.$refs.tableRef.handleQuery();
            });
        },
        /**
         * 查询前的钩子
         * @param {Object} params 参数
         */
        queryParamsHook(params) {
            const { calculateDateRange } = params;
            if (calculateDateRange && calculateDateRange.length > 0) {
                this.tableConfig.queryParams.executeStartDate =
                    calculateDateRange[0];
                this.tableConfig.queryParams.executeEndDate =
                    calculateDateRange[1];
            }
        }
    }
};
</script>

<style scoped lang="scss">
::v-deep .el-select {
    width: 100%;
}
.table {
    // 设置最小高度，避免下拉框的option显示不全
    min-height: 500px;
}
</style>
