<template>
    <el-form-item v-bind="elFormItemAttrs" class="transfer-list">
        <div v-for="(item, index) in transferLabels" :key="index" class="transfer-list__item">
            {{ item.label }}
            <div class="transfer-list__delete" @click="handleDelete(item)">
                <i class="el-icon-close"></i>
            </div>
        </div>
        <div class="transfer-list__add" @click="handleClickAdd">
            <i class="el-icon-plus"></i>
        </div>
        <SnbcBaseDialog :config="dialogConfig" ref="dialogRef" @confirm="confirm">
            <template #dialog-body>
                <el-transfer
                    filterable
                    :filter-method="filterMethod"
                    filter-placeholder="请输入搜索内容"
                    v-model="transferModal"
                    :data="config.transferData"
                >
                </el-transfer>
            </template>
        </SnbcBaseDialog>
    </el-form-item>
</template>
<script>
import SnbcBaseDialog from '../snbc-dialog/SnbcBaseDialog';

export default {
    name: 'SnbcFormTransfer',
    components: {
        SnbcBaseDialog
    },
    props: {
        /**
         * SnbcFormTransfer
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elTransferAttrs: {},
                    // { key: '', label: '' }
                    transferData: []
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            transferModal: [],
            transferDataMap: {}
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // snbc-dialog 组件属性
        dialogConfig() {
            return {
                elDialogAttrs: {
                    'append-to-body': true,
                    'title': this.config.dialogTitle || '选择',
                    'width': '622px',
                    'custom-class': 'transfer-dialog'
                },
                autoHideOnCancel: true
            };
        },
        transferLabels() {
            return this.config.modelObj[this.config.modelKey].map((item) => {
                return {
                    key: item,
                    label: this.transferDataMap[item] || ''
                };
            });
        }
    },
    watch: {
        'config.transferData': {
            handler(val) {
                this.transferDataMap = {};
                val.forEach((item) => {
                    this.transferDataMap[item.key] = item.label;
                });
            },
            deep: true
        }
    },
    methods: {
        filterMethod(query, item) {
            if (this.config.filterMethod) {
                return this.config.filterMethod(query, item);
            }
            return item.label.indexOf(query) > -1;
        },
        handleClickAdd() {
            this.transferModal = [...this.config.modelObj[this.config.modelKey]];
            this.$refs.dialogRef.openDialog();
        },
        async handleDelete(item) {
            await this.$tools.confirm(`删除后数据将无法恢复！是否删除？`);
            const arr = this.config.modelObj[this.config.modelKey];
            this.config.modelObj[this.config.modelKey] = arr.filter((ele) => ele !== item.key);
        },
        confirm() {
            this.config.modelObj[this.config.modelKey] = [...this.transferModal];
            this.$refs.dialogRef.hideDialog();
        }
    }
};
</script>
<style lang="scss" scoped>
.transfer-list {
    ::v-deep .el-form-item__content {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;

        .transfer-list__item {
            border: 1px #0099ff solid;
            border-radius: 99999px;
            text-align: center;
            position: relative;
            padding: 0 30px;
            margin-right: 8px;
            margin-bottom: 8px;

            .transfer-list__delete {
                color: #ff6e6e;
                cursor: pointer;
                font-size: 14px;
                position: absolute;
                right: 10px;
                top: 0;

                &:active {
                    transform: scale(1.2);
                }
            }
        }

        .transfer-list__add {
            color: #0099ff;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;

            &:active {
                transform: scale(1.2);
            }
        }
    }
}
</style>
<style lang="scss">
.transfer-dialog {
    .el-dialog__footer {
        text-align: center !important;
    }
}
</style>
