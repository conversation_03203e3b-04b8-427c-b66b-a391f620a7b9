import { CONSTANTS } from '@/constants';

const { COLOR_LIST } = CONSTANTS;

const handleLineData = (data, typeList) => {
    return typeList.map((i) => {
        return data.map((j) => {
            const index = j.financeDetailShowVos.findIndex((l) => l.item === i);
            if (index !== -1) {
                return (
                    Math.trunc(j.financeDetailShowVos[index].value / 100) / 100
                ).toFixed(2);
            }
            return '0.00';
        });
    });
};

const handlePieData = (data) => {
    return data.map((i) => {
        return {
            value: (Math.trunc(i.value / 100) / 100).toFixed(2),
            name: i.item
        };
    });
};
const formatTooltip = (params, type = 'seriesName') => {
    let tooltipContent = '';
    params.forEach((param) => {
        tooltipContent += `${param.marker}${param[type]}：${param.data}万元</br>`;
    });
    return tooltipContent;
};

const formatPieTooltip = (params) => {
    return `${params.name}: ${params.value} 万(${params.percent.toFixed(1)}%)`;
};
// 按月份：柱状图
// eslint-disable-next-line max-lines-per-function
export const getBarChartOptionsByMonth = (data, typeList) => {
    const options = handleLineData(data, typeList);
    return {
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params);
            },
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: [...typeList, '月度合计']
        },
        color: COLOR_LIST,
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: [
                    '1月',
                    '2月',
                    '3月',
                    '4月',
                    '5月',
                    '6月',
                    '7月',
                    '8月',
                    '9月',
                    '10月',
                    '11月',
                    '12月'
                ],
                axisTick: {
                    alignWithLabel: true
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '费用科目月度支出(万元)',
                position: 'left',
                axisLabel: {
                    formatter: (value) => {
                        return value.toFixed(2);
                    }
                }
            },
            {
                type: 'value',
                name: '月度费用支出合计(万元)',
                position: 'right',
                splitLine: {
                    show: false
                },
                axisLabel: {
                    formatter: (value) => {
                        return value.toFixed(2);
                    }
                }
            }
        ],
        series: [
            ...options.map((i, index) => {
                return {
                    name: typeList[index],
                    type: 'bar',
                    data: i
                };
            }),
            {
                name: '月度合计',
                type: 'line',
                yAxisIndex: 1,
                data: data.map((i) =>
                    (Math.trunc(i.total / 100) / 100).toFixed(2)
                ),
                label: {
                    show: true
                }
            }
        ]
    };
};

// 按月份：饼图
export const getPieChartOptionsByMonth = (data) => {
    const typeList = [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月'
    ];
    const options = data.map((i, index) => {
        return {
            value: (Math.trunc(i.total / 100) / 100).toFixed(2),
            name: typeList[index]
        };
    });
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return formatPieTooltip(params);
            }
        },
        color: COLOR_LIST,
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: ['30%', '85%'],
                center: ['50%', '50%'],
                data: options,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};

// 按科目：柱状图
export const getBarChartOptionsBySubject = (data) => {
    const typeList = data.map((i) => i.item);
    return {
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params, 'axisValue');
            },
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '4%',
            right: '2%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: typeList,
                axisTick: {
                    alignWithLabel: true
                },
                axisLabel: {
                    fontSize: 12,
                    width: 100,
                    // 强制展示label
                    overflow: 'truncate',
                    interval: 0
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '费用科目年度费用支出(万元)',
                position: 'left',
                axisLabel: {
                    formatter: (value) => {
                        return value.toFixed(2);
                    }
                }
            }
        ],
        series: {
            type: 'bar',
            data: data.map((i) => {
                return (Math.trunc(i.value / 100) / 100).toFixed(2);
            }),
            label: {
                show: true
            }
        }
    };
};

// 按科目：饼图
export const getPieChartOptionsBySubject = (data) => {
    const options = handlePieData(data);
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}: ${
                    params.value
                } 万(${params.percent.toFixed(1)}%)`;
            }
        },
        color: COLOR_LIST,
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: ['30%', '85%'],
                center: ['50%', '50%'],
                data: options,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};
