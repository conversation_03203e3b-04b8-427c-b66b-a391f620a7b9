<template>
  <div class="box-nostart">
    <div class="nostart-one">
      <div class="one-table">启动时间</div>
      <div> 
        <el-date-picker
          style=" height:100%;"
          v-model="timeEstimaString.planStartTime"
          type="date"
          placeholder="选择日期"
          value-format = "yyyy-MM-dd"
          :clearable="false"
          :disabled = "isdisabled"
          @change="updateTime('planStartTime',timeEstimaString.planStartTime)"
          >
        </el-date-picker>
      </div>
      <div>
        {{timeEstimaString.planStartTimeWorkDays}}
      </div>
    </div>
     <div class="nostart-one">
      <div class="one-table">概念阶段</div>
      <div> 
        <el-date-picker
          style=" height:100%;"
          v-model="timeEstimaString.planConceptStage"
          type="date"
          placeholder="选择日期"
          value-format = "yyyy-MM-dd"
          :clearable="false"
          :disabled = "isdisabled"
          @change="updateTime('planConceptStage',timeEstimaString.planConceptStage)"
          >
        </el-date-picker>
      </div>
      <div>
         {{timeEstimaString.planConceptStageWorkDays}}
      </div>
    </div>
     <div class="nostart-one">
      <div class="one-table">计划阶段</div>
      <div> 
        <el-date-picker
          style=" height:100%;"
          v-model="timeEstimaString.planPlanStage"
          type="date"
          placeholder="选择日期"
          value-format = "yyyy-MM-dd"
          :clearable="false"
          :disabled = "isdisabled"
          @change="updateTime('planPlanStage',timeEstimaString.planPlanStage)"
          >
        </el-date-picker>
      </div>
      <div>
        {{timeEstimaString.planPlanStageWorkDays}}
      </div>
    </div>
     <div class="nostart-one">
      <div class="one-table">开发阶段</div>
      <div> 
        <el-date-picker
          style=" height:100%;"
          v-model="timeEstimaString.planDevStage"
          type="date"
          placeholder="选择日期"
          value-format = "yyyy-MM-dd"
          :clearable="false"
          :disabled = "isdisabled"
          @change="updateTime('planDevStage',timeEstimaString.planDevStage)"
          >
        </el-date-picker>
      </div>
      <div>
        {{timeEstimaString.planDevStageWorkDays}}
      </div>
    </div>
     <div class="nostart-one">
      <div class="one-table">验证阶段</div>
      <div> 
        <el-date-picker
        style=" height:100%;"
          v-model="timeEstimaString.planTestStage"
          type="date"
          placeholder="选择日期"
          value-format = "yyyy-MM-dd"
          :clearable="false"
          :disabled = "isdisabled"
          @change="updateTime('planTestStage',timeEstimaString.planTestStage)"
        >
        </el-date-picker>
      </div>
      <div>
        {{timeEstimaString.planTestStageWorkDays}}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    timeEstimaString: {
      type: Object,
      required: true
    },
    isdisabled:{
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
    };
  },
 
  methods: {
    updateTime(key,value){
      this.$emit('update-time', key, value);
    }
  },
};
</script>

<style lang="scss" scoped>
  .box-nostart{
    width:100%;
    height:180px;
    display:flex;
  }
  .nostart-one{
    width:25%;
    height:100%;
    display:flex;
    flex-direction: column;
  
  }
  .nostart-one div:first-child{
    width: 100%;
    height: 50%;
    color:#333;
    font-weight: bolder;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #eeeeee;
  }
  .nostart-one div:nth-child(2){
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #eeeeee;
  }
  .nostart-one div:nth-child(3){
    width: 100%;
    height: 50%;
    color:#333;
    font-weight: bolder;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #eeeeee;
  }
  .one-table{
    background-color: #f0f0f0;
  }
</style>
