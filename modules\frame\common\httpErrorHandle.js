import { Message } from 'element-ui';
// 用于处理4500毫秒内，只进行一次弹窗处理
let msgFlag = false;

// eslint-disable-next-line require-jsdoc
function msgTips(status, i18n, router, store) {
    if (!msgFlag) {
        msgFlag = true;
        // 解决token失效，无法弹出提示框问题
        setTimeout(() => {
            Message.error({
                message: i18n.t(`httpCode.http${status}`)
            });
        }, 300);

        setTimeout(() => {
            msgFlag = false;
        }, 4500);

        // 清除登录信息，并跳转到登录页
        store.dispatch('user/logout');
        router.push('login');
    }
}

// eslint-disable-next-line require-jsdoc
function redirectErrorPage(status, i18n, router, store) {
    // 判断当前是否二级路由
    const isSubRouter =
        router.currentRoute.path.split('/').filter((v) => !!v).length >= 2;
    // 根据错误状态码，跳转到对应的错误界面
    if (isSubRouter) {
        //  删除缓存数组中最后一个
        const delRoute = store.state.tagsView.cachedViews.pop();
        store.dispatch('tagsView/delCachedView', delRoute);
        router.push(`/error/${status}`);
    } else {
        // 一级路由情况先不考虑，router.push({ name: `${status}`, params: { from: 'login' }});
    }
}

/*
公用请求的方法
返回数据，路由
*/
export default (response, i18n, router, store) => {
    let flag = null;
    switch (response && response.status) {
        case 200:
            flag = true;
            break;
        case 401:
        case 406:
            // 这里关闭其他消息，避免消息雪崩
            Message.closeAll();
            msgTips(response.status, i18n, router, store);
            break;
        case 404:
        case 403:
        case 500:
            // 这里关闭其他消息，避免消息雪崩
            Message.closeAll();
            redirectErrorPage(response.status, i18n, router, store);
            break;
        default:
            flag = true;
    }
    return flag;
};
