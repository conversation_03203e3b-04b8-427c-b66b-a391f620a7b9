<template>
    <el-dialog
        title="部门选择"
        :visible.sync="dialogVisible"
        width="500px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
    >
        <div class="department-tree-container">
            <el-tree
                ref="departmentTree"
                :data="treeData"
                :props="defaultProps"
                show-checkbox
                node-key="id"
                :default-expand-all="true"
                :check-strictly="false"
                @check-change="handleCheckChange"
            >
            </el-tree>
        </div>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm"> 确 定 </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'DepartmentSelector',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        selectedDepartments: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            treeData: [
                {
                    id: 'tech-center',
                    name: '技术中心',
                    children: [
                        {
                            id: 'struct-dev-1',
                            name: '结构开发部',
                            children: [
                                {
                                    id: 'industrial-design-1',
                                    name: '工业设计室'
                                },
                                {
                                    id: 'machine-struct-1',
                                    name: '整机结构研究室'
                                },
                                {
                                    id: 'module-struct-1',
                                    name: '模块结构研究室'
                                },
                                {
                                    id: 'auto-struct-1',
                                    name: '自动化结构研究室'
                                }
                            ]
                        },
                        {
                            id: 'struct-dev-2',
                            name: '结构开发部',
                            children: [
                                {
                                    id: 'industrial-design-2',
                                    name: '工业设计室'
                                },
                                {
                                    id: 'machine-struct-2',
                                    name: '整机结构研究室'
                                },
                                {
                                    id: 'module-struct-2',
                                    name: '模块结构研究室'
                                },
                                {
                                    id: 'auto-struct-2',
                                    name: '自动化结构研究室'
                                }
                            ]
                        },
                        {
                            id: 'struct-dev-3',
                            name: '结构开发部',
                            children: [
                                {
                                    id: 'industrial-design-3',
                                    name: '工业设计室'
                                },
                                {
                                    id: 'machine-struct-3',
                                    name: '整机结构研究室'
                                },
                                {
                                    id: 'module-struct-3',
                                    name: '模块结构研究室'
                                },
                                {
                                    id: 'auto-struct-3',
                                    name: '自动化结构研究室'
                                }
                            ]
                        },
                        {
                            id: 'struct-dev-4',
                            name: '结构开发部',
                            children: [
                                {
                                    id: 'industrial-design-4',
                                    name: '工业设计室'
                                },
                                {
                                    id: 'machine-struct-4',
                                    name: '整机结构研究室'
                                },
                                {
                                    id: 'module-struct-4',
                                    name: '模块结构研究室'
                                },
                                {
                                    id: 'auto-struct-4',
                                    name: '自动化结构研究室'
                                }
                            ]
                        }
                    ]
                }
            ],
            checkedNodes: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    watch: {
        visible: {
            async handler(newVal) {
                if (newVal) {
                    await this.getTreeData();
                    this.initSelectedDepartments();
                }
            },
            immediate: true
        }
    },
    methods: {
        // 初始化已选择的部门
        initSelectedDepartments() {
            this.$nextTick(() => {
                if (
                    this.selectedDepartments &&
                    this.selectedDepartments.length > 0
                ) {
                    this.$refs.departmentTree.setCheckedKeys(
                        this.selectedDepartments
                    );
                }
            });
        },

        // 处理节点选择变化
        handleCheckChange() {
            this.checkedNodes = this.$refs.departmentTree.getCheckedNodes();
        },

        // 确定按钮
        handleConfirm() {
            const checkedKeys = this.$refs.departmentTree.getCheckedKeys();
            const checkedNodes = this.$refs.departmentTree.getCheckedNodes();
            console.log(
                {
                    keys: checkedKeys,
                    nodes: checkedNodes
                },
                '111111111'
            );

            this.$emit('confirm', {
                keys: checkedKeys,
                nodes: checkedNodes
            });

            this.$emit('update:visible', false);
        },

        // 关闭弹窗
        handleClose() {
            this.$emit('update:visible', false);
        },
        async getTreeData() {
            const params = { orgCode: '0002' };
            const api = this.$service.department.group.getAllSubDepartment;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                console.log(res.body, 'resr');
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.department-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
}

.dialog-footer {
    text-align: right;
}

::v-deep .el-tree {
    .el-tree-node__content {
        height: 32px;
        line-height: 32px;
    }

    .el-tree-node__label {
        font-size: 14px;
    }
}
</style>
