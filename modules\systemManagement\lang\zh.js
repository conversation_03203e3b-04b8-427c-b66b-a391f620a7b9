/* eslint-disable max-lines */
/**
 * 中文国际化配置：项目模块国际化配置文件route和project必填、httpCode可选
 * @param {Object} route 项目模块路由国际化
 * @param {Object} httpCode 项目模块httpCode国际化
 * @param {Object} project 项目模块除路由、httpCode外，其他信息国际化
 */
export default {
    route: {
        systemManagement: '系统管理',
        configuration: '配置管理',
        dictionary: '字典管理',
        area: '地域管理',
        organization: '组织机构',
        logger: '日志管理',
        informationAnnouncement: '信息公告',
        messageNotice: '消息通知',
        role: '角色管理',
        permissionManagement: '权限管理',
        tenant: '商户列表',
        tenantlogs: '商户日志',
        tenantDetail: '商户详情',
        exportRecords: '导出记录',
        topMenu: '顶部菜单',
        messageTemplate: '消息模板',
        superset: '报表查看'
    },
    project: {
        filterTitle: '查询搜索',
        listTitle: '信息列表',
        submit: '保存',
        configure: '配置',
        cancled: '已取消',
        components: {
            treeSelectOrg: {
                title: '组织机构选择',
                confirm: '确定',
                cancel: '取消',
                filterPlaceholder: '输入关键字进行过滤'
            }
        },
        msg: {
            serviceFailed: '连接服务器异常',
            operateSuccess: '操作成功',
            success: '成功',
            failed: '失败',
            createSuccess: '创建成功!',
            createFailed: '创建失败!',
            editSuccess: '修改成功!',
            editFailed: '修改失败!',
            deleteSuccess: '删除成功!',
            deleteFailed: '删除失败!',
            deleteFailed_1: '该组织机构下有用户，无法删除',
            deleteFailed_2: '该地域编号已存在',
            canceled: '已取消!',
            treeWarningMsg_1: '请选择一个菜单类型树节点!',
            treeWarningMsg_2:
                '只能给菜单节点添加按钮和接口类型权限，请选择菜单类型树节点!',
            treeWarningMsg_3: '是否确定改变树节点的位置?',
            tableWarningMsg_1: '请选择列表中的数据后删除!',
            deleteDataWarningMsg_1: '此操作将永久删除选中的数据, 是否继续?',
            required: '必须填写！',
            checkMsg_1: '只能输入英文，数字，特殊符号！',
            checkMsg_2: '长度在 1 到 10 个字符！',
            checkMsg_3: '必须选择规则类型！',
            checkMsg_4: '必须选择一个图标！',
            checkMsg_5: '长度在 1 到 45 个字符！',
            checkMsg_6: '长度不能超过 255 个字符！',
            checkMsg_7: '长度在 1 到 20 个字符！',
            checkMsg_8: '长度在 1 到 100 个字符！',
            checkMsg_CodeRepeat: '权限编码重复',
            confirmEnable: '您是否确定启用该权限？',
            confirmDisable: '您是否确定禁用该权限？',
            messageError1: '您购买的服务已到期，请您及时续费',
            messageError2: '您没有购买过该服务，请购买后使用',
            jobNumberMsg: '请重新输入工号（由字母、数字、-任意组成）'
        },
        bgReturnError: {
            111111: '操作失败',
            990001: '数据解析错误',
            950204: '消息模式不能为空',
            950201: '消息Id错误',
            991313: '非超管不能修改用户手机号码',
            994006: '输入框中包含非法字符，如有疑问请联系管理员',
            991103: '组织机构ID错误',
            991101: '新增组织机构失败',
            991102: '组织机构ID不能为空',
            991104: '修改组织机构失败',
            991105: '查询组织机构失败',
            991106: '删除组织机构失败',
            991107: '组织机构下有用户，无法删除',
            991108: '获取组织机构树失败',
            991109: '组织机构拖拽失败',
            991110: '组织机构名称重复',
            991111: '用户组织机构关系解绑失败',
            991112: '请选择要导出的用户！',
            991113: '没有操作权限',
            990301: '用户名不能为空',
            990302: '用户手机号码不能为空',
            990303: '用户邮箱地址不能为空',
            990304: '用户信息为空，请登录系统后操作!',
            990305: '用户工号不能为空',
            990306: '用户所属部门不能为空',
            990307: '用户ID不能为空',
            990308: '用户ID已存在',
            990309: '用户工号长度最多20位',
            993301: '用户手机号格式不正确',
            993302: '用户手机号长度为16位内的数字',
            993303: '用户邮箱地址格式不正确',
            993304: '用户邮箱地址最多64位',
            991301: '用户名已存在',
            991302: '关联人员被禁用',
            991303: '用户ID错误',
            991304: '用户不存在',
            991305: '入参用户状态不合法',
            991306: '每次必须且只能上传一个文件',
            991307: '只能上传excel文件',
            991308: '用户工号已经存在',
            991309: '用户手机号已经存在',
            991310: '用户邮箱已经存在',
            991311: '用户手机号检测失败',
            991312: '该手机号下无用户,无法变更管理员',
            990313: '您输入的验证码错误或已失效，请重新输入',
            991314: '您输入的新管理员手机号与原手机号一致，请重新输入',
            990315: '您输入的验证码次数过多，请重新发送验证码',
            990316: '此号码已绑定用户，请更换其他手机号',
            991317: '手机号码未注册',
            991318: '非超管不能修改用户手机号码',
            991708: '配置编码已存在',
            990504: '请删除子节点后再删除',
            990502: '先解绑角色权限关联再删除',
            991502: '数据编码重复',
            990503: '请先删除数据权限配置后再删除',
            991401: '角色名称已经存在',
            991406: '超级管理员不能被禁用',
            991407: '超级管理员角色不能添加或移除用户',
            991408: '无法为用户分配超级管理员角色',
            991903: '字典类型编号已存在',
            991901: '字典值编码或字典值已存在',
            991801: '地域编号已存在',
            991803: '地域编号不能为0',
            991501: '最多添加10个顶部菜单',
            991503: '导航菜单编码已存在',
            950208: '该公告已发布',
            920101: '用户Id错误',
            920102: '文件路径错误',
            920103: '文件错误，或空文件',
            920104: '文件后缀错误,不能上传此类型文件',
            920105: '不能上传此类型文件',
            920301: '上传频率过高',
            920302: '下载频率过高',
            920303: '文件过大，无法上传',
            920304: '文件格式错误',
            920305: '文件已经存在',
            920306: '文件不存在',
            920307: '断点续传错误',
            950213: '模板编号，消息渠道，发送平台已经存在',
            950209: '消息模板ID不能为空',
            950215: '消息类型不合法',
            950216: '消息渠道不合法',
            950217: '发送平台不合法',
            950218: '消息模板操作人不能为空',
            950223: '模板映射参数中每个变量须包含在模板内容中',
            990209: '消息模板ID不能为空',
            950210: '消息模板ID不存在',
            950211: '消息模板状态不合法',
            950220: '每次必须且只能上传一个文件',
            950219: '消息模板操作人ID不能为空',
            950221: '只能上传excel文件',
            990102: '产品编码已存在',
            991115: '该组织机构下有角色，无法删除'
        },
        permission: {
            permissionName: '权限名称',
            routingUrl: '路由地址',
            permissionIcon: '图标',
            permissionCode: '权限编码',
            permissionType: '类型',
            enableFlag: '是否启用',
            operation: '操作',
            parentName: '父级权限',
            system: '系统',
            menu: '菜单',
            button: '按钮',
            interface: '接口',
            enable: '已启用',
            disabled: '未启用',
            permissionList: '权限列表',
            scopeSetting: '权限配置',
            searchPlaceholder: '请输入关键字',
            dialog: {
                addTitle: '新增权限',
                updateTitle: '编辑权限',
                detailTitle: '查看权限',
                chooseIcon: '选择图标',
                remarks: '备注',
                scopeFieldPlaceholder: '请输入可见字段',
                remarksPlaceholder: '请输入备注信息',
                scopeClassPlaceholder: '请输入权限类名',
                dataScopeTitle: '【系统管理】数据权限',
                dataScopeName: '权限名称',
                dataScopeCode: '权限编码',
                dataScopeColumn: '权限字段',
                dataScopeType: '规则类型',
                dataScopeField: '可见字段',
                dataScopeClass: '权限类名',
                dataScopeType_0: '自定义',
                dataScopeType_1: '本人可见',
                dataScopeType_2: '所在机构可见',
                dataScopeType_3: '所在机构及以下可见',
                dataScopeType_4: '全部可见'
            },
            placeholder: {
                msg1: '请输入权限名称',
                msg2: '请输入权限编码',
                msg3: '请输入路由地址',
                msg4: '请输入备注',
                msg5: '请输入权限字段',
                msg6: '请选择图标'
            }
        },
        configuration: {
            paramName: '参数名称',
            placeholderParamName: '请输入参数名称',
            paramTypeName: '参数类型值',
            paramCode: '参数编号',
            paramValue: '参数值',
            pleaseInput: '请输入',
            paramTypeCode: '参数类型编号',
            createTime: '创建时间',
            addParam: '新增参数',
            editParam: '编辑参数',
            content: '内容',
            inputParamName: '请输入参数名称',
            inputParamCode: '请输入参数编号',
            inputParamValue: '请输入参数值',
            inputParamTypeCode: '请输入参数类型编号',
            selectParamTypeName: '请选择参数类型值',
            paramCodeRule: '请输入字母、数字，最大长度为45',
            message: {
                deleteTipTitle: '删除提示',
                deleteTypeTip: '你确定要删除该项吗？',
                deleteValueTip: '你确定删除该项数据吗？',
                deleteSuccess: '删除成功！',
                deleteFailure: '删除失败！'
            }
        },
        area: {
            addRegion: '增加地域',
            regionName: '地域名称',
            regionCode: '地域编号',
            importArea: '导入地域',
            downloadTemplate: '下载模板',
            describe: '描述',
            addArea: '新增地域',
            fatherName: '父级名称',
            editArea: '编辑地域',
            inputRegionName: '请输入地域名称',
            inputRegionCode: '请输入地域编码',
            message: {
                deleteTipTitle: '删除提示',
                deleteTypeTip:
                    '您删除该地域，如果该地域有子地域也会一起删除，您确定么？',
                deleteSuccess: '删除成功！',
                deleteFailure: '删除失败！'
            },
            placeholder: {
                msg1: '请输入地域名称',
                msg2: '请输入地域编号',
                msg3: '请输入描述',
                msg4: '请输入父级名称'
            }
        },
        dictionary: {
            search: '搜索',
            view: '查看',
            add: '新增',
            addValue: '新增字典值',
            addType: '新增字典类型',
            edit: '编辑',
            editValue: '编辑字典值',
            editType: '编辑字典类型',
            delete: '删除',
            back: '返回',
            save: '保存',
            cancel: '取消',
            exportAll: '导出',
            reset: '重置',
            listData: '列表数据',
            dictTitle: '字典值信息',
            typeTitle: '字典类型信息',
            title: '字典管理',
            columnName: {
                sequence: '序号',
                operate: '操作',
                type: '字典类型',
                typeName: '字典类型名称',
                typeCode: '字典类型编码',
                value: '字典值',
                valueName: '字典值名称',
                valueParentName: '父级字典',
                valueCode: '字典值编码',
                valueDesc: '字典值描述'
            },
            placeholder: {
                valueParent: '请选择',
                msg1: '请输入字典类型名称',
                msg2: '请输入字典值',
                msg3: '请输入字典值编码'
            },
            message: {
                placeholderPre: '请输入',
                selectDefaultText: '请选择',
                ruleName: '请输入汉字、字母、数字和下划线，最大长度为20',
                ruleCode: '请输入字母、数字、中划线和下划线，最大长度为40',
                ruleCodeValue: '请输入空格以外的字符，最大长度为50',
                ruleNoPass: '请正确填写输入项内容！',
                saveSuccess: '保存成功',
                saveFailure: '保存失败',
                queryListFailure: '查询列表数据失败',
                deleteTipTitle: '删除提示',
                deleteTypeTip:
                    '你确定删除该项数据吗？如果有字典值也将一并删除！',
                deleteValueTip: '你确定删除该项数据吗？',
                deleteSuccess: '删除成功！',
                deleteFailure: '删除失败！',
                exportExcelFailure: '导出日志失败',
                noMoreData: '已到底部，没有更多数据！'
            }
        },
        logger: {
            querySearch: '查询搜索',
            username: '用户名',
            clientType: '客户端类型',
            organization: '所属机构',
            loginIp: '登录IP',
            timeRange: '时间范围',
            operateModule: '操作模块',
            operateType: '操作类型',
            query: '查询',
            reset: '重置',
            queryList: '查询列表',
            more: '更多',
            downloadPage: '导出当前页',
            downloadAll: '导出日志',
            noData: '暂无数据',
            startDate: '开始时间',
            to: '至',
            endDate: '结束时间',
            logExcelName: '系统日志记录表',
            columnName: {
                logTime: '日志时间',
                userId: '用户ID',
                username: '用户名',
                organ: '所属机构',
                tenantName: '商户名称',
                loginIp: '登录IP',
                operateModule: '操作模块',
                operateType: '操作类型',
                content: '内容',
                remarks: '备注',
                clientType: '客户端类型',
                browser: '浏览器及版本'
            },
            message: {
                inputUsername: '请输入用户名',
                inputClientType: '请选择客户端类型',
                inputOrgan: '请输入商户名称',
                inputOperateModule: '请输入操作模块',
                inputLoginIp: '请输入登录IP',
                inputOperateType: '请输入操作类型',
                loginIp: '请输入登录ip',
                queryListFailure: '查询列表数据失败',
                exportExcelFailure: '导出日志失败',
                export1: '导出日志的时间请小于',
                export2: '天',
                export3: '导出日志数量超过',
                export4: '条，无法导出，请修改查询条件'
            }
        },
        role: {
            search: '@:common.searchBtn',
            add: '@:common.add',
            edit: '@:common.edit',
            delete: '@:common.delete',
            back: '@:common.return',
            save: '@:common.save',
            cancel: '@:common.cancel',
            addRole: '创建角色',
            addUser: '添加用户',
            removeUser: '移除用户',
            assignRole: '分配角色',
            roleList: '角色列表',
            waitAssignUser: '待分配用户',
            roleTitle: '角色信息',
            enable: '启用',
            disable: '禁用',
            permissionSetting: '权限设置',
            expand: '展开/折叠',
            allSelect: '全选/全不选',
            linkage: '父子联动',
            roleNameTip:
                '角色名称格式不正确，请重新输入角色名称（限制为10个汉字或20个字符）',
            defaultPermissionPrompt: '确定要重置该角色权限吗？',
            reSetSuccess: '操作成功',
            columnName: {
                sequence: '@:common.No',
                userName: '姓名',
                phone: '手机号码',
                orgName: '部门',
                isLock: '状态',
                jobNumber: '工号',
                roleId: '角色编号',
                roleName: '角色名称',
                roleDesc: '角色描述',
                department: '所属机构',
                permissionMenu: '权限类型',
                permissionFunc: '权限项',
                permissionData: '数据权限范围'
            },
            placeholder: {
                userFilter: '请输入姓名/手机号码',
                msg1: '请输入可分配角色名称',
                msg2: '请输入姓名/工号查询'
            },
            message: {
                placeholderPre: '请输入',
                maxLenTip: '最大长度不能超过{0}个字符！',
                saveSuccess: '保存成功',
                saveFailure: '保存失败',
                queryListFailure: '查询列表数据失败',
                deleteTipTitle: '删除提示',
                deleteValueTip: '@:common.deleteTip',
                deleteSuccess: '删除成功！',
                deleteFailure: '删除失败！',
                removeSuccess: '移除成功！',
                removeFailure: '移除失败！',
                userListEmptySelect: '请勾选用户信息！',
                removeUserTip: '确认将【{0}】从【{1}】角色中移除吗？',
                deleteRoleTip: '确认删除角色【{0}】吗？',
                assignRoleEmptySelect: '请选择需要分配的角色！',
                addUserEmptySelect: '请选择需要添加的用户！',
                currentSelectUserCount: '当前已选{0}人',
                currentSelectRoleCount: '已选：{0}',
                pleaseUntie: '先解绑角色关联的用户再删除'
            },
            permissionTable: {
                permissionsLabel: '角色所用权限',
                authorityLabel: '数据权限范围',
                radioMine: '本人',
                radioOrg: '所在机构',
                radioOrgSub: '所在机构及子机构',
                radioAll: '全部'
            },
            tooltip: {
                look: '查看角色权限',
                edit: '编辑角色权限',
                delete: '删除角色权限',
                reset: '重置角色权限'
            }
        },
        tenant: {
            search: '@:common.searchBtn',
            reset: '重置',
            add: '@:common.add',
            edit: '@:common.edit',
            save: '@:common.save',
            cancel: '@:common.cancel',
            title: '商户',
            detail: '详情',
            columnName: {
                tenantCode: '商户编码',
                tenantName: '商户名称',
                contactName: '联系人',
                contactPhone: '联系电话',
                channelName: '接入渠道',
                dataStatus: '是否启用',
                contactAddress: '所在地区',
                address: '详细地址',
                industryInfo: '行业信息',
                serviceLevel: '服务级别',
                csdName: '所属客服',
                busManName: '所属业务员',
                tenantTags: '商户标签',
                remark: '备注信息',
                createTime: '注册时间',
                operate: '操作'
            },
            placeholder: {
                timeRange: '请选择时间范围',
                startDate: '开始时间',
                endDate: '结束时间',
                tenantName: '请输入商户名称',
                contactName: '请输入联系人',
                contactPhone: '请输入联系电话',
                channel: '请选择接入渠道',
                contactAddress: '请选择所在地区',
                address: '请输入详细地址',
                industryInfo: '请选择行业信息',
                serviceLevel: '请选择服务级别',
                csdName: '请选择所属客服',
                busManName: '请选择所属业务员',
                tenantTags: '请选择客户标签',
                remark: '请输入备注信息'
            },
            message: {
                maxLenTip: '最大长度不能超过{0}个字符！',
                queryListFailure: '查询列表数据失败',
                deleteTipTitle: '删除提示',
                updateStatusTipTitle: '确认提示',
                updateStatusTipMsg: '确认要{1}商户[{0}]吗？',
                updateStatusSuccess: '设置成功',
                updateStatusFailure: '设置失败',
                tenantExport: '商户日志记录表'
            },
            tenantDetail: {
                tenantNo: '商户编号：',
                contacts: '联系人：',
                telephone: '联系电话：',
                area: '所在地区：',
                address: '详细地址：',
                industryInfo: '行业信息：',
                serviceLevel: '服务级别：',
                customer: '所属客服：',
                business: '所属业务员：',
                label: '客户标签：',
                remark: '备注信息：',
                registerTime: '注册时间：',
                extension: '延期',
                productServices: '产品服务',
                dialogTitle: '延长服务时间',
                extensionTime: '服务到期时间',
                extensionRemark: '备注说明',
                inputExtensionTime: '请填写服务到期时间',
                inputRemark: '请输入备注',
                column: {
                    serviceNo: '服务编号',
                    serviceName: '服务名称',
                    serviceType: '服务类型',
                    serviceStatus: '状态',
                    createTime: '创建时间',
                    expireTime: '到期时间'
                },
                msg: {
                    inputDueTime: '请输入服务到期时间',
                    timeTip: '延期时间不能小于当前服务的到期时间'
                }
            }
        },
        messageNotice: {
            title: '标题',
            content: '内容',
            delete: '删除',
            deleteAll: '全部删除',
            setRead: '标记已读',
            setAllRead: '全部已读',
            messageType: '消息类型',
            msgStatus: '消息状态',
            placeholderMessageContent: '请输入消息内容',
            columnName: {
                messageTitle: '消息标题',
                sendTime: '通知时间',
                messageType: '消息类型'
            },
            messageListEmptySelect: '请勾选消息！',
            allMessages: '全部类型消息',
            inputPlaceholder: '请输入消息标题/内容',
            back: '返回',
            previous: '上一条',
            next: '下一条',
            messageNotification: '消息通知',
            allMsg: '全部消息',
            unreadMessage: '未读消息',
            deleteConfirmTip: '删除提示',
            deleteConfirmMessage: '此操作将永久删除选中的消息, 是否继续？',
            readMessage: '已读消息',
            drawer: {
                title: '通知中心',
                allRead: '全部已读',
                timeFilter: '时间筛选',
                statusFilter: '状态筛选',
                noMoreData: '暂无更多数据',
                serviceMsg: '服务消息',
                all: '全部',
                tipTitle: '确认提示',
                cancel: '已取消',
                tipMsg1: '此操作将全部未读消息标记为已读，是否继续?',
                tipMsg2: '此操作将该类未读消息标记为已读，是否继续?'
            }
        },
        organization: {
            departmentHead: '部门主管：',
            telephoneNumber: '手机号码：',
            memberInformation: '成员信息',
            placeholder: {
                msg1: '请输入姓名/手机号'
            },
            addUser: '添加用户',
            removeUser: '移除用户',
            adjustmentDepartment: '调整部门',
            moreMenu: '更多菜单',
            enterUser: '导入用户',
            outputUser: '导出用户',
            exportUser: '导出全部',
            table: {
                name: '姓名',
                number: '工号',
                phone: '手机号',
                mail: '邮箱',
                depart: '部门',
                role: '角色',
                status: '状态'
            },
            dialog: {
                add: '新增',
                edit: '编辑',
                user: '用户',
                name: '姓名',
                number: '工号',
                phone: '手机号码',
                telPhone: '手机号:',
                mail: '邮箱',
                depart: '部门',
                role: '角色',
                isEnable: '是否启用',
                LDAPUser: 'LDAP用户',
                placeholder: {
                    msg1: '请输入姓名',
                    msg2: '请输入工号',
                    msg3: '请输入手机号码',
                    msg4: '请输入邮箱',
                    msg5: '请选择部门',
                    msg6: '请选择角色',
                    msg7: '请输入组织机构名称搜索',
                    msg8: '请输入可分配角色名称',
                    msg9: '请输入部门名称',
                    msg10: '请输入部门名称',
                    msg11: '请输入部门描述',
                    msg12: '请选择',
                    msg13: '请输入名称'
                },
                removeUserMsg: '您确认将用户从当前部门移除吗？',
                tips: '提示',
                organization: '组织机构',
                assignRoles: '分配角色',
                roleList: '角色列表',
                departName: '部门名称',
                superiorDepartment: '上级部门',
                departmentDescription: '部门描述',
                departmentHead: '部门主管',
                rules: {
                    msg1: '请输入机构名称',
                    msg2: '请输入父级机构',
                    msg3: '请输入机构描述',
                    msg4: '手机号码只能是由5-16位以数字组成',
                    msg5: '请输入姓名',
                    msg6: '请输入工号',
                    msg7: '请输入手机号码',
                    msg8: '电话号码只能是由5-16位的数字组成',
                    msg9: '联系电话只能是由5-16位的数字组成'
                },
                importUser: '导入用户',
                pleaseDownload: '请先下载',
                importDocument: '导入模板文档',
                finishedImport: '，填写完成后再上传导入。',
                finishedNotice: '导入完成后，您会收到系统消息通知。',
                finishedWarning: '请注意：单次导入最大支持1000条数据',
                exceedMax: '您导入的用户数据量大于1000条，请您重新调整',
                exceedMaxTip: '导入模板中的用户数据后再导入。',
                importServicet: '导入服务',
                clickUpload: '点击上传',
                userList: '用户列表',
                allUser: '所有用户',
                userListResult: '用户列表导入结果',
                tipMsg: {
                    msg1: '所有用户信息导入成功',
                    msg2: '请选择部门信息！',
                    msg3: '有子部门，将会一并删除，确定删除吗？',
                    msg4: '确定删除部门',
                    msg5: '吗？',
                    msg6: '确定提示',
                    importTip: '导入提示'
                },
                orgError: '所属机构不能为空',
                mailMsg: '请输入正确的邮箱地址'
            },
            message: {
                success: '操作成功',
                failture: '操作失败',
                repeatName: '用户名已存在',
                exportTip: '导出提示',
                exportMsgOne: '您确定要导出选中的用户数据吗？',
                exportMsgTwo: '导出完成后将会给您发送系统通知，导出数据文件',
                exportMsgThree: '请在【导出记录】中下载'
            }
        },
        dictionaryManagement: {
            type: {
                industryField: '行业领域',
                enterpriseScale: '企业规模',
                createChannel: '租户创建渠道',
                param_type: '参数类型值',
                userWorkStatus: '用户状态',
                tenantTags: '商户标签',
                serviceLevel: '服务级别',
                ability_control_switch: '能力控制开关',
                apply_type: '开具类型',
                charge_type: '收费类型',
                charge_type_unit: '付费类型单位',
                contract_product_code: '合同赠送产品编码',
                grade_unit: '阶梯单位',
                invoice_carrier: '开票介质',
                invoice_status: '发票状态',
                invoice_type: '开票类型',
                is_default: '是否默认',
                is_renew: '是否续费',
                logistics_company: '物流公司',
                log_type: '日志类型',
                notice_type: '通知类型',
                paytimeoutmin: '支付过期时间',
                pay_type: '支付类型',
                product_category: '服务类目',
                product_status: '服务状态',
                product_type: '服务类型',
                purchase_channel: '支付渠道',
                purchase_status: '订单状态',
                refund_type: '退款类型',
                send_mail: '合同赠送角色邮箱集',
                message_send_time: '消息发送时间',
                message_status: '消息状态',
                message_type: '消息类型',
                dbType: 'dbType',
                logTarget: '操作模块',
                logOpration: '操作类型'
            },
            value: {
                Working: '工作中',
                VIPCustomer: 'VIP客户',
                Major: '主要客户',
                Ordinary: '普通客户',
                Small: '小客户',
                VIPService: 'VIP服务',
                ClassA: 'A类服务',
                ClassB: 'B类服务',
                ClassC: 'C类服务',
                Applet: '小程序',
                Wechat: '微信',
                Logistics: '物流行业',
                Financial: '金融行业',
                NewRetai: '新零售行业',
                Media: '传媒/广告/会展行业',
                Internet: '互联网/信息技术',
                Professional: '专业服务业',
                Manufacturing: '制造业',
                Others: '其他',
                People50: '0-50人',
                People100: '50-100人',
                People500: '100-500人',
                People1000: '500-1000人',
                People5000: '1000-5000人',
                Background: '平台录入',
                ThreeSync: '三方同步',
                ApplicationService: '应用服务',
                CustomizedService: '定制服务',
                StandardEdition: '标准版',
                EnterpriseEdition: '企业版',
                UltimateEdition: '旗舰版',
                ProductPackage: '产品包',
                ThirdPartyService: '第三方服务',
                UnReleased: '待发布',
                Released: '已发布',
                Disabled: '已禁用',
                Removed: '已下架',
                ChargeDay: '按天收费',
                ChargeMonth: '按月收费',
                ChargeQuarter: '按季收费',
                ChargeYear: '按年收费',
                OfflinePay: '线下支付',
                WechatPay: '微信支付',
                ToBePaid: '待支付',
                Paid: '已支付',
                PaidSuccess: '购买成功',
                Cancelled: '已取消',
                Refunded: '已退款',
                Applied: '已申请',
                NewlyBuy: '新购买',
                Renew: '续费',
                Alipay: '支付宝',
                WeChat: '微信',
                ReleasedNotice: '服务发布通知',
                ExpirationNotice: '服务到期通知',
                AboutToExpire: '服务即将到期',
                ServiceDisabled: '服务禁用通知',
                SeviceOffine: '服务下线通知',
                ServiceEnabled: '服务启用通知',
                ServiceOnline: '服务上线通知',
                Online: '线上',
                Offline: '线下',
                Day: '天',
                Month: '月',
                Season: '季',
                Year: '年',
                NonDefault: '非默认',
                Default: '默认',
                Personal: '个人',
                Enterprise: '企业',
                OrdinaryInvoice: '增值税普通发票',
                SpecialInvoice: '增值税专用发票',
                PaperInvoice: '纸质发票',
                ElectronicInvoice: '电子发票',
                Invoiced: '已开票',
                Notification: '消息通知',
                Authorize: '授权',
                SFExpress: '顺丰速运',
                JDLogistics: '京东物流',
                EMS: '邮政EMS',
                ZTOExpress: '中通快递',
                STOExpress: '申通快递',
                YTOExpress: '圆通速递',
                BestExpress: '百世快递',
                YundaExpress: '韵达快递',
                Try: '试用',
                RemittanceConfirmed: '汇款待确认',
                Timeout: '已超时',
                PayByAlipay: '支付宝支付',
                ServiceMessage: '服务消息',
                OperationMessage: '运营消息',
                ProductMessage: '产品消息',
                FailureMessage: '故障消息',
                SystemMessage: '系统消息',
                NoticeMessage: '公告消息',
                Unread: '未读',
                HaveRead: '已读',
                Today: '今天',
                Yesterday: '昨天',
                Last7Days: '近7天',
                Last30Days: '近30天',
                MYSQL: 'MYSQL',
                ORACLE: 'ORACLE',
                org: '组织机构',
                userManage: '用户管理',
                dictManage: '字典管理',
                login: '登录',
                confManage: '配置管理',
                permissionManage: '权限管理',
                regionManage: '地域管理',
                roleManage: '角色管理',
                tenantManage: '商户管理',
                logManage: '日志管理',
                messageCenter: '消息中心',
                enterprise: '企业信息',
                invoiceManage: '发票管理',
                payConfig: '支付配置',
                productFunctionDefin: '产品功能定义',
                productServiceManage: '产品服务管理',
                merchantMonthlyReport: '商户月报',
                orderService: '服务市场',
                exportFile: '导出文件',
                addOrg: '新增组织机构',
                modifyOrg: '修改组织机构',
                delOrg: '删除组织机构',
                rankOrg: '组织机构排序',
                changPwd: '修改密码',
                forgetPwd: '忘记密码',
                modifyUser: '修改用户',
                addUser: '新增用户',
                adjustOrg: '调整部门',
                importExportUser: '导入导出用户',
                changeAdmin: '变更管理员',
                modifyDictType: '修改字典类型',
                addDictType: '新增字典类型',
                delDictType: '删除字典类型',
                addDictValue: '新增字典值',
                modifyDictValue: '修改字典值',
                delDictValue: '删除字典值',
                userLogin: '用户登录',
                userLogout: '用户登出',
                loginLock: '登录锁定',
                unbindThreeLogin: '解绑三方登录',
                addParam: '新增参数',
                ModifyParam: '修改参数',
                delParam: '删除参数',
                permissionSort: '权限排序',
                changPermissionStatus: '变更权限状态',
                delPermission: '删除权限',
                modifyPermission: '修改权限',
                addPermission: '新增权限',
                addDataPermission: '新增数据权限',
                modifyDataPermission: '修改数据权限',
                delDataPermission: '删除数据权限',
                addRegion: '新增地域',
                modifyRegion: '修改地域',
                delRegion: '删除地域',
                modifyRole: '修改角色',
                addRole: '新增角色',
                delRole: '删除角色',
                roleAssocUser: '角色绑定/解绑用户',
                resetRole: '重置角色',
                addMerchant: '新增商户',
                modifyMerchant: '修改商户',
                changeMerchantStatus: '变更商户状态',
                exportLog: '导出日志',
                delMessage: '删除消息',
                changeEnterInfo: '变更企业信息',
                exportInvoice: '导出发票记录',
                issueInvoice: '开具发票',
                applyInvoice: '申请发票',
                modifyPayConfig: '修改支付配置',
                functionSort: '产品功能排序',
                changeFunctionStatus: '变更产品功能状态',
                delFunction: '删除产品功能',
                modifyFunction: '修改产品功能',
                addFunction: '新增产品功能',
                addProduct: '新增产品服务',
                modifyProduct: '修改产品服务',
                publishProduct: '发布产品服务',
                modifyPublishedProduct: '修改已发布产品服务',
                delProduct: '删除产品服务',
                downloadMonthlyRep: '下载商户月报',
                confirmPayment: '确认商户付款',
                canceOrder: '取消订单',
                purchaseProduct: '购买产品服务',
                productRenewal: '产品服务续费',
                fillRremittance: '填写汇款单',
                deleteFile: '删除文件',
                notice: '通知',
                announcement: '公告',
                unpublished: '未发布',
                published: '已发布',
                CancelRelease: '版本取消',
                ToBeReleased: '待发布',
                Release: '已发布',
                HaveCompleted: '已完成',
                Locked: '已锁定',
                RolledBack: '已回滚',
                PartialRollback: '部分回滚',
                sys: '系统',
                email: '邮件',
                sms: '短信',
                wechat: '微信',
                dingding: '钉钉',
                aliyun: '阿里云',
                huyiwuxian: '互亿无线',
                tencent: '腾讯',
                enable: '启用',
                disable: '禁用',
                Running: '运行中',
                TrialPeriod: '试用中',
                Expired: '已到期',
                addExtendRecord: '延长服务时长'
            }
        },
        exportRecords: {
            fileName: '文件名称',
            operator: '操作人',
            timeScope: '时间范围',
            refresh: '刷新',
            table: {
                fileName: '文件名称',
                status: '状态',
                operator: '操作人',
                createTime: '创建时间',
                operation: '操作',
                downLoad: '下载'
            },
            msg: {
                msg1: '用户导出的文件生成后会暂存在服务器上，30天后会自动删除',
                msg2: '请输入文件名称',
                msg3: '请输入操作人',
                msg4: '用户导出的文件生成后会暂存在服务器上，',
                msg5: '天后会自动删除'
            }
        },
        topMenu: {
            addMenu: '新增菜单',
            editMenu: '编辑菜单',
            pleaseInput: '请输入',
            pleaseSelect: '请选择',
            isEmpty: '不能为空',
            addSuccess: '新增成功',
            editSuccess: '编辑成功',
            deleteAllMsg: '您确定要删除这些菜单记录吗？',
            deleteMsg: '您确定要删除该菜单记录吗？',
            selectItem: '请选择删除项',
            listControl: '最多添加10条数据',
            setMenu: '配置菜单',
            setSuccess: '设置成功',
            column: {
                name: '菜单名称',
                code: '菜单编号',
                icon: '图标',
                sort: '排序',
                releaseTime: '发布时间',
                order: '菜单顺序'
            },
            message: {
                requireNum: '菜单顺序必须为数字',
                enterName: '请输入菜单名称',
                enterNumber: '请输入菜单编号',
                enterIcon: '请选择图标',
                enterOrder: '请输入菜单排序'
            }
        },
        informationAnnounce: {
            title: '标题',
            content: '内容',
            publisher: '发布人',
            timeRange: '时间范围',
            publishStatus: '发布状态',
            startDate: '开始时间',
            to: '至',
            endDate: '结束时间',
            add: '新增',
            delete: '删除',
            selectDept: '选择',
            message: {
                inputTheme: '请输入标题',
                inputContent: '请输入内容',
                inputPublisher: '请输入发布人',
                inputPublishStatus: '请选择发布状态',
                titleMax: '标题最大长度为50',
                selectClass: '请选择分类',
                deleteTitle: '删除提示',
                deleteItem: '您确定要删除该公告信息吗？',
                deleteItems: '您确定要删除这些公告信息吗？',
                toDeleteItems: '请选择删除项',
                deleteSuccess: '删除成功',
                releaseTitle: '发布提示',
                releaseBefore: '您确定要发布【',
                releaseBack: '】吗？',
                releaseSuccess: '发布成功',
                fileMax: '单个附件上传最大为20M',
                fileFormat:
                    '支持 pdf 、ppt、jpg、png 、jpeg、excel、word 等文件文件格式。',
                addSuccess: '新增成功',
                editSuccess: '编辑成功',
                publishSuccess: '发布成功',
                alreadyDelete: '您查看的公告（通知）已被发布者删除！',
                searchPerson: '请输入姓名/手机号查询',
                noticeNoEmpty: '通知范围不能为空'
            },
            columnName: {
                number: '序号',
                title: '标题',
                publisher: '发布人',
                class: '类别',
                publishTime: '发布时间',
                readTimes: '阅读次数',
                status: '状态',
                operateType: '操作'
            },
            operate: {
                edit: '编辑',
                publish: '发布',
                delete: '删除',
                preview: '预览'
            },
            addPage: {
                title: '标题',
                class: '分类',
                noticeInfo: '公告信息',
                addFile: '添加附件',
                noticeRange: '通知范围',
                attachmentsList: '附件列表',
                back: '@:common.return',
                addUser: '请选择推送部门或人员',
                btn: {
                    preview: '预览',
                    save: '保存',
                    publish: '发布'
                },
                selectedPart: '已选部门',
                selectedPerson: '已选人员'
            }
        },
        messageTemplate: {
            templateNumber: '模板编号',
            templateName: '模板名称',
            templateType: '消息类型',
            messageSendType: '消息渠道',
            sendingPlatform: '发送平台',
            platformId: '平台对应ID',
            templateStatus: '模板状态',
            createTime: '创建时间',
            startTime: '开始时间',
            endTime: '结束时间',
            search: '查询搜索',
            templateImport: '模板导入',
            templateDescOne: '请先下载',
            messageTemplate: '消息模板',
            templateDescTwo: '，然后点击上传按钮上传文件',
            inportService: '导入服务',
            clickUpload: '点击上传',
            reMark: '备注：数据不能超过1000条 (支持xlsx、xls格式)',
            disabledTemplate: '你确定禁用这个模板吗？',
            endbleTemplate: '你确定启用这个模板吗？',
            statusTipTitle: '确认提示',
            exceedMax: ' 您导入的数据为空或大于1000条，请您重新调整',
            exceedMaxTip: '导入模板中的数据后再导入。',
            importTip: '导入提示',
            column: {
                number: '序号',
                templateContent: '模板内容',
                templateParamMap: '模板参数映射',
                operate: '操作'
            },
            placeholder: {
                number: '请输入模板编号',
                name: '请输入模板名称',
                templateType: '请选择消息类型',
                messageType: '请选择消息渠道',
                platform: '请选择发送平台',
                platformId: '请输入平台对应ID',
                tempplateStatus: '请选择模板状态',
                templateContent: '请输入模板内容',
                templateParamMap: '请输入模板参数影射',
                deleteFailure: '删除失败，',
                templateIdUnexcixt: '模板ID不存在'
            },
            operation: {
                reset: '重置',
                search: '查询',
                add: '新增',
                export: '导入',
                batchDelete: '批量删除',
                columnOptional: '列可选',
                edit: '编辑',
                detail: '详情',
                delete: '删除',
                success: '操作成功',
                toDeleteItems: '请选择删除项',
                confirmDelete: '你确定要删除消息模板吗？',
                deleteTitle: '删除提示',
                operateSuccess: '操作成功',
                operateFailure: '操作失败'
            }
        }
    }
};
