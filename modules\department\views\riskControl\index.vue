<template>
    <div>
        <department-selector
            @input="handleDepartmentChange"
            placeholder="请选择部门或小组"
        ></department-selector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="工期工时预警" name="workHoursRisk">
                    <WorkHoursAlert
                        ref="workHoursAlertRef"
                        :department="departmentValue"
                        :group="groupValue"
                        :orgValue="orgValue"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import WorkHoursAlert from './components/WorkHoursAlert';
import departmentSelector from 'department/components/departmentSelector';

export default {
    components: { WorkHoursAlert, departmentSelector },
    data() {
        return {
            activeName: 'workHoursRisk',
            departmentValue: '',
            groupValue: '',
            // 当前组织机构的值
            orgValue: ''
        };
    },
    methods: {
        /**
         * 组织或小组改变之后的动作
         * @param {Array} value 选中的值
         */
        async handleDepartmentChange(value) {
            // 处理路径，获取选择的部门或小组
            if (value.length === 3) {
                this.departmentValue = await value[1];
                this.groupValue = await value[2];
            } else if (value.length === 2) {
                this.departmentValue = await value[1];
                this.groupValue = '';
            } else if (value.length === 1) {
                this.departmentValue = await value[0];
                this.groupValue = '';
            }
            this.orgValue = value[value.length - 1];
        }
    }
};
</script>

<style scoped lang="scss">
.box-main {
    padding: 0 15px;
    max-height: calc(100vh - 65px);
    overflow-y: auto;
}
</style>
