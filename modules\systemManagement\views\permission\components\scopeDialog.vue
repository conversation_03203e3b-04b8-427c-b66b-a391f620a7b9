<template>
  <el-dialog
    v-if="scopeDialogVisible"
    :title="dialogTitle"
    :visible.sync="scopeDialogVisible"
    @close="closeWindowHandler"
  >
    <el-form
      ref="scopeForm"
      :rules="scopeFormRules"
      :model="scopeDialogFormData"
      :disabled="dialogStatus === 'detail'"
      :hide-required-asterisk="dialogStatus === 'detail'"
      label-width="auto"
    >
      <el-form-item :label="$t('systemManagement.permission.dialog.dataScopeName')" prop="scopeName">
        <el-input v-model="scopeDialogFormData.scopeName" :placeholder="$t('systemManagement.permission.placeholder.msg1')" />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.permission.dialog.dataScopeCode')" prop="scopeCode">
        <el-input v-model="scopeDialogFormData.scopeCode" :placeholder="$t('systemManagement.permission.placeholder.msg2')" />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.permission.dialog.dataScopeColumn')" prop="scopeColumn">
        <el-input v-model="scopeDialogFormData.scopeColumn" :placeholder="$t('systemManagement.permission.placeholder.msg5')" />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.permission.dialog.dataScopeType')" prop="scopeType">
        <el-select v-model="scopeDialogFormData.scopeType" placeholder="请选择">
          <el-option v-for="item in scopeTypeOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('systemManagement.permission.dialog.dataScopeField')" prop="scopeField">
        <el-input v-model="scopeDialogFormData.scopeField" :autosize="{ minRows: 3, maxRows: 3}" maxlength="255" type="textarea" :placeholder="$t('systemManagement.permission.dialog.scopeFieldPlaceholder')" />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.permission.dialog.dataScopeClass')" prop="scopeClass">
        <el-input v-model="scopeDialogFormData.scopeClass" :autosize="{ minRows: 3, maxRows: 3}" maxlength="100" type="textarea" :placeholder="$t('systemManagement.permission.dialog.scopeClassPlaceholder')" />
      </el-form-item>
      <el-form-item :label="$t('systemManagement.permission.dialog.remarks')" prop="remarks">
        <el-input v-model="scopeDialogFormData.remarks" :autosize="{ minRows: 3, maxRows: 3}" maxlength="255" type="textarea" :placeholder="$t('systemManagement.permission.placeholder.msg4')" />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button v-if="dialogStatus !== 'detail'" @click="closeWindowHandler">
        {{ $t('common.cancel') }}
      </el-button>
      <el-button v-if="dialogStatus !== 'detail'" type="primary" @click="dialogStatus==='create'?createData():updateData()">
        {{ $t('common.done') }}
      </el-button>
      <el-button v-if="dialogStatus === 'detail'" type="primary" @click="closeWindowHandler">
        {{ $t('common.done') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
    name: 'ScopeDialog',
    props: {
        // 是否显示弹窗
        showWindow: {
            type: Boolean,
            default: false
        },
        // 标识符，弹窗是新增权限还是编辑权限，新增--'create' 更新--'update' 查看--'detail'
        dialogStatus: {
            type: String,
            default: ''
        },
        // 当前正在处理的节点对象
        processingScopeItem: {
            type: Object,
            default() {
                return {};
            }
        },
        // 上个页面列表行数据的permissionId
        permissionId: {
            type: String,
            default: '-1'
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            scopeTypeOptions: [
                {
                    value: 0,
                    label: 'systemManagement.permission.dialog.dataScopeType_0'
                },
                {
                    value: 1,
                    label: 'systemManagement.permission.dialog.dataScopeType_1'
                },
                {
                    value: 2,
                    label: 'systemManagement.permission.dialog.dataScopeType_2'
                },
                {
                    value: 3,
                    label: 'systemManagement.permission.dialog.dataScopeType_3'
                },
                {
                    value: 4,
                    label: 'systemManagement.permission.dialog.dataScopeType_4'
                }
            ],
            // 新增权限页面formdata对象
            scopeDialogFormData: {
                // 数据权限id
                scopeId: '',
                // 数据权限名称
                scopeName: '',
                // 数据权限code
                scopeCode: '',
                // 权限字段
                scopeColumn: '',
                // 权限类型  0：自定义 1：本人可见 2：所在机构可见 3：所在机构及以下可见 3：全部可见
                scopeType: 0,
                // 可见字段
                scopeField: '',
                // 权限类名
                scopeClass: '',
                // 备注
                remarks: '',
                oldParam: {},
                // 权限id
                permissionId: '',
                // reset方法
                reSet() {
                    this.scopeName = '';
                    this.scopeCode = '';
                    this.scopeColumn = '';
                    this.scopeType = 0;
                    this.scopeField = '';
                    this.scopeClass = '';
                    this.remarks = '';
                    this.permissionId = '';
                    this.scopeId = '';
                    this.oldParam = {};
                },
                initUpdateData(dataObject) {
                    this.scopeId = dataObject.scopeId || '';
                    this.scopeName = dataObject.scopeName || '';
                    this.scopeCode = dataObject.scopeCode || '';
                    this.scopeColumn = dataObject.scopeColumn || '';
                    this.scopeType = dataObject.scopeType || 0;
                    this.scopeField = dataObject.scopeField || '';
                    this.scopeClass = dataObject.scopeClass || '';
                    this.remarks = dataObject.remarks || '';
                    this.oldParam = dataObject.oldParam || '';
                },
                getData(id) {
                    if (this.dialogStatus === 'create') {
                        return {
                            scopeId: this.scopeId,
                            scopeName: this.scopeName,
                            scopeCode: this.scopeCode,
                            scopeColumn: this.scopeColumn,
                            scopeType: this.scopeType,
                            scopeField: this.scopeField,
                            scopeClass: this.scopeClass,
                            remarks: this.remarks,
                            permissionId: id
                        };
                    } 
                        return {
                            scopeId: this.scopeId,
                            scopeName: this.scopeName,
                            scopeCode: this.scopeCode,
                            scopeColumn: this.scopeColumn,
                            scopeType: this.scopeType,
                            scopeField: this.scopeField,
                            scopeClass: this.scopeClass,
                            remarks: this.remarks,
                            permissionId: id,
                            oldParam: this.oldParam
                        };
                    
                }
            },
            // 新增权限页面是否弹窗变量
            scopeDialogVisible: false
        };
    },
    computed: {
        dialogTitle() {
            // dialogStatus === 'detail'
            let title = '数据权限';
            if (this.dialogStatus === 'create') {
                title = this.$t('systemManagement.permission.dialog.addTitle');
            } else if (this.dialogStatus === 'detail') {
                title = this.$t('systemManagement.permission.dialog.detailTitle');
            } else if (this.dialogStatus === 'update') {
                title = this.$t('systemManagement.permission.dialog.updateTitle');
            }

            return title;
        },
        // eslint-disable-next-line max-lines-per-function
        scopeFormRules() {
            // 校验函数，不允许输入中文
            const checkNoChineseData = (rule, value, callback) => {
                if (value === '') {
                    return callback(new Error(this.$t('systemManagement.msg.required')));
                }
                const regBox = {
                    regWords: /[\u4E00-\u9FA5]/g
                    // regWords: /^([\u4e00-\u9fa50-9]+|[A-Za-z0-9]+)$/g
                };
                const result = regBox.regWords.test(value);
                if (!result) {
                    return callback();
                } 
                    return callback(new Error(this.$t('systemManagement.msg.checkMsg_1')));
                
            };
            return {
                scopeName: [
                    {
                        required: true,
                        message: this.$t('systemManagement.msg.required'),
                        trigger: ['blur', 'change']
                    },
                    {
                        min: 1,
                        max: 20,
                        message: this.$t('systemManagement.msg.checkMsg_7'),
                        trigger: ['blur', 'change']
                    }
                ],
                scopeCode: [
                    {
                        required: true,
                        validator: checkNoChineseData,
                        trigger: ['blur', 'change']
                    },
                    {
                        min: 1,
                        max: 10,
                        message: this.$t('systemManagement.msg.checkMsg_2'),
                        trigger: ['blur', 'change']
                    }
                ],
                scopeColumn: [
                    {
                        required: true,
                        validator: checkNoChineseData,
                        trigger: ['blur', 'change']
                    },
                    {
                        min: 1,
                        max: 100,
                        message: this.$t('systemManagement.msg.checkMsg_8'),
                        trigger: ['blur', 'change']
                    }
                ],
                scopeType: [
                    {
                        required: true,
                        message: this.$t('systemManagement.msg.checkMsg_3'),
                        trigger: ['blur', 'change']
                    }
                ],
                scopeField: [
                    {
                        required: true,
                        message: this.$t('systemManagement.msg.required'),
                        trigger: ['blur', 'change']
                    },
                    {
                        min: 1,
                        max: 255,
                        message: this.$t('systemManagement.msg.checkMsg_6'),
                        trigger: ['blur', 'change']
                    }
                ],
                scopeClass: [
                    {
                        required: true,
                        message: this.$t('systemManagement.msg.required'),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    watch: {
        showWindow(val) {
            if (val) {
                this.scopeDialogFormData.reSet();
                if (this.dialogStatus === 'update' || this.dialogStatus === 'detail') {
                    this.scopeDialogFormData.initUpdateData(
                        this.processingScopeItem
                    );
                }
                this.scopeDialogVisible = true;
            } else {
                this.scopeDialogVisible = false;
            }
        }
    },
    methods: {
        // 抛出事件让父页面的树刷新数据
        updateScopeData() {
            this.$emit('scopeNeedUpdateEvent');
        },
        // 点击确定按钮后，先校验，后保存数据
        createData() {
            this.$refs['scopeForm'].validate((valid) => {
                if (valid) {
                    const param = Object.assign(this.scopeDialogFormData.getData(this.permissionId), {
                        permissionName: this.processingScopeItem.permissionName,
                        permissionCode: this.processingScopeItem.permissionCode
                    });
                    this.$service.systemManagement
                        .postCreateDataScope(
                            param
                        )
                        .then((response) => {
                            if (response.head.code === '000000') {
                                this.$message({
                                    title: this.$t('common.success'),
                                    message: this.$t('systemManagement.msg.createSuccess'),
                                    type: 'success',
                                    duration: 2000
                                });
                                // 刷新父页面数据
                                this.updateScopeData();
                                // 关闭窗口
                                this.closeWindowHandler();
                            } else {
                                const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                }
            });
        },
        // 编辑权限弹窗，更新权限，点击确定按钮后，先校验，后保存数据
        updateData() {
            this.$refs['scopeForm'].validate((valid) => {
                if (valid) {
                    const param = Object.assign(this.scopeDialogFormData.getData(this.permissionId), {
                        permissionName: this.processingScopeItem.permissionName,
                        permissionCode: this.processingScopeItem.permissionCode
                    });
                    this.$service.systemManagement
                        .putUpdateDataScope(
                            param
                        )
                        .then((response) => {
                            if (response.head.code === '000000') {
                                this.$message({
                                    title: this.$t('common.success'),
                                    message: this.$t('systemManagement.msg.editSuccess'),
                                    type: 'success',
                                    duration: 2000
                                });
                                // 刷新父页面数据
                                this.updateScopeData();
                                // 关闭窗口
                                this.closeWindowHandler();
                            } else {
                                this.$message({
                                    title: this.$t('systemManagement.msg.failed'),
                                    // message: this.$t('systemManagement.msg.editFailed'),
                                    message: response.head.message,
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                }
            });
        },
        // 关闭窗口处理
        closeWindowHandler() {
            this.scopeDialogVisible = false;
            this.$emit('update:showWindow', false);
        }
    }
};
</script>
