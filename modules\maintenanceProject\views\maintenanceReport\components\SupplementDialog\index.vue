<template>
    <el-dialog
        title="进展补充"
        :visible.sync="dialogVisible"
        :before-close="reset"
        width="700px"
        append-to-body
    >
        <el-form :model="form" label-width="100px">
            <el-form-item label="进展补充">
                <el-input
                    type="textarea"
                    v-model="form.supplement"
                    :autosize="{ minRows: 4, maxRows: 6 }"
                    maxlength="500"
                />
            </el-form-item>
        </el-form>
        <div class="footer" slot="footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="save">保 存</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'SupplementDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        supplement: {
            type: String,
            default: ''
        },
        id: {
            type: String,
            default: ''
        },
        apiService: {
            type: Function,
            default: null
        }
    },

    data() {
        return {
            form: {
                supplement: ''
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.form.supplement = this.supplement;
            }
        }
    },
    methods: {
        /**
         * 关闭对话框
         */
        closeDialog() {
            this.reset();
        },
        /**
         * 保存补充信息
         */
        async save() {
            // 如果传入了自定义API服务，使用自定义的；否则使用默认的
            const api =
                this.apiService ||
                this.$service.maintenanceProject.weekly.editSupplementInfo;
            const params = {
                id: this.id,
                progressDetail: this.form.supplement
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.$emit('success');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 重置组件状态
         */
        reset() {
            this.form = {
                supplement: ''
            };
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.w-50 {
    width: 50%;
}
.w-100 {
    width: 100%;
}
.space-between {
    justify-content: space-between;
}
.ml-10 {
    margin-left: 10px;
}
.pl-20 {
    padding-left: 20px;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}

::v-deep.form .el-form-item__label {
    font-weight: bold;
}
</style>
