<template>
    <el-dialog :visible.sync="showDialogImgVisible" width="700px" :title="$t('frame.selectPicBox.title')" @close="closeShowImg">
        <el-row :gutter="20">
            <el-col :span="16">
                <div ref="imgCutterBox" class="grid-content bg-purple" style="height: 300px; overflow: hidden">

                    <vue-cropper ref="cropper" :img="option.img" :output-size="option.size" :output-type="option.outputType" :info-true="true" :full="option.full" :fixed="fixed" :fixed-number="fixedNumber" :can-move="option.canMove" :can-move-box="option.canMoveBox" :fixed-box="option.fixedBox" :original="option.original" :auto-crop="option.autoCrop" :auto-crop-width="option.autoCropWidth" :auto-crop-height="option.autoCropHeight" :center-box="option.centerBox" :high="option.high" mode="cover" :max-img-size="option.max" @real-time="realTime" />
                </div>
            </el-col>
            <el-col :span="2">
                <div style="text-align: center; padding: 10px;" />
            </el-col>
            <el-col :span="6">
                <div style="text-align: left; padding: 10px;">
                    {{ $t('frame.selectPicBox.picPreview') }}
                </div>
                <div :style="previewStyle">
                    <div :style="previews.div">
                        <img :src="previews.url" :style="previews.img">
                    </div>
                </div>
                <div v-if="showDownload &&!!imgSrc" style="text-align: left;padding-top: 20px;">
                    <el-link type="success" :class="'btn btn-light' + (imgSrc ? '' : ' disabled')" :disabled="!imgSrc" :href="imgSrc" download="demoimage.jpg">{{ $t('frame.selectPicBox.picDownload') }}</el-link>
                </div>
            </el-col>
            <el-col>
                <div class="image-type">
                    图片格式只支持：jpg、jpeg、png。
                </div>
            </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">

            <label class="inputbtn" for="uploads">{{ $t('frame.selectPicBox.selectPic') }}</label>
            <input id="uploads" ref="referenceUpload" type="file" style="position:absolute; clip:rect(0 0 0 0);" accept=".png, .jpeg, .bmp, .jpg, .gif" @change="uploadImg($event)">
            <el-button type="primary" :disabled="imgSrc === ''" @click="finish('blob')">
                {{ $t('frame.selectPicBox.picSave') }}</el-button>
        </div>
    </el-dialog>
</template>
<script>
// 剪辑图片的控件
import { VueCropper } from 'vue-cropper';

export default {
    name: 'ImgCutterModal',
    components: { VueCropper },
    // prop是单向绑定，不能更改数据，只能由父组件传输过来
    props: {
        // 是否显示下载图片的
        showDownload: {
            type: Boolean,
            default: false
        },
        // 最大的显示模块 默认为50K
        maxFileLength: {
            type: Number,
            default: 512000
        },
        // 是否压缩图片
        isCompressImg: {
            type: Boolean,
            default: true
        },
        // 引用插件的识别 1表示上传头像，其它表示上传图片
        postFileIndex: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            // 是否展示对话框
            dialogImgVisible: false,
            // 截屏数据
            imgSrc: '',
            // 返回最终的图形内容
            userimgSrc: '',
            // 文件名
            downloadName: '',
            previews: {},
            lists: [
                {
                    img: ''
                }
            ],
            option: {
                img: '',
                size: 1,
                // 是否输出原图比例的截图
                full: false,
                outputType: 'jpeg',
                canMove: true,
                // 截图框固定大小
                fixedBox: false,
                // 上传图片是否显示原始宽高 (针对大图 可以铺满)
                original: false,
                // 能否拖动截图框
                canMoveBox: true,
                autoCrop: true,
                // 只有自动截图开启 宽度高度才生效
                autoCropWidth: 250,
                autoCropHeight: 250,
                // 是否在固定框框里
                centerBox: true,
                high: false,
                max: 99999
            },
            show: true,
            fixed: true,
            // 预览样式
            previewStyle: {},
            fixedNumber: [1, 1]
        };
    },
    computed: {
        showDialogImgVisible: {
            get() {
                return this.dialogImgVisible;
            },
            set(val) {
                this.dialogImgVisible = val;
            }
        },
        showDownloadHeight() {
            return this.showDownload ? '200px' : '240px';
        }
    },
    methods: {
        // 关闭时候
        closeShowImg() {
            this.option.img = '';
            this.imgSrc = '';
            // 将选择文件内容清空
            this.$refs.referenceUpload.value = null;
            this.dialogImgVisible = false;
        },

        // 保存 输出
        finish(type) {
            // test.document.body.innerHTML = '图片生成中..'
            if (type === 'blob') {
                // 不需要判断直接上传
                this.$refs.cropper.getCropData((data) => {
                    // 压说
                    this.compressbase64(data, 120, 1).then((res) => {
                        this.uploadBase64(res);
                    });
                });
            } else {
                this.$refs.cropper.getCropData((data) => {
                    this.model = true;
                    this.modelSrc = data;
                });
            }
        },
        // 实时预览函数
        realTime(data) {
            // 预览图片
            this.imgSrc = data.url;
            this.previews = data;
            // 固定为 120 高度
            this.previewStyle = {
                width: `${this.previews.w}px`,
                height: `${this.previews.h}px`,
                overflow: 'hidden',
                margin: '0px',
                zoom: 120 / this.previews.h
            };
            this.previews = data;
            // 支持下载的时候转换
            if (this.showDownload) {
                // eslint-disable-next-line no-shadow
                this.$refs.cropper.getCropData((data) => {
                    this.imgSrc = data;
                });
            }
        },
        // 上传图片
        uploadImg(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                if (
                    !/\.(gif|jpg|jpeg|png|bmp)$/.test(
                        e.target.value.toLowerCase()
                    )
                ) {
                    this.$message({
                        title: this.$t('systemManagement.msg.failed'),
                        message: this.$t(
                            'frame.selectPicBox.message.picFormatErrorMsg'
                        ),
                        type: 'error',
                        duration: 2000
                    });
                    return false;
                }
                const reader = new FileReader();
                // eslint-disable-next-line no-shadow
                reader.onload = (e) => {
                    let data;
                    if (typeof e.target.result === 'object') {
                        // 把Array Buffer转化为blob 如果是base64不需要
                        data = window.URL.createObjectURL(
                            new Blob([e.target.result])
                        );
                    } else {
                        data = e.target.result;
                    }
                    this.option.img = data;
                };
                const index = e.target.value.lastIndexOf('\\');
                this.downloadName = e.target.value.slice(index + 1);
                // 转化为blob
                reader.readAsArrayBuffer(file);
                // 将选择文件内容清空
                this.$refs.referenceUpload.value = null;
            }
        },
        // base64压缩图片，压缩的宽，压缩质量
        compressbase64(base64String, w, quality) {
            const newImage = new Image();
            let imgWidth;
            let imgHeight;
            // 异步处理
            const promise = new Promise(
                // eslint-disable-next-line no-return-assign
                (resolve) => (newImage.onload = resolve)
            );
            newImage.src = base64String;
            return promise.then(() => {
                imgWidth = newImage.width;
                imgHeight = newImage.height;
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (Math.max(imgWidth, imgHeight) > w) {
                    if (imgWidth > imgHeight) {
                        canvas.width = w;
                        canvas.height = (w * imgHeight) / imgWidth;
                    } else {
                        canvas.height = w;
                        canvas.width = (w * imgWidth) / imgHeight;
                    }
                } else {
                    canvas.width = imgWidth;
                    canvas.height = imgHeight;
                }
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(newImage, 0, 0, canvas.width, canvas.height);
                let imgType = this.downloadName.slice(
                    this.downloadName.indexOf('.') + 1
                );
                if (imgType === 'jpg') {
                    imgType = 'jpeg';
                }
                const base64 = canvas.toDataURL(`image/${imgType}`, quality);
                return base64;
            });
        },
        // base64转码（压缩完成后的图片为base64编码，这个方法可以将base64编码转回file文件）
        dataURLtoFile(dataurl) {
            const arr = dataurl.split(',');
            const mime = arr[0].match(/:(.*?);/)[1];
            const bstr = atob(arr[1]);
            let n = bstr.length;
            const u8arr = new Uint8Array(n);
            while (n) {
                // eslint-disable-next-line no-plusplus
                n--;
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], { type: mime });
        },
        // uploadBase64
        uploadBase64(data) {
            const files = this.dataURLtoFile(data);
            const formData = new FormData();
            formData.append('file', files, this.downloadName);
            if (this.postFileIndex === 1) {
                this.$service.frame
                    .postUploadFiles(formData)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            this.userimgSrc = response.body;
                            this.$emit('imgCutDown', this.userimgSrc);
                            this.dialogImgVisible = false;
                        } else {
                            const { code } = response.head;
                            const msg = `frame.bgReturnError[${code}]`;
                            this.$message({
                                message:
                                    code === '920303'
                                        ? this.$t(msg).replace(
                                              '{0}',
                                              response.head.message
                                          )
                                        : this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
            } else {
                this.$service.frame
                    .postUploadFileUrl(formData)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            this.userimgSrc = response.body;
                            this.$emit('imgCutDown', this.userimgSrc);
                            this.dialogImgVisible = false;
                        } else {
                            const { code } = response.head;
                            const msg = `frame.bgReturnError[${code}]`;
                            this.$message({
                                message:
                                    code === '920303'
                                        ? this.$t(msg).replace(
                                              '{0}',
                                              response.head.message
                                          )
                                        : this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
            }
            // 传递裁剪的事件
            this.$emit('imgCutDown', this.userimgSrc);
            this.dialogImgVisible = false;
        }
    }
};
</script>

<style scoped >
.inputbtn {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #c0ccda;
    color: #1f2d3d;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 20px 10px 0px 0px;
    padding: 9px 15px;
    font-size: 14px;
    border-radius: 4px;
    color: #fff;
    background-color: #3370ff;
    border-color: #3370ff;
    transition: all 0.2s ease;
    text-decoration: none;
    user-select: none;
}
.image-type {
    margin-top: 10px;
    font-size: 12px;
    color: #ff4949;
}
</style>
