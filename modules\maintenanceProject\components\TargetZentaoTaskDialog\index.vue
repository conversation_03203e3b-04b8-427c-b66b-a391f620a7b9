<template>
    <div>
        <el-dialog
            title="建立禅道任务"
            :visible.sync="dialogVisible"
            :before-close="reset"
            width="500px"
        >
            <el-form ref="form" :model="form" class="form" size="large">
                <el-form-item
                    label="目标禅道项目"
                    prop="proProjectId"
                    :rules="required"
                >
                    <el-select
                        class="w-100"
                        v-model="form.proProjectId"
                        placeholder="请选择关联禅道项目"
                        filterable
                    >
                        <el-option
                            v-for="item in zentaoProjectList"
                            :label="item.name"
                            :key="item.id"
                            :value="item.id"
                        ></el-option
                    ></el-select>
                </el-form-item>
            </el-form>
            <div class="footer" slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'TargetZentaoTaskDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectManager: {
            type: String,
            default: ''
        },
        // 是否为订单或者开发项目的风险页面
        isOrderOrRisk: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            form: { proProjectId: '' },
            zentaoProjectList: [],
            required: {
                required: true,
                message: '请选择目标禅道项目',
                trigger: ['change', 'blur']
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.$refs.form && this.$refs.form.resetFields();
                this.getZentaoProjectList();
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.$refs.form.resetFields();
            this.dialogVisible = false;
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                return;
            }
            this.$emit('save', this.form.proProjectId);
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 关闭弹窗前的回调
         * @param {Function} done 关闭弹窗的函数
         */
        reset(done) {
            this.$refs.form.resetFields();
            done();
        },
        /**
         * 获取禅道项目下拉列表
         */
        async getZentaoProjectList() {
            const api =
                this.$service.maintenanceProject.zentao.getZentaoProjectList;
            const params = {
                projectCode: this.isOrderOrRisk ? '' : '维护',
                projectManagerAccount: this.projectManager
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.zentaoProjectList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.w-100 {
    width: 100%;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
}
</style>
