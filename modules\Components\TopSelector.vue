<template>
    <!-- TopSelector 顶部选择器组件，用于展示项目状态展示和级联选择 -->
    <div class="top-selector-container" v-if="!infoDisabled">
        <!-- 项目信息和状态展示区域 -->
        <div class="info" :style="{ width: infoWidth }">
            <!-- 级联选择器组件，用于进行项目选择操作 -->
            <div class="infoTag">
                <el-cascader
                    class="selector"
                    @change="handleChange"
                    :placeholder="placeholder"
                    :props="props"
                    v-model="inputValue"
                    :options="options"
                    ref="cascaderRef"
                    popper-class="topselector-cascader"
                >
                    <div
                        slot-scope="{ data }"
                        @click="clickNode"
                        class="span-click"
                    >
                        {{ data.label }}
                    </div>
                </el-cascader>
                <!-- 项目状态显示区域，根据当前状态显示对应的文本和样式 -->
                <div class="status" :style="statusStyle" v-if="!statusDisabled">
                    {{ statusText }}
                </div>
            </div>
        </div>
    </div>
</template>
<script>
/**
 * TopSelector 组件
 * @module Components/TopSelector
 * @desc 该组件用于展示信息的级联选择和状态展示
 * @param {String} [placeholder]   - 级联选择器的占位符文本
 * @param {Boolean} [statusDisabled] - 控制状态展示区域是否禁用的布尔值
 * @param {Boolean} [infoDisabled]  - 控制级联选择器是否禁用的布尔值
 * @param {Array} [value] - 级联选择器当前选择的值，数组格式
 * @param {Function} [lazyLoad] - 动态加载处理函数
 * @example 调用示例
 *  <TopSelector :placeholder="placeholder" :statusDisabled="statusDisabled" :infoDisabled="infoDisabled" :value="value"></TopSelector>
 * */

export default {
    name: 'TopSelector',
    props: {
        placeholder: {
            type: String
        },
        statusDisabled: {
            type: Boolean,
            default: false
        },
        infoDisabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: Array,
            default: () => []
        },
        options: {
            type: Array,
            default: () => []
        },
        status: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            props: {
                // 配置动态加载
                lazy: true,
                lazyLoad: this.lazyLoadHandler,
                // 是否可以直接选中父节点
                checkStrictly: true,
                // 配置展开方式
                expandTrigger: 'hover',
                // 悬停状态保持时间，小于这个时间不会触发hover事件
                hoverThreshold: 150
            },
            inputValue: this.$props.value,
            // 当前项目状态
            currentStatus: ''
        };
    },
    computed: {
        statusText() {
            // 根据状态返回相应的中文文本
            return this.currentStatus || '项目状态';
        },
        statusStyle() {
            // 根据状态返回相应的样式
            const colorMap = {
                市场待立项: '#909399',
                已取消: '#ffcccc',
                排队中: '#FFA500',
                进行中: '#67C23A',
                已暂停: '#800080',
                已结项: '#409EFF',
                已终止: '#8B4513'
            };
            return {
                'background-color': colorMap[this.currentStatus] || '#606266'
            };
        },
        // 展示信息的宽度
        infoWidth() {
            return this.statusDisabled ? '380px' : '430px';
        }
    },
    mounted() {
        // 挂载后向外传递ref
        this.$nextTick(() => {
            this.$emit('cascaderRef', this.$refs.cascaderRef);
        });
        // 如果外部传入status,则使用外部的值
        this.currentStatus = this.status;
    },
    methods: {
        /**
         * 通过点击文字选中的处理函数
         * @param {Object} e 事件对象
         */
        clickNode(e) {
            // 模拟点击对应的radio
            e.target.parentElement.parentElement.firstElementChild.click();
        },
        /**
         * 选择的值发生变化时触发
         * @param {Array} value 选择的值
         */
        handleChange(value) {
            // 变更项目状态
            const selectedOption = this.$refs.cascaderRef?.getCheckedNodes();
            if (!selectedOption) return;
            // 根据选择的值查找option中对应的选项，从而改变项目状态信息
            if (selectedOption[0]?.data && selectedOption[0]?.data?.status) {
                this.currentStatus = selectedOption[0].data.status;
            } else {
                this.currentStatus = '';
            }
            // 向父组件传递选中的值
            this.$emit('input', value);

            // 每次选择结束之后自动关闭
            if (this.$refs?.cascaderRef?.dropDownVisible) {
                this.$refs.cascaderRef.dropDownVisible = false;
            }
        },
        /**
         * 向父组件传递动态加载事件
         * @param {Object} node 节点
         * @param {Function} resolve 加载成功后的回调
         */
        lazyLoadHandler(node, resolve) {
            this.$emit('lazyLoad', node, resolve);
        }
    }
};
</script>

<style lang="scss" scoped>
.top-selector-container {
    height: 45px;
    display: flex;
    margin: 10px 0 7px 16px;
}
.info {
    width: 430px;
    background-color: #fff;
    margin-right: 10px;
}
.infoTag {
    height: 45px;
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    justify-content: center;
    align-items: center;
    position: relative;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    .selector {
        margin: 0 10px;
        flex: 1;
        ::v-deep .el-input--mini .el-input__inner {
            line-height: 34px;
            height: 34px;
            font-weight: 400;
        }
    }
    .status {
        font-size: 12px;
        font-weight: 400;
        width: 100px;
        padding: 10px 15px;
        color: #fff;
        text-align: center;
        height: 34px;
        margin-right: 10px;
    }
}
.infoTag:after {
    content: '';
    position: absolute;
    right: -14px;
    top: 0px;
    width: 0;
    height: 0;
    border-top: 23px solid transparent;
    border-bottom: 23px solid transparent;
    border-left: 15px solid #f0f0f0;
}
// 利用placeholder进行数据回显，修改字体颜色
::v-deep .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685) !important;
}
.span-click {
    width: 100%;
}
</style>
<style lang="scss">
// 隐藏单选框
.el-cascader-panel .el-radio__input {
    display: none;
}
.el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
}
.topselector-cascader .el-cascader-menu__wrap {
    height: 300px;
}
</style>
