<template>
    <div class="report-weekly-container">
        <el-button type="primary" @click="hanldeReturn" class="return-button"
            >返回</el-button
        >
        <Header @select-change="handleSelectChange" />
        <DevProjectList
            v-show="activeComponent === 'DevProjectList'"
            @select-change="handleSelectChange"
        ></DevProjectList>
        <div
            style="padding: 0 20px"
            v-show="
                activeComponent === 'DevProjectDetail' ||
                activeComponent === 'MaintainProjectDetail'
            "
        >
            <DevProjectDetail
                v-show="activeComponent === 'DevProjectDetail'"
                style="
                    height: calc(100vh - 80px);
                    overflow-y: auto;
                    padding-right: 10px;
                "
            ></DevProjectDetail>
            <el-tabs
                v-model="maintainActiveName"
                v-show="activeComponent === 'MaintainProjectDetail'"
            >
                <el-tab-pane label="需求周报" name="demand" :lazy="true">
                    <Demand></Demand>
                </el-tab-pane>
                <el-tab-pane label="订单周报" name="order" :lazy="true">
                    <Order></Order>
                </el-tab-pane>
                <el-tab-pane label="缺陷周报" name="defect" :lazy="true">
                    <Defect></Defect>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
/**
 * 该页面为点击上部页头切换展出出的三个组件的集合
 * 三个组件分别为：开发项目列表，开发项目详情，维护项目详情
 * 其中：点击开发项目列表的具体值，可以跳转到开发项目详情
 * 选择页头上的开发项目/具体的开发项目/维护项目分别对应展示这三个组件
 */

import DevProjectList from './DevProjectList';
import Header from 'reportForm/views/weekly/components/Header';
import DevProjectDetail from 'project/views/projectReport/components/ProjectWeekly';
import Demand from 'maintenanceProject/views/maintenanceReport/Demand';
import Order from 'maintenanceProject/views/maintenanceReport/Order';
import Defect from 'maintenanceProject/views/maintenanceReport/Defect';

export default {
    name: 'ReportFormWeeklyInfo',
    components: {
        DevProjectList,
        Header,
        DevProjectDetail,
        Demand,
        Order,
        Defect
    },
    props: {},
    data() {
        return {
            activeComponent: '',
            developActivename: 'projectWeekly',
            maintainActiveName: 'demand'
        };
    },
    computed: {
        // 当前选中的页头路径值
        weeklyValue() {
            return this.$store.state.reportForm.weeklyValue;
        },
        // 当前选中的页头选项值
        weeklyOption() {
            return this.$store.state.reportForm.weeklyOption;
        }
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
    },
    beforeDestroy() {
        // 切换页面时，如果不是切换到报表首页，清除周报选择项
        if (this.weeklyValue.length !== 1) {
            this.$store.dispatch('reportForm/setOutsideWeekly', null);
            this.$store.dispatch('reportForm/selectedWeeklyValue', []);
            this.$store.dispatch('reportForm/selectedWeeklyOption', []);
        }
    },
    deactivated() {
        // 切换页面时，如果不是切换到报表首页，清除周报选择项
        if (this.weeklyValue.length !== 1) {
            this.$store.dispatch('reportForm/setOutsideWeekly', null);
            this.$store.dispatch('reportForm/selectedWeeklyValue', []);
            this.$store.dispatch('reportForm/selectedWeeklyOption', []);
        }
    },
    methods: {
        hanldeReturn() {
            let newWeeklyValue;
            let newWeeklyOption;

            if (this.weeklyValue.length === 3) {
                newWeeklyValue = this.weeklyValue.slice(0, 2);
                newWeeklyOption = this.weeklyOption.slice(0, 2);
            } else if (this.weeklyValue.length === 2) {
                newWeeklyValue = this.weeklyValue.slice(0, 1);
                newWeeklyOption = this.weeklyOption.slice(0, 1);
            } else {
                return;
            }

            this.$store.dispatch(
                'reportForm/selectedWeeklyValue',
                newWeeklyValue
            );
            this.$store.dispatch(
                'reportForm/selectedWeeklyOption',
                newWeeklyOption
            );
            this.handleSelectChange();
        },
        /**
         * @description: 页头select改变
         */
        handleSelectChange() {
            const { isDevelopWeekly } = this.$store.state.reportForm;
            this.$nextTick(() => {
                // 选择了具体的开发项目，直接切换到开发项目周报详情
                if (this.weeklyValue.length === 3) {
                    this.activeComponent = 'DevProjectDetail';
                } else if (this.weeklyValue.length === 2) {
                    if (isDevelopWeekly) {
                        // 展示开发周报列表
                        this.activeComponent = 'DevProjectList';
                    } else {
                        // 展示维护周报详情
                        this.activeComponent = 'MaintainProjectDetail';
                        this.$store.dispatch(
                            'reportForm/setOutsideWeekly',
                            this.weeklyOption[1].maintainReportIdList
                        );
                    }
                } else {
                    const { path } = this.$route;
                    // 根据路径长度判断选中的哪一级
                    if (
                        this.weeklyValue.length === 1 &&
                        path !== '/reportForm/reportFormWeekly'
                    ) {
                        this.$router.push({
                            path: '/reportForm/reportFormWeekly'
                        });
                        return;
                    }
                    this.activeComponent = '';
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.return-button {
    position: absolute;
    top: 10px;
    right: 20px;
}
.report-weekly-container {
    position: relative;
}
</style>
