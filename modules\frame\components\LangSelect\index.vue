<template>
  <el-dropdown placement="bottom" trigger="click" class="international" @command="handleSetLanguage">
    <div class="avatar-wrapper">
      <div v-if="isChinese" class="lang-box">
        <svg-icon icon-class="china" class="current-lang" />
        <span v-show="isShow" class="lang-name">{{ $t('frame.lang.zh') }}</span>
      </div>
      <div v-else class="lang-box">
        <svg-icon icon-class="english" class="current-lang" />
        <span v-show="isShow" class="lang-name">{{ $t('frame.lang.en') }}</span>
      </div>
      <i class="el-icon-caret-bottom" />
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language==='zh'" command="zh">
        <svg-icon icon-class="china" class="lang-icon" />
        <span>{{ $t('frame.lang.zh') }}</span>
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='en'" command="en">
        <svg-icon icon-class="english" class="lang-icon" />
        <span>{{ $t('frame.lang.en') }}</span>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import Cookies from 'js-cookie';

export default {
    name: 'LangSelect',
    props: {
        isShow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isChinese: true
        };
    },
    computed: {
        language() {
            return this.$store.getters.language;
        }
    },
    mounted() {
        if (Cookies.get('language') && Cookies.get('language') === 'en') {
            this.isChinese = false;
        } else {
            this.isChinese = true;
        }
    },
    methods: {
        // 切换语言
        handleSetLanguage(lang) {
            this.$i18n.locale = lang;
            if (lang === 'en') {
                this.isChinese = false;
            } else {
                this.isChinese = true;
            }
            this.$store.dispatch('app/setLanguage', lang);
            this.$message({
                message: this.$t('common.changeLanguage'),
                type: 'success'
            });
        }
    }
};
</script>
<style scoped lang="scss">
	.lang-icon.svg-icon{
		width: 30px;
		height:20px;
		margin-right:10px;
	}
	.avatar-wrapper{
		display: flex;
		align-items: center;
		color:#fff;
		.lang-box{
			display:flex;
			align-items: center;
			.current-lang{
				width:30px;
				height:20px;
			}
			.lang-name{
				margin-left:5px;
			}
		}
	}
</style>
