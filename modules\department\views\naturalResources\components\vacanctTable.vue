<template>
    <div class="box-table">
        <div class="vacanct-top">
            <el-button
                type="primary"
                size="mini"
                icon="el-icon-help"
                v-if="showtvacantpermission()"
                @click="vacanctAdd('add')"
                >新增缺编资源</el-button
            >
        </div>
        <el-table
            :data="vacanctData"
            border
            style="width: 100%"
            :header-cell-style="{ background: '#3370ff' }"
            class="snbc-table"
        >
            <el-table-column
                prop="lackName"
                label="缺编人员名称"
                align="center"
                width="120"
                fixed
            >
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">{{ scope.row.lackName }}</div>
                        <div class="ellipsis">{{ scope.row.lackName }}</div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                prop="post"
                label="岗位"
                width="120"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="level"
                label="级别"
                width="120"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="expectStartDate"
                label="期望到岗时间"
                align="center"
                width="160"
            >
            </el-table-column>

            <el-table-column
                prop="projectNames "
                label="逾期到岗影响项目"
                header-align="center"
                align="left"
            >
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">
                            {{ scope.row.projectNames }}
                        </div>
                        <div class="ellipsis">
                            {{ scope.row.projectNames }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                prop="impactConsequence"
                label="影响后果"
                header-align="center"
                align="left"
            >
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">
                            {{ scope.row.impactConsequence }}
                        </div>
                        <div class="ellipsis">
                            {{ scope.row.impactConsequence }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                align="center"
                width="90"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        size="small"
                        v-if="showtvacantpermission()"
                        @click="vacanctAdd('edit', scope.row)"
                        >编辑</el-button
                    >
                    <el-button
                        type="text"
                        size="small"
                        v-if="showtvacantpermission()"
                        @click="vacanctDel(scope.row)"
                        style="color: red"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="paging"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="queryParams.total"
            @size-change="vacanctSizeChange"
            @current-change="vacanctCurrentChange"
            :page-size="20"
        />
        <el-dialog
            :title="vacanctVisible.title"
            :visible.sync="vacanctVisible.isShow"
            class="vanclass"
        >
            <el-form
                ref="dataForm"
                label-position="right"
                :model="userEditDataInfo"
                :rules="userRules"
            >
                <div class="dialog-box">
                    <el-form-item label="缺编人员名称" prop="lackName">
                        <el-input
                            v-model="userEditDataInfo.lackName"
                            placeholder="请输入缺编人员名称"
                            maxlength="50"
                            clearable
                            style="width: 80% !important"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="岗位" prop="post">
                        <el-select
                            v-model="userEditDataInfo.post"
                            filterable
                            clearable
                            placeholder="请选择岗位"
                            style="width: 80% !important"
                        >
                            <el-option
                                v-for="item in postData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="级别" prop="level">
                        <el-select
                            v-model="userEditDataInfo.level"
                            placeholder="请选择级别"
                            filterable
                            clearable
                            style="width: 80% !important"
                        >
                            <el-option
                                v-for="item in levelData"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="期望到岗时间" prop="expectStartDate">
                        <el-date-picker
                            v-model="userEditDataInfo.expectStartDate"
                            type="date"
                            placeholder="请选择期望到岗时间"
                            value-format="yyyy-MM-dd"
                            style="width: 80% !important"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="逾期到岗影响项目" prop="projectIdList">
                        <el-select
                            v-model="userEditDataInfo.projectIdList"
                            placeholder="请选择项目"
                            filterable
                            multiple
                            clearable
                            style="width: 80% !important"
                        >
                            <el-option
                                v-for="item in projectdata"
                                :key="item.projectId"
                                :label="item.projectName"
                                :value="item.projectId"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="影响后果" prop="impactConsequence">
                        <el-input
                            type="textarea"
                            :rows="5"
                            placeholder="请输入影响后果"
                            maxlength="500"
                            v-model="userEditDataInfo.impactConsequence"
                            style="width: 80% !important"
                        >
                        </el-input>
                    </el-form-item>
                </div>
            </el-form>
            <span slot="footer">
                <el-button @click="vacanctCancel">取消</el-button>
                <el-button
                    type="primary"
                    @click="vacanctSave"
                    :disabled="isSave"
                    >确认</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';

const { LEAVEL_GRADE, POST_STATION, PROJECT_ROLE } = CONSTANTS;

export default {
    props: {
        vacanctData: {
            type: Array,
            required: true
        },
        queryParams: {
            type: Object,
            required: true
        },
        departmentCode: String
    },
    data() {
        return {
            vacanctVisible: {
                title: '',
                isShow: false,
                type: ''
            },
            postData: [],
            roleData: [],
            levelData: [],
            projectSelect: [],
            isSave: false,
            userEditDataInfo: {},
            // 表单必填
            userRules: {
                post: [
                    { required: true, message: '请选择岗位', trigger: 'change' }
                ],
                level: [
                    { required: true, message: '请选择级别', trigger: 'change' }
                ],
                lackName: [
                    {
                        required: true,
                        message: '请输入缺编人员名称,最多50个字符',
                        trigger: 'blur'
                    }
                ],
                expectStartDate: [
                    {
                        required: true,
                        message: '请选择期望到岗时间',
                        trigger: 'change'
                    }
                ],
                projectIdList: [
                    {
                        required: true,
                        message: '请选择逾期到岗影响项目',
                        trigger: 'change'
                    }
                ],
                impactConsequence: [
                    {
                        required: true,
                        message: '请输入影响后果，最多500个字符',
                        trigger: 'change'
                    }
                ]
            },
            projectdata: []
        };
    },

    mounted() {
        const api = this.$service.department.naturalResources;
        api.getProjects().then((res) => {
            if (res.head.code === '000000') {
                this.projectSelect = res.body || [];
            } else {
                this.$message.error(res.head.message);
            }
        });
        api.getProjectselect().then((res) => {
            if (res.head.code === '000000') {
                this.projectdata = res.body || [];
            } else {
                this.$message.error(res.head.message);
            }
        });
    },
    methods: {
        showtvacantpermission() {
            return this.$store.state.permission.btnDatas.includes('vacancyBtn');
        },
        vacanctAdd(type, data) {
            if (this.$refs.dataForm) {
                this.$refs.dataForm.resetFields();
            }

            if (!this.departmentCode) {
                this.$message.error('请选择部门后新增！');
                return;
            }
            this.vacanctVisible.isShow = true;
            this.vacanctVisible.type = type;
            this.postData = POST_STATION;
            this.roleData = PROJECT_ROLE;
            this.levelData = LEAVEL_GRADE;
            this.vacanctVisible.title =
                type === 'add' ? '新增缺编资源' : '编辑缺编资源';
            this.userEditDataInfo = type === 'add' ? {} : { ...data };
        },
        vacanctSave() {
            // 避免重复提交
            if (this.isSave) {
                return;
            }
            this.$refs.dataForm.validate((valid) => {
                if (!valid) {
                    return false;
                }
                // 禁用确认按钮
                this.isSave = true;
                const params = {
                    ...this.userEditDataInfo,
                    departmentCode: this.departmentCode
                };
                const serviceMethod =
                    this.vacanctVisible.type === 'add'
                        ? this.$service.department.naturalResources
                              .addnaturalResources
                        : this.$service.department.naturalResources
                              .editnaturalResources;
                serviceMethod(params).then((res) => {
                    if (res.head.code === '000000') {
                        this.vacanctVisible.isShow = false;
                        this.$message.success(
                            `${
                                this.vacanctVisible.type === 'add'
                                    ? '新增'
                                    : '编辑'
                            }成功`
                        );
                        // 保存成功后启用确认按钮
                        this.isSave = false;
                        this.$emit('add-success');
                    } else {
                        this.$message.error(res.head.message);
                        // 保存失败时启用确认按钮
                        this.isSave = false;
                    }
                });
            });
        },
        // 取消
        vacanctCancel() {
            this.vacanctVisible.isShow = false;
        },
        // 分页
        vacanctSizeChange(val) {
            this.$emit('vacanct-size-change', val);
        },
        vacanctCurrentChange(val) {
            this.$emit('vacanct-current-change', val);
        },
        // 删除
        vacanctDel(val) {
            this.$confirm('请确认删除该条信息?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                cancelButtonClass: 'custom-cancel-button',
                type: 'warning',
                showCancelButton: true,
                showClose: false
            }).then(() => {
                const params = {
                    id: val.id + ''
                };
                this.$service.department.naturalResources
                    .delnaturalResources(params)
                    .then((res) => {
                        if (res.head.code === '000000') {
                            this.$message.success('删除成功');
                            this.$emit('search-delete');
                        } else {
                            this.$message.error(res.head.message);
                        }
                    });
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.box-table {
    width: 100%;
    display: flex;
    flex-direction: column;
}
.paging {
    padding-top: 10px;
}
.vacanct-top {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 5px 0px 5px 0px;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
.dialog-box {
    width: 100%;
    display: flex;
    flex-direction: column;
}
.natural-pop1 {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-around;
}

.natural-pop {
    width: 100%;
    height: 70px;
    display: flex;
    align-items: center;
    position: relative;
}
.natural-text {
    width: 100%;
    height: 50px;
    font-size: 14px;
    font-weight: bolder;
    display: flex;
    align-items: center;
}

::v-deep .el-form-item--mini .el-form-item__error {
    padding-top: 1px;
    padding-left: 35px !important;
}
/**改变边框颜色*/
::v-deep .el-table {
    border: 1px solid #8c8c8c !important;
}
.vanclass {
    ::v-deep .el-form-item__label {
        width: 150px !important;
        display: flex;
        justify-content: flex-end;
    }
    ::v-deep .el-dialog .el-dialog__body {
        padding: 20px 20px 20px 65px !important;
    }
}

::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep .el-table tr {
    height: 40px !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}
.ellipsis {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #3370ff;
}
.snbc-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
}
::v-deep .el-table__fixed-header-wrapper {
    height: 40px !important;
    display: flex;
    align-items: center;
}
::v-deep .el-dialog .el-dialog__body .el-form-item__label {
    padding: 6px 17px 0 0 !important;
}
</style>
