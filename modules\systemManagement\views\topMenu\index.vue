<template>
    <div class="view">
        <div class="container">
            <div class="container">
                <div class="header">
                    <span class="header__title">{{
            $t("systemManagement.listTitle")
          }}</span>
                    <span class="header__btns">
                        <el-button v-permission="['config_manage_add']" type="primary" icon="fa fa-plus-square-o" @click="handleAdd">
                            {{ $t("common.add") }}
                        </el-button>
                        <el-button type="default" icon="fa fa-trash-o" class="button" @click="handleDelete">
                            {{ $t("common.delete") }}
                        </el-button>
                    </span>
                </div>
                <el-table id="configurationTable" :data="tableData" border :header-cell-style="{ background: '#F5F6FA' }" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="45" align="center" />
                    <el-table-column prop="navigationName" :label="$t('systemManagement.topMenu.column.name')" />
                    <el-table-column prop="navigationCode" :label="$t('systemManagement.topMenu.column.code')" />
                    <el-table-column prop="navigationIcon" :label="$t('systemManagement.topMenu.column.icon')" />
                    <el-table-column prop="navigationOrder" :label="
              $t('systemManagement.topMenu.column.sort')
            " />
                    <el-table-column prop="createTime" :label="$t('systemManagement.topMenu.column.releaseTime')" width="240px">
                        <template slot-scope="{ row }">
                            <span>{{
                row.createTime.split("T").join(" ")
              }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')" width="340px">
                        <template slot-scope="{ row,$index}">
                            <el-button v-permission="['config_manage_edit']" type="primary" icon="fa fa-pencil" size="mini" class="button table-inner-button" @click="handleEdit(row)">
                                {{ $t("table.edit") }}
                            </el-button>
                            <el-button type="primary" size="mini" icon="fa fa-cog" @click="getMenuTree(row,$index)">
                                {{ $t("systemManagement.configure") }}
                            </el-button>
                            <el-button v-permission="['config_manage_delete']" size="mini" icon="fa fa-trash-o" class="button table-delete-button" @click="handleDelete(row, 'signal')">
                                {{ $t("table.delete") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 添加、编辑顶部菜单弹窗 -->
        <el-dialog :title="!isEdit ? $t('systemManagement.topMenu.addMenu') : $t('systemManagement.topMenu.editMenu')" :visible.sync="addDialogVisible">
            <el-form ref="tableForm" :model="menuItem" label-position="right" label-width="auto" :rules="menuRules" class="custom-form">
                <el-form-item :label="
            $t(
              'systemManagement.topMenu.column.name'
            )
          " prop="navigationName">
                    <el-input v-model="menuItem.navigationName" maxlength="45" :placeholder="$t('systemManagement.topMenu.message.enterName')" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.topMenu.column.code'
            )
          " prop="navigationCode">
                    <el-input v-model="menuItem.navigationCode" maxlength="45" :validate-event="false" :placeholder="$t('systemManagement.topMenu.message.enterNumber')" />
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.topMenu.column.icon'
            )
          " prop="navigationIcon">
                    <el-input v-model="menuItem.navigationIcon" readonly :placeholder="$t('systemManagement.topMenu.message.enterIcon')" class="choose-icon-input" @click.native="chooseIconClickHandler">
                        <div slot="append" class="choose-icon-show">
                            <i v-if="selectedIcon.isElementIcon" :class="selectedIcon.className" />
                            <svg-icon v-else :icon-class="selectedIcon.className" class-name="disabled" />
                        </div>
                    </el-input>
                </el-form-item>
                <el-form-item :label="
            $t(
              'systemManagement.topMenu.column.order'
            )
          " prop="navigationOrder">
                    <el-input v-model.number="menuItem.navigationOrder" maxlength="5" :placeholder="$t('systemManagement.topMenu.message.enterOrder')" />
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="cancel">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="saveMenu">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>
        <!-- 选择图标的弹框 -->
        <el-dialog :title="$t('systemManagement.permission.dialog.chooseIcon')" width="50%" :visible.sync="chooseIconDialogVisible">
            <icons ref="iconComponent" :selected-icon-name="menuItem.navigationIcon" @iconSelectedEventByClick="iconSelectedEventByClickHandler" @iconSelectedEventByDBClick="iconSelectedEventByDBClickHandler" />
            <div slot="footer">
                <el-button type="primary" @click="chooseIconDialogVisible = false">
                    {{ $t('common.done') }}
                </el-button>
            </div>
        </el-dialog>
        <!-- 配置顶部菜单抽屉 -->
        <el-drawer :modal="false" :visible.sync="visibleType" custom-class="menu-tree-box" :title="`${$t(
        'systemManagement.topMenu.setMenu'
      )}`" :append-to-body="true" :show-close="false" :before-close="closeDrawer" @setCheckedKeys="setCheckedKeys">
            <div class="tree-content">
                <div class="tree">
                    <el-input v-model="searchValue" :placeholder="$t('systemManagement.permission.placeholder.msg1')" class="search-input-view">
                        <i slot="suffix" class="fa fa-search el-input__icon" />
                    </el-input>

                    <el-tree ref="tree" :highlight-current="true" :expand-on-click-node="false" :check-on-click-node="true" :default-expanded-keys="defaultShowNodes" :data="treeSource" :props="defaultProps" node-key="permissionId" show-checkbox default-expand-all :filter-node-method="filterNode" :empty-text="$t('systemManagement.logger.noData')" @check="handleCheckChange">
                        <el-tooltip slot-scope="{ node }" :content="node.label" placement="right">
                            <span class="block">
                                <span class="block__text">{{
                  node.label
                }}</span>
                            </span>
                        </el-tooltip>
                    </el-tree>
                </div>
                <div class="footer">
                    <el-button @click="visibleType = false">{{
            $t("table.cancel")
          }}</el-button>
                    <el-button type="primary" @click="saveNodes">{{
            $t("table.confirm")
          }}</el-button>
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import Icons from '../../components/Icons';

export default {
    name: 'TopMenu',
    components: { Icons },
    data() {
        return {
            // 表格数据
            tableData: [],
            // 新增顶部菜单
            menuItem: {
                navigationName: '',
                navigationCode: '',
                navigationIcon: '',
                navigationOrder: ''
            },
            // 新增菜单弹窗
            addDialogVisible: false,
            // 选择图标页面是否弹窗变量
            chooseIconDialogVisible: false,
            // 已经选中的图标对象
            selectedIcon: {
                iconName: '',
                className: '',
                isChecked: false,
                isElementIcon: true
            },
            // 是否是编辑窗口
            isEdit: false,
            // 列表选中项
            multipleSelection: [],
            // 控制抽屉显示隐藏
            visibleType: false,
            // 权限树数据源
            treeSource: [],
            searchValue: '',
            // 这里存放要默认展开的节点 id
            defaultShowNodes: ['-1'],
            // 树节点绑定的值
            defaultProps: {
                children: 'children',
                label: 'permissionName'
            },
            currentCheckedNodes: [],
            currentMenuId: '',
            currentSetIndex: 0
        };
    },
    computed: {
        // 表单验证规则
        menuRules() {
            // 校验函数，不允许输入中文
            const checkNoChineseData = (rule, value, callback) => {
                if (!value) {
                    return callback(
                        new Error(
                            `${this.$t(
                                'systemManagement.topMenu.column.code'
                            )}${this.$t('systemManagement.topMenu.isEmpty')}`
                        )
                    );
                }
                if (value.length > 45) {
                    return callback(
                        new Error(this.$t('systemManagement.msg.checkMsg_5'))
                    );
                }
                const regBox = {
                    regWords: /[\u4E00-\u9FA5]/g
                };
                const result = regBox.regWords.test(value);
                if (result) {
                    return callback(
                        new Error(this.$t('systemManagement.msg.checkMsg_1'))
                    );
                }
                callback();
            };
            return {
                // 菜单名称
                navigationName: [
                    {
                        required: true,
                        message: `${this.$t(
                            'systemManagement.topMenu.column.name'
                        )}${this.$t('systemManagement.topMenu.isEmpty')}`,
                        trigger: ['blur', 'change']
                    },
                    {
                        min: 1,
                        max: 10,
                        message: this.$t('systemManagement.msg.checkMsg_2'),
                        trigger: ['blur', 'change']
                    }
                ],
                // 菜单编号
                navigationCode: [
                    { validator: checkNoChineseData, required: true }
                ],
                // 菜单图标
                navigationIcon: [
                    {
                        required: true,
                        message: `${this.$t(
                            'systemManagement.topMenu.column.icon'
                        )}${this.$t('systemManagement.topMenu.isEmpty')}`,
                        trigger: ['blur', 'change']
                    }
                ],
                // 菜单顺序
                navigationOrder: [
                    {
                        required: true,
                        message: `${this.$t(
                            'systemManagement.topMenu.column.order'
                        )}${this.$t('systemManagement.topMenu.isEmpty')}`
                    },
                    {
                        type: 'number',
                        min: 1,
                        message: this.$t(
                            'systemManagement.topMenu.message.requireNum'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    watch: {
        searchValue(val) {
            this.$refs.tree.filter(val);
        }
    },
    created() {
        // 获取顶部菜单列表
        this.getMenuFuncList();
    },
    methods: {
        // 新增刷新顶部导航功能列表
        freshMenuFuncList() {
            this.$service.systemManagement.getMenuList().then((res) => {
                if (res.head.code === '000000') {
                    this.tableData = res.body;
                    if (this.tableData.length === 0) {
                        this.currentMenuId = '';
                        sessionStorage.setItem('boss-currentId', '');
                    }
                    // 查看当前的id是否存在，如果存在，找到对应的索引改变索引值，如果不存在则展示第一个
                    const currentId = sessionStorage.getItem('boss-currentId');
                    const isExist = this.tableData.some((item) => {
                        return item.navigationId === currentId;
                    });
                    if (isExist) {
                        this.tableData.forEach((item, index) => {
                            if (item.navigationId === currentId) {
                                this.currentMenuId = currentId;
                                sessionStorage.setItem(
                                    'boss-currentId',
                                    currentId
                                );
                                this.$store.dispatch(
                                    'user/setCurrentIndex',
                                    index
                                );
                            }
                        });
                    } else {
                        this.$store.dispatch('user/setCurrentIndex', 0);
                    }
                    // 存储当前的顶部菜单
                    this.$store.dispatch('user/setTopMenu', res.body);
                    // 刷新列表
                    this.getMenuFuncList();
                    // 刷新左侧菜单
                    this.getPermissionList();
                } else {
                    const msg = `productService.bgReturnError[${res.head.code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        getMenuFuncList() {
            this.$service.systemManagement.getMenuList().then((res) => {
                if (res.head.code === '000000') {
                    this.tableData = res.body;
                    if (this.tableData.length === 0) {
                        this.currentMenuId = '';
                    } else {
                        // 存储当前的顶部菜单
                        this.$store.dispatch('user/setTopMenu', res.body);
                    }

                    // 刷新左侧菜单
                    this.getPermissionList();
                } else {
                    const msg = `productService.bgReturnError[${res.head.code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        // 增加
        handleAdd() {
            // 清空提示
            this.$nextTick(() => {
                if (this.$refs.tableForm) {
                    this.$refs.tableForm.resetFields();
                }
                this.menuItem.navigationName = '';
                this.menuItem.navigationCode = '';
                this.menuItem.navigationIcon = '';
                this.menuItem.navigationOrder = '';
            });
            if (this.tableData.length === 10) {
                this.$message({
                    type: 'error',
                    message: this.$t('systemManagement.topMenu.listControl')
                });
            } else {
                this.addDialogVisible = true;
                this.isEdit = false;
            }
        },
        // 编辑
        handleEdit(param) {
            this.isEdit = true;
            this.addDialogVisible = true;
            // 编辑的表单数据对象赋值
            this.menuItem.navigationName = param.navigationName;
            this.menuItem.navigationCode = param.navigationCode;
            this.menuItem.navigationIcon = param.navigationIcon;
            this.menuItem.navigationId = param.navigationId;
            this.menuItem.navigationOrder = param.navigationOrder;
        },
        // 删除
        handleDelete(param, type) {
            if (type !== 'signal' && this.multipleSelection.length === 0) {
                this.$message({
                    message: this.$t('systemManagement.topMenu.selectItem'),
                    type: 'warning'
                });
                return false;
            }
            let tipMsg = this.$t('systemManagement.topMenu.deleteMsg');
            if (type !== 'signal') {
                tipMsg = this.$t('systemManagement.topMenu.deleteAllMsg');
            }
            this.$confirm(
                tipMsg,
                this.$t(
                    'systemManagement.configuration.message.deleteTipTitle'
                ),
                {
                    type: 'warning'
                }
            )
                .then(() => {
                    this.deleteParam(param, type);
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: this.$t('systemManagement.msg.canceled')
                    });
                });
        },
        //  取消弹框
        cancel() {
            this.addDialogVisible = false;
            this.clearItem();
        },
        // 关闭弹窗的回调
        clearItem() {
            this.isEdit = false;
            // 重置表单内容
            this.menuItem.navigationName = '';
            this.menuItem.navigationCode = '';
            this.menuItem.navigationIcon = '';
            this.menuItem.navigationOrder = '';
            this.$refs['tableForm'].resetFields();
        },
        // 用户列表check选择
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 删除菜单
        deleteParam(param, type) {
            let params = {};
            // 如果是单独删除
            if (type === 'signal') {
                params = {
                    navigationIds: param.navigationId
                };
            } else {
                params = {
                    navigationIds: this.multipleSelection
                        .map((item) => item.navigationId)
                        .join(',')
                };
            }
            // 调用删除接口
            this.$service.systemManagement.deleteTopMenu(params).then((res) => {
                if (res.head.code === '000000') {
                    this.getMenuFuncList();
                    // 刷新列表
                    this.freshMenuFuncList();
                    this.$message.success(
                        this.$t(
                            'systemManagement.configuration.message.deleteSuccess'
                        )
                    );
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        // 新增、编辑保存菜单
        saveMenu() {
            this.$refs['tableForm'].validate((valid) => {
                if (valid) {
                    // 调用接口
                    if (!this.isEdit) {
                        // 如果是新增，则调用新增接口
                        this.$service.systemManagement
                            .postAddTopMenu(this.menuItem)
                            .then((res) => {
                                if (res.head.code === '000000') {
                                    this.$message({
                                        message: this.$t(
                                            'systemManagement.topMenu.addSuccess'
                                        ),
                                        type: 'success'
                                    });
                                    // 关闭弹窗
                                    this.addDialogVisible = false;
                                    this.clearItem();
                                    // 刷新列表
                                    this.freshMenuFuncList();
                                } else {
                                    const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                                    this.$message({
                                        message: this.$t(msg),
                                        type: 'error'
                                    });
                                }
                            });
                    } else {
                        // 否则调编辑接口
                        this.$service.systemManagement
                            .putEditTopMenu(this.menuItem)
                            .then((res) => {
                                if (res.head.code === '000000') {
                                    this.$message({
                                        message: this.$t(
                                            'systemManagement.topMenu.editSuccess'
                                        ),
                                        type: 'success'
                                    });
                                    // 关闭弹窗
                                    this.addDialogVisible = false;
                                    // 刷新列表
                                    this.freshMenuFuncList();
                                } else {
                                    const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                                    this.$message({
                                        message: this.$t(msg),
                                        type: 'error'
                                    });
                                }
                            });
                    }
                }
            });
        },
        // 单击选中图标后，触发的事件，用于记录选中的图标
        iconSelectedEventByClickHandler(item) {
            if (item) {
                this.selectedIcon = item;
            }
            if (this.selectedIcon) {
                this.menuItem.navigationIcon = this.selectedIcon.className;
            }
        },
        // 双击选中图标后，触发的事件，用于记录选中的图标
        iconSelectedEventByDBClickHandler(item) {
            this.selectedIcon = item;
            if (this.selectedIcon) {
                this.menuItem.navigationIcon = this.selectedIcon.className;
                // 双击选中图标后，直接关闭窗口
                this.chooseIconDialogVisible = false;
            }
        },
        // 点击图标input框，在弹窗中选择icon
        chooseIconClickHandler() {
            this.chooseIconDialogVisible = true;
        },
        // 菜单配置弹窗
        getMenuTree(row, index) {
            this.visibleType = true;
            // 清空搜索条件
            this.searchValue = '';
            this.getPermissionTreeData();
            this.currentMenuId = row.navigationId;
            // 获取所选菜单的菜单
            this.currentSetIndex = index;
            this.getCurrentMenu();
        },
        // 获得权限树    数据源
        getPermissionTreeData() {
            this.treeLoading = true;
            this.$service.systemManagement
                .getPermissionTreeList({ level: 0 })
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.treeSource = response.body;
                        this.$nextTick(() => {
                            this.$refs.tree.filter(this.searchValue);
                        });
                    }
                    this.treeLoading = false;
                })
                .catch(() => {
                    this.treeLoading = false;
                });
        },
        // 树节点展开
        handleNodeExpand(data) {
            // 保存当前展开的节点
            let flag = false;
            // eslint-disable-next-line array-callback-return
            this.defaultShowNodes.some((item) => {
                if (item === data.permissionId) {
                    // 判断当前节点是否存在， 存在不做处理
                    flag = true;
                    return true;
                }
            });
            if (!flag) {
                // 不存在则存到数组里
                this.defaultShowNodes.push(data.permissionId);
            }
        },
        // 树节点关闭
        handleNodeCollapse(data) {
            // eslint-disable-next-line array-callback-return
            this.defaultShowNodes.some((item, i) => {
                if (item === data.permissionId) {
                    // 删除关闭节点
                    this.defaultShowNodes.length = i;
                }
            });
        },
        // 过滤节点
        filterNode(value, data) {
            if (!value) return true;
            return data.permissionName.indexOf(value) !== -1;
        },
        // 收起弹框
        closeDrawer() {
            // 清空搜索条件
            this.searchValue = '';
            this.visibleType = false;
        },
        // 处理树选择
        handleCheckChange(data, currentObj) {
            this.currentCheckedNodes = currentObj.checkedKeys;
        },
        // 保存配置的菜单
        saveNodes() {
            const param = {
                navigationId: this.currentMenuId,
                permissionIds: this.currentCheckedNodes
            };
            // 调用保存导航菜单关系接口
            this.$service.systemManagement
                .postSaveMenuRelation(param)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.topMenu.setSuccess'
                            ),
                            type: 'success'
                        });
                        this.visibleType = false;
                        // 如果设置的是当前菜单，则刷新左侧菜单
                        if (
                            this.currentSetIndex ===
                            this.$store.state.user.currentIndex
                        ) {
                            // 刷新左侧菜单
                            this.getPermissionList();
                        }
                    } else {
                        const msg = `productService.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 设置节点的勾选状态
        setCheckedKeys(keys) {
            this.$refs.tree.setCheckedKeys(keys);
        },
        // 获取当前顶部菜单对应的菜单
        getCurrentMenu() {
            const param = {
                navigationId: this.currentMenuId
            };
            // 调用保存导航菜单关系接口
            this.$service.systemManagement
                .getMenuRelation(param)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.setCheckedKeys(res.body);
                        this.currentCheckedNodes = res.body;
                    } else {
                        const msg = `productService.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 接口获取菜单数据+按钮权限数据
        getPermissionList() {
            const menuData = {
                // 系统标识 0：web系统 1:移动端 2：终端
                sysType: '0',
                langKey: 'cn',
                // 权限类型  1系统，2菜单，3按钮，4接口
                permissionType: '2',
                navId: this.currentMenuId
            };
            // 获取菜单信息
            this.$service.frame.getMenuList(menuData).then((resMenuList) => {
                if (resMenuList.head.code === '000000') {
                    this.$store.dispatch(
                        'permission/generateRoutes',
                        resMenuList.body
                    );

                    this.getFunctionList();
                }
            });
        },
        // 获取按钮权限数据
        getFunctionList() {
            // 获取权限按钮
            const btnData = {
                // 系统标识 0：web系统 1:移动端 2：终端
                sysType: '0',
                langKey: 'cn',
                // 权限类型  1系统，2菜单，3按钮，4接口
                permissionType: '3',
                navId: this.currentMenuId
            };
            this.$service.frame.getMenuList(btnData).then((resBtnList) => {
                if (resBtnList.head.code === '000000') {
                    const datas = resBtnList.body
                        ? resBtnList.body.map((item) => {
                              return item.permissionCode;
                          })
                        : [];
                    this.$store.dispatch('permission/btnPermissionData', datas);
                }
            });
        }
    }
};
</script>
<style lang="scss">
.el-message-box__wrapper {
    .el-message-box {
        .el-message-box__header {
            .el-message-box__title {
                font-size: 14px;
                font-weight: 700;
                color: #000000;
            }
        }
    }
}
</style>
<style lang="scss" scoped>
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.el-drawer__wrapper {
    margin-top: 105px;
    height: calc(100vh - 105px);
    ::v-deep .el-drawer__title {
        margin-bottom: 0px;
    }

    ::v-deep .el-drawer__body {
        &::-webkit-scrollbar {
            display: none;
        }
    }

    ::v-deep .el-drawer__header {
        background: #3271fe;
        height: 60px;
        font-size: 1.6rem;
        color: #ffffff;
        font-weight: bold;
        text-align: center;
        padding: 10px 10px;
        margin-bottom: 0px;
    }
    ::v-deep .menu-tree-box {
        width: 300px !important;
        height: calc(100vh - 125px) !important;
        background: #fff;
        .tree-content {
            height: 100%;
            .tree {
                height: calc(100% - 70px);
                padding-bottom: 70px;
                border-bottom: 1px solid #f5f6fa;
                .search-input-view {
                    margin-top: 10px;
                    width: 96%;
                    margin-left: 9px;
                    margin-bottom: 10px;
                }
                .el-tree {
                    overflow-y: auto;
                    height: 100%;
                }
            }
            .footer {
                position: absolute;
                background: #fff;
                z-index: 999999999;
                padding-bottom: 20px;
                margin-top: 20px;
                bottom: 0;
            }
        }
    }
}
</style>
