<template>
    <div class="view">
        <div class="wtf-components-user-info">
            <div class="title">{{ $t('frame.userInfo.title') }}</div>
            <div class="message-detail">
                <div
                    class="img-box"
                    @mouseenter="enterImg"
                    @mouseleave="leaveImg"
                >
                    <div v-show="showMask" class="mask" @click="showImg">
                        <span class="mask-text">{{
                            $t('frame.userInfo.reselect')
                        }}</span>
                    </div>
                    <img v-if="userInfo.avatar" :src="userInfo.avatar" />
                    <img v-else src="../../assets/default-img.png" />
                </div>
                <img-cutter-modal
                    ref="imgresult"
                    :max-file-length="51200"
                    :show-download="false"
                    :post-file-index="1"
                    @imgCutDown="imgcutDown"
                />
                <div class="info">
                    <div class="item">
                        <span class="name">{{
                            $t('frame.userInfo.infoName.name')
                        }}</span>
                        <span>{{ userInfo.userName }}</span>
                    </div>
                    <!-- <div class="item">
            <el-button :type="userInfo.userStatus | userStatusStyle" size="mini">{{
              userInfo.userStatusName
            }}</el-button>
          </div> -->
                    <div class="item">
                        <span class="name">{{
                            $t('frame.userInfo.infoName.phone')
                        }}</span>
                        <span>{{ userInfo.phone }}</span>
                        <span class="change" @click="changePhone">{{
                            $t('frame.userInfo.changePhone.change')
                        }}</span>
                    </div>
                    <div class="item">
                        <span class="name">{{
                            $t('frame.userInfo.infoName.jobNumber')
                        }}</span>
                        <span> {{ userInfo.jobNumber }}</span>
                    </div>
                </div>
            </div>
            <div class="options">
                <div class="item">
                    <span class="name">{{
                        $t('frame.userInfo.columnName.department')
                    }}</span>
                    <div class="org-box">
                        <span v-for="item in userInfo.orgInfo" :key="item.orgId"
                            >{{ item.orgName }}
                        </span>
                    </div>
                </div>
                <div class="item">
                    <span class="name">{{
                        $t('frame.userInfo.columnName.role')
                    }}</span>
                    <div class="role-box">
                        <div class="left">
                            <span
                                v-for="(item, index) in userInfo.roleInfo"
                                :key="item.roleId"
                                :class="{ active: index === 0 }"
                            >
                                {{ item.roleName }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div style="margin-top: 20px;">{{ $t('frame.userInfo.thirdBindTitle') }}</div>
            <div class="options">
                <div v-for="(item,index) in userBindArr" :key="index" class="item">
                    <div class="name">
                        <img :src="item.icon">
                        <span>{{ $t(item.label) }}</span>
                    </div>
                    <div class="part-box">
                        <div v-if="item.bindState">{{ userInfo.phone }}</div>
                        <div v-else class="unbound">{{ $t('frame.userInfo.columnName.Unbound') }}</div>
                        <div>
                            <el-button v-if="item.bindState" :disabled="item.bindName === 'DD'" @click="unbind">{{ $t('frame.userInfo.columnName.Unbinding') }}</el-button>
                            <el-button v-else type="primary" :disabled="item.bindName === 'DD'" @click="gotoBind">{{ $t('frame.userInfo.columnName.bind') }}</el-button>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>

        <el-dialog
            :title="$t('frame.userInfo.changePhone.securityVerify')"
            :visible.sync="verificatifyDialogVisible"
            custom-class="system-dialog custom-dialog change-phone-dialog"
            @close="cancelVerificatify"
        >
            <div class="desc">
                <div class="tip">
                    <i class="el-icon-warning" />
                    <span
                        >{{ $t('frame.userInfo.changePhone.msg.msg1') }}
                        <span>{{ userInfo.userName }} </span
                        >{{ $t('frame.userInfo.changePhone.msg.msg2') }}</span
                    >
                </div>
                <p>
                    {{ $t('frame.userInfo.changePhone.msg.msg3') }}
                    <span>{{ userInfo.phone }}</span
                    >：
                </p>
            </div>
            <div class="verification">
                <el-form
                    ref="dataForm"
                    :model="formDataInfoPhone"
                    :rules="rules"
                    size="normal"
                    label-width="auto"
                >
                    <el-form-item
                        :label="$t('frame.userInfo.change.verification')"
                        prop="smsCaptcha"
                    >
                        <el-input
                            v-model="formDataInfoPhone.smsCaptcha"
                            type="text"
                            autocomplete="off"
                            :placeholder="
                                $t('frame.changePwd.placeholder.smsCaptcha')
                            "
                        >
                            <el-button
                                v-if="sendCountDownPhone <= 0"
                                slot="append"
                                icon="el-icon-position"
                                class="send-sms-code"
                                @click="sendSmsCaptchaPhone('')"
                                >{{
                                    $t('frame.changePwd.sendCodeButton')
                                }}</el-button
                            >
                            <el-button
                                v-else
                                slot="append"
                                disabled
                                class="send-sms-code--disabled"
                                >{{
                                    $t('frame.changePwd.sendCountdown', [
                                        sendCountDownPhone
                                    ])
                                }}</el-button
                            >
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="btn-border" @click="cancelVerificatify">{{
                    $t('table.cancel')
                }}</el-button>
                <el-button
                    type="primary"
                    class="btn-bg"
                    @click="nextStepSecurity('dataForm')"
                    >{{ $t('frame.userInfo.change.next') }}</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            :title="$t('frame.userInfo.changePhone.setNewPhone')"
            :visible.sync="setDialogVisible"
            custom-class="system-dialog custom-dialog change-phone-dialog"
            @close="cancelSet"
        >
            <div class="verification">
                <el-form
                    ref="dataFormSet"
                    :model="formDataInfoPhoneSet"
                    :rules="rules"
                    size="normal"
                    label-width="auto"
                >
                    <el-form-item
                        :label="$t('frame.userInfo.changePhone.newPhone')"
                        prop="newPhone"
                    >
                        <el-input
                            v-model="formDataInfoPhoneSet.newPhone"
                            autocomplete="off"
                            class="new-phone"
                            :placeholder="
                                $t('frame.userInfo.changePhone.msg.msg6')
                            "
                            maxlength="16"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('frame.userInfo.change.verification')"
                        prop="smsCaptcha"
                    >
                        <el-input
                            v-model="formDataInfoPhoneSet.smsCaptcha"
                            type="text"
                            autocomplete="off"
                            :placeholder="
                                $t('frame.changePwd.placeholder.smsCaptcha')
                            "
                        >
                            <el-button
                                v-if="sendCountDownPhoneSet <= 0"
                                slot="append"
                                icon="el-icon-position"
                                class="send-sms-code"
                                @click="sendSmsCaptchaPhoneSet('')"
                                >{{
                                    $t('frame.changePwd.sendCodeButton')
                                }}</el-button
                            >
                            <el-button
                                v-else
                                slot="append"
                                disabled
                                class="send-sms-code--disabled"
                                >{{
                                    $t('frame.changePwd.sendCountdown', [
                                        sendCountDownPhoneSet
                                    ])
                                }}</el-button
                            >
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="btn-border" @click="cancelSet">{{
                    $t('table.cancel')
                }}</el-button>
                <el-button
                    type="primary"
                    class="btn-bg"
                    @click="finishChangePhone('dataFormSet')"
                    >{{ $t('frame.userInfo.changePhone.finished') }}</el-button
                >
            </span>
        </el-dialog>
        <el-dialog
            :title="$t('frame.userInfo.bind.title')"
            :visible.sync="bindDialogVisible"
            custom-class="system-dialog custom-dialog change-phone-dialog"
            @close="cancelBind"
        >
            <div class="verification">
                <el-form
                    ref="dataBind"
                    :model="formDataBind"
                    :rules="rules"
                    size="normal"
                    label-width="80px"
                >
                    <div class="desc">
                        <div class="tip">
                            <i class="el-icon-warning" />
                            <span>{{ $t('frame.userInfo.bind.tip') }} </span>
                        </div>
                    </div>
                    <el-form-item
                        :label="$t('frame.userInfo.bind.phone')"
                        prop="phone"
                    >
                        <el-input
                            v-model="userInfo.phone"
                            autocomplete="off"
                            class="new-phone"
                            maxlength="16"
                            :disabled="true"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('frame.userInfo.bind.veritify')"
                        prop="smsCaptcha"
                    >
                        <el-input
                            v-model="formDataBind.smsCaptcha"
                            type="text"
                            autocomplete="off"
                            :placeholder="
                                $t('frame.userInfo.bind.verificationMsg')
                            "
                        >
                            <el-button
                                v-if="sendCountDownBind <= 0"
                                slot="append"
                                icon="el-icon-position"
                                class="send-sms-code"
                                @click="sendSmsCaptchaBind"
                                >{{
                                    $t('frame.userInfo.bind.getVerification')
                                }}</el-button
                            >
                            <el-button
                                v-else
                                slot="append"
                                disabled
                                class="send-sms-code--disabled"
                                >{{
                                    $t('frame.changePwd.sendCountdown', [
                                        sendCountDownBind
                                    ])
                                }}</el-button
                            >
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="btn-border" @click="cancelBind">{{
                    $t('table.cancel')
                }}</el-button>
                <el-button
                    type="primary"
                    class="btn-bg"
                    @click="confirmVerify('dataBind')"
                    >{{ $t('frame.userInfo.bind.confirm') }}</el-button
                >
            </span>
        </el-dialog>
        <!-- qrCodeFrame -->
        <el-dialog
            :title="$t('frame.userInfo.bind.wxLogin')"
            :visible.sync="qrCodeFrame"
            custom-class="system-dialog custom-dialog bind-dialog"
            @close="closeDialog"
        >
            <div>
                <div class="code-content">
                    <div id="qrcode" ref="qrcode" />
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// 剪辑图片的封装组件
import imgCutterModal from '../../components/imgCutterModal';
import { encryptByMd5 } from 'wtf-core-vue/src/utils/crypto';
import { randomStr, randomTime } from 'wtf-core-vue/src/methods/signature';
import wechat from '../../assets/wechat-active.png';
import dingding from '../../assets/dingding-active.png';
import QRCode from 'qrcodejs2';

export default {
    // 引入剪辑图片的封装组件
    components: { imgCutterModal },
    filters: {
        // 工作状态按钮样式显示
        // 用户状态 0请假中 1生病中 2出差中 3会议中 4外出中 5忙碌中 6调休中
        userStatusStyle: (value) => {
            if (value === '5') {
                return 'danger';
            } else if (value === '3') {
                return 'warning';
            } else if (value === '4') {
                return 'info';
            }
            return 'primary';
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        const random = randomStr(13);
        const ranTime = randomTime();
        const final =
            random +
            ranTime +
            encryptByMd5(
                `${random}&${ranTime}axeonmc&/console/user/updateAvatarFile&POST`
            );
        return {
            // 头像上传地址
            avatarAction: '',
            avatarUploadHeaders: {
                'Authorization': `Bearer ${this.$tools.getToken()}`,
                'signature': final,
                'b-g-version': '2.0.16'
            },
            // 用户信息
            userInfo: {
                userId: '',
                userName: '',
                jobNumber: '',
                phone: '',
                // 头像地址
                avatar: '',
                // 用户状态，如 工作中、空闲中
                userStatus: '',
                // 用户所属组织机构，格式[{roleId,roleName}]
                orgInfo: [],
                // 用户所属组织机构，格式[{orgId,orgName}]
                roleInfo: []
            },
            // 图片是否显示遮罩
            showMask: false,
            // 安全验证弹框
            verificatifyDialogVisible: false,
            // 设置新手机号弹框
            setDialogVisible: false,
            // 原手机号短信发送倒计时
            sendCountDownPhone: 0,
            // 新手机号短信发送倒计时
            sendCountDownPhoneSet: 0,
            // 原手机号获取验证码
            formDataInfoPhone: {
                smsCaptcha: ''
            },
            // 新手机号验证码
            formDataInfoPhoneSet: {
                newPhone: '',
                smsCaptcha: ''
            },
            // 绑定验证
            formDataBind: {
                phone: '',
                smsCaptcha: ''
            },
            // 绑定验证弹框
            bindDialogVisible: false,
            sendCountDownBind: 0,
            // 二维码弹框
            qrCodeFrame: false,
            // 绑定二维码
            bindQrCodeUrl: '',
            // 轮询状态定时器
            qrCodeTimer: null,
            // 关闭二维码定时器
            clearQrcodeTime: null,
            isBindWechat: false,
            isBindDingding: false,
            userBindArr: [],
            bindIcon: {
                WX: wechat,
                DD: dingding
            },
            bindLabel: {
                WX: 'frame.userInfo.columnName.weChat',
                DD: 'frame.userInfo.columnName.dingding'
            }
        };
    },
    computed: {
        // 验证规则
        rules() {
            return {
                newPhone: [
                    {
                        required: true,
                        message: this.$t('frame.userInfo.changePhone.msg.msg4'),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^\d{5,16}$/,
                        message: this.$t('frame.userInfo.changePhone.msg.msg5'),
                        trigger: ['blur', 'change']
                    }
                ],
                smsCaptcha: [
                    {
                        required: true,
                        message: this.$t(
                            'frame.changePwd.message.ruleEmptySmsCaptcha'
                        ),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[0-9]{6}$/,
                        message: this.$t(
                            'frame.changePwd.message.ruleSmsCaptcha'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    created() {
        this.getUploadAvatarUrl();
        this.getUserInfo();
        // 获取用户绑定详情
        this.getBindDetail();
    },
    beforeDestroy() {
        window.clearTimeout(this.qrCodeTime);
        this.qrCodeTime = null;
        window.clearTimeout(this.clearQrcodeTime);
        this.clearQrcodeTime = null;
    },
    methods: {
        // 调用接口，获取用户信息
        getUserInfo() {
            this.$service.frame.getUserInfo({}).then((response) => {
                if (response.head.code === '000000') {
                    Object.assign(this.userInfo, response.body);
                }
            });
        },
        // 获取上传路径地址
        getUploadAvatarUrl() {
            this.avatarAction = this.$service.frame.getUploadAvatarUrl();
        },

        // /剪贴图片
        imgcutDown(res) {
            this.userInfo.avatar = res;
            this.$store.dispatch('user/setUserAvatar', this.userInfo.avatar);
        },

        // 显示
        showImg() {
            this.$refs.imgresult.dialogImgVisible = true;
        },
        // 移入图片
        enterImg() {
            this.showMask = true;
        },
        // 移除图片
        leaveImg() {
            this.showMask = false;
        },
        // 取消安全验证
        cancelVerificatify() {
            this.verificatifyDialogVisible = false;
            this.sendCountDownPhone = 0;
            this.$refs.dataForm.resetFields();
        },
        // 取消设置新手机号
        cancelSet() {
            this.setDialogVisible = false;
            this.sendCountDownPhoneSet = 0;
            this.$refs.dataFormSet.resetFields();
        },
        // 更换手机号
        changePhone() {
            this.verificatifyDialogVisible = true;
        },
        // 给原手机号发送短信验证码
        sendSmsCaptchaPhone() {
            const params = {
                msgType: 3
            };
            this.$service.frame.getCurrentVerifCode(params).then((res) => {
                const { code } = res.head;
                if (code === '000000') {
                    //  发送成功，进入倒计时
                    this.sendCountDownPhone = 60;
                    const t = setInterval(() => {
                        if (this.sendCountDownPhone === 0) {
                            clearInterval(t);
                        }
                        // eslint-disable-next-line no-plusplus
                        this.sendCountDownPhone--;
                    }, 1000);
                } else {
                    // 显示后台返回的错误信息
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        // 给新手机号发送短信验证码
        sendSmsCaptchaPhoneSet() {
            const params = {
                phone: this.formDataInfoPhoneSet.newPhone
            };
            if (this.formDataInfoPhoneSet.newPhone) {
                this.$service.frame.getCheckNewPhone(params).then((res) => {
                    const { code } = res.head;
                    if (code === '000000') {
                        //  发送成功，进入倒计时
                        this.sendCountDownPhoneSet = 60;
                        const t = setInterval(() => {
                            if (this.sendCountDownPhoneSet === 0) {
                                clearInterval(t);
                            }
                            // eslint-disable-next-line no-plusplus
                            this.sendCountDownPhoneSet--;
                        }, 1000);
                    } else {
                        // 显示后台返回的错误信息
                        const msg = `frame.bgReturnError[${code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
            } else {
                this.$message({
                    message: this.$t('frame.userInfo.message.notEmpty'),
                    type: 'error'
                });
            }
        },
        // 下一步，验证验证码
        nextStepSecurity(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 调验证验证码接口
                    const params = {
                        smsCode: this.formDataInfoPhone.smsCaptcha,
                        msgType: 3,
                        isDelSmsCode: true,
                        userId: this.userInfo.userId
                    };
                    this.$service.frame
                        .PostCheckVerifCode(params)
                        .then((res) => {
                            const { code } = res.head;
                            if (code === '000000') {
                                this.verificatifyDialogVisible = false;
                                this.setDialogVisible = true;
                                // 数据重置
                                this.$refs[formName].resetFields();
                                this.sendCountDownPhone = 0;
                            } else {
                                const msg = `frame.bgReturnError[${code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error'
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        // 完成更换手机号
        finishChangePhone(formName) {
            // 更换新手机号接口
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 调验证验证码接口
                    const params = {
                        phone: this.formDataInfoPhoneSet.newPhone,
                        smsVerifCode: this.formDataInfoPhoneSet.smsCaptcha,
                        oldPhone: this.userInfo.phone
                    };
                    this.$service.frame.changeNewPhone(params).then((res) => {
                        const { code } = res.head;
                        if (code === '000000') {
                            this.setDialogVisible = false;
                            // 数据重置
                            this.$refs[formName].resetFields();
                            this.sendCountDownPhoneSet = 0;
                            // 刷新当前用户信息
                            this.getUserInfo();
                        } else {
                            const msg = `frame.bgReturnError[${code}]`;
                            this.$message({
                                message: this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        // 获得用户登录绑定详情
        getBindDetail() {
            this.$service.frame.getBindDetail().then((res) => {
                if (res.head.code === '000000') {
                    if (res.body.length > 0) {
                        this.userBindArr = res.body;
                        this.userBindArr.forEach((item) => {
                            this.$set(
                                item,
                                'icon',
                                this.bindIcon[item.bindName]
                            );
                            this.$set(
                                item,
                                'label',
                                this.bindLabel[item.bindName]
                            );
                        });
                    }
                }
            });
        },
        // 绑定
        gotoBind() {
            this.bindDialogVisible = true;
        },
        // 绑定微信，给当前手机号发送短信验证码
        sendSmsCaptchaBind() {
            const params = {
                msgType: 41
            };
            this.$service.frame.getCurrentVerifCode(params).then((res) => {
                const { code } = res.head;
                if (code === '000000') {
                    //  发送成功，进入倒计时
                    this.sendCountDownBind = 60;
                    const t = setInterval(() => {
                        if (this.sendCountDownBind === 0) {
                            clearInterval(t);
                        }
                        // eslint-disable-next-line no-plusplus
                        this.sendCountDownBind--;
                    }, 1000);
                } else {
                    // 显示后台返回的错误信息
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        // 确认验证码，获取绑定二维码
        confirmVerify(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 调验证验证码接口
                    const params = {
                        smsCode: this.formDataBind.smsCaptcha,
                        msgType: 41
                    };
                    // 校验绑定手机短信验证码获取绑定二维码
                    this.$service.frame.checkSmsCode(params).then((res) => {
                        const { code } = res.head;
                        const { message } = res.head;
                        if (code === '000000') {
                            // 关闭验证弹框
                            this.bindDialogVisible = false;
                            // 弹出二维码弹框
                            this.qrCodeFrame = true;
                            this.$nextTick(() => {
                                this.qrcodeScan(res.body.qrCodeUrl);
                            });
                            // 设置定时器，超出30秒不扫描则自动关闭
                            this.clearQrcodeTime = setTimeout(() => {
                                this.qrCodeFrame = false;
                                window.clearTimeout(this.qrCodeTimer);
                                this.qrCodeTimer = null;
                            }, 120000);
                            // 数据重置
                            this.$refs[formName].resetFields();
                            this.sendCountDownBind = 0;
                            // 调用获得用户绑定二维码状态的接口
                            this.qrCodeTimer = setTimeout(() => {
                                this.getCodeBindStatus(res.body.qrCodeParams);
                            }, 1000);
                        } else {
                            window.clearTimeout(this.qrCodeTimer);
                            this.qrCodeTimer = null;
                            const msg = `frame.bgReturnError[${code}]`;
                            this.$message({
                                message:
                                    code === '900018'
                                        ? this.$t(msg).replace('{0}', message)
                                        : this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        // 取消绑定
        cancelBind() {
            this.$refs.dataBind.resetFields();
            this.bindDialogVisible = false;
        },
        // 获得用户绑定二维码状态
        getCodeBindStatus(data) {
            const params = {
                bindId: data
            };
            this.$service.frame.getCodeBindStatus(params).then((res) => {
                const { code } = res.head;
                if (code === '000000') {
                    // 扫码成功之后，关闭二维码弹框，关闭定时器，刷新用户绑定详情
                    this.qrCodeFrame = false;
                    window.clearTimeout(this.qrCodeTimer);
                    this.qrCodeTimer = null;
                    this.getBindDetail();
                } else if (res.head.code === '900001') {
                    // 如果用户未扫码，则进行轮询
                    window.clearTimeout(this.qrCodeTimer);
                    this.qrCodeTimer = null;
                    this.qrCodeTimer = setTimeout(() => {
                        this.getCodeBindStatus(data);
                    }, 1000);
                } else {
                    // eslint-disable-next-line no-shadow
                    const { code } = res.head;
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                    window.clearTimeout(this.qrCodeTimer);
                    this.qrCodeTimer = null;
                }
            });
        },
        // 解除用户绑定
        unbind() {
            this.$confirm(
                this.$t('frame.userInfo.bind.confirmBox.msg'),
                this.$t('frame.userInfo.bind.confirmBox.title'),
                {
                    confirmButtonText: this.$t('common.done'),
                    cancelButtonText: this.$t('common.cancel'),
                    type: 'warning'
                }
            )
                .then(() => {
                    const params = {
                        bindType: 41
                    };
                    // 调用解除绑定的接口
                    this.$service.frame.unbind(params).then((res) => {
                        if (res.body === 'ok') {
                            this.$message({
                                message: this.$t(
                                    'frame.userInfo.bind.msg.success'
                                ),
                                type: 'success'
                            });
                            // 刷新详情列表
                            this.getBindDetail();
                        } else {
                            const { code } = res.head;
                            const msg = `frame.bgReturnError[${code}]`;
                            this.$message({
                                message: this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: this.$t('frame.userInfo.bind.msg.canceled')
                    });
                });
        },
        // 转二维码
        qrcodeScan(url) {
            // 生成二维码
            this.$refs.qrcode.innerHTML = '';
            const qrcode = new QRCode('qrcode', {
                // 设置宽度，单位像素
                width: 200,
                // 设置高度，单位像素
                height: 200,
                // 设置二维码内容或跳转地址
                text: url
            });
            qrcode._el.title = '';
        },
        /**
         * 关闭二维码窗口的回调
         */
        closeDialog() {
            //  关闭定时器
            window.clearTimeout(this.qrCodeTimer);
            this.qrCodeTimer = null;
            window.clearTimeout(this.clearQrcodeTime);
            this.clearQrcodeTime = null;
        }
    }
};
</script>

<style lang="scss" scoped>
.wtf-components-user-info {
    height: calc(100vh - 130px);
    overflow: auto;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    .title {
        font-size: 14px;
        font-weight: 500;
        color: #1e222d;
        padding-bottom: 20px;
        border-bottom: 1px solid #e7e7e7;
    }
    .message-detail {
        padding: 20px 0;
        display: flex;
        border-bottom: 1px solid #e7e7e7;
        align-items: center;
        .el-upload {
            border-radius: 50%;
            .avatar {
                font-size: 50px;
            }
        }
        .info {
            margin-left: 40px;
            .item {
                line-height: 33px;
                .name {
                    display: inline-block;
                    width: 100px;
                    text-align: right;
                    margin-right: 24px;
                }
                .change {
                    font-size: 14px;
                    font-weight: 400;
                    color: #5c88fa;
                    box-sizing: border-box;
                    padding: 8px 14px;
                    background: #ffffff;
                    border: 1px solid #5885fa;
                    margin-left: 15px;
                    cursor: pointer;
                }
            }
        }
    }
    .options {
        padding-top: 40px;
        margin-left: 164px;
        .item {
            position: relative;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            img {
                margin-right: 17px;
            }
            .name {
                width: 100px;
                margin-right: 24px;
                text-align: right;
                margin-left: 10px;
                display: flex;
                align-items: center;
            }
            .el-select {
                width: 521px;
            }
            .org-box {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                span {
                    box-sizing: border-box;
                    padding: 14px;
                    background: #f9fafc;
                    border: 1px solid #ececec;
                    margin-right: 10px;
                    margin-bottom: 10px;
                }
            }
            .part-box {
                display: flex;
                width: 400px;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                .unbound {
                    color: #ccc;
                    margin-left: 13px;
                }
                span {
                    box-sizing: border-box;
                    padding: 14px;
                    background: #f9fafc;
                    border: 1px solid #ececec;
                    margin-right: 10px;
                    margin-bottom: 10px;
                }
            }
            .role-box {
                display: flex;
                justify-content: space-between;
                align-content: center;
                width: 521px;
                .left {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    span {
                        box-sizing: border-box;
                        padding: 14px;
                        background: #f9fafc;
                        border: 1px solid #ececec;
                        margin-right: 10px;
                        margin-bottom: 15px;
                        flex-shrink: 1;
                        &.active {
                            font-size: 14px;
                            font-weight: 400;
                            color: #5c88fa;
                            box-sizing: border-box;
                            padding: 14px;
                            background: #ffffff;
                            border: 1px solid #5885fa;
                            margin-right: 15px;
                        }
                    }
                }
                .right {
                    width: 100px;
                    height: 40px;
                    background: linear-gradient(0deg, #3370ff, #6b91f8);
                    border-radius: 8px;
                    border: transparent;
                    color: #ffffff;
                    cursor: pointer;
                }
            }
        }
    }
}
::v-deep .el-dialog.custom-dialog.change-phone-dialog {
    width: 40%;
    .desc {
        font-size: 14px;
        font-weight: 400;
        color: #000000;
        margin-bottom: 31px;
        .tip {
            display: flex;
            align-items: center;
            .el-icon-warning {
                color: #1890ff;
                font-size: 24px !important;
                margin-right: 15px;
            }
        }
        p {
            margin-left: 40px;
            margin-top: 20px;
        }
    }
    .verification {
        display: flex;
        align-items: center;
        .el-form {
            width: 100%;
        }
        .send-sms-code {
            background: linear-gradient(0deg, #3370ff, #6b91f8);
            border-radius: 0px;
            color: #ffffff !important;
            width: 130px;
            height: 40px;
        }
        .send-sms-code--disabled {
            border-radius: 0px;
            width: 130px;
            height: 40px;
        }
        .input {
            display: flex;
            width: 521px;
            .btn-code {
                width: 134px;
                background: linear-gradient(0deg, #3370ff, #6b91f8);
                color: #ffffff;
                outline: none;
                border: transparent;
                cursor: pointer;
            }
            .code-input {
                flex: 1;
            }
        }
    }
    .el-dialog__footer {
        padding-top: 20px;
        border-top: 1px solid #f5f6fa;
    }
}
.img-box {
    position: relative;
    width: 120px;
    height: 120px;
    img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
    .mask {
        position: absolute;
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 120px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        cursor: pointer;
        .mask-text {
            color: #ffffff;
        }
    }
}
::v-deep .el-dialog.custom-dialog.bind-dialog {
    width: 300px;
    .el-dialog__body {
        display: flex;
        justify-content: center;
        align-items: center;
        .code-title {
            font-size: 16px;
            margin-bottom: 25px;
            text-align: center;
            margin-top: 10px;
            color: #000;
        }
        .code-content {
            border: 1px solid silver;
            width: 220px;
            height: 220px;
            border-radius: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
                width: 96%;
                height: 96%;
            }
        }
    }
}
</style>
