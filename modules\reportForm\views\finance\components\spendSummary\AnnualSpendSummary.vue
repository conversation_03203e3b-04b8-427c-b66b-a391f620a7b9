<template>
    <div class="annual-spend-summary" ref="overallChartBox">
        <div class="flex info-container">
            <el-date-picker
                v-model="year"
                value-format="yyyy"
                type="year"
                placeholder="选择年"
                format="yyyy自然年"
                :picker-options="{
                    disabledDate(time) {
                        return (
                            time.getTime() < new Date('2024-01-01').getTime()
                        );
                    }
                }"
                :clearable="false"
                @input="handleYearChange"
            >
            </el-date-picker>
            <div class="info">
                当前支出总计：
                <span class="fee">¥ {{ spendSummary }}</span>
                万
            </div>
        </div>
        <el-card class="chart-container">
            <div class="flex">
                <el-radio-group
                    class="radio-group"
                    v-model="chartType"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="month"> 按月份 </el-radio-button>
                    <el-radio-button label="subject"> 按科目</el-radio-button>
                    <el-radio-button label="productLine">
                        按产品线</el-radio-button
                    >
                    <el-radio-button label="productCategory">
                        按类别</el-radio-button
                    >
                </el-radio-group>
                <el-radio-group
                    class="radio-group chart-type"
                    v-model="chartShowType"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="bar">
                        <svg-icon icon-class="barChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <div
                ref="monthBarChart"
                class="chart"
                v-show="chartType === 'month' && chartShowType === 'bar'"
            ></div>
            <div
                ref="monthPieChart"
                class="chart"
                v-show="chartType === 'month' && chartShowType === 'pie'"
            ></div>
            <div
                ref="subjectBarChart"
                class="chart"
                v-show="chartType === 'subject' && chartShowType === 'bar'"
            ></div>
            <div
                ref="subjectPieChart"
                class="chart"
                v-show="chartType === 'subject' && chartShowType === 'pie'"
            ></div>
            <div
                ref="productLineBarChart"
                class="chart"
                v-show="chartType === 'productLine' && chartShowType === 'bar'"
            ></div>
            <div
                ref="productLinePieChart"
                class="chart"
                v-show="chartType === 'productLine' && chartShowType === 'pie'"
            ></div>
            <div
                ref="productCategoryBarChart"
                class="chart"
                v-show="
                    chartType === 'productCategory' && chartShowType === 'bar'
                "
            ></div>
            <div
                ref="productCategoryPieChart"
                class="chart"
                v-show="
                    chartType === 'productCategory' && chartShowType === 'pie'
                "
            ></div>
        </el-card>
        <div class="flex selector-group">
            <el-select
                v-model="queryConditions.curSelectTable"
                class="table-type selector"
                @input="handleTableTypeChange"
            >
                <el-option
                    v-for="item in queryOptions.tabType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="queryConditions.month"
                placeholder="月份"
                class="selector month"
                clearable
            >
                <el-option
                    v-for="item in queryOptions.month"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="queryConditions.productLine"
                placeholder="产品线"
                class="selector"
                @input="handleProductLineChange"
                clearable
            >
                <el-option
                    v-for="item in queryOptions.productLine"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="queryConditions.subProductLine"
                placeholder="细分产品线"
                class="selector"
                clearable
            >
                <el-option
                    v-for="item in queryOptions.subProductLine"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-select
                v-model="queryConditions.costSubject"
                placeholder="费用科目"
                class="selector"
                :disabled="!showFeeSubject"
                clearable
            >
                <el-option
                    v-for="item in queryOptions.costSubject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <form @submit.prevent="handleTableQuery" class="flex projectQuery">
                <el-input
                    v-model="queryConditions.projectName"
                    placeholder="请输入项目名称关键字"
                    :disabled="
                        queryConditions.curSelectTable === 'maintainProject' ||
                        queryConditions.curSelectTable === 'maintainProjectSub'
                    "
                    class="project-name"
                ></el-input>
                <el-button
                    @click="handleTableQuery"
                    type="primary"
                    class="button"
                    >搜索</el-button
                >
            </form>
            <el-button
                @click="handleTableReset"
                type="primary"
                class="reset-button"
                >重置</el-button
            >
        </div>
        <SnbcBaseTable
            class="table"
            ref="tableRef"
            :table-config="tableConfig"
            :showTableHeader="false"
        >
            <template #cost="{ row }">
                ￥{{
                    parseFloat(row.costAmount).toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
        </SnbcBaseTable>
    </div>
</template>

<script>
import {
    getBarChartOptionsByMonth,
    getPieChartOptionsByMonth,
    getBarChartOptionsBySubject,
    getPieChartOptionsBySubject,
    getBarChartOptionsByProductLine,
    getPieChartOptionsByProductLine,
    getBarChartOptionsByProductCategory,
    getPieChartOptionsByProductCategory
} from './summaryChartOptions';
import * as echarts from 'echarts';
import { debounce } from 'lodash';
import { getTableConfig } from './tableConfig';
import { getStatusArray } from './tableHeader';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import { monthOptions } from 'reportForm/common.js';

// 几种类型的表格与接口对应的值
const tableTypeMap = {
    devProject: 1,
    maintainProject: 2,
    devProjectSub: 3,
    maintainProjectSub: 4
};

export default {
    name: 'AnnualSpendSummary',
    components: { SnbcBaseTable },
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            year: new Date().getFullYear().toString(),
            // 图表缓存
            charts: {},
            optionsMap: {},
            chartType: 'month',
            chartShowType: 'bar',
            // 是否展示费用科目
            showFeeSubject: false,
            // 列表各项配置
            tableConfig: getTableConfig(this),
            // 查询条件
            queryConditions: {
                // 汇总/分项表
                curSelectTable: 'devProject',
                // 月份
                month: '',
                // 产品线
                productLine: '',
                // 细分产品线
                subProductLine: '',
                // 对应费用科目
                costSubject: '',
                // 项目名称
                projectName: ''
            },
            // 查询条件的选项
            queryOptions: {
                // 汇总/分项表
                tabType: [
                    { label: '表1：开发项目月度费用表', value: 'devProject' },
                    {
                        label: '表2：维护项目月度费用表',
                        value: 'maintainProject'
                    },
                    {
                        label: '表3：开发项目月度费用科目子表',
                        value: 'devProjectSub'
                    },
                    {
                        label: '表4：维护项目月度费用科目子表',
                        value: 'maintainProjectSub'
                    }
                ],
                // 月份
                month: monthOptions,
                // 产品线
                productLine: this.$store.state.project.productLine.map((i) => {
                    return { value: i.label, label: i.label };
                }),
                // 细分产品线
                subProductLine: [],
                // 对应费用科目
                costSubject: []
            },
            // 支出总计
            spendSummary: '0.00',
            // 所有费用科目的数组
            costList: []
        };
    },
    watch: {
        activeName(newVal) {
            if (newVal === 'annualSpendSummary') {
                this.handleYearChange();
            }
        }
    },
    created() {
        this.getTopInfo();
        this.getQueryOptions('细分产品线');
    },
    mounted() {
        this.getQueryOptions('费用科目').then(this.handleYearChange);
    },
    activated() {
        if (this.activeName === 'annualSpendSummary') {
            this.handleYearChange();
        }
    },
    methods: {
        /**
         * 获取顶部信息
         */
        async getTopInfo() {
            try {
                const api = this.$service.reportForm.finance.getTopInfo;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.spendSummary =
                    Math.trunc(res.body.expenditureTotal / 100) / 100;
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 年份变更
         */
        handleYearChange() {
            this.getTopInfo();
            this.setChartByMonthOptions();
            this.setChartBySubjectOptions();
            this.setChartByProductLinetOptions();
            this.setChartByProductCategorytBarOptions();
            this.setChartByProductCategorytPieOptions();
            this.handleTableQuery();
        },
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const { overallChartBox } = this.$refs;
            const observer = new ResizeObserver(
                debounce((entries) => {
                    this.activeName === 'annualSpendSummary' &&
                        myChart.resize();
                }, 100)
            );
            observer.observe(overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 切换图表显示时，重新确定图表尺寸
         */
        handleRadioClick() {
            this.$nextTick(() => {
                for (const option in this.optionsMap) {
                    if (
                        Object.prototype.hasOwnProperty.call(
                            this.optionsMap,
                            option
                        )
                    ) {
                        this.charts[option] && this.charts[option].resize();
                    }
                }
            });
        },
        /**
         * 表格查询
         */
        handleTableQuery() {
            this.tableConfig.queryParams = {
                year: this.year,
                tabType: tableTypeMap[this.queryConditions.curSelectTable],
                ...this.queryConditions
            };
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 获取下拉列表选项
         * @param {String} type 哪种option
         * @param {String} value 类型
         * @returns {Promise} 是否成功获取选项
         */
        async getQueryOptions(type, value = '') {
            try {
                const api = this.$service.project.finance.getSelectOptions;
                const res = await api({
                    paramName: type,
                    paramType: value
                });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    throw new Error(res.head.message);
                }
                // 处理选项
                const options = res.body.map((i) => {
                    return {
                        value: i.paramName,
                        label: i.paramName
                    };
                });
                if (type === '费用科目') {
                    this.queryOptions.costSubject = options;
                    this.costList = options.map((i) => i.value);
                } else if (type === '细分产品线') {
                    this.queryConditions.subProductLine = '';
                    this.queryOptions.subProductLine = options;
                }
            } catch (err) {
                console.error('Error:', err);
                throw err;
            }
        },
        /**
         * 处理不同产品线对应的细分产品线
         * @param {String} value 产品线
         */
        handleProductLineChange(value) {
            this.getQueryOptions('细分产品线', value);
        },
        /**
         * 处理表格类型变化
         * @param {String} value 表格类型
         */
        handleTableTypeChange(value) {
            this.queryConditions.costSubject = '';
            switch (value) {
                default:
                case 'devProject':
                    this.tableConfig.elTableColumns = getStatusArray(0, this);
                    this.showFeeSubject = false;
                    break;
                case 'maintainProject':
                    this.tableConfig.elTableColumns = getStatusArray(
                        0,
                        this,
                        true
                    );
                    this.showFeeSubject = false;
                    break;
                case 'devProjectSub':
                    this.tableConfig.elTableColumns = getStatusArray(1, this);
                    this.showFeeSubject = true;
                    break;
                case 'maintainProjectSub':
                    this.tableConfig.elTableColumns = getStatusArray(
                        1,
                        this,
                        true
                    );
                    this.showFeeSubject = true;
                    break;
            }
            this.handleTableQuery();
        },
        /**
         * 排序前的hook
         * @param {Object} params 参数
         */
        sortChangeHook(params) {
            const { prop: sortKey, order: sortOrder } = params;
            this.tableConfig.sortParams.sortKey = sortKey;
            this.tableConfig.sortParams.sortOrder =
                sortOrder === 'ascending' ? 'ASC' : 'DESC';
        },
        /**
         * 按月份图表
         */
        async setChartByMonthOptions() {
            try {
                const api =
                    this.$service.reportForm.finance.getChartDataByMonth;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.monthBar = getBarChartOptionsByMonth(
                    res.body,
                    this.costList
                );

                this.initChart('monthBar');
                this.optionsMap.monthPie = getPieChartOptionsByMonth(res.body);
                this.initChart('monthPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 按科目图表（饼图）
         */
        async setChartBySubjectOptions() {
            try {
                const api =
                    this.$service.reportForm.finance.getChartDataBySubject;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.subjectBar = getBarChartOptionsBySubject(
                    res.body
                );
                this.initChart('subjectBar');
                this.optionsMap.subjectPie = getPieChartOptionsBySubject(
                    res.body
                );
                this.initChart('subjectPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 按产品线图表
         */
        async setChartByProductLinetOptions() {
            try {
                const api =
                    this.$service.reportForm.finance.getChartDataByProductLine;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.productLineBar =
                    getBarChartOptionsByProductLine(res.body);
                this.initChart('productLineBar');
                this.optionsMap.productLinePie =
                    getPieChartOptionsByProductLine(res.body);
                this.initChart('productLinePie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 按项目类别图表（柱状图）
         */
        async setChartByProductCategorytBarOptions() {
            try {
                const api =
                    this.$service.reportForm.finance
                        .getChartDataByProductCategoryBar;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.productCategoryBar =
                    getBarChartOptionsByProductCategory(res.body);
                this.initChart('productCategoryBar');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 按项目类别图表（饼图）
         */
        async setChartByProductCategorytPieOptions() {
            try {
                const api =
                    this.$service.reportForm.finance
                        .getChartDataByProductCategoryPie;
                const params = {
                    year: this.year,
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.productCategoryPie =
                    getPieChartOptionsByProductCategory(res.body);
                this.initChart('productCategoryPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 重置查询
         */
        handleTableReset() {
            this.queryConditions = {
                // 汇总/分项表
                curSelectTable: this.queryConditions.curSelectTable,
                // 月份
                month: '',
                // 产品线
                productLine: '',
                // 细分产品线
                subProductLine: '',
                // 对应费用科目
                costSubject: '',
                // 项目名称
                projectName: ''
            };
            this.handleTableQuery();
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
.annual-spend-summary {
    .info-container {
        width: 100%;
        .info {
            line-height: 30px;
            margin-left: 20px;
            font-weight: 600;
            .fee {
                color: #0054ca;
                font-size: 18px;
                margin-right: 2px;
            }
        }
    }
    .chart-container {
        margin-top: 10px;
        width: 100%;
        padding: 10px;
        height: 400px;
        .chart-box-title {
            width: 120px;
            height: 30px;
            background-color: #3370ff;
            color: #fff;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            padding: 8px;
            border-radius: 10px;
        }
        .radio-group {
            line-height: 30px;
        }
        .chart-type {
            margin-left: auto;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-top: 20px;
        }
    }
    .select-icon {
        height: 15px;
        width: 15px;
    }

    .selector-group {
        flex-wrap: wrap;
        align-items: center;
        margin: 10px 0;
        gap: 10px;
        // 修改placeholder颜色
        ::v-deep .el-input__inner::placeholder {
            color: rgba(0, 0, 0, 0.685) !important;
        }
        .projectQuery {
            .project-name {
                max-width: 230px;
                min-width: 200px;
            }
            .button {
                margin-left: 5px;
                height: 28px;
            }
        }
        .selector {
            min-width: 110px;
            flex: 1;
        }
        .selector.month {
            min-width: 80px;
        }
        .table-type {
            min-width: 230px;
        }
    }
    .reset-button {
        margin-left: 5px;
        height: 28px;
        justify-self: flex-end;
    }
    .table {
        min-height: 500px;
    }
}
</style>
