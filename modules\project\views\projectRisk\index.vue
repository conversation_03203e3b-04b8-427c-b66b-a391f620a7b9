<template>
    <div>
        <ProjectSelector
            @input="handleChange"
            :key="projectSelectorKey"
        ></ProjectSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="风险列表" name="riskList" :lazy="true">
                    <RiskList
                        :activeName="activeName"
                        :projectId="projectId"
                        :projectName="projectName"
                        :proProjectId="proProjectId"
                        :projectStatus="projectStatus"
                        projectType="develop"
                    ></RiskList>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector.vue';
import RiskList from 'maintenanceProject/views/maintenanceRisk/components/RiskList';

export default {
    name: 'ProjectRisk',
    components: {
        ProjectSelector,
        RiskList
    },
    data() {
        return {
            activeName: 'riskList',
            // 顶部级联组件key
            projectSelectorKey: 0,
            projectId: '',
            projectName: '',
            proProjectId: '',
            projectStatus: ''
        };
    },
    async created() {
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    activated() {
        this.projectSelectorKey += 1;
    },
    methods: {
        /**
         * 选择后的回调
         * @param {Array} value  选中的值
         */
        handleChange(value) {
            this.projectId = value[value.length - 1];

            const { proProjectId, projectName, projectStatus } =
                this.$store.state.project.projectInfo;
            this.projectStatus = projectStatus;

            this.proProjectId = proProjectId;
            this.projectName = projectName;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 0px 20px;
    background-color: #ffffff;
    overflow: auto;
}
</style>
