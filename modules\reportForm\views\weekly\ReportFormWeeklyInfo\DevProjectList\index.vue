<template>
    <div>
        <div class="dev-project-list-container">
            <el-tabs
                class="tabs"
                v-model="activeIndex"
                @tab-click="handleTabChange"
                :stretch="true"
            >
                <el-tab-pane
                    v-for="item in projectStatusGroup"
                    :label="`${item.projectStatus} (${item.projectStatusNum})`"
                    :key="`${tabKey}${item.projectStatus}`"
                    :lazy="true"
                ></el-tab-pane>
            </el-tabs>
            <SnbcBaseTable
                class="table"
                ref="tableRef"
                :table-config="tableConfig"
            >
                <template #currentMilestone="{ row }">
                    <el-tooltip
                        v-if="row.detailName === '--'"
                        effect="dark"
                        :content="
                            row.projectName.includes(`审批中`)
                                ? '立项审批中，里程碑未发布'
                                : '当前里程碑已全部完成'
                        "
                        placement="top"
                        trigger="hover"
                    >
                        <div>{{ row.detailName }}</div>
                    </el-tooltip>
                    <span v-else>{{ row.detailName }} </span>
                </template>
                <template #projectName="{ row }">
                    <!-- 注意这里保持行内样式，否则会被覆盖 -->
                    <el-button
                        type="text"
                        style="
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            text-align: left;
                            padding: 0px !important;
                            max-width: 100%;
                        "
                        @click="handleProjectNameClick(row)"
                        >{{ row.projectName }}</el-button
                    >
                </template>
                <template #projectNumber="{ row }">
                    <svg-icon
                        class="projectNumber-icon"
                        v-if="row.reportStatus === '已更新'"
                        icon-class="reportFrom-already-update"
                    />
                    <svg-icon
                        class="projectNumber-icon"
                        v-if="row.reportStatus === '待更新'"
                        icon-class="reportForm-to-be-update"
                    />
                    {{ row.projectNumber }}
                </template>
            </SnbcBaseTable>
        </div>
    </div>
</template>

<script>
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import { CONSTANTS } from '@/constants';
import { getStatusArray } from './tableHeader';
import { getTableConfig } from './tableConfig';
// 项目状态
const { PROJECT_STATUS } = CONSTANTS;
// 构建项目状态列表
const getProjectStatusGroup = () => {
    return PROJECT_STATUS.map((item) => ({
        projectStatus: item.label,
        projectStatusNum: '0',
        projectValue: item.value
    })).filter((i) => i.projectStatus !== '市场待立项');
};

export default {
    name: 'DevProjectList',
    components: { SnbcBaseTable },
    data() {
        return {
            // 列表各项配置
            tableConfig: getTableConfig(this),
            PROJECT_STATUS,
            CONSTANTS,
            // 当前激活的tab（默认为进行中）
            activeIndex: '1',
            // 项目名称
            projectName: '',
            // tab页的key
            tabKey: 0,
            // 项目状态列表
            projectStatusGroup: getProjectStatusGroup(),
            // 当前选择的终止时间
            curStopReason: ''
        };
    },
    computed: {
        // 查询参数
        computedQueryParams() {
            return {
                productLine: this.weeklyOption[1]?.value || '',
                weekNumber: this.weeklyOption[0]?.weekNumber || '',
                yearVal: this.weeklyOption[0]?.year || ''
            };
        },
        // 项目状态
        projectStatus() {
            return this.projectStatusGroup[parseInt(this.activeIndex)]
                .projectValue;
        },
        // 当前选中的页头选项值
        weeklyOption() {
            return this.$store.state.reportForm.weeklyOption;
        },
        // 当前选中的页头的值
        weeklyValue() {
            return this.$store.state.reportForm.weeklyValue;
        }
    },
    watch: {
        // 不同项目状态展示不同的表头
        activeIndex(newVal) {
            this.tableConfig.elTableColumns = getStatusArray(
                parseInt(this.activeIndex),
                this
            );
        },
        weeklyValue(newVal) {
            if (newVal) {
                this.handleQuery();
                this.setDefaultSortOrder();
            }
        }
    },
    mounted() {
        this.setDefaultSortOrder();
        this.handleQuery();
    },
    activated() {
        this.setDefaultSortOrder();
        this.handleQuery();
    },
    methods: {
        /**
         * 查询
         */
        handleQuery() {
            this.tableConfig.queryParams = {
                projectStatus: this.projectStatus,
                ...this.computedQueryParams
            };
            this.getProjectStatusCount();
            this.$refs.tableRef.handleQuery();
        },

        /**
         * 获取项目状态下各项目的数量
         */
        async getProjectStatusCount() {
            const api =
                this.$service.reportForm.weekly.getDevelopProjectStatusNumbers;
            const params = this.computedQueryParams;
            try {
                const result = await api(params);
                if (result.head.code !== '000000') {
                    this.$message.error(result.head.message);
                    return;
                }
                const res = result.body;
                // 重置数据列表
                this.projectStatusGroup = getProjectStatusGroup();
                // 对数量重新赋值
                this.projectStatusGroup.forEach((item) => {
                    for (let i = 0; i < res.length; i++) {
                        if (item.projectValue === res[i].projectStatus) {
                            item.projectStatusNum = res[i].projectStatusNo;
                        }
                    }
                });
                this.tabKey += 1;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 排序前的hook
         * @param {Object} params 参数
         */
        sortChangeHook(params) {
            const { prop: sortKey, order: sortOrder } = params;
            this.tableConfig.sortParams.sortKey = sortKey;
            this.tableConfig.sortParams.sortOrder =
                sortOrder === 'ascending' ? 'ASC' : 'DESC';
        },
        /**
         * 重置筛选条件
         */
        resetSearch() {
            this.setDefaultSortOrder();
            this.handleQuery();
        },
        /**
         * 设置产品线在各个状态时的默认排序
         */
        setDefaultSortOrder() {
            this.tableConfig.sortParams = {
                sortKey: '',
                sortOrder: ''
            };
        },
        /**
         * 切换tab页签的处理函数
         */
        handleTabChange() {
            // 清除排序
            const ref = this.$refs.tableRef?.$children[1]?.$children[0];
            ref.clearSort();
            this.setDefaultSortOrder();
            this.handleQuery();
        },
        /**
         * @description: 点击终止原因
         * @param {string} oriStopReason 终止原因
         */
        handleStopReasonClick(oriStopReason = '') {
            if (!oriStopReason) {
                this.curStopReason = '暂无数据';
                return;
            }
            this.curStopReason = oriStopReason.replace(/<br\s*\/?>/gi, '\n');
        },
        /**
         * 点击项目名称之后的处理
         * @param {Object} row 行数据
         */
        handleProjectNameClick(row) {
            const projectList =
                this.$store.state.reportForm.weeklyOption[1].devProjectList;
            // 当前选中对应的项目选项
            const currentOption = projectList.find(
                (i) => i.projectId === row.projectId
            );
            const currentValue = this.$store.state.reportForm.weeklyValue;
            this.$store.dispatch('reportForm/selectedWeeklyValue', [
                ...currentValue,
                currentOption.projectId
            ]);
            this.$store.dispatch('reportForm/selectedWeeklyOption', [
                ...this.$store.state.reportForm.weeklyOption,
                currentOption
            ]);
            this.$emit('select-change');
        }
    }
};
</script>

<style lang="scss" scoped>
.tabs {
    max-width: 470px;

    margin-bottom: 10px;
    ::v-deep .el-tabs__item {
        padding: 0 16px;
    }
}
.dev-project-list-container {
    padding: 0px 20px 10px 20px;
}
.table {
    ::v-deep .snbc-header {
        display: none !important;
    }
}
.projectNumber-icon {
    width: 15px;
    height: 15px;
    padding: 0;
}

// 修改排序箭头样式
::v-deep .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #ffdc37;
}
::v-deep .el-table .descending .sort-caret.descending {
    border-top-color: #ffdc37;
}
// 修改tab选项卡样式
::v-deep .el-tabs__header {
    margin: 0px !important;
}
::v-deep .el-tabs__item.is-top {
    padding: 0 8px;
}
</style>
