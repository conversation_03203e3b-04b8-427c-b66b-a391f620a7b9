<template>
    <el-dialog
        title="任务指派"
        :visible.sync="dialogVisible"
        :before-close="dialogBeforeClose"
    >
        <el-form :model="form" class="assign-task" :rules="rules" ref="form">
            <el-form-item :label="computedTransforLabel" prop="transferor">
                <div v-if="isInfo">{{ form.transferor }}</div>
                <PeopleSelector
                    v-else
                    placeholder="请选择责任人"
                    v-model="form.transferor"
                    ref="transferor"
                    :clearable="true"
                    :isMultipled="false"
                ></PeopleSelector>
            </el-form-item>
            <el-form-item label="指派原因" prop="transferReason">
                <div v-if="isInfo">{{ form.transferReason }}</div>
                <el-input
                    v-else
                    placeholder="请选择指派原因"
                    v-model="form.transferReason"
                    type="textarea"
                    :autosize="{ minRows: 3 }"
                    maxlength="500"
                ></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" v-if="!isInfo">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import PeopleSelector from 'Components/PeopleSelector';

const validateReason = (rule, value, callback) => {
    if (!value || value.trim() === '') {
        callback(new Error('请输入有效的指派原因'));
    } else {
        callback();
    }
};

const rules = {
    transferor: [
        {
            required: true,
            message: '请选择责任人',
            trigger: ['change', 'blur']
        }
    ],
    transferReason: [
        {
            required: true,
            message: '请输入指派原因',
            trigger: ['change', 'blur']
        },
        {
            validator: validateReason,
            trigger: ['change', 'blur']
        }
    ]
};

export default {
    name: 'AlertAssignPeopleDialog',
    components: { PeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        rowData: {
            type: Object,
            default: () => ({})
        },
        isInfo: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            form: { transferor: '', transferReason: '' },
            rules
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 根据转入/转出判断显示的label
        computedTransforLabel() {
            if (this.isInfo) {
                return this.form.transferFlag === '转入' ? '转出人' : '接收人';
            }
            return '责任人';
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (!newVal) return;
            // 编辑时，不反显form
            if (this.isInfo) {
                this.form = this.$tools.cloneDeep(this.rowData);
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗前的处理
         * @param {Function} done 关闭弹窗的函数
         */
        dialogBeforeClose(done) {
            this.resetForm();
            this.dialogVisible = false;
            done();
        },
        /**
         * 取消
         */
        handleCancel() {
            this.resetForm();
            this.dialogVisible = false;
        },
        /**
         * 提交
         */
        async submitForm() {
            try {
                let valid = false;
                this.$refs.form.validate((v) => {
                    valid = v;
                });
                if (!valid) return;
                const api =
                    this.$service.department.riskControl.assignWarningTask;
                // 注意这里用form的值覆盖掉rowData的值
                const res = await api({ ...this.rowData, ...this.form });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.handleCancel();
                this.$emit('submit-success');
            } catch (error) {
                console.error(error, '指派任务失败！');
            }
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.form = { transferor: '', transferReason: '' };
            this.$refs.form.resetFields();
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .assign-task .el-form-item__label {
    font-weight: bold;
}
</style>
