// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/reportForm',
        redirect: 'noRedirect',
        name: 'ReportForm',
        useLayout: true,
        alwaysShow: true,
        meta: {
            title: '报表',
            icon: 'el-icon-box'
        },
        children: [
            {
                path: 'reportFormFinance',
                component: () => import('../views/finance/index.vue'),
                name: 'ReportFormFinance',
                meta: {
                    title: '财务',
                    icon: 'el-icon-share'
                }
            },
            {
                path: 'reportFormReview',
                component: () => import('../views/review/index.vue'),
                name: 'ReportFormReview',
                meta: {
                    title: '审核',
                    icon: 'el-icon-data-analysis'
                }
            },
            {
                path: 'reportFormWeekly',
                component: () => import('../views/weekly/index.vue'),
                name: 'ReportFormWeekly',
                meta: {
                    title: '周报',
                    icon: 'el-icon-data-analysis'
                }
            },
            {
                path: 'reportFormWeeklyInfo',
                component: () => import('../views/weekly/ReportFormWeeklyInfo'),
                name: 'ReportFormWeeklyInfo',
                hidden: true,
                meta: {
                    title: '周报信息',
                    icon: 'el-icon-data-analysis'
                }
            }
        ]
    }
];
