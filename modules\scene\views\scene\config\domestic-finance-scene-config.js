export default {
    其他场景: {
        name: '其他场景',
        // 按钮坐标
        point: { x: 840, y: 190 }
    },
    管理平台: {
        name: '管理平台',
        // 按钮坐标
        point: { x: 580, y: 108 },
        // 必经坐标
        crossPoints: [{ x: 380, y: 120 }],
        // 一级的坐标清单
        children: [
            {
                x: 200,
                y: 85,
                level: 1,
                position: 'left',
                crossPoints: [{ x: 380, y: 120 }]
            }
        ]
    },
    柜员场景: {
        name: '柜员场景',
        point: { x: 600, y: 248 },
        crossPoints: [
            { x: 640, y: 290 },
            { x: 930, y: 290 }
        ],
        children: [0, 1, 2].map((num) => {
            const x = 980;
            const y = 135 + num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'right',
                crossPoints: [
                    { x: 930, y: 170 + num * 120 },
                    { x: 930 + 50, y: 170 + num * 120 }
                ],
                nextPoints: getRightPoints2(x, y)
            };
        })
    },
    服务场景: {
        name: '服务场景',
        point: { x: 400, y: 368 },
        crossPoints: [{ x: 330, y: 380 }],
        children: [0, 1].map((num) => {
            const x = 200;
            const y = 345 - num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'left',
                crossPoints: [
                    { x: 330, y: 380 - num * 120 },
                    { x: x + 70, y: 380 - num * 120 }
                ],
                nextPoints: getLeftPoints2(x, y)
            };
        })
    },
    外拓营销: {
        name: '外拓营销',
        point: { x: 770, y: 340 },
        crossPoints: [{ x: 810, y: 490 }],
        children: [0, 1, 2, 3].map((num) => {
            const x = 755 + num * 100;
            const y = 540;
            return {
                x,
                y,
                level: 1,
                position: 'bottom',
                crossPoints: [
                    { x: 790, y: y - 50 },
                    { x: 790 + num * 100, y: y - 50 },
                    { x: 790 + num * 100, y }
                ],
                nextPoints: getBottomPoints2(x, y)
            };
        })
    },
    大堂场景: {
        name: '大堂场景',
        point: { x: 550, y: 360 },
        crossPoints: [{ x: 590, y: 490 }],
        children: [0, 1, 2, 3, 4, 5, 6].map((num) => {
            const x = 55 + num * 100;
            const y = 540;
            return {
                x,
                y,
                level: 1,
                position: 'bottom',
                crossPoints: [
                    { x: 590, y: y - 50 },
                    { x: 90 + num * 100, y: y - 50 },
                    { x: 90 + num * 100, y }
                ],
                nextPoints: getBottomPoints2(x, y)
            };
        })
    }
};

/**
 * 获取底部点位配置，第二排，最多放12个
 * @param {*} parentX 父级x坐标
 * @param {*} parentY 父级y坐标
 * @returns {array} 底部点位配置
 */
function getBottomPoints2(parentX, parentY) {
    return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((num) => {
        const x = 55 + num * 100;
        const y = parentY + 170;
        return {
            x,
            y,
            level: 2,
            position: 'bottom',
            crossPoints: [
                { x: parentX + 35, y: y - 50 },
                { x: 90 + num * 100, y: y - 50 },
                { x: 90 + num * 100, y }
            ]
        };
    });
}

/**
 * 获取右边点位配置，第二排
 * @param {*} parentX 父级x坐标
 * @param {*} parentY 父级y坐标
 * @returns {array} 底部点位配置
 */
function getRightPoints2(parentX, parentY) {
    return [0, 1, 2].map((num) => {
        const x = parentX + 160;
        const y = 135 + num * 120;
        return {
            x,
            y,
            level: 2,
            position: 'right',
            crossPoints: [
                { x: parentX + 70 + 40, y: parentY + 35 },
                { x: parentX + 70 + 40, y: 170 + num * 120 },
                { x, y: 170 + num * 120 }
            ]
        };
    });
}

/**
 * 获取左边点位配置，第二排
 * @param {*} parentX 父级x坐标
 * @param {*} parentY 父级y坐标
 * @returns {array} 底部点位配置
 */
function getLeftPoints2(parentX, parentY) {
    return [0, 1, 2].map((num) => {
        const x = parentX - 160;
        const y = 345 - num * 120;
        return {
            x,
            y,
            level: 2,
            position: 'left',
            crossPoints: [
                { x: parentX - 40, y: parentY + 35 },
                { x: parentX - 40, y: y + 35 },
                { x: x + 70, y: 380 - num * 120 }
            ]
        };
    });
}
