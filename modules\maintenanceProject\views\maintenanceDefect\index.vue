<template>
    <div>
        <MaintenanceSelector
            @input="handleChange"
            :key="projectSelectorKey"
        ></MaintenanceSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="缺陷列表" name="defectList" :lazy="true">
                    <DefectList
                        :activeName="activeName"
                        :productLine="productLine"
                        :subProductLine="subProductLine"
                        :projectManager="projectManager"
                    ></DefectList>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import MaintenanceSelector from 'maintenanceProject/components/MaintenanceSelector';
import DefectList from './components/DefectList';

export default {
    name: 'MaintenanceDefect',
    components: {
        MaintenanceSelector,
        DefectList
    },
    data() {
        return {
            activeName: 'defectList',
            // 顶部级联组件key
            projectSelectorKey: 0,
            productLine: '',
            subProductLine: '',
            projectManager: ''
        };
    },
    async created() {
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    methods: {
        handleChange(value) {
            const [productLine, subProductLine, projectManager] = value;
            this.productLine = productLine;
            this.subProductLine = subProductLine;
            this.projectManager = projectManager;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 0px 20px;
    background-color: #ffffff;
    overflow: auto;
}
</style>
