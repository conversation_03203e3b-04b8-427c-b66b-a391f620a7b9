import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        resourceLoad: {
            // 维护项目资源负载--日视图
            getResoureLoadDataByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintain_project_resource_load/resource_day_view_maintain_project`,
                    method: 'post',
                    data
                });
            },
            // 维护项目资源负载--日视图--任务列表
            getResoureLoadTaskListByDay(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintain_project_resource_load/resource_day_view_task_info_maintain_project`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 维护项目资源负载--月视图
            getResoureLoadDataByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintain_project_resource_load/resource_month_view_maintain_project`,
                    method: 'post',
                    data
                });
            },
            // 维护项目资源负载--月视图--任务列表
            getResoureLoadTaskListByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintain_project_resource_load/resource_month_view_task_info_maintain_project`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 查询资源关联项目
            getRelatedProject(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintain_project_resource_load/getResourceAssMaintainProject`,
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
