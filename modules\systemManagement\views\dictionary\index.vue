<template>
    <div class="container">
        <div class="view">
            <el-container>
                <el-aside>
                    <div class="title-export">
                        <span>{{ $t("systemManagement.dictionary.title") }}</span>
                        <!-- <el-button size="middle" class="margin-left" icon="fa fa-sign-out" @click="handleExportCmd">{{
              $t("systemManagement.dictionary.exportAll")
            }}</el-button> -->
                    </div>
                    <el-input v-model="listQuery.dictTypeName" :placeholder="$t('systemManagement.dictionary.placeholder.msg1')" @keyup.enter.native="refreshList">
                        <i slot="suffix" class="fa fa-search" @click="refreshList" />
                    </el-input>
                    <div class="add-type">
                        <el-button v-permission="['dic_manage_list_new']" size="middle" type="primary" class="margin-left" icon="fa fa-plus-square-o" @click="showAdd">{{
              $t("systemManagement.dictionary.addType")
            }}</el-button>
                    </div>
                    <div class="type-list">
                        <ul>
                            <li v-for="(item,index) in listType" :key="item.id" :class="{active:item.active === true}" @click="dictInfoShow(item)">
                                <el-tooltip class="item" effect="dark" placement="right-start">
                                    <div slot="content">{{ $t('systemManagement.dictionaryManagement.type.'+item.typeCode) }}</div>
                                    <span class="block">
                                        <span class="block__text"> {{ $t('systemManagement.dictionaryManagement.type.'+item.typeCode) }}</span>
                                        <span class="block__btns">
                                            <el-button type="text" size="mini" icon="fa fa-pencil" @click.stop="showEdit(item)" />
                                            <el-button type="text" size="mini" icon="fa fa-trash-o" @click.stop="showDelete(item, index)" />
                                        </span>
                                    </span>
                                </el-tooltip>
                            </li>
                            <infinite-loading ref="infiniteLoading" @infinite="onInfinite">
                                <div v-if="listType.length === 0" slot="no-results">{{ $t("systemManagement.logger.noData") }}</div>
                                <div v-else slot="no-results">{{ $t("systemManagement.dictionary.message.noMoreData") }}</div>
                            </infinite-loading>
                        </ul>
                    </div>
                </el-aside>
                <el-main>
                    <div class="header">
                        <div>{{ $t("systemManagement.dictionary.typeTitle") }}</div>
                    </div>
                    <el-row type="flex" justify="space-between" class="info">
                        <el-col :span="12">
                            <div class="desc-info">
                                <i class="fa fa-file-text-o color-blue color-blue-phone" />
                                <span>{{ $t('systemManagement.dictionary.columnName.typeName') }}：</span>
                                <el-tooltip class="item" effect="dark" :content="$t('systemManagement.dictionaryManagement.type.'+dictTypeInfo.typeCode)" placement="top-start">
                                    <span class="desc-text">{{ $t('systemManagement.dictionaryManagement.type.'+dictTypeInfo.typeCode) }}</span>
                                </el-tooltip>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="desc-info">
                                <i class="fa fa-file-code-o color-blue color-blue-phone" />
                                <span>{{ $t('systemManagement.dictionary.columnName.typeCode') }}：</span>
                                <el-tooltip class="item" effect="dark" :content="dictTypeInfo.typeCode" placement="top-start">
                                    <span class="desc-text">{{ dictTypeInfo.typeCode }}</span>
                                </el-tooltip>
                            </div>
                        </el-col>
                    </el-row>
                    <div class="header">
                        <span class="header__title">{{ $t('systemManagement.dictionary.dictTitle') }}</span>
                        <span class="header__body">
                            <el-input v-model="typeValueName" :placeholder="$t('systemManagement.dictionary.placeholder.msg3')" @keyup.enter.native="getList">
                                <i slot="suffix" class="fa fa-search" @click="getList" />
                            </el-input>
                        </span>
                        <span class="header__btns">
                            <el-button v-permission="['dic_manage_details_new']" type="primary" class="margin-left" icon="fa fa-plus-square-o" @click="showAddValue">{{
                $t("systemManagement.dictionary.add")
              }}</el-button>
                        </span>
                    </div>
                    <el-table id="dictionaryTable" :data="list" border :header-cell-style="{background:'#F5F6FA'}">
                        <el-table-column :label="
                $t(
                  'systemManagement.dictionary.columnName.valueName'
                )
              " align="left">
                            <template slot-scope="{ row }">
                                <span>{{ $t('systemManagement.dictionaryManagement.value.'+row.valueName) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="
                $t(
                  'systemManagement.dictionary.columnName.valueCode'
                )
              " align="left" prop="valueName" />
                        <el-table-column :label="
                $t('systemManagement.dictionary.columnName.value')
              " align="left" prop="valueCode" show-overflow-tooltip />
                        <el-table-column :label="
                $t(
                  'systemManagement.dictionary.columnName.valueParentName'
                )
              " align="left">
                            <template slot-scope="{ row }">
                                <span>{{ row.parentValueCode }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="
                $t(
                  'systemManagement.dictionary.columnName.valueDesc'
                )
              " align="left" prop="valueDesc" show-overflow-tooltip />
                        <el-table-column :label="
                $t('systemManagement.dictionary.columnName.operate')
              " width="200px" align="center" class-name="small-padding fixed-width">
                            <template slot-scope="{ row }">
                                <el-button v-permission="['dic_manage_details_edit']" icon="fa fa-pencil" size="mini" type="primary" @click="showEditValue(row)">
                                    {{ $t("systemManagement.dictionary.edit") }}
                                </el-button>
                                <el-button v-permission="['dic_manage_details-delete']" icon="fa fa-trash-o" size="mini" @click="showDeleteValue(row, dictTypeInfo.typeCode)">
                                    {{ $t("systemManagement.dictionary.delete") }}
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-main>
            </el-container>
            <!-- 字典类型新增、编辑弹窗 -->
            <dict-type-list-edit-info v-if="editInfo.isShow" :show.sync="editInfo.isShow" :edit="editInfo.isEdit" :form-data="editInfo.formData" @view-save-finish="saveFinishType" />
            <!-- 新增、编辑组件窗口 -->
            <dict-value-list-edit-info v-if="editInfoValue.isShow" :show.sync="editInfoValue.isShow" :edit="editInfoValue.isEdit" :form-data="editInfoValue.formData" @view-save-finish="saveFinish" />
        </div>
    </div>
</template>

<script>
import List from '../../mixins/list';
import ExcelExport from '../../mixins/excel-export';
import InfiniteLoading from 'vue-infinite-loading';
import DictTypeListEditInfo from './components/DictTypeListEditInfo.vue';
import DictValueListEditInfo from './components/DictValueListEditInfo.vue';

export default {
    name: 'Dictionary',
    components: {
        InfiniteLoading,
        DictTypeListEditInfo,
        DictValueListEditInfo
    },
    mixins: [List, ExcelExport],
    data() {
        return {
            // 查询、分页表单
            // 查询、分页表单
            listQuery: {
                dictTypeName: '',
                pageSize: 30,
                pageNum: 1
            },
            // 总记录数
            totalCount: 0,
            // 字典类型数据
            listType: [],
            // 字典类型编辑操作相关数据
            editInfo: {
                // 编辑数据
                formData: {
                    typeName: '',
                    typeCode: ''
                }
            },
            // 判断是否搜索过
            isSearched: false,
            // 点击左侧列表的字典值信息
            dictTypeInfo: '',
            // 字典值编辑操作相关数据
            editInfoValue: {
                // 是否显示窗口
                isShow: false,
                // 窗口模式，true：编辑，false：新增
                isEdit: false,
                // 编辑数据
                formData: {
                    valueId: '',
                    typeCode: this.typeCode,
                    parentId: '',
                    valueName: '',
                    valueCode: '',
                    valueDesc: ''
                }
            },
            //  按字典值搜索
            typeValueName: ''
        };
    },
    methods: {
        // 获取列表数据
        async getTypeList() {
            return new Promise((resolve, reject) => {
                this.$service.systemManagement
                    .getDictionaryTypeList(this.listQuery)
                    .then((response) => {
                        if (response.head.code === '000000') {
                            const currentlist = response.body.list;
                            //  首次进入，默认选中第一条
                            if (this.listQuery.pageNum === 1) {
                                currentlist.forEach((element, index) => {
                                    if (index === 0) {
                                        this.$set(element, 'active', true);
                                        this.dictTypeInfo = element;
                                        // 获取对应的字典值列表
                                        this.getList();
                                    } else {
                                        this.$set(element, 'active', false);
                                    }
                                });
                            } else {
                                currentlist.forEach((element, index) => {
                                    this.$set(element, 'active', false);
                                });
                            }
                            response.body.list = currentlist;
                            resolve(response.body);
                        } else {
                            const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                            this.$message({
                                message: this.$t(msg),
                                type: 'error'
                            });
                            resolve({
                                list: [],
                                pageSize: this.listQuery.pageSize,
                                pageNum: this.listQuery.pageNum
                            });
                        }
                    });
            });
        },
        // 搜索
        refreshList() {
            this.listQuery.pageNum = 1;
            this.listType = [];
            this.$nextTick(() => {
                this.$refs.infiniteLoading.$emit('$InfiniteLoading:reset');
            });
        },
        //  左侧列表滚动加载
        onInfinite(state) {
            this.getTypeList().then((response) => {
                const tempList = response.list;
                this.listQuery.pageNum = response.pageNum;
                this.listQuery.pageSize = response.pageSize;
                this.totalCount = response.total;
                const maxPages = Math.ceil(
                    this.totalCount / this.listQuery.pageSize
                );
                this.listType = this.listType.concat(tempList);
                if (this.listQuery.pageNum < maxPages) {
                    state.loaded();
                    // eslint-disable-next-line no-plusplus
                    this.listQuery.pageNum++;
                } else {
                    state.complete();
                }
            });
        },
        // 导出字典类型
        handleExportCmd() {
            this.$service.systemManagement
                .getDictionaryTypeExport({ dictTypeName: '' })
                .then((response) => {
                    // 调用下载excel方法
                    this.download(
                        response,
                        `${this.$tools.parseTime(
                            new Date(),
                            '{y}{m}{d}{h}{i}{s}'
                        )}.xlsx`
                    );
                });
        },
        // 调用接口删除字典类型数据
        postDelete(data) {
            this.$service.systemManagement
                .postRemoveDictionaryType(data)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.dictionary.message.deleteSuccess'
                            ),
                            type: 'success'
                        });
                        this.refreshList();
                    } else {
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.dictionary.message.deleteFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 显示删除字典类型确认提示
        showDelete(row) {
            this.$confirm(
                this.$t('systemManagement.dictionary.message.deleteTypeTip'),
                this.$t('systemManagement.dictionary.message.deleteTipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.postDelete({
                    typeCode: row.typeCode,
                    typeName: row.typeCode
                });
            });
        },
        // 点击左侧列表获取对象值
        dictInfoShow(item) {
            this.dictTypeInfo = item;
            //  获取右侧字典值数据
            this.getList();
            // 遍历listType，改变active的值
            this.listType.forEach((element) => {
                if (element.id === item.id) {
                    element.active = true;
                } else {
                    element.active = false;
                }
            });
        },
        // 获取字典值数据列表
        getList() {
            const query = {
                typeCode: this.dictTypeInfo.typeCode,
                pageNum: 1,
                pageSize: 10000,
                typeValueName: this.typeValueName
            };
            this.$service.systemManagement
                .getDictionaryValueList(query)
                .then((response) => {
                    if (response.head.code === '000000') {
                        const resultObj = response.body;
                        this.list = resultObj;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 调用接口删除数据
        postDeleteValue(data) {
            this.$service.systemManagement
                .postRemoveDictionaryValue(data)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.dictionary.message.deleteSuccess'
                            ),
                            type: 'success'
                        });
                        this.getList();
                    } else {
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.dictionary.message.deleteFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 显示新增窗口
        showAddValue() {
            // 清空数据
            Object.assign(
                this.editInfoValue.formData,
                this.$options.data().editInfoValue.formData
            );
            // 新增的类型编码，为当前类别类型编码
            this.editInfoValue.formData.typeCode = this.dictTypeInfo.typeCode;
            // 新增的类型名称，为当前类别类型名称
            this.editInfoValue.formData.typeName = this.dictTypeInfo.typeName;
            // 指定弹窗为新增模式
            this.editInfoValue.isShow = true;
            this.editInfoValue.isEdit = false;
        },
        // 显示删除确认提示
        showDeleteValue(row, dictType) {
            const typeName = this.$t(
                `systemManagement.dictionaryManagement.type.${dictType}`
            );
            this.$confirm(
                this.$t('systemManagement.dictionary.message.deleteValueTip'),
                this.$t('systemManagement.dictionary.message.deleteTipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                const param = {
                    valueId: row.valueId,
                    typeName,
                    valueName: row.valueName
                };
                // 确认删除后，调用删除的接口
                this.postDeleteValue(param);
            });
        },
        // 显示编辑窗口
        showEditValue(row) {
            Object.assign(this.editInfoValue.formData, row);
            // 新增的类型名称，为当前类别类型名称
            this.editInfoValue.formData.typeName = this.dictTypeInfo.typeName;
            this.editInfoValue.formData.oldParam = JSON.parse(
                JSON.stringify(this.editInfoValue.formData)
            );
            this.editInfoValue.isShow = true;
            this.editInfoValue.isEdit = true;
        },
        // 保存完成回调，isSuccess保存是否成功标识
        saveFinishType(isSuccess) {
            if (!isSuccess) {
                return;
            }
            this.refreshList();
        }
    }
};
</script>

<style lang="scss" scoped>
.role-detail {
    width: 100%;
    background: #f5f6fa;
    box-sizing: border-box;
    padding: 20px;
    margin-bottom: 20px;
    .label {
        font-size: 14px;
        font-weight: 400;
        color: #676c6f;
        margin-bottom: 20px;
        word-break: break-all;
        &:last-child {
            margin-bottom: 0;
        }
    }
    .edit-icon {
        margin-left: 20px;
        cursor: pointer;
        color: #3370ff;
    }
}
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.red-color {
    color: #ff4949;
}
.green-color {
    color: #13ce66;
}
.title-export {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    span {
        font-weight: bold;
    }
}
.add-type {
    margin-bottom: 10px;
    margin-top: 20px;
    .margin-left {
        width: 100%;
    }
}
.type-list {
    height: calc(100% - 90px);
    overflow: auto;
    ul {
        list-style: none;
        font-size: 14px;
        padding: 0;
        li {
            font-size: 14px;
            height: 40px;
            line-height: 40px;
            color: #676c6f;
            cursor: pointer;
            padding-left: 12px;
            &:hover {
                background: #f4f7fa;
                border-radius: 4px;
            }
            .block__btns {
                display: none;
            }
            .block__text {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        li:hover {
            .block__btns {
                display: inline-block;
            }
        }
        li.active {
            background: #eff4ff;
        }
    }
}
.info {
    height: 60px;
    display: flex;
    align-items: center;
    background: #f5f6fa;
    box-sizing: border-box;
    padding: 22px 20px;
    margin-bottom: 20px;
    .desc-info {
        display: flex;
        align-items: center;
        .color-blue {
            color: #3370ff;
            width: 13px;
            margin-right: 10px;
        }
        .color-blue-phone {
            font-size: 18px;
        }
        .desc-text {
            margin-left: 5px;
            width: 465px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
