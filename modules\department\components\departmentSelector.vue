<template>
    <TopSelector
        @lazyLoad="handleLazyLoad"
        :statusDisabled="true"
        :infoDisabled="infoDisabled"
        @input="handleChange"
        :placeholder="curPlaceholder"
        :value="value"
        :key="key"
    ></TopSelector>
</template>

<script>
/**
 * DepartmentSelector 组件
 * @module department/components
 * @desc 用于选择公司部门和小组的级联选择器，并支持懒加载数据。
 *       切换页面时从vuex中读取值，同时每次变更时会更新vuex的值
 * @param {Boolean} [infoDisabled=false] - 控制级联选择器是否禁用，默认为 false
 * @param {String} [placeholder='请选择小组'] - 级联选择器的占位符文本，默认为“请选择小组”
 * @param {Function} [input] - 选择值发生变化时触发的方法
 * @param {Boolean} [teamDisabled=false] - 是否不选择小组，默认为false
 * @example 调用示例
 *  <DepartmentSelector
 *    :infoDisabled="infoDisabled"
 *    :placeholder="placeholder"
 *    @input="handleChange"
 *  ></DepartmentSelector>
 * */
import TopSelector from 'Components/TopSelector';
import { CONSTANTS } from '@/constants';

const { TECH_CENTER_CODE } = CONSTANTS;
export default {
    name: 'DepartmentSelector',
    components: {
        TopSelector
    },
    props: {
        infoDisabled: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择小组'
        },
        teamDisabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 是否为小组的数据
            isTeam: false,
            key: 0,
            // 获取部门信息的接口
            getSubDepartmentListApi:
                this.$service.department.group.getSubDepartment,
            // 获取小组信息的接口
            getTeamListApi:
                this.$service.department.group.getGroupInfoWithPermission
        };
    },
    computed: {
        value() {
            return this.$store.state.department?.departmentStore || [];
        },
        curPlaceholder() {
            const storeVal =
                this.$store.state.department?.departmentLabel || '';
            if (storeVal) {
                return storeVal;
            }
            return this.placeholder;
        }
    },
    mounted() {
        // 挂载时触发事件，便于查询
        if (this.value.length !== 0) {
            this.handleChange(this.value);
        }
    },
    methods: {
        /**
         * 处理节点懒加载
         * @param {Object} node 当前节点
         * @param {Function} resolve 加载成功后的回调
         * @return {Function} 加载成功后的回调
         */
        handleLazyLoad(node, resolve) {
            const { level, value } = node;
            // 确定菜单最大长度
            let maxLevel = 3;
            if (this.teamDisabled) {
                maxLevel = 2;
            }
            if (level >= maxLevel) {
                return resolve();
            }
            // 动态节点
            let nodes = [];
            let params = '';
            let api = this.getSubDepartmentListApi;
            if (level === 0) {
                // 第一级菜单默认查技术中心的orgCode
                params = { orgCode: TECH_CENTER_CODE };
            } else if (level === 1) {
                params = { orgCode: value };
            } else {
                // 第三级菜单替换为查询小组的接口
                api = this.getTeamListApi;
                params = { departmentCode: value };
            }
            api(params)
                .then((res) => {
                    if (res.head.code !== '000000') {
                        this.$message.error(res.head.message);
                        return;
                    }
                    // 构建节点数组
                    nodes = res.body.map((item) => {
                        let obj;
                        if (params?.orgCode) {
                            obj = {
                                value: item.orgCode,
                                label: item.orgName,
                                leaf: level >= maxLevel - 1,
                                disabled: !item.hasPermission
                            };
                        } else {
                            obj = {
                                value: item.id,
                                label: item.teamName,
                                leaf: level >= maxLevel - 1
                            };
                        }
                        return obj;
                    });
                    // 加载成功，返回数据
                    resolve(nodes);
                })
                .catch((error) => {
                    console.error('Error:', error);
                });
        },
        /**
         *  向上传递选中值后的回调
         * @param {Array} value 选中的值
         */
        handleChange(value) {
            const teamValue = value;
            if (teamValue.length === 3) {
                this.isTeam = true;
            }
            this.$store.dispatch('department/changeDepartment', value);
            if (this.teamDisabled && this.isTeam) {
                teamValue.pop();
                this.$emit('input', teamValue);
                return;
            }
            this.$emit('input', value);
        }
    }
};
</script>
<style lang="scss" scoped></style>
