<template>
    <div class="header-container" v-show="showHeader">
        <el-select
            v-model="weeklyId"
            size="medium"
            placeholder="请选择周报"
            class="select-week"
            @change="handleSelectChange"
        >
            <el-option
                v-for="item in options"
                :key="`${item.yearVal}年第${item.week}周(${item.startDate}-${item.endDate})`"
                :label="formatWeekLabel(item)"
                :value="item.weekReportId"
            >
            </el-option>
        </el-select>
        <div class="header-right-items">
            <!-- 有项目周报才会展示操作按钮 -->
            <slot name="customButton" v-if="options.length > 0"></slot>
            <!-- 只有在窗口期，并且选择项目经理的是否才有周报状态 -->
            <el-button
                v-if="
                    weeklyStatus !== '归档' &&
                    this.projectStore.length === 3 &&
                    options.length > 0
                "
                :disabled="!(isProjectManager || isPqa)"
                :type="weeklyStatus === '已更新' ? 'success' : 'primary'"
                style="height: 30px"
                @click="handleRadioChange"
                >{{ weeklyStatus }}</el-button
            >
            <el-button
                v-if="
                    showAddButton &&
                    this.projectStore.length === 3 &&
                    (isProjectManager || isPqa)
                "
                @click="handleAddNewWeekly"
                type="primary"
                >新增周报</el-button
            >
        </div>
    </div>
</template>

<script>
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';
import { Loading } from 'element-ui';

export default {
    name: 'Header',
    components: {},
    props: {
        pageType: {
            type: String,
            default: ''
        }
    },
    /**
     * 组件数据初始化
     * @returns {Object} 组件的响应式数据对象
     */
    data() {
        return {
            // 项目经理
            projectManager: '',
            // PQA
            pqa: [],
            // 当前选择的周报信息
            currentOption: {},
            // 项目列表下拉选项
            options: [],
            // 包含产品线与PQA信息的数组
            productLineInfoList: [],
            // 周报状态
            weeklyStatus: '待更新',
            // 是否展示新增周报按钮
            showAddButton: false
        };
    },
    computed: {
        // 周报ID
        weeklyId: {
            get() {
                return this.$store.state.maintenanceProject
                    .maintenanceReportHeader.weeklyId;
            },
            set(value) {
                this.$store.dispatch('maintenanceProject/setWeeklyId', value);
            }
        },
        // 当前选中的周报选项（从store获取）
        currentSelectedOption() {
            return this.$store.state.maintenanceProject.currentSelectedOption;
        },
        // 项目周报是否需要更新（是否在窗口期）
        // 不在窗口期代表也没有编辑权限
        // 如果项目周报在窗口期（一般为上周五、本周一和本周二），则需要调用更新接口
        // 如果项目周报在窗口期之外（包括过去的项目周报），则不需要调用更新接口
        shouldUpdate() {
            return this.currentSelectedOption?.whetherUpdate || false;
        },
        // 维护项目信息：产品线/细分产品线/项目经理
        projectStore() {
            return (
                this.$store.state.maintenanceProject.maintenanceProjectStore ||
                []
            );
        },
        // 是否是项目经理
        isProjectManager() {
            // 不选到项目经理，无法编辑
            if (this.projectStore.length !== 3) return false;
            return getUserAccount(this) === this.projectStore[2];
        },
        // 是否为PQA，PQA对进展补充有编辑权限
        isPqa() {
            // 不选到项目经理，无法编辑
            if (this.projectStore.length !== 3) return false;
            // 在该细分产品线下的pqa里面包含登录用户
            const managerList =
                this.productLineInfoList.filter(
                    (i) => i.subProductLine === this.projectStore[1]
                ) || [];
            if (managerList.length === 0) return false;
            const pqaList = managerList[0].pqa || [];
            this.$store.dispatch(
                'maintenanceProject/setIsPqa',
                pqaList.includes(getUserAccount(this))
            );
            return pqaList.includes(getUserAccount(this));
        },
        // 当前所在页签
        activeName() {
            return (
                this.$store.state.maintenanceProject.maintenanceReportHeader
                    .activeName || ''
            );
        },
        // 如果外部传入周报ID，就不展示头部选择项
        showHeader() {
            return !(
                this.$store.state.reportForm.outsideWeekly &&
                this.$store.state.reportForm.outsideWeekly.length > 0
            );
        },
        // 外部传入的周报ID（报表页面）
        outsideWeekly() {
            return this.$store.state.reportForm.outsideWeekly;
        }
    },
    watch: {
        projectStore: {
            handler(newVal) {
                this.pageType === this.activeName &&
                    newVal.length > 0 &&
                    this.handleProjectChange();
            },
            deep: true
        },
        activeName(newVal) {
            if (this.projectStore.length === 0) return;
            this.pageType === newVal && this.handleProjectChange();
        },
        // 监听周报状态，传递出去用于判断是否需要编辑
        weeklyStatus() {
            this.$store.dispatch(
                'maintenanceProject/setWeeklyStatus',
                this.weeklyStatus
            );
        },
        outsideWeekly(newVal) {
            newVal && this.handleProjectChange();
        }
    },
    created() {
        this.getProductLineManager();
        this.handleProjectChange();
    },
    activated() {
        this.getProductLineManager();
        this.handleProjectChange();
    },
    methods: {
        /**
         * 格式化周报标签
         * @param {Object} item 周报项目数据
         * @returns {String} 格式化后的标签
         */
        formatWeekLabel(item) {
            if (!item || !item.startDate || !item.endDate) {
                return `${item.yearVal || ''}年第${item.week || ''}周`;
            }

            // 将日期从 2025-01-20 格式转换为 25/01/20 格式
            const formatDate = (dateStr) => {
                if (!dateStr) return '';
                return dateStr.slice(2).replace(/-/g, '/');
            };

            const startDate = formatDate(item.startDate);
            const endDate = formatDate(item.endDate);

            return `${item.yearVal}年第${item.week}周(${startDate}-${endDate})`;
        },
        /**
         * 更新store中的当前选中选项
         */
        updateCurrentSelectedOption() {
            const options = this.options.find(
                (i) => i.weekReportId === this.weeklyId
            ) || { weekReportIdGetList: [] };
            this.$store.dispatch(
                'maintenanceProject/setCurrentSelectedOption',
                options
            );
        },
        /**
         * 项目变更的处理
         */
        async handleProjectChange() {
            // 如果由外部传入周报ID，直接查
            const { outsideWeekly } = this.$store.state.reportForm;
            if (outsideWeekly) {
                this.$emit('get-table-list');
                return;
            }
            const loadingInstance = Loading.service({
                text: '项目周报更新中',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            try {
                // 更新store中的当前选中选项，注意需要在判断shouldUpdate之前更新
                await this.updateCurrentSelectedOption();
                // 选择项目之后，获取项目周报列表
                await this.getProjectWeeklyList();
                if (this.shouldUpdate) {
                    await this.updateWeekly();
                    await this.getProjectWeeklyList();
                }
                // 查询窗口期时间
                this.getWindowPeriod();
                // 获取周报列表并且判断完是否需要更新之后，获取需求/订单/缺陷列表
                this.$emit('get-table-list');
            } catch (error) {
                console.error('Error in handleProjectChange:', error);
            } finally {
                loadingInstance && loadingInstance.close();
            }
        },
        /**
         * 获取项目团队成员、项目经理、PQA
         * @param {Object} data 原始数据
         */
        handleTeamGroupData(data) {
            this.projectManager =
                data.projectTeams.find((i) => i.position === '项目经理')
                    ?.leader[0] || '';
            this.pqa =
                data.projectTeams.find((i) => i.position === 'PQA')?.leader ||
                [];
        },
        /**
         * 获取项目周报列表
         */
        async getProjectWeeklyList() {
            const params = {
                productLine: this.projectStore[0] || '',
                subProductLine: this.projectStore[1] || '',
                projectManager: this.projectStore[2] || ''
            };

            const api =
                this.$service.maintenanceProject.weekly.getWeekReportList;

            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject(new Error(res.head.message));
                }

                if (res.body.length === 0) {
                    this.options = [];
                    this.weeklyId = '';
                    return Promise.resolve(res.body);
                }
                // 选了产品线/细分产品线，没有总体的周报ID
                // 前端赋一个值用来区分
                if (this.projectStore.length !== 3) {
                    this.options = res.body.map((i) => ({
                        ...i,
                        weekReportId: i.startDate
                    }));
                } else {
                    this.options = res.body;
                }

                // 检查当前store中是否已经有选中的weeklyId，且在新的options中存在
                const currentWeeklyId = this.weeklyId;
                const currentOptionExists =
                    currentWeeklyId &&
                    this.options.find(
                        (i) => i.weekReportId === currentWeeklyId
                    );

                if (currentOptionExists) {
                    // 如果当前选中的weeklyId在新的options中存在，保持当前选择
                    this.weeklyStatus = currentOptionExists.weekReportStatus;
                    this.$store.dispatch(
                        'maintenanceProject/setShouldUpdate',
                        currentOptionExists.whetherUpdate
                    );
                } else {
                    // 只有在没有有效选择时才默认选择第一个
                    this.$store.dispatch(
                        'maintenanceProject/setWeeklyId',
                        this.options[0].weekReportId
                    );
                    this.$store.dispatch(
                        'maintenanceProject/setShouldUpdate',
                        this.options[0].whetherUpdate
                    );
                    // 周报状态
                    this.weeklyStatus = this.options[0].weekReportStatus;
                }

                // 更新store中的当前选中选项
                this.updateCurrentSelectedOption();

                return Promise.resolve(this.options);
            } catch (error) {
                console.error('Error in getProjectWeeklyList:', error);
                return Promise.reject(error);
            }
        },
        /**
         * 处理周报状态单选按钮变化
         * 更新项目周报状态
         */
        async handleRadioChange() {
            // 点击按钮切换更新状态
            this.weeklyStatus =
                this.weeklyStatus === '待更新' ? '已更新' : '待更新';
            const api =
                this.$service.maintenanceProject.weekly.editWeekReportStatus;
            const params = {
                weekReportId: this.weeklyId,
                weekReportStatus: this.weeklyStatus
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                await this.getProjectWeeklyList();
                this.$message.success('更新项目周报状态成功');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 切换项目周报
         * 1.判断是否要更新，需要就更新，更新之后要更新项目周报下拉列表
         * 2.然后查询列表数据
         */
        async handleSelectChange() {
            const loadingInstance = Loading.service({
                text: '项目周报更新中',
                background: 'rgba(0, 0, 0, 0.1)'
            });

            try {
                // 更新store中的当前选中选项，注意需要在判断shouldUpdate之前更新
                await this.updateCurrentSelectedOption();
                if (this.shouldUpdate) {
                    await this.updateWeekly();
                    await this.getProjectWeeklyList('update');
                } else {
                    // 不需要更新，就更新状态
                    this.weeklyStatus = this.options.find(
                        (i) => i.weekReportId === this.weeklyId
                    ).weekReportStatus;
                }
                // 查询窗口期时间
                this.getWindowPeriod();

                // 判断完是否需要更新之后，获取需求/订单/缺陷列表
                this.$emit('get-table-list');
            } catch (error) {
                console.error(error);
            } finally {
                loadingInstance && loadingInstance.close();
            }
        },
        /**
         * 更新项目周报
         */
        async updateWeekly() {
            const activeNameMap = {
                demand: '需求',
                order: '订单',
                defect: '缺陷'
            };
            const api =
                this.$service.maintenanceProject.weekly.updateWeekReportList;
            const params = {
                reportType: activeNameMap[this.activeName],
                weekReportIdList:
                    this.currentSelectedOption?.weekReportIdUpdateList || []
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    Promise.reject();
                    return;
                }
                Promise.resolve();
            } catch (error) {
                console.error('Error:', error);
                Promise.reject();
            }
        },
        /**
         * 获取产品线PQA信息
         */
        async getProductLineManager() {
            try {
                const res =
                    await this.$service.project.productLine.getProductLine();
                if (res.head.code === '000000') {
                    this.productLineInfoList = res.body || [];
                    return Promise.resolve(res.body);
                }
                this.$message.error(res.head.message);
                return Promise.reject(new Error(res.head.message));
            } catch (error) {
                console.error(error, 'error');
                return Promise.reject(error);
            }
        },
        /**
         * 如果项目经理或者PQA认为缺少周报，可以进行手动新增
         */
        async handleAddNewWeekly() {
            const params = {
                productLine: this.projectStore[0] || '',
                subProductLine: this.projectStore[1] || '',
                projectManager: this.projectStore[2] || ''
            };

            const api = this.$service.maintenanceProject.weekly.addNewWeekly;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('新增成功');
                this.handleProjectChange();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取窗口期信息，用于处理新建项目的显示和隐藏
         */
        async getWindowPeriod() {
            if (this.options.length === 0) {
                this.showAddButton = true;
                return;
            }
            const api = this.$service.maintenanceProject.weekly.getWindowPeriod;
            try {
                const res = await api();
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (!res.body) {
                    // 返回null的时候不展示
                    this.showAddButton = false;
                    return;
                }
                // 如果开始日期不等于最新周报的开始日期，就展示新增周报按钮
                this.showAddButton =
                    this.options[0].startDate !== res.body?.startDate;
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.header-container {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.select-week {
    width: 600px;
    ::v-deep .el-input__inner {
        font-weight: bold;
        border: none;
        padding-left: 0;
    }
}
.header-right-items {
    display: flex;
    align-items: center;
}

.radio-group-spacing {
    margin-left: 10px;
}
</style>
