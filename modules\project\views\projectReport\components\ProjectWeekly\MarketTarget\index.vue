<template>
    <div>
        <div class="market-target-header">
            <div class="title">市场目标</div>
        </div>

        <el-table :data="marketTargetList" style="width: 100%">
            <el-table-column
                prop="index"
                label="序号"
                width="65"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="marketTarget"
                label="市场目标"
                header-align="center"
            ></el-table-column>
            <el-table-column
                prop="planEndDate"
                label="完成日期"
                width="120"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="completionStatus"
                label="完成状态"
                width="90"
                align="center"
            >
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'MarketTarget',
    components: {},
    props: {
        marketTargetList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {};
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.title {
    @include section-title;
}
.market-target-header {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #8c8c8c;
    margin-bottom: 15px;
    height: 28px;
}

.blue-bar {
    width: 4px;
    height: 20px;
    background-color: #4a7bec;
    margin-right: 8px;
}

.risk-tag {
    margin-right: 5px;
}
</style>
