<template>
    <div>
        <el-dialog
            title="编辑会议任务"
            :visible.sync="dialogVisible"
            width="85%"
            :before-close="reset"
        >
            <el-card>
                <el-descriptions
                    :title="form.meetingTitle"
                    size="medium"
                    border
                    :column="12"
                >
                    <el-descriptions-item label="会议日期">{{
                        handleTime(form)
                    }}</el-descriptions-item>
                    <el-descriptions-item label="提出人">{{
                        form.creatorName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="针对问题/事项">{{
                        form.problemItem
                    }}</el-descriptions-item>
                    <el-descriptions-item label="会议要求">{{
                        form.meetingRequire
                    }}</el-descriptions-item>
                </el-descriptions>
            </el-card>
            <el-form ref="form" :model="form" class="form" size="large">
                <div class="flex minutes">
                    <el-form-item
                        label="责任人"
                        :rules="required"
                        prop="responsibleAccount"
                    >
                        <PeopleSelector
                            placeholder=""
                            v-model="form.responsibleAccount"
                            ref="responsible"
                            :clearable="true"
                            :options="externalOptions"
                            :isMultipled="false"
                        ></PeopleSelector>
                    </el-form-item>
                    <el-form-item
                        label="计划完成时间"
                        :rules="required"
                        prop="planFinishDate"
                    >
                        <el-date-picker
                            v-model="form.planFinishDate"
                            type="date"
                            placeholder="请输入计划完成时间"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item
                        label="任务状态"
                        :rules="required"
                        prop="finishStatus"
                    >
                        <el-select
                            v-model="form.finishStatus"
                            placeholder="请选择完成状态"
                        >
                            <el-option
                                v-for="item in CONSTANTS.TASK_STATUS"
                                :label="item"
                                :key="item"
                                :value="item"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                </div>
                <el-form-item label="完成情况" prop="finishDesc">
                    <el-input
                        v-model="form.finishDesc"
                        type="textarea"
                        :rows="5"
                        placeholder="请输入完成情况"
                        maxlength="2000"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';
import { getSelectedLabel } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MeetingTraceTaskDialog',
    components: { PeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },

    data() {
        return {
            form: {},
            CONSTANTS,
            required: {
                required: true,
                // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.$nextTick(() => {
                    this.form = this.$tools.cloneDeep(this.data);
                });
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.dialogVisible = false;
            this.$emit('update');
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            const api = this.$service.feature.meetingTaskTrace.edit;

            const responsibleName = getSelectedLabel(this.$refs.responsible);
            try {
                const res = await api({ ...this.form, responsibleName });
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 关闭弹窗前的回调
         * @param {Function} done 关闭弹窗的函数
         */
        reset(done) {
            this.$refs.form.resetFields();
            done();
        },
        handleTime(form) {
            if (!form.endTime) {
                if (form.startTime) {
                    return form?.startTime?.split(' ')[0];
                }
                return '';
            }
            const date = form?.startTime.split(' ')[0];
            const startTime = form?.startTime.split(' ')[1];
            const endTime = form?.endTime.split(' ')[1];
            return `${date} ${startTime}-${endTime}`;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
}
</style>
