const handleChartData = (data, toHandle) => {
    const res = [];
    data.forEach((i) => {
        res.push((+Math.floor(i[toHandle] / 100) / 100).toFixed(2));
    });
    return res;
};

const formatTooltip = (params) => {
    let tooltipContent = '';
    params.forEach((param) => {
        tooltipContent += `${param.marker}${param.seriesName}：${param.data}${
            param.seriesName === '预算支出比率' ? '%' : '万元'
        }</br>`;
    });
    return tooltipContent;
};

const handlePieData = (data, type) => {
    return data.map((i) => {
        return {
            value: (Math.floor(i[type] / 100) / 100).toFixed(2),
            name: i.stageOrSubject
        };
    });
};
// 总体执行情况-柱状图
export const getOverallOptions = (data) => {
    return {
        grid: {
            bottom: 40,
            top: 40,
            left: 60
        },
        xAxis: {
            type: 'category',
            data: ['概算值', '预算值', '实际支出'],
            axisTick: {
                show: false
            }
        },
        yAxis: {
            name: '金额(万元)',
            type: 'value',
            axisLabel: {
                show: true,
                formatter: (value) => value.toFixed(2)
            }
        },
        tooltip: {
            show: true,
            trigger: 'axis',
            formatter: (params) =>
                `${params[0].marker}${params[0].axisValue}：${params[0].data}万元</br>`
        },
        series: [
            {
                name: '金额（万元）',
                type: 'bar',
                data: [
                    (
                        Math.floor(data.financeEstimateAmount / 100) / 100
                    ).toFixed(2),
                    (Math.floor(data.budgetAmount / 100) / 100).toFixed(2),
                    (Math.floor(data.costAmount / 100) / 100).toFixed(2)
                ],
                itemStyle: {
                    color: '#3370ff'
                },
                label: {
                    show: true
                }
            }
        ]
    };
};
// 按阶段执行情况-柱状图
// eslint-disable-next-line max-lines-per-function
export const getPhaseOptions = (data) => {
    // 预算值
    const budgetAmountArr = handleChartData(
        data.projectFinanceByGroupVoList,
        'budgetAmount'
    );
    // 实际支出
    const costAmountArr = handleChartData(
        data.projectFinanceByGroupVoList,
        'costAmount'
    );
    // 预算支出比率
    const budgetCostRatioArr = [];
    data.projectFinanceByGroupVoList.forEach((i) => {
        budgetCostRatioArr.push(i.budgetCostRatio);
    });
    const dataList = data.projectFinanceByGroupVoList.map(
        (i) => i.stageOrSubject
    );
    return {
        legend: {
            data: ['预算值', '实际支出', '预算支出比率'],
            show: true,
            itemGap: 20
        },
        grid: {
            bottom: 40,
            top: 50
        },
        xAxis: {
            type: 'category',
            data: dataList,
            axisTick: {
                show: false
            }
        },
        yAxis: [
            {
                name: '金额(万元)',
                type: 'value',
                axisLabel: {
                    show: true,
                    formatter: (value) => value.toFixed(2)
                }
            },
            {
                name: '预算支出比率',
                type: 'value',
                axisLabel: {
                    show: true,
                    formatter: (value) => `${value.toFixed(2)}%`
                },
                splitLine: {
                    show: false
                }
            }
        ],
        tooltip: {
            show: true,
            trigger: 'axis',
            formatter: (params) => formatTooltip(params)
        },
        series: [
            {
                name: '预算值',
                type: 'bar',
                data: budgetAmountArr,
                itemStyle: {
                    color: '#e6e6e6'
                },
                label: {
                    show: true
                },
                emphasis: {
                    disabled: true,
                    focus: 'none'
                }
            },
            {
                name: '实际支出',
                type: 'bar',
                data: costAmountArr,
                itemStyle: {
                    color: '#3370ff'
                },
                label: {
                    show: true
                }
            },
            {
                name: '预算支出比率',
                type: 'line',
                yAxisIndex: 1,
                data: budgetCostRatioArr,
                lineStyle: {
                    color: '#3370ff',
                    type: 'dashed'
                },
                label: {
                    show: true,
                    formatter: '{c}%'
                }
            }
        ]
    };
};
// 按阶段执行情况-预算比例
export const getPhaseBudgetOptions = (data) => {
    const pieData = data.projectFinanceByGroupVoList.map((i) => {
        return {
            value: (Math.floor(i.budgetAmount / 100) / 100).toFixed(2),
            name: i.stageOrSubject
        };
    });
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}(${params.percent.toFixed(0)}%)`;
            }
        },
        series: [
            {
                type: 'pie',
                data: pieData,
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 80,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(0)}%)`;
                    }
                },
                labelLine: {
                    length: 1,
                    length2: 1,
                    maxSurfaceAngle: 80,
                    lineStyle: {
                        width: 1
                    }
                },
                center: ['50%', '50%']
            }
        ]
    };
};
// 按阶段执行情况-实际比例
export const getPhaseActualOptions = (data) => {
    const pieData = data.projectFinanceByGroupVoList.map((i) => {
        return {
            value: (Math.floor(i.costAmount / 100) / 100).toFixed(2),
            name: i.stageOrSubject
        };
    });
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}(${params.percent.toFixed(0)}%)`;
            }
        },
        series: [
            {
                type: 'pie',
                data: pieData,
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 80,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(0)}%)`;
                    }
                },
                labelLine: {
                    length: 1,
                    maxSurfaceAngle: 180,
                    lineStyle: {
                        width: 1
                    }
                },
                center: ['50%', '50%']
            }
        ]
    };
};
// 按科目执行情况-柱状图
// eslint-disable-next-line max-lines-per-function
export const getSubjectOptions = (data) => {
    const dataList = data.map((i) => i.stageOrSubject);
    // 预算值
    const budgetAmountArr = handleChartData(data, 'budgetAmount');
    // 实际支出
    const costAmountArr = handleChartData(data, 'costAmount');
    // 预算支出比率
    const budgetCostRatioArr = [];
    data.forEach((i) => {
        budgetCostRatioArr.push(i.budgetCostRatio);
    });
    return {
        legend: {
            data: ['预算值', '实际支出', '预算支出比率'],
            show: true,
            itemGap: 20
        },
        grid: {
            bottom: 40,
            top: 50,
            left: 80,
            right: 80
        },
        xAxis: {
            type: 'category',
            data: dataList,
            axisLabel: {
                fontSize: 12,
                width: 100,
                overflow: 'truncate',
                interval: 0
            },
            axisTick: {
                show: false
            }
        },
        yAxis: [
            {
                name: '金额(万元)',
                type: 'value',
                axisLabel: {
                    show: true,
                    formatter: (value) => `${value.toFixed(2)}`
                }
            },
            {
                name: '预算支出比率',
                type: 'value',
                splitLine: {
                    show: false
                },
                axisLabel: {
                    formatter: (value) => `${value.toFixed(2)}%`
                }
            }
        ],
        tooltip: {
            show: true,
            trigger: 'axis',
            formatter: (params) => formatTooltip(params)
        },
        series: [
            {
                name: '预算值',
                type: 'bar',
                data: budgetAmountArr,
                itemStyle: {
                    color: '#e6e6e6'
                },
                label: {
                    show: true
                },
                emphasis: {
                    disabled: true,
                    focus: 'none'
                }
            },
            {
                name: '实际支出',
                type: 'bar',
                data: costAmountArr,
                itemStyle: {
                    color: '#3370ff'
                },
                label: {
                    show: true,
                    overflow: 'none'
                }
            },
            {
                name: '预算支出比率',
                type: 'line',
                yAxisIndex: 1,
                data: budgetCostRatioArr,
                lineStyle: {
                    color: '#3370ff',
                    type: 'dashed'
                },
                label: {
                    show: true,
                    formatter: '{c}%'
                }
            }
        ]
    };
};
// 按科目执行情况-预算比例
export const getSubjectBudgetOptions = (data) => {
    const pieData = handlePieData(data, 'budgetAmount');
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}(${params.percent.toFixed(0)}%)`;
            }
        },
        series: [
            {
                type: 'pie',
                data: pieData,
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(0)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                },
                center: ['50%', '50%']
            }
        ]
    };
};
// 按科目执行情况-实际比例
export const getSubjectActualOptions = (data) => {
    const pieData = handlePieData(data, 'costAmount');
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}(${params.percent.toFixed(0)}%)`;
            }
        },
        series: [
            {
                type: 'pie',
                data: pieData,
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(0)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                },
                center: ['50%', '50%']
            }
        ]
    };
};
