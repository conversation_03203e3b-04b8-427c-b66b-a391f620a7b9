<template>
    <div class="scene-button">{{ buttonName }}</div>
</template>
<script>
export default {
    name: 'SceneButtonNode',
    data() {
        return {
            buttonName: ''
        };
    },
    mounted() {
        // 初始化数据
        this.initData();
    },
    methods: {
        // 初始化数据
        initData() {
            const { buttonName } = this.$vnode.data.node.data;
            this.buttonName = buttonName;
        }
    }
};
</script>
<style lang="scss" scoped>
.scene-button {
    width: 80px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 100%;
    color: #0f417a;
    border-radius: 12px;
    border: 1px solid #ffffff;
    background-color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
}
.scene-button:hover {
    background-color: rgba(255, 255, 255, 0.9);
}
</style>
