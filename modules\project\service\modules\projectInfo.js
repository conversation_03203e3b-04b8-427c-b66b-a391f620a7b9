/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 通过产品线获取项目信息
        projectInfo: {
            // 获取某一产品线下面所有的项目
            getProjectListByProductLine(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjects',
                    method: 'get',
                    params: data
                });
            },
            // 根据查询条件获取项目列表
            getProjectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectInfoList',
                    method: 'post',
                    data
                });
            },
            // 获取项目详细信息
            getProjectDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectInfo',
                    method: 'get',
                    params: data
                });
            },
            // 新增项目信息
            addProjectInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/addProjectInfo',
                    method: 'post',
                    data
                });
            },
            // 修改项目信息
            editProjectInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/updateProjectInfo',
                    method: 'post',
                    data
                });
            },
            // 获取禅道项目列表
            getZentaoProjectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/zentao/project/getProProjectList',
                    method: 'get',
                    params: data
                });
            },
            // 获取项目状态下各项目的数量
            getProjectStatusCount(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectStatusGroup',
                    method: 'post',
                    data
                });
            },
            // 获取项目暂停原因
            getPausedReason(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectPauseReason',
                    method: 'get',
                    params: data
                });
            },
            // 删除项目
            deleteProject(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/deleteProjectInfo',
                    method: 'delete',
                    params: data
                });
            }
        }
    };

    return service;
};
