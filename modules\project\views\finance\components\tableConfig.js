import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange } = CommonItems;
const flowName = {
    ...select,
    name: '流程名称',
    modelKey: 'flowName',
    elOptions: []
};
const costSubject = {
    ...select,
    name: '费用科目',
    modelKey: 'costSubject',
    elOptions: []
};
const projectStage = {
    ...select,
    name: '阶段',
    modelKey: 'projectStage',
    elOptions: [
        {
            label: '概念阶段',
            value: '概念阶段'
        },
        {
            label: '计划阶段',
            value: '计划阶段'
        },
        {
            label: '开发阶段',
            value: '开发阶段'
        },
        {
            label: '验证阶段',
            value: '验证阶段'
        },
        {
            label: '迁移阶段',
            value: '迁移阶段'
        }
    ]
};
const flowId = {
    ...input,
    name: '流程ID',
    modelKey: 'flowId'
};
const calculateDateRange = {
    ...dateRange,
    name: '申请日期',
    modelKey: 'calculateDateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 查询参数初始化
const queryParams = {
    flowName: '',
    costSubject: '',
    projectStage: '',
    flowId: '',
    calculateDateRange: [],
    sortStr: 'applyDate desc'
};

// 查询区域配置项
const queryConfigItems = [
    flowName,
    costSubject,
    projectStage,
    flowId,
    calculateDateRange
];

// eslint-disable-next-line max-lines-per-function
const getTableConfig = (scope) => {
    return {
        // 列表查询参数
        queryParams,
        // 查询项配置
        queryConfig: {
            items: queryConfigItems
        },
        // 查询api配置
        queryApi: scope.$service.project.finance.getCostList,
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: [
            {
                label: '数据来源',
                prop: 'flowSource',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '流程名称',
                prop: 'flowName',
                show: true,
                minWidth: 160,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                },
                slot: 'flowName'
            },
            {
                label: '流程ID',
                prop: 'flowId',
                show: true,
                minWidth: 210,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '流程申请人',
                prop: 'applyPersonName',
                show: true,
                minWidth: 90,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目经理',
                prop: 'calculatePersonName',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '费用科目',
                prop: 'costSubject',
                show: true,
                minWidth: 100,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '申请日期',
                prop: 'applyDate',
                show: true,
                minWidth: 110,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '所属阶段',
                prop: 'projectStage',
                show: true,
                minWidth: 90,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '核算日期',
                prop: 'calculateDate',
                show: true,
                minWidth: 110,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '费用',
                prop: 'calculateAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'sortable': 'custom',
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'cost'
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        // 固定表头
        elTableAttrs: {
            'header-cell-style': '{text-align:center}'
        },
        headerButtons: [
            {
                name: '人工费导出',
                type: 'primary',
                handleClick: scope.handleEmployeeFeeExport,
                handleShow: () =>
                    scope.$store.state.permission.btnDatas.includes(
                        'laborCostEmployee'
                    )
            }
        ],
        hooks: {
            queryParamsHook: scope.queryParamsHook,
            sortChangeHook: scope.sortChangeHook
        }
    };
};
export { getTableConfig };
