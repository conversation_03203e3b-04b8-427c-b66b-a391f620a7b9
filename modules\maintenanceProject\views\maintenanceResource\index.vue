<template>
    <div>
        <MaintenanceSelector
            @input="handleChange"
            :key="projectSelectorKey"
        ></MaintenanceSelector>
        <div class="box-main">
            <el-tabs v-model="activeName" @tab-click="tabChange">
                <el-tab-pane
                    label="资源负载"
                    name="projectResource"
                    :lazy="true"
                >
                    <resource-load
                        :productLine="productLine"
                        :subProductLine="subProductLine"
                        :projectManager="projectManager"
                        class="resourceLoad"
                        :key="resourceLoadKey"
                    ></resource-load>
                </el-tab-pane>
                <el-tab-pane
                    label="资源关联项目"
                    name="relatedProject"
                    :lazy="true"
                >
                    <RelatedProject :data="relatedProjectList"></RelatedProject>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import { Message } from 'element-ui';
import MaintenanceSelector from 'maintenanceProject/components/MaintenanceSelector';
import ResourceLoad from './components/resouceLoad';
import RelatedProject from './components/relatedProject';

export default {
    name: 'MaintenanceResource',
    components: {
        MaintenanceSelector,
        ResourceLoad,
        RelatedProject
    },
    data() {
        return {
            activeName: 'projectResource',
            // 资源关联项目
            relatedProjectList: [],
            // 项目ID
            projectId: '',
            // 产品线
            productLine: '',
            // 细分产品线
            subProductLine: '',
            // 项目经理
            projectManager: '',
            // 顶部级联组件key
            projectSelectorKey: 0,
            // 资源负载key
            resourceLoadKey: 0
        };
    },
    activated() {
        // 因为做了缓存，其他页面改变项目之后本页面的级联选择器不会同步，改变key令其同步
        this.projectSelectorKey += 1;
    },
    methods: {
        // 调用组件值
        handleChange(value) {
            this.reset();
            // 注意这里所有的值都要取，不能只取最后一个
            const [productLine, subProductLine = null, projectManager = null] =
                value;
            this.productLine = productLine;
            this.subProductLine = subProductLine;
            this.projectManager = projectManager;
            if (this.activeName === 'relatedProject') {
                this.getRelatedProject();
            }
        },
        /**
         * 切换页签的处理
         */
        tabChange() {
            if (!this.productLine) {
                Message.closeAll();
                this.$message.warning('请选择产品线或产品经理后查询');
                return;
            }
            if (this.activeName === 'relatedProject') {
                this.getRelatedProject();
            }
        },
        /**
         * 获取资源关联项目列表
         */
        async getRelatedProject() {
            const api =
                this.$service.maintenanceProject.resourceLoad.getRelatedProject;
            const params = {
                productLine: this.productLine,
                projectManager: this.projectManager,
                subProductLine: this.subProductLine
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.relatedProjectList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 重置
         */
        reset() {
            this.productLine = '';
            this.subProductLine = '';
            this.projectManager = '';
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 10px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
::v-deep .el-tabs__header {
    margin: 0px !important;
}
.resourceLoad {
    margin-top: 20px;
}
</style>
