/**
 * 进入首页时，获取所有人员名单
 * @param {number} isAll 0-当前人员名单，1-所有人员名单（包含离职人员）
 * @param {Obejct} scope this
 */
export const getAllEmployeeList = async (isAll, scope) => {
    const hasAllData =
        scope?.$store?.state?.project?.allEmployeeList?.length !== 0;
    const hasCurData =
        scope?.$store?.state?.project?.currentEmployeeList?.length !== 0;
    // 第一次查过之后从store里面拿值
    if (hasAllData && hasCurData) {
        return;
    }
    const params = {
        orgCode: '',
        isAll
    };
    const api = scope.$service.department.group.getDepartName;
    try {
        const res = await api(params);
        if (res.head.code !== '000000') {
            scope.$message.error(res.head.message);
            return;
        }
        if (isAll === 1) {
            scope.$store.dispatch(
                'project/changeAllEmployeeList',
                res.body || []
            );
        } else if (isAll === 0) {
            scope.$store.dispatch(
                'project/changeCurrentEmployeeList',
                res.body || []
            );
        }
    } catch (error) {
        console.error('Error:', error);
    }
};
/**
 * 获取产品线
 * @param {Obejct} scope this
 * @returns {Array} 产品线列表
 */
export const getProductLine = async (scope) => {
    const api = scope.$service.project.finance.getSelectOptions;
    const params = {
        paramName: '产品线',
        paramType: ''
    };
    try {
        const res = await api(params);
        if (res.head.code !== '000000') {
            scope.$message.error(res.head.message);
            return;
        }
        const list = res.body.map((i) => ({ label: i.paramName }));
        scope.$store.dispatch('project/changeProductLine', list || []);
    } catch (error) {
        console.error('Error:', error);
    }
};
