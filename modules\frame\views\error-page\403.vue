<template>
    <div class="error-container">
        <div class="error-context">
            <img src="../../assets/error-img/error-403.png" alt="">
            <div class="message">
                <div>{{ $t('frame.msg.errorPageMsg1') }}</div>
                <div class="tip">{{ $t('frame.msg.errorPageMsg2') }}</div>
                <el-button type="primary" class="message-button" @click="backHandler">{{ $t('frame.goBack') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '403',
    methods: {
        backHandler() {
            // 跳回首页
            this.$router.push('/');
        }
    }
};
</script>

<style lang="scss" scoped>
.error-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .error-context {
        img {
            width: 411px;
            height: 243px;
        }
        .message {
            text-align: center;
            margin-top: 40px;
            .tip {
                margin-top: 15px;
                margin-bottom: 30px;
            }
            .message-button {
                border-radius: 8px;
                height: 40px;
            }
        }
    }
}
</style>
