import CommonItems from 'snbcCommon/common/form-items.js';
import moment from 'moment';

const { select, input, dateRange } = CommonItems;
const flowName = {
    ...select,
    name: '流程名称',
    modelKey: 'flowName',
    elOptions: []
};
const costSubject = {
    ...select,
    name: '费用科目',
    modelKey: 'costSubject',
    elOptions: []
};
const flowId = {
    ...input,
    name: '流程ID',
    modelKey: 'flowId'
};
const calculateDateRange = {
    ...dateRange,
    name: '申请日期',
    modelKey: 'calculateDateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd',
        'picker-options': {
            disabledDate(time) {
                // 获取当前年的第一天
                const startOfYear = moment().startOf('year');
                // 转换为Date对象
                const startDate = startOfYear.toDate();
                // 获取下一年的第一天
                const endOfYear = moment().add(1, 'year').startOf('year');
                // 转换为Date对象
                const endDate = endOfYear.toDate();
                // 注意这里必须为>=下一年的第一天，否则当前最后一天会无法选择
                return time.getTime() < startDate || time.getTime() >= endDate;
            }
        }
    }
};

// 查询参数初始化
const queryParams = {
    flowName: '',
    costSubject: '',
    flowId: '',
    calculateDateRange: [],
    // 0:默认的排序（申请日期的倒序）1：费用升序 2：费用降序
    sortStr: 0
};

// 查询区域配置项
const queryConfigItems = [flowName, costSubject, flowId, calculateDateRange];

// eslint-disable-next-line max-lines-per-function
const getTableConfig = (scope) => {
    return {
        // 列表查询参数
        queryParams,
        // 查询项配置
        queryConfig: {
            items: queryConfigItems
        },
        // 查询api配置
        queryApi:
            scope.$service.maintenanceProject.finance
                .getMaintainProjectPayDetailList,
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: [
            {
                label: '数据来源',
                prop: 'flowSource',
                show: true,
                minWidth: 60,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '流程名称',
                prop: 'flowName',
                show: true,
                minWidth: 185,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                },
                slot: 'flowName'
            },
            {
                label: '流程申请人',
                prop: 'applyPersonName',
                show: true,
                minWidth: 90,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目经理',
                prop: 'calculatePersonName',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '费用科目',
                prop: 'costSubject',
                show: true,
                minWidth: 100,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '申请日期',
                prop: 'applyDate',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '所属项目',
                prop: 'projectName',
                show: true,
                minWidth: 150,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '核算日期',
                prop: 'calculateDate',
                show: true,
                minWidth: 100,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '费用',
                prop: 'calculateAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'sortable': 'custom',
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'cost'
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        // 固定表头
        elTableAttrs: {
            'header-cell-style': '{text-align:center}'
        },
        hooks: {
            queryParamsHook: scope.queryParamsHook,
            sortChangeHook: scope.sortChangeHook
        }
    };
};
export { getTableConfig };
