<template>
  <div>
    <!-- 用户编辑 -->
    <el-dialog
      :title="title"
      :visible.sync="isShow"
    >
      <el-form
        ref="dataForm"
        label-position="right"
        label-width="120px"
        :model="userEditDataInfo"
        :rules="userRules"
        class="custom-form"
      >
        <el-form-item :label="$t('systemManagement.organization.dialog.name')" prop="userName">
          <el-input
            v-model="userEditDataInfo.userName"
            :placeholder="$t('systemManagement.organization.dialog.placeholder.msg1')"
            autocomplete="text"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.number')" prop="jobNumber">
          <el-input
            v-model="userEditDataInfo.jobNumber"
            :placeholder="$t('systemManagement.organization.dialog.placeholder.msg2')"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.phone')" prop="phone">
          <el-input
            v-model="userEditDataInfo.phone"
            :disabled="!isSuperadmin"
            :placeholder="$t('systemManagement.organization.dialog.placeholder.msg3')"
            maxlength="16"
          />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.mail')" prop="email">
          <el-input
            v-model="userEditDataInfo.email"
            :placeholder="$t('systemManagement.organization.dialog.placeholder.msg4')"
          />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.depart')" prop="orgIds">
          <div
            class="org-container"
            contenteditable="false"
            :placeholder="$t('systemManagement.organization.dialog.placeholder.msg5')"
            @click="showOrgTransfer()"
          >
            <el-tag
              v-for="tag in dialogOrgInfo.orgTagList"
              :key="tag.orgId"
              :closable="tag.canRemove === 'false' ? false:true"
              type="info"
              @close="removeOrgTag(tag)"
            >
              {{ tag.orgName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.role')" prop="role">
          <div
            class="org-container"
            contenteditable="false"
            :placeholder="$t('systemManagement.organization.dialog.placeholder.msg6')"
            @click="showRoleTransfer"
          >
            <el-tag
              v-for="tag in dialogRoleInfo.roleTagList"
              :key="tag.roleId"
              closable
              type="info"
              @close="removeRoleTag(tag)"
            >
              {{ tag.roleName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.isEnable')">
          <el-switch
            v-model="userEditDataInfo.hasLock"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item :label="$t('systemManagement.organization.dialog.LDAPUser')">
          <el-switch
            v-model="userEditDataInfo.isLdap"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="isShow = false">{{
          $t("table.cancel")
        }}</el-button>
        <el-button type="primary" @click="save">{{
          $t("table.confirm")
        }}</el-button>
      </span>
    </el-dialog>

    <user-org-change
      v-if="dialogOrgInfo.isShow"
      :org-source-data="orgSourceDataList"
      :show.sync="dialogOrgInfo.isShow"
      :selection.sync="dialogOrgInfo.orgTagList"
      @confirm-selection="dialogOrgInfo.isShow=false"
      :reserveOrgs="reserveOrgs"
    />

    <user-role-change
      :show.sync="dialogRoleInfo.isShow"
      :selection.sync="dialogRoleInfo.roleTagList"
      :reserveRoles="reserveRoles"
    />
  </div>
</template>

<script>
import UserOrgChange from './UserOrgChange.vue';
import UserRoleChange from './UserRoleChange.vue';
import listEditInfo from '../../../mixins/list-edit-info.js';

export default {
    components: { UserOrgChange, UserRoleChange },
    mixins: [listEditInfo],
    props: {
        userid: {
            type: String,
            default: ''
        },
        // 组织机构源数据
        orgSourceData: {
            type: Array,
            default() {
                return [];
            }
        },
        // 添加用户时自动带出所选部门
        orgTagList: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            userEditDataInfo: {
                userName: '',
                userId: '',
                jobNumber: '',
                phone: '',
                email: '',
                hasLock: 1,
                orgName: '',
                // 是否是ladp用户，默认不是
                isLdap: 0
            },
            // 部门dialog窗口
            dialogOrgInfo: {
                // 用户 - 调整组织机构
                orgTagList: [],
                isShow: false
            },
            // 角色dialog窗口
            dialogRoleInfo: {
                // 用户 - 调整角色
                roleTagList: [],
                isShow: false
            },
            // 是否是超管
            isSuperadmin: true,
            // 保留不可删除的角色
            reserveRoles:[],
            // 保留不可删除的部门
            reserveOrgs:[],
        };
    },
    computed: {
        title() {
            return (this.isEdit ? this.$t('systemManagement.organization.dialog.edit') : this.$t('systemManagement.organization.dialog.add')) + this.$t('systemManagement.organization.dialog.user');
        },
        orgSourceDataList() {
            return this.orgSourceData;
        },
        // 表单验证规则
        userRules() {
            const _this = this;
            return {
                userName: [
                    {
                        required: true,
                        message: this.$t('systemManagement.organization.dialog.rules.msg5'),
                        trigger: ['blur', 'change']
                    }
                ],
                jobNumber: [
                    {
                        required: true,
                        message: this.$t('systemManagement.organization.dialog.rules.msg6'),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[0-9a-zA-Z-]{1,20}$/,
                        message: this.$t('systemManagement.msg.jobNumberMsg'),
                        trigger: ['blur', 'change']
                    }
                ],
                phone: [
                    {
                        required: true,
                        message: this.$t('systemManagement.organization.dialog.rules.msg7'),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^\d{5,16}$/,
                        message: this.$t('systemManagement.organization.dialog.rules.msg4'),
                        trigger: ['blur', 'change']
                    }
                ],
                orgIds: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (this.dialogOrgInfo.orgTagList.length > 0) {
                                callback();
                            } else {
                                return callback(new Error(_this.$t('systemManagement.organization.dialog.orgError')));
                            }
                        }
                    }
                ],
                email: [
                    { type: 'email', message: this.$t('systemManagement.organization.dialog.mailMsg'), trigger: ['blur', 'change'] }
                ]
            };
        }
    },
    created() {
        if (this.isEdit) {
            this.getUserById();
            this.getUserRole();
        } else {
            this.isSuperadmin = true;
        }

        this.dialogOrgInfo.orgTagList = [];
        this.dialogRoleInfo.roleTagList = [];
    },
    methods: {
        getUserById() {
            this.$service.systemManagement
                .getUserById({ userId: this.userid }).then(res => {
                    if (res.head.code === '000000') {
                        Object.assign(this.userEditDataInfo, res.body);
                        this.userEditDataInfo.oldParam = JSON.parse(JSON.stringify(this.userEditDataInfo));
                        this.userEditDataInfo.orgNames = this.userEditDataInfo.orgInfo.map((item) => {
                            return item.orgName;
                        }).join(',');
                        this.dialogOrgInfo.orgTagList = res.body.orgInfo || [];
                        this.dialogRoleInfo.roleTagList = res.body.roleInfo || [];
                        if(res.body.roleInfo.length >0 ){
                            this.reserveRoles = res.body.roleInfo.filter((item) => {
                                return item.canRemove === 'false';
                            });
                        }
                        if(res.body.orgInfo.length >0 ){
                            this.reserveOrgs = res.body.orgInfo.filter((item) => {
                                return item.canRemove === 'false';
                            });
                        }
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 根据接口判断当前用户是否是超管
        getUserRole() {
            this.$service.systemManagement
                .getUserRole().then(res => {
                    const {code} = res.head;
                    if (code === '000000') {
                        this.isSuperadmin = res.body;
                    } else {
                        const msg = `frame.bgReturnError[${code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 保存用户
        postSave() {
            const params = {
                userName: this.userEditDataInfo.userName,
                jobNumber: this.userEditDataInfo.jobNumber,
                phone: this.userEditDataInfo.phone,
                email: this.userEditDataInfo.email,
                belongOrgIds: this.dialogOrgInfo.orgTagList.map(item => item.orgId).join(','),
                belongRoleIds: this.dialogRoleInfo.roleTagList.map(item => item.roleId).join(','),
                hasLock: this.userEditDataInfo.hasLock,
                isLdap: this.userEditDataInfo.isLdap
            };
            let service;
            if (!this.isEdit) {
                service = this.$service.systemManagement.putAddUserList(params);
            } else {
                params.oldParam = this.userEditDataInfo.oldParam;
                params.orgNames = this.userEditDataInfo.orgNames;
                params.userId = this.userEditDataInfo.userId;
                service = this.$service.systemManagement.putUpdateUser(params);
            }
            service.then((response) => {
                this.doResponse(response);
            });
        },
        // 组织机构选择弹窗
        showOrgTransfer(flag) {
            this.dialogOrgInfo.isShow = true;
        },
        // 角色选择弹窗
        showRoleTransfer(flag) {
            this.dialogRoleInfo.isShow = true;
        },
        removeOrgTag(tag) {
            const idx = this.dialogOrgInfo.orgTagList.indexOf(tag);
            this.dialogOrgInfo.orgTagList.splice(idx, 1);
        },
        removeRoleTag(tag) {
            const idx = this.dialogRoleInfo.roleTagList.indexOf(tag);
            this.dialogRoleInfo.roleTagList.splice(idx, 1);
        }
    }
};
</script>

<style lang="scss" scoped>
.el-dialog {
    .is-error {
        .org-container {
            border: 1px solid #ff4949;
        }
    }
    .disabled.org-container {
        background: #f5f7fa;
    }
    .org-container {
        border: 1px solid #dfe4ed;
        border-radius: 4px;
        padding-left: 15px;
        min-height: 40px;
        max-height:80px;
        overflow-y:auto;

        .el-tag {
            margin: 5px;
        }
        &:empty {
            &:before {
                content: attr(placeholder);
                color: #bbb;
            }
        }
        &:hover,
        &:focus {
            border:1px solid #3370FF;
            &:empty {
                &:before {
                    content: attr(placeholder);
                    color: #bbb;
                }
            }
        }
    }
    .import-title {
        color: #f04844;
    }
    .import-content {
        margin-top: 40px;
    }
}
</style>
