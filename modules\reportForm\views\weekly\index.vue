<template>
    <div class="product-line-container">
        <!-- 顶部页头 -->
        <Header :level="1" @select-change="handleChange"></Header>
        <div class="content-container">
            <div class="product-line-title">产品线周报</div>

            <!-- 产品卡片网格组件 -->
            <ProductCardGrid :productLineList="productLineList" />

            <!-- 支持事项部分组件 -->
            <SupportSection
                @radio-change="getSupplymentList"
                :supplymentList="supplymentList"
            />
        </div>
    </div>
</template>
<script>
import ProductCardGrid from './components/ProductCardGrid';
import SupportSection from './components/SupportSection';
import Header from 'reportForm/views/weekly/components/Header';

export default {
    name: 'ReportFormWeekly',
    components: {
        ProductCardGrid,
        SupportSection,
        Header
    },
    data() {
        return {
            // 产品线列表
            productLineList: [],
            // 维度（部门/产品线）
            supportDimension: '部门',
            // 支持事项列表
            supplymentList: []
        };
    },
    computed: {
        // 当前选中的页头选项值
        weeklyOption() {
            return this.$store.state.reportForm.weeklyOption;
        }
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
    },
    methods: {
        /**
         * @description: 页头select改变
         */
        async handleChange() {
            this.getProductLineList();
            this.getSupplymentList();
        },
        /**
         * @description: 获取产品线列表
         */
        async getProductLineList() {
            if (this.weeklyOption.length === 0) return;

            const api = this.$service.reportForm.weekly.getProductLineList;
            const params = {
                weekNumber: this.weeklyOption[0]?.weekNumber,
                yearVal: this.weeklyOption[0]?.year
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.productLineList = res.body.map((i) => ({
                    actions: i.weatherMainReport
                        ? ['开发项目', '维护工作']
                        : ['开发项目'],
                    ...i
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取部门/产品线卡片列表
         * @param {String} value 部门/产品线
         */
        async getSupplymentList(value) {
            if (this.weeklyOption.length === 0) return;
            if (value) {
                this.supportDimension = value;
            }
            const api = this.$service.reportForm.weekly.getSupplymentList;
            const params = {
                weekNumber: this.weeklyOption[0]?.weekNumber,
                yearVal: this.weeklyOption[0]?.year,
                supportDimensionType: this.supportDimension,
                startDate: this.weeklyOption[0]?.startDate
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.supplymentList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>
<style scoped lang="scss">
.product-line-container {
    height: 100vh;
    overflow-y: auto;
}
.content-container {
    padding: 10px 20px 20px 20px;
}

.product-line-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}
</style>
