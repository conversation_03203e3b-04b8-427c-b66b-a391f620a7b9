import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        dataExport: {
            // 预计工时
            estimateWorkHour(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/zentao/hours_assignment/importHoursAssignmentList`,
                    method: 'post',
                    responseType: 'blob',
                    timeout: 300000,
                    data
                });
            },
            // 工期工时预警
            riskWarning(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/work_day_warning/import_all_work_day_warning`,
                    method: 'post',
                    responseType: 'blob',
                    timeout: 300000,
                    data
                });
            }
        }
    };

    return service;
};
