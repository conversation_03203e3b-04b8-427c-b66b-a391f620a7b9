<template>
    <div class="overflow">
        <div class="box-table">
            <div class="flex">
                <el-upload
                    class="excel-uploader"
                    :action="url"
                    :show-file-list="false"
                    :on-success="handleUploadSuccess"
                    :on-error="handleUploadError"
                    :before-upload="validateExcelFile"
                    :auto-upload="true"
                    :data="{ projectId: projectId }"
                    name="fileName"
                    :headers="headers"
                >
                    <el-button
                        size="small"
                        type="primary"
                        :disabled="!projectId"
                        >上传变更原因</el-button
                    >
                </el-upload>
            </div>
            <el-table
                :data="basicLineData"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#3370ff' }"
                class="snbc-table"
            >
                <el-table-column
                    prop="taskName"
                    label="里程碑"
                    align="left"
                    min-width="180"
                    header-align="center"
                    :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column
                    prop="projectSource"
                    label="来源"
                    align="center"
                    min-width="60"
                ></el-table-column>
                <el-table-column
                    prop="currentDevExecuteBaseline"
                    label="当前研发执行基线"
                    align="center"
                    min-width="120"
                ></el-table-column>
                <el-table-column
                    prop="endDate"
                    label="实际完成时间"
                    align="center"
                    min-width="170"
                >
                    <template slot-scope="scope">
                        <el-date-picker
                            style="width: 95%; padding: 2px"
                            v-model="scope.row.endDate"
                            type="date"
                            placeholder="选择日期时间"
                            value-format="yyyy-MM-dd"
                            @change="handleTimeChange(scope.row)"
                            :disabled="!hasEndDateEditPermisson"
                        >
                        </el-date-picker>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="executeDeviateDays"
                    label="与研发执行基线偏差"
                    align="center"
                    min-width="120"
                ></el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import { encryptByMd5 } from 'wtf-core-vue/src/utils/crypto';
import { randomStr, randomTime } from 'wtf-core-vue/src/methods/signature';

const random = randomStr(13);
const ranTime = randomTime();
const final =
    random +
    ranTime +
    encryptByMd5(
        `${random}&${ranTime}axeonmc&/console/v1/boss/productservice/fileUpload&POST`
    );
export default {
    props: {
        basicLineData: {
            type: Array,
            required: true
        },
        projectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 变市场基线弹窗
            markketLine: {
                isShow: false,
                title: ''
            },
            SClineShow: false,
            YFlineShow: false,
            RDLine: {
                isShow: false,
                title: ''
            },
            userEditDataInfo: {
                teamName: '',
                teamLeaderId: '',
                members: []
            },
            textarea: '',
            headers: {
                Accept: 'application/json, text/plain, */*',
                Authorization: `Bearer ${this.$tools.getToken()}`,
                signature: final
            }
        };
    },
    computed: {
        // 是否有编辑实际完成时间的权限
        hasEndDateEditPermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'edit_actual_completion_time'
            );
        },
        // 上传变更原因的地址
        url() {
            return this.$service.project.schedule.uploadMilestoneChangeReason();
        }
    },
    methods: {
        // 时间选择
        handleTimeChange(value) {
            this.$emit('handle-time-change', value);
        },
        // 变更市场基线弹窗
        markketLineChange() {
            this.markketLine.title = '市场需求变更';
            this.markketLine.isShow = true;
        },
        SClineChange() {
            // 请求接口
            this.SClineShow = true;
        },
        // 变更研发基线弹窗
        rdLineChange() {
            this.RDLine.title = '项目计划变更申请';
            this.RDLine.isShow = true;
        },
        // 校验上传文件是否为excel且大小不超过2MB
        validateExcelFile(file) {
            const isExcel =
                file.type ===
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';

            if (!isExcel) {
                this.$message.error('只能上传Excel文件!');
                return false;
            }

            const maxSize = 2 * 1024 * 1024;
            if (file.size > maxSize) {
                this.$message.error('文件大小不能超过2MB!');
                return false;
            }

            return true;
        },
        handleUploadSuccess(response) {
            if (response.head.code !== '000000') {
                this.$message.error(response.body);
                return;
            }
            this.$message.success('文件上传成功!');
        },
        handleUploadError(err) {
            this.$message.error('文件上传失败,请重试!');
            console.error('上传错误:', err);
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.overflow {
    height: calc(100vh - 150px);
    overflow: auto;
}
.excel-uploader {
    margin-left: auto;
    margin-right: 10px;
    margin-bottom: 10px;
}
.box-table {
    display: flex;
    flex-direction: column;
    .table-search {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: flex-end;
    }
    .el-button {
        height: 40px;
        font-weight: bolder;
    }
}
.dialog-top,
.dialog-info,
.dialog-btn {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    letter-spacing: 2px;
}
.dialog-top {
    justify-content: center;
}
.dialog-content {
    width: 100%;
    height: 80%;
    display: flex;
}
.dialog-left,
.dialog-right {
    width: 50%;
    height: 80%;
    letter-spacing: 3px;
    padding: 20px 0px 0px 60px;
    div {
        height: 50px;
    }
}
.dialog-info {
    justify-content: space-around;
}
.dialog-btn {
    justify-content: center;
    margin-top: 10px;
}
.dialog-textarea {
    width: 100%;
    height: 50px;
    display: flex;
}
.dialog-textarea div:first-child {
    width: 20%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.dialog-textarea div:nth-child(2) {
    width: 80%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep.el-table--border th {
    border-right: 0px solid #dfe6ec !important;
}
/**改变边框颜色*/
::v-deep .el-table {
    border: 1px solid #8c8c8c !important;
}

::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}
.ellipsis {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #3370ff;
}
.snbc-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
}
</style>
