<template>
    <div :style="pageStyle">
        <div
            ref="pageRef"
            class="domest-page"
            :style="{ transform: `scale(${scale})` }"
        >
            <tabulating-card class="tabulat-page"></tabulating-card>
            <OverseasFinanceScene
                class="finance-page"
                :scale="scale"
                :parentWidth="parentWidth"
            />
            <scene-table-vue
                :productsData="productsData"
                :usageStatuses="usageStatuses"
            ></scene-table-vue>
        </div>
    </div>
</template>

<script>
import scaleMixin from 'scene/mixins/scale-mixin.js';
import OverseasFinanceScene from './OverseasFinanceScene.vue';
import SceneTableVue from '../../components/SceneTable.vue';
import TabulatingCard from '../../components/TopTabulatingCard.vue';

export default {
    components: {
        TabulatingCard,
        OverseasFinanceScene,
        SceneTableVue
    },
    mixins: [scaleMixin],
    data() {
        return {
            // 父节点id
            parentId: 'scene-view',
            productsData: [],
            usageStatuses: [],
            // 各个场景及其细分场景列表
            sceneList: []
        };
    },
    computed: {
        pageStyle() {
            const el = document.getElementById('scene-view');
            return {
                width: '100%',
                height: el ? `${this.scale * el.offsetHeight}px` : '100%'
            };
        },
        // 新增项目时的标志
        count() {
            return this.$store.state.scene.changeProjectCountSuccess;
        }
    },
    watch: {
        count(newVal) {
            if (newVal) {
                this.getAndUsage();
            }
        }
    },
    mounted() {
        this.getAndUsage();
    },
    methods: {
        getAndUsage() {
            const params = {
                sceneType: '海外网点',
                productSet: '核心模块'
            };
            this.$service.scene.getKeyModuleAndUsage(params).then((res) => {
                if (res.head.code === '000000') {
                    if (res.body) {
                        this.productsData = res.body.keyModulesVo || [];
                        this.usageStatuses =
                            res.body.productTypeUsageVoList || [];
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.domest-page {
    background-color: #f2f5fc;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
    width: 1250px;
    transform-origin: 0 0;
}
.tabulat-page {
    position: absolute;
    right: 30px;
    top: 50px;
    z-index: 1;
}
.scencetable-page {
    z-index: 2;
    padding: 30px 3px;
}
</style>
