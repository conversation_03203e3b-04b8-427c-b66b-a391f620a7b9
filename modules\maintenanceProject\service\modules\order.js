import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        order: {
            // 订单列表查询
            getList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/order/getOrderList`,
                    method: 'post',
                    data
                });
            },
            // 订单详情查询
            getDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/order/getOrderInfo`,
                    method: 'get',
                    params: data
                });
            },
            // 编辑订单
            updateOrder(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/order/updateOrderInfo `,
                    method: 'post',
                    data
                });
            },
            // 创建禅道任务并关联订单
            createZentaoTask(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/order/addOrderZtTask`,
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
