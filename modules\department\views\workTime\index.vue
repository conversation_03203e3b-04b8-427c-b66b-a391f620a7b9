<template>
    <div>
        <department-selector
            :infoDisabled="false"
            @input="handleDepartmentChange"
            placeholder="请选择部门或小组"
        ></department-selector>
        <div class="box-main">
            <el-tabs v-model="activeName" @tab-click="handleClick(activeName)">
                <el-tab-pane
                    label="预计工时分配"
                    name="expectedWorkHour"
                    v-if="hasExpectedWorkHourPermission"
                >
                    <expected-work-line
                        :taskType="taskType"
                        :expectedData="expectedWorkTable"
                        @handle-time-bular="timeBular"
                        @task-assign-success="handleCurrentChange"
                    >
                    </expected-work-line>
                    <pagination
                        class="paging"
                        :total="expectedWorkQuery.total"
                        :page.sync="expectedWorkQuery.currentPage"
                        :limit.sync="expectedWorkQuery.pageSize"
                        @pagination="handleCurrentChange"
                    />
                </el-tab-pane>
                <el-tab-pane
                    label="异常预计工时"
                    name="deviationWorkHour"
                    v-if="hasDeviationWorkHoursPermission"
                >
                    <expected-work-line
                        :taskType="taskType"
                        :expectedData="expectedWorkTable"
                        @handle-time-bular="timeBular"
                        @task-assign-success="errorCurrentChange"
                    >
                    </expected-work-line>
                    <pagination
                        class="paging"
                        :total="errordWorkQuery.total"
                        :page.sync="errordWorkQuery.currentPage"
                        :limit.sync="errordWorkQuery.pageSize"
                        @pagination="errorCurrentChange"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import departmentSelector from 'department/components/departmentSelector';
import expectedWorkLine from './components/expectedWork.vue';
import Pagination from 'wtf-core-vue/src/components/Pagination';

export default {
    components: {
        expectedWorkLine,
        departmentSelector,
        Pagination
    },
    data() {
        return {
            orgCode: '',
            teamId: '',
            isExceptionQuery: 'false',
            rate: 0,
            expectedWorkTable: [],
            expectedWorkQuery: {
                currentPage: 1,
                pageSize: 50,
                total: 0
            },
            errordWorkQuery: {
                currentPage: 1,
                pageSize: 50,
                total: 0
            },
            activeName: ''
        };
    },
    computed: {
        // 是否有预计工时分配页签权限
        hasExpectedWorkHourPermission() {
            return this.$store.state.permission.btnDatas.includes(
                'ExpectedWorkHourTab'
            );
        },
        // 是否有异常预计工时页签权限
        hasDeviationWorkHoursPermission() {
            return this.$store.state.permission.btnDatas.includes(
                'DeviationWorkHoursTab'
            );
        },
        // 当前的任务类型
        taskType() {
            return this.activeName === 'expectedWorkHour' ? '预计' : '异常';
        }
    },
    created() {
        this.activeName = this.hasExpectedWorkHourPermission
            ? 'expectedWorkHour'
            : 'deviationWorkHour';
    },
    methods: {
        // 调用组件值
        handleDepartmentChange(value) {
            this.orgCode = value[value.length - 1] || '';
            this.teamId = value[2] || '';
            this.handleClick(this.activeName);
        },
        // tab点击
        handleClick(tab) {
            if (this.orgCode || this.teamId) {
                if (tab === 'expectedWorkHour') {
                    this.expectedWorkTable = [];
                    this.expectedWorkQuery.total = 0;
                    this.expectedWorkQuery.pageSize = 50;
                    this.expectedWorkQuery.currentPage = 1;
                    this.isExceptionQuery = 'false';
                    this.rate = 0;
                } else {
                    this.expectedWorkTable = [];
                    this.errordWorkQuery.total = 0;
                    this.expectedWorkQuery.pageSize = 50;
                    this.errordWorkQuery.currentPage = 1;
                    this.isExceptionQuery = 'true';
                    this.rate = 1.1;
                }
                this.getDepartTime(this.orgCode, this.teamId);
            } else {
                this.$message.error('请选择具体部门或小组后查询！');
            }
        },
        // 查询
        getDepartTime(orgCode, teamId) {
            let params;
            if (this.activeName === 'expectedWorkHour') {
                params = this.expectedWorkQuery;
            } else {
                params = this.errordWorkQuery;
            }
            params.orgCode = orgCode;
            params.teamId = teamId;
            params.isExceptionQuery = this.isExceptionQuery;
            params.rate = this.rate;
            this.$service.department.workTime
                .getWorkTime(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.expectedWorkTable = res.body.list || [];
                        this.expectedWorkQuery.total = res.body.total;
                        this.errordWorkQuery.total = res.body.total;
                    } else {
                        this.expectedWorkTable = [];
                        this.$message.error(res.head.message);
                    }
                });
        },
        handleCurrentChange() {
            this.getDepartTime(this.orgCode, this.teamId);
        },
        errorCurrentChange() {
            this.getDepartTime(this.orgCode, this.teamId);
        },
        // 输入预计工时
        timeBular(value) {
            const params = {
                planHours: value.planHours,
                taskId: value.taskId,
                taskType: this.taskType
            };
            this.$service.department.workTime
                .editWorkTime(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message.success(res.head.message);
                        this.getDepartTime(this.orgCode, this.teamId);
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>
