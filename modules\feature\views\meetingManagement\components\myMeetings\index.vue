<!-- 我的会议 -->
<template>
    <div class="meetings-container">
        <div class="top-nav">
            <div class="nav-items">
                <div
                    v-for="tab in tabs"
                    :key="tab.value"
                    class="nav-item"
                    :class="{ active: activeTab === tab.value }"
                    @click="handleTabClick(tab.value)"
                >
                    {{ tab.label }}
                </div>
            </div>
        </div>

        <MyMeetingsManagement
            ref="meetingsManagement"
            class="management-list"
            :tableList="meetingManagementList"
            @update="getTodoList"
        ></MyMeetingsManagement>
    </div>
</template>

<script>
import MyMeetingsManagement from './MyMeetingsManagement.vue';
import { getExternalStaffPeople } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MyMeetings',
    components: { MyMeetingsManagement },
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            meetingManagementList: [],
            meetingUpdateVisible: false,
            activeTab: '',
            tabs: [
                { label: '全部', value: '' },
                { label: '会议', value: '会议' }
            ]
        };
    },
    computed: {},
    watch: {},
    created() {
        this.getTodoList();
        getExternalStaffPeople(this);
    },
    methods: {
        /**
         * 获取待办任务列表
         */
        async getTodoList() {
            const api = this.$service.feature.toDoList.getList;
            const params = {
                taskModule: this.tabs.find(
                    (item) => item.value === this.activeTab
                ).value
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.meetingManagementList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
                this.$emit('success', this.meetingManagementList.length);
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 创建会议
         */
        createMeeting() {
            this.meetingUpdateVisible = true;
        },
        /**
         * 选项卡点击事件
         */
        handleTabClick(tabValue) {
            this.activeTab = tabValue;
            this.getTodoList();
        }
    }
};
</script>

<style lang="scss" scoped>
.meetings-container {
    height: calc(100vh - 100px);
    overflow: auto;
    position: relative;

    .active {
        color: #409eff;
        font-weight: 600;
    }

    .top-nav {
        display: flex;
        align-items: center;
        height: 40px;
        border-bottom: 1px solid #ebeef5;
    }

    .nav-items {
        display: flex;
    }

    .nav-item {
        padding: 0 15px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
    }

    .divider {
        .el-divider__text {
            font-weight: 800;
        }
    }
    .fixed-button {
        position: absolute;
        top: 10px;
        right: 5px;
        z-index: 1;
    }
    .meeting-type {
        margin-left: auto;
    }
    .query-list {
        margin-top: 10px;
    }
    .management-list {
        margin-top: 15px;
        margin-bottom: 25px;
    }
}
</style>
