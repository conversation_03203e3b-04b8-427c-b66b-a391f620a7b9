<template>
    <div class="container">
        <el-button
            v-permission="['role_manage_create_roles']"
            size="middle"
            type="primary"
            class="margin-left"
            icon="fa fa-plus-square-o"
            @click="showAddRole"
            >{{ $t('systemManagement.role.addRole') }}</el-button
        >
        <el-tree
            v-show="showList"
            ref="tree"
            class="filter-tree"
            :highlight-current="true"
            :expand-on-click-node="false"
            :check-on-click-node="true"
            :default-expanded-keys="[-1]"
            :current-node-key="currentSelectRole.roleId"
            :data="dataRoleList"
            :props="defaultProps"
            icon-class="tree-node-icon"
            node-key="roleId"
            @node-click="treeNodeClick"
        >
            <el-tooltip
                slot-scope="{ node }"
                :content="node.label"
                placement="right"
            >
                <div class="block">
                    <span class="block__text">{{ node.label }}</span>
                </div>
            </el-tooltip>
        </el-tree>
    </div>
</template>

<script>
import list from '../../../mixins/list';

export default {
    mixins: [list],
    props: {
        // 当前选中的角色
        selectRole: {
            type: Object,
            default() {
                return {
                    // 角色编码
                    roleId: '',
                    // 角色名称
                    roleName: '',
                    // 角色描述
                    roleDesc: ''
                };
            }
        },
        isEnable: {
            type: Boolean,
            dafault: false
        }
    },
    data() {
        return {
            // 树节点显示的内容字段
            defaultProps: {
                label: 'roleName'
            },
            // uese列表数据
            dataRoleList: [],
            showList: false,
            // 点击创建角色展示的权限树
            dataPermission: []
        };
    },
    computed: {
        // 当前角色的计算属性
        currentSelectRole() {
            return this.selectRole;
        }
    },
    watch: {
        isEnable: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.showList = true;
                }
            },
            immediate: true
        }
    },
    created() {
        this.getRoleList();
    },
    methods: {
        // 获取角色列表
        getRoleList() {
            this.$service.systemManagement.getRoleList().then((response) => {
                if (response.head.code === '000000') {
                    // 这里添加了一个，待分配角色。角色id为空。放置没有角色的用户
                    this.dataRoleList = [
                        {
                            roleId: '',
                            roleName: '待分配用户'
                        },
                        ...response.body
                    ];
                } else {
                    const { code } = response.head;
                    const msg = `systemManagement.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        // 调用接口删除数据
        postDelete(data) {
            this.$service.systemManagement
                .postDeleteRole(data)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.role.message.deleteSuccess'
                            ),
                            type: 'success'
                        });
                        this.getRoleList();
                        this.treeNodeClick({});
                    } else if (response.head.code === '991402') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.role.message.pleaseUntie'
                            ),
                            type: 'success'
                        });
                    } else {
                        this.$message({
                            message: response.head.message,
                            type: 'error'
                        });
                    }
                });
        },
        // 点击节点，显示列表
        treeNodeClick(data) {
            this.$emit('role-node-click', data);
        },
        // 待分配人员按钮
        showEmptyClick() {
            this.$refs.tree.setCurrentKey(null);
            this.$emit('role-node-click', {
                roleId: ''
            });
        },
        // 显示新增弹框
        showAddRole() {
            this.$emit('role-add-click');
        },
        // 显示删除页面
        showDeleteRole(data) {
            const param = { roleId: data.roleId, roleName: data.roleName };
            this.$confirm(
                this.$t('systemManagement.role.message.deleteRoleTip', [
                    data.roleName
                ]),
                this.$t('systemManagement.role.message.deleteTipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.postDelete(param);
            });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
</style>
