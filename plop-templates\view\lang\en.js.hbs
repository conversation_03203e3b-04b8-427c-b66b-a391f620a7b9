,
        {{viewName}}: {
            title: 'new page',
            columnName: {
{{#each fieldList}}
                {{this}}: 'field{{@index}}'
    {{~#unless @last~}}
                ,
    {{~/unless~}}
{{/each}}
            },
            placeholder: {
{{#each fieldList}}
                {{this}}: 'input field {{@index}}'
    {{~#unless @last~}}
                ,
    {{/unless~}}
{{/each}}
            },
            message: {
{{#each fieldList}}
                {{this}}: 'field{{@index}} not empty',
{{/each}}
                queryListFailure: 'query failure',
                deleteTipTitle: 'delete',
                deleteTip: 'confirm delete !',
                deleteSuccess: 'delete success',
                deleteFailure: 'delete failure'
            }
        }
    }
};
