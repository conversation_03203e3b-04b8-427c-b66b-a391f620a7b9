<template>
    <div class="casue-box">
        <div v-for="(item, index) in listData" :key="index">
            <div class="box-top">
                <div></div>
                <div>{{ item.taskName }}</div>
                <el-button
                    type="primary"
                    size="mini"
                    icon="el-icon-help"
                    v-if="shouldShowNewButton(item.detailDelayReasonList)"
                    @click="hanleReason(item, 'add')"
                    >新建</el-button
                >
                <el-button
                    type="primary"
                    size="mini"
                    icon="el-icon-s-data"
                    v-if="shouldShowEditButton(item.detailDelayReasonList)"
                    @click="hanleReason(item, 'edit')"
                    >编辑</el-button
                >
            </div>
            <div
                v-if="shouldShowEditButton(item.detailDelayReasonList)"
                style="width: 100%"
            >
                <el-table
                    class="snbc-table"
                    :data="item.detailDelayReasonList"
                    :span-method="objectSpanMethod"
                    border
                    style="width: 100%"
                    :header-cell-style="{ background: '#3370ff' }"
                >
                    <el-table-column
                        prop="taskName"
                        label="里程碑"
                        align="left"
                        min-width="160"
                        header-align="center"
                        :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="currentDevEvaluationBaseline"
                        label="考核研发基线"
                        align="center"
                        width="110"
                    ></el-table-column>
                    <el-table-column
                        prop="endDate"
                        label="实际完成时间"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="executeDeviateDays"
                        label="与研发执行基线偏差"
                        align="center"
                        width="100"
                    ></el-table-column>
                    <el-table-column
                        prop="delayOneLevelReason"
                        label="一级原因"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="delayTwoLevelReason"
                        label="二级原因"
                        align="center"
                        min-width="100"
                        :show-overflow-tooltip="true"
                    ></el-table-column>
                    <el-table-column
                        prop="responsibilityPersonName"
                        label="责任人"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="delayDays"
                        label="延期天数"
                        align="center"
                        width="70"
                    ></el-table-column>
                    <el-table-column
                        prop="detailsReason"
                        label="详细原因"
                        align="center"
                        :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="associatedModule"
                        label="关联模块"
                        align="center"
                        :show-overflow-tooltip="true"
                    ></el-table-column>
                </el-table>
            </div>
            <div v-if="shouldShowNewButton(item.detailDelayReasonList)">
                <div class="no-table-data">暂无数据，点击新建</div>
            </div>
        </div>
        <div class="customer-dialog">
            <el-dialog :title="title" :visible.sync="dialogVisible">
                <el-form
                    ref="reasonform"
                    :model="form"
                    label-width="100px"
                    style="padding-right: 27px !important"
                >
                    <div
                        v-for="(item, index) in form.dynamicItem"
                        :key="index"
                        class="form-content"
                    >
                        <div class="form-top">
                            <el-form-item
                                label="一级原因"
                                prop="delayOneLevelReason"
                            >
                                <el-select
                                    v-model="item.delayOneLevelReason"
                                    placeholder="请选择一级原因"
                                    clearable
                                    :required="item.required"
                                    @change="clearSubReason(index)"
                                >
                                    <el-option
                                        v-for="item in reasons"
                                        :key="item.label"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="二级原因"
                                prop="delayTwoLevelReason"
                            >
                                <el-select
                                    v-model="item.delayTwoLevelReason"
                                    placeholder="请选择二级原因"
                                    clearable
                                    :required="item.required"
                                >
                                    <el-option
                                        v-for="item in subReasons[index]"
                                        :key="item.label"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="关联模块"
                                prop="associatedModule"
                            >
                                <el-select
                                    :required="item.required"
                                    v-model="item.associatedModule"
                                    multiple
                                    collapse-tags
                                    placeholder="请选择关联模块"
                                    clearable
                                >
                                    <el-option
                                        v-for="item in keyMOdules"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <div class="form-row">
                            <el-form-item label="责任人">
                                <people-selector
                                    v-model="item.responsibilityPerson"
                                    :isMultipled="false"
                                    placeholder="请输入负责人"
                                ></people-selector>
                            </el-form-item>
                            <el-form-item
                                label="延期天数"
                                prop="age"
                                style="margin-left: 32px"
                            >
                                <el-input
                                    v-model.number="item.delayDays"
                                    placeholder="请输入延期天数"
                                    clearable
                                    :required="item.required"
                                >
                                </el-input>
                            </el-form-item>
                        </div>
                        <div class="form-bottom">
                            <el-form-item
                                label="详细原因"
                                style="width: 67%"
                                prop="detailsReason"
                            >
                                <el-input
                                    class="form-style"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入详细原因"
                                    maxlength="500"
                                    v-model="item.detailsReason"
                                >
                                </el-input>
                            </el-form-item>
                            <el-form-item class="form-btn">
                                <el-button
                                    v-if="index + 1 == form.dynamicItem.length"
                                    type="primary"
                                    size="mini"
                                    @click="addItem(form.dynamicItem.length)"
                                    >+</el-button
                                >
                                <el-button
                                    v-if="index !== 0"
                                    type="danger"
                                    size="mini"
                                    @click="deleteItem(item, index)"
                                    >-</el-button
                                >
                            </el-form-item>
                        </div>
                    </div>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button size="small" @click="resonCancel"
                        >取 消</el-button
                    >
                    <el-button
                        size="small"
                        type="primary"
                        @click="handleSave(form.dynamicItem)"
                        >保 存</el-button
                    >
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';

const { LEVEL_RRASON, KEY_MODULES } = CONSTANTS;
export default {
    components: { PeopleSelector },
    props: {
        listData: {
            type: Array,
            required: true
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            form: {
                dynamicItem: [
                    {
                        delayOneLevelReason: '',
                        delayTwoLevelReason: '',
                        detailsReason: '',
                        associatedModule: '',
                        responsibilityPerson: '',
                        delayDays: ''
                    }
                ]
            },
            dialogVisible: false,
            title: '',
            type: '',
            projectDetailId: '',
            reasons: [],
            keyMOdules: []
        };
    },
    computed: {
        // 动态获取一级原因下面对应的二级原因
        subReasons() {
            return this.form.dynamicItem.map((item) => {
                let selectedReason;
                if (item.delayOneLevelReason) {
                    selectedReason = this.reasons.find(
                        (reason) => reason.value === item.delayOneLevelReason
                    );
                }
                return selectedReason?.subReasons || [];
            });
        }
    },
    methods: {
        // 判断新建还是编辑
        shouldShowNewButton(detailDelayReasonList) {
            return (
                detailDelayReasonList.length === 0 &&
                this.$store.state.permission.btnDatas.includes('reasonAdd')
            );
        },
        shouldShowEditButton(detailDelayReasonList) {
            return (
                detailDelayReasonList.length > 0 &&
                this.$store.state.permission.btnDatas.includes('reasonEdit')
            );
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex < 4) {
                // 如果是前四列，则进行合并
                if (rowIndex === 0) {
                    for (let i = 0; i < this.listData.length; i++) {
                        const rowData = this.listData[i];
                        // eslint-disable-next-line max-depth
                        if (
                            rowData.detailDelayReasonList.length !== 0 &&
                            row.taskName === rowData.taskName
                        ) {
                            const rowspan =
                                rowData.detailDelayReasonList.length;
                            return { rowspan, colspan: 1 };
                        }
                    }
                }
            } else {
                // 如果是后四列，则不进行合并，返回默认值
                return { rowspan: 1, colspan: 1 };
            }
            // 如果以上条件都不满足，则返回默认值
            return { rowspan: 0, colspan: 0 };
        },

        // 选择完一级原因后清空
        clearSubReason(index) {
            this.form.dynamicItem[index].delayTwoLevelReason = '';
        },
        // 新增 编辑功能
        hanleReason(target, type) {
            this.dialogVisible = true;
            this.title = target.taskName;
            this.projectDetailId = target.id;
            this.type = type;
            this.reasons = LEVEL_RRASON;
            this.keyMOdules = KEY_MODULES;
            this.form.dynamicItem =
                type === 'add'
                    ? [
                          {
                              delayOneLevelReason: '',
                              delayTwoLevelReason: '',
                              associatedModule: '',
                              detailsReason: '',
                              responsibilityPerson: '',
                              delayDays: ''
                          }
                      ]
                    : target.detailDelayReasonList.map((item) => ({
                          delayOneLevelReason: item.delayOneLevelReason || '',
                          delayTwoLevelReason: item.delayTwoLevelReason || '',
                          associatedModule: item.associatedModule
                              ? item.associatedModule
                                    .split(',')
                                    .map((module) => module.trim())
                              : [],
                          detailsReason: item.detailsReason || '',
                          responsibilityPerson: item.responsibilityPerson || '',
                          delayDays:
                              item.delayDays === null ? '' : item.delayDays
                      }));
        },
        // 确认
        handleSave(formName) {
            const isValid = this.form.dynamicItem.every(
                (item) =>
                    item.delayOneLevelReason &&
                    item.delayTwoLevelReason &&
                    item.associatedModule &&
                    item.associatedModule.length > 0 &&
                    item.responsibilityPerson &&
                    item.delayDays !== ''
            );
            if (!isValid) {
                this.$message.error('请填写完整信息');
                return;
            }
            const isNumber = this.form.dynamicItem.every((item) => {
                return (
                    typeof item.delayDays === 'number' &&
                    !Number.isNaN(item.delayDays)
                );
            });
            if (!isNumber) {
                this.$message.error('延期天数必须为整数值');
                return;
            }
            const isPositive = this.form.dynamicItem.every((item) => {
                return item.delayDays > 0 && item.delayDays < 10000;
            });
            if (!isPositive) {
                this.$message.error('延期天数必须为小于5位的正整数');
                return;
            }
            const params = {
                detailDelayReasonList: formName.map((item) => ({
                    ...item,
                    associatedModule: item.associatedModule.join(', ')
                })),
                projectDetailId: this.projectDetailId
            };
            const successMessage =
                this.type === 'add' ? '新增成功' : '编辑成功';
            this.$service.project.schedule
                .editReasonAnalysis(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.dialogVisible = false;
                        this.$message.success(successMessage);
                        this.$emit(
                            this.type === 'add' ? 'addSuccess' : 'editSuccess'
                        );
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 取消
        resonCancel() {
            this.dialogVisible = false;
        },
        // 新增方法
        addItem(length) {
            if (length >= 10) {
                this.$message({
                    type: 'warning',
                    message: '最多可新增10条!'
                });
            } else {
                this.form.dynamicItem.push({
                    delayOneLevelReason: '',
                    delayTwoLevelReason: '',
                    detailsReason: '',
                    associatedModule: '',
                    responsibilityPerson: ''
                });
            }
        },
        // 删除方法
        deleteItem(item, index) {
            this.form.dynamicItem.splice(index, 1);
        }
    }
};
</script>

<style lang="scss" scoped>
.casue-box {
    height: calc(100vh - 150px);
    padding-bottom: 20px;
    overflow: auto;
}
.box-top {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
}
.box-top div:first-child {
    width: 5px;
    height: 25px;
    background-color: #3370ff;
}
.box-top div:nth-child(2) {
    font-weight: bolder;
    padding: 0px 25px 0px 15px;
}
.no-table-data {
    padding: 10px;
    text-align: center;
    color: #999;
}
.form-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 25px;
}
.form-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.form-row {
    width: 100%;
    display: flex;
    justify-content: flex-start;
}
.form-bottom {
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.form-style {
    width: 100%;
}
.form-btn {
    width: 33%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}
.ellipsis {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #3370ff;
}

::v-deep.el-table th > .cell {
    color: #fff !important;
}
/**改变边框颜色*/
::v-deep .el-table {
    border: 1px solid #8c8c8c !important;
}

::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
    background-color: #3370ff !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}

::v-deep .el-select__tags {
    max-width: 98% !important;
}
.snbc-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
}
::v-deep .el-dialog .el-dialog__body .el-form-item__label {
    padding: 6px 17px 0 0 !important;
}
::v-deep .el-dialog {
    width: 65% !important;
}
</style>
