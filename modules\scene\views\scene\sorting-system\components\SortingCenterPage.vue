<template>
    <div>
        <div
            ref="pageRef"
            class="domest-page"
            :style="{ transform: `scale(${scale})` }"
        >
            <tabulating-card class="tabulat-page"></tabulating-card>
            <SortingCenterScene
                class="finance-page"
                :scale="scale"
                :parentWidth="parentWidth"
            ></SortingCenterScene>
        </div>
    </div>
</template>

<script>
import scaleMixin from 'scene/mixins/scale-mixin.js';
import SortingCenterScene from './SortingCenterScene.vue';
import TabulatingCard from '../../components/TopTabulatingCard.vue';

export default {
    components: {
        TabulatingCard,
        SortingCenterScene
    },
    mixins: [scaleMixin],
    data() {
        return {
            // 父节点id
            parentId: 'scene-view',
            productsData: [],
            usageStatuses: []
        };
    }
};
</script>

<style lang="scss" scoped>
.domest-page {
    background-color: #f2f5fc;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
    width: 1250px;
    transform-origin: 0 0;
    overflow: hidden;
}
.tabulat-page {
    position: absolute;
    right: 30px;
    top: 50px;
    z-index: 1;
}
.scencetable-page {
    z-index: 2;
    padding: 30px 3px;
}
</style>
