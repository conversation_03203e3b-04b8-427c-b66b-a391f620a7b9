export default {
    自动分拣: {
        name: '自动分拣',
        // 按钮坐标
        point: { x: 580 + 20, y: 208 + 40 },
        crossPoints: [
            { x: 300, y: 220 + 40 },
            { x: 200, y: 220 + 40 }
        ],
        children: [0, 1, 2].map((num) => {
            const x = 40;
            const y = 65 + num * 120 + 40;
            return {
                x,
                y,
                level: 1,
                position: 'left',
                crossPoints: [
                    { x: 200, y: 140 + num * 120 },
                    { x: 200 - 50, y: 140 + num * 120 }
                ]
            };
        })
    },
    自动输送: {
        name: '自动输送',
        point: { x: 680 + 30, y: 268 - 60 },
        crossPoints: [
            { x: 700, y: 280 - 60 },
            { x: 900, y: 280 - 60 }
        ],
        children: [0, 1, 2].map((num) => {
            const x = 1030 - 80;
            const y = 65 + num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'right',
                crossPoints: [
                    { x: 900, y: 100 + num * 120 },
                    { x: 900 + 50, y: 100 + num * 120 }
                ]
            };
        })
    },
    自动识别: {
        name: '自动识别',
        point: { x: 610, y: 318 },
        crossPoints: [{ x: 650, y: 540 }],
        children: [0, 1, 2, 3].map((num) => {
            const x = 465 + num * 100;
            const y = 590;
            return {
                x,
                y,
                level: 1,
                position: 'bottom',
                crossPoints: [
                    { x: 500 + num * 100, y: y - 50 },
                    { x: 500 + num * 100, y }
                ]
            };
        })
    },
    自动装卸: {
        name: '自动装卸',
        point: { x: 390, y: 150 },
        crossPoints: [{ x: 350, y: 160 }],
        children: [
            {
                x: 220,
                y: 125,
                level: 1,
                position: 'left',
                crossPoints: [{ x: 350, y: 160 }]
            }
        ]
    }
};
