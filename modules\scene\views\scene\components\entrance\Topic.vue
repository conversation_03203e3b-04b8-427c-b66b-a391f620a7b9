<template>
    <div class="outside">
        <div class="topic">
            <div class="background">
                <img
                    :src="dedicatedPrinter"
                    alt="dedicatedPrinter"
                    class="img"
                />
                <p class="title">专用打印机</p>
                <p class="description">专业化产品解决方案</p>
                <div class="btn" @click="handleClick('dedicated-printer')">
                    进入专题
                </div>
            </div>
        </div>
        <div class="topic">
            <div class="background">
                <img
                    :src="smartSelfServiceTerminal"
                    alt="smartSelfServiceTerminal"
                    class="img"
                />
                <p class="title">智能自助终端</p>
                <p class="description">专业化产品解决方案</p>
                <div class="btn" @click="handleClick('smart-terminal')">
                    进入专题
                </div>
            </div>
        </div>
        <div class="topic">
            <div class="background">
                <img :src="financeDevice" alt="financeDevice" class="img" />
                <p class="title">金融机具</p>
                <p class="description">场景化产品解决方案</p>
                <div class="btn" @click="handleClick('finance-scene')">
                    进入专题
                </div>
            </div>
        </div>
        <div class="topic">
            <div class="background">
                <img
                    :src="logisticsSorting"
                    alt="logisticsSorting"
                    class="img"
                />
                <p class="title">物流分拣</p>
                <p class="description">场景化产品解决方案</p>
                <div class="btn" @click="handleClick('sorting-system')">
                    进入专题
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import dedicatedPrinter from 'scene/assets/businessSector/dedicated-printer.png';
import financeDevice from 'scene/assets/businessSector/finance-device.png';
import logisticsSorting from 'scene/assets/businessSector/logistics-sorting.png';
import smartSelfServiceTerminal from 'scene/assets/businessSector/smart-self-service-terminal.png';
import buttonImg from 'scene/assets/businessSector/button-background.png';
import { Message } from 'element-ui';

export default {
    name: 'Topic',
    data() {
        return {
            dedicatedPrinter,
            financeDevice,
            logisticsSorting,
            smartSelfServiceTerminal,
            buttonImg
        };
    },
    methods: {
        /**
         * 处理点击
         * @param {string} type 点击了哪个按钮
         */
        handleClick(type) {
            Message.closeAll();
            this.$router.push({
                path: `/bizConfig/${type}`
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.topic {
    width: 100%;
    height: 100%;
}
.background {
    height: 100%;
    width: 100%;
    background-image: url('~scene/assets/businessSector/topic-background.png');
    background-size: cover;
    background-position: center;
}
.img {
    width: 50%;
    height: 55%;
    margin: -4vh 0 0 1vw;
}
.title {
    font-size: 2.2vh;
    font-weight: 600;
    margin-left: 2.3vw;
    margin-bottom: 0;
}
.description {
    margin-top: 0.5vh;
    margin-left: 2.3vw;
    font-size: 0.9vw;
    font-weight: 100;
}
.outside {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 10% 5%;
    width: 90%;
    height: 82%;
}
.btn {
    height: 15%;
    width: 30%;
    background-color: #fff;
    margin-left: 2.3vw;
    border-radius: 99rem;
    border: 1px solid #334bb8;
    text-align: center;
    font-size: 1vw;
    color: #4e6bf0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &:hover {
        transform: scale(1.1);
    }
}
</style>
