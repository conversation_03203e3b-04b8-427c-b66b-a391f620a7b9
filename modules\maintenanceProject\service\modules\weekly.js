import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        weekly: {
            // 创建禅道任务并关联缺陷
            getWeekReportList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getMaintainWeekReportList`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 更新维护项目周报
            updateWeekReportList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/updateMaintainWeekReportList`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 修改维护项目周报状态
            editWeekReportStatus(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/updateWeekReportStatus`,
                    method: 'put',
                    loading: false,
                    params: data
                });
            },
            // 根据周报ID查询需求列表
            getDemandListByWeeklyId(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getMaintainWeekDemandList`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 查询项目经理下需求列表
            getDemandList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getManagerDemandList`,
                    method: 'post',
                    data
                });
            },
            // 为维护项目周报新增需求
            addDemand(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/insertDemandWeekList`,
                    method: 'post',
                    data
                });
            },
            // 根据周报ID查询订单列表
            getOrderListByWeeklyId(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getMaintainWeekOrderList`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 查询项目经理下订单列表
            getOrderList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getManagerOrderList`,
                    method: 'post',
                    data
                });
            },
            // 为维护项目周报新增订单
            addOrder(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/insertOrderWeekList`,
                    method: 'post',
                    data
                });
            },
            // 根据周报ID查询缺陷列表
            getDefectListByWeeklyId(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getMaintainWeekFaultList`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 查询项目经理下缺陷列表
            getDefectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getManagerFaultList`,
                    method: 'post',
                    data
                });
            },
            // 为维护项目周报新增缺陷
            addDefect(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/insertFaultWeekList`,
                    method: 'post',
                    data
                });
            },
            // 编辑任务进展补充信息
            editSupplementInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/updateProgressDetail`,
                    method: 'put',
                    data
                });
            },
            // 在没有该项目经理的时候，可以调用该接口手动新增周报
            addNewWeekly(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/insertMaintainWeekReport`,
                    method: 'post',
                    data
                });
            },
            // 获取窗口期信息（项目更新计划）
            getWindowPeriod(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/maintainWeekReportController/getCurrWeekReportPlan`,
                    method: 'get',
                    params: data
                });
            }
        }
    };

    return service;
};
