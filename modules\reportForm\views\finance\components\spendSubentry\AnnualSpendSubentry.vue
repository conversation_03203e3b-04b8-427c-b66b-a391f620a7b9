<template>
    <div class="annual-spend-subentry" ref="overallChartBox">
        <div class="flex info-container">
            <el-date-picker
                v-model="year"
                value-format="yyyy"
                type="year"
                placeholder="选择年"
                format="yyyy自然年"
                :picker-options="{
                    disabledDate(time) {
                        return (
                            time.getTime() < new Date('2024-01-01').getTime()
                        );
                    }
                }"
                :clearable="false"
                @input="handleYearChange"
            >
            </el-date-picker>
        </div>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">人工费：按项目类别</div>
                <el-radio-group
                    class="radio-group"
                    v-model="laborCostProductCategory"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="line">
                        <svg-icon icon-class="lineChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <div
                v-show="laborCostProductCategory === 'line'"
                class="chart"
                ref="laborCostProductCategoryLineChart"
            ></div>
            <div
                v-show="laborCostProductCategory === 'pie'"
                class="chart"
                ref="laborCostProductCategoryPieChart"
            ></div>
        </el-card>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">人工费：按级别</div>
                <el-radio-group
                    class="radio-group"
                    v-model="laborCost"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="line">
                        <svg-icon icon-class="lineChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <div
                v-show="laborCost === 'line'"
                class="chart"
                ref="laborCostLineChart"
            ></div>
            <div
                v-show="laborCost === 'pie'"
                class="chart"
                ref="laborCostPieChart"
            ></div>
        </el-card>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">人工费：按产品线细分</div>
                <el-select
                    v-model="month"
                    placeholder="请选择月份"
                    class="selector"
                    @input="monthChange"
                    clearable
                >
                    <el-option
                        v-for="item in monthOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </div>
            <div class="chart" ref="laborCostProductLineChart"></div>
        </el-card>
        <el-card class="card chart-container last">
            <div class="flex">
                <div class="chart-box-title">物料消耗费：按流程</div>
                <el-radio-group
                    class="radio-group"
                    v-model="materialConsumptionCost"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="line">
                        <svg-icon icon-class="lineChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <div
                v-show="materialConsumptionCost === 'line'"
                class="chart"
                ref="materialConsumptionCostLineChart"
            ></div>
            <div
                v-show="materialConsumptionCost === 'pie'"
                class="chart"
                ref="materialConsumptionCostPieChart"
            ></div>
        </el-card>
    </div>
</template>

<script>
import { monthOptions } from 'reportForm/common.js';
import {
    getLaborCostProductCategoryLineChartOptions,
    getLaborCostProductCategoryPieChartOptions,
    getLaborCostLineChartOptions,
    getLaborCostPieChartOptions,
    getLaborCostProductLineChartOptions,
    getMaterialConsumptionCostLineChartOptions,
    getMaterialConsumptionCostPieChartOptions
} from './subentryChartOptions.js';
import * as echarts from 'echarts';
import { debounce } from 'lodash';

export default {
    name: 'AnnualSpendSubentry',
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            year: new Date().getFullYear().toString(),
            month: '',
            // 图表缓存
            charts: {},
            optionsMap: {},
            monthOptions,
            laborCostProductCategory: 'line',
            laborCost: 'line',
            materialConsumptionCost: 'line',
            // 流程列表
            flowList: []
        };
    },
    watch: {
        activeName(newVal) {
            if (newVal === 'annualSpendSubentry') {
                this.handleYearChange();
            }
        }
    },
    mounted() {
        this.getFlowList().then(this.handleYearChange);
    },
    activated() {
        if (this.activeName === 'annualSpendSubentry') {
            this.handleYearChange();
        }
    },
    methods: {
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const observer = new ResizeObserver(
                debounce((entries) => {
                    this.activeName === 'annualSpendSubentry' &&
                        myChart.resize();
                }, 100)
            );
            observer.observe(this.$refs.overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 切换图表显示时，重新确定图表尺寸
         */
        handleRadioClick() {
            this.$nextTick(() => {
                for (const option in this.optionsMap) {
                    if (
                        Object.prototype.hasOwnProperty.call(
                            this.optionsMap,
                            option
                        )
                    ) {
                        this.charts[option] && this.charts[option].resize();
                    }
                }
            });
        },
        /**
         * 月份改变的处理函数
         */
        monthChange() {
            this.setLaborCostByProductLine();
        },
        /**
         * 处理年份改变的函数
         */
        handleYearChange() {
            this.setLaborCostByProductLineCategory();
            this.setLaborCostByProductPieCategory();
            this.setLaborCostByLine();
            this.setLaborCostByPie();
            this.setLaborCostByProductLine();
            this.setMaterialConsumptionCostByLine();
            this.setMaterialConsumptionCostByPie();
        },
        /**
         * 人工费：产品类别折线图
         */
        async setLaborCostByProductLineCategory() {
            try {
                const api =
                    this.$service.reportForm.finance
                        .getLaborCostLineChartByProductCategory;
                const params = {
                    year: this.year.toString()
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.laborCostProductCategoryLine =
                    getLaborCostProductCategoryLineChartOptions(res.body);
                this.initChart('laborCostProductCategoryLine');
            } catch (err) {
                console.error('Error:', err);
            }
        },

        /**
         * 人工费：产品类别饼图
         */
        async setLaborCostByProductPieCategory() {
            try {
                const api =
                    this.$service.reportForm.finance
                        .getLaborCostPieChartByProductCategory;
                const params = {
                    year: this.year.toString()
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.laborCostProductCategoryPie =
                    getLaborCostProductCategoryPieChartOptions(res.body);
                this.initChart('laborCostProductCategoryPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 人工费：按级别折线图设置
         */
        async setLaborCostByLine() {
            try {
                const api =
                    this.$service.reportForm.finance.getLaborCostForLineChart;
                const params = {
                    year: this.year.toString(),
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.laborCostLine = getLaborCostLineChartOptions(
                    res.body
                );
                this.initChart('laborCostLine');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 人工费：按级别饼图设置
         */
        async setLaborCostByPie() {
            try {
                const api =
                    this.$service.reportForm.finance.getLaborCostForPieChart;
                const params = {
                    year: this.year.toString(),
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.laborCostPie = getLaborCostPieChartOptions(
                    res.body
                );
                this.initChart('laborCostPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 人工费：按产品线设置
         */
        async setLaborCostByProductLine() {
            try {
                const api =
                    this.$service.reportForm.finance.getLaborCostByProductLine;
                const params = {
                    year: this.year.toString(),
                    month: this.month
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.laborCostProductLine =
                    getLaborCostProductLineChartOptions(res.body);
                this.initChart('laborCostProductLine');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 物料消耗费：折线图
         * @param {String} month 月份
         */
        async setMaterialConsumptionCostByLine() {
            try {
                const api =
                    this.$service.reportForm.finance
                        .getMaterialConsumptionCostForLineChart;
                const params = {
                    year: this.year.toString(),
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.materialConsumptionCostLine =
                    getMaterialConsumptionCostLineChartOptions(
                        res.body,
                        this.flowList
                    );
                this.initChart('materialConsumptionCostLine');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 物料消耗费：饼图
         */
        async setMaterialConsumptionCostByPie() {
            try {
                const api =
                    this.$service.reportForm.finance
                        .getMaterialConsumptionCostForPieChart;
                const params = {
                    year: this.year.toString(),
                    month: ''
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.materialConsumptionCostPie =
                    getMaterialConsumptionCostPieChartOptions(
                        res.body,
                        this.flowList
                    );
                this.initChart('materialConsumptionCostPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 获取物料消耗费的全部流程
         * @returns {Promise} 返回是否拿到流程选项
         */
        async getFlowList() {
            try {
                const api = this.$service.reportForm.finance.getWholeFlow;
                const res = await api({});
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    throw new Error(res.head.message);
                }
                this.flowList = res.body;
            } catch (err) {
                console.error('Error:', err);
                throw err;
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
.annual-spend-subentry {
    .info-container {
        width: 100%;
        .info {
            line-height: 30px;
            margin-left: 20px;
            font-weight: 600;
        }
    }
    .card {
        margin-top: 10px;
    }
    .last {
        margin-bottom: 15px;
    }
    .chart-container {
        margin-top: 10px;
        width: 100%;
        height: 300px;
        padding: 10px;
        height: 400px;
        .chart-box-title {
            width: 150px;
            height: 30px;
            background-color: #3370ff;
            color: #fff;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            padding: 8px;
            border-radius: 10px;
        }
        .radio-group {
            margin-left: auto;
            line-height: 30px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-top: 20px;
        }
        .select-icon {
            width: 15px;
            height: 15px;
        }
        .selector {
            margin-left: 10px;
            margin-top: 2px;
        }
    }
}
</style>
