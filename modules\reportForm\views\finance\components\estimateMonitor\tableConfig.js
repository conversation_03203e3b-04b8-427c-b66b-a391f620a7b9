const elButtonFn = () => {
    // 去除button的padding，设置max-width，否则tooltip与省略号不会正常显示
    return {
        type: 'text',
        style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align:left; padding:0px!important; max-width:100%;'
    };
};
const getTableConfig = (scope) => {
    return {
        // 查询api配置
        queryApi: scope.$service.reportForm.finance.getEstimateCostTable,
        elTableColumns: [
            {
                label: '产品线',
                prop: 'productLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '产品线细分',
                prop: 'subProductLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目编号',
                prop: 'projectNo',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目名称',
                prop: 'projectName',
                show: true,
                minWidth: 200,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '项目状态',
                prop: 'projectStatus',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目概算',
                prop: 'estAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'estimate'
            },
            {
                label: '项目预算',
                prop: 'budgetAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'budget'
            },
            {
                label: '比率',
                prop: 'proportion',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        }
    };
};
export { getTableConfig };
