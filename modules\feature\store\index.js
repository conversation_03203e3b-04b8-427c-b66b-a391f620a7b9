/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.test.xxx
 */

const state = {
    // 包含外部人员在内的全部人员
    externalStaff: []
};

const mutations = {
    SET_EXTERNAL_STAFF(state, externalStaff) {
        state.externalStaff = externalStaff;
    }
};

const actions = {
    setExternalStaff({ commit }, externalStaff) {
        commit('SET_EXTERNAL_STAFF', externalStaff);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
