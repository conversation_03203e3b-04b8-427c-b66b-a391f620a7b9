<template>
    <div class="message-check-container">
        <el-tabs v-model="activeName">
            <el-tab-pane label="事件执行情况" name="eventCheck">
                <EventCheck :activeName="activeName" />
            </el-tab-pane>
            <el-tab-pane label="消息发送情况" name="sendCheck" :lazy="true">
                <SendCheck :activeName="activeName" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import EventCheck from './eventCheck/EventCheck.vue';
import SendCheck from './sendCheck/SendCheck.vue';

export default {
    name: 'MessageCheck',
    components: { EventCheck, SendCheck },
    data() {
        return { activeName: 'eventCheck' };
    }
};
</script>

<style scoped lang="scss">
.message-check-container {
    padding: 15px 15px 0 15px;
    height: 100vh;
    overflow-y: auto;
}
</style>
