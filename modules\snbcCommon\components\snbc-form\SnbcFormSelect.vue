<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-select
            class="select"
            v-model="config.modelObj[config.modelKey]"
            v-bind="elSelectAttrs"
            @change="handleChange"
        >
            <select-all :config="config" />
            <el-option
                v-for="(option, index) in config.elOptions"
                :key="index"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
            />
        </el-select>
    </el-form-item>
</template>
<script>
import SelectAll from './components/SelectAll.vue';

export default {
    name: 'SnbcFormSelect',
    components: { SelectAll },
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elOptions: [],
                    elFormItemAttrs: {},
                    elSelectAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                'filterable': true,
                'clearable': true,
                'collapse-tags': true
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elSelectAttrs || {})
            };
        }
    },
    methods: {
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
<style scoped>
.select {
    font-weight: initial;
}
</style>
