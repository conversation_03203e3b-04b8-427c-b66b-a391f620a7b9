/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';
// 文件下载/导出接口超时时间
const downlaodTimeout = 300000;
const downlaodTimeoutEx = 1300000;
export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    let service = {
        // 权限管理页面   获得权限列表数据
        getPermissionTreeList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/getPermissionTree',
                method: 'get',
                params: query
            });
        },
        // 权限管理页面   获得权限列表数据
        getPermissionTableList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/getList',
                method: 'get',
                params: query
            });
        },
        // 权限管理页面   新增权限
        postPermissionCreateData(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/addPermission',
                method: 'post',
                data
            });
        },
        // 权限管理页面   更新权限
        putPermissionUpdateData(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/editPermission',
                method: 'put',
                data
            });
        },
        // 权限管理页面   删除权限
        deletePermissionData(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/delPermission',
                method: 'delete',
                data
            });
        },
        // 权限管理页面   权限编码唯一性校验
        postPermissionCodeCheck(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/isExist',
                method: 'post',
                data
            });
        },
        // 权限管理页面   权限定义状态变更
        postPermissionChangeStatus(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/changeStatus',
                method: 'post',
                data
            });
        },
        // 权限管理页面   权限定义状态变更
        postPermissionDragOrderBy(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/dragOrderBy',
                method: 'post',
                data
            });
        },
        // 数据权限弹窗页面   获取数据权限列表
        getDataScopeList(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/getDataScopeList',
                method: 'get',
                params: data
            });
        },
        // 数据权限弹窗页面   删除数据权限
        deleteDataScope(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/delDataScope',
                method: 'delete',
                data
            });
        },
        // 数据权限弹窗页面   新增数据权限
        postCreateDataScope(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/addDataScope',
                method: 'post',
                data
            });
        },
        // 数据权限弹窗页面   编辑数据权限
        putUpdateDataScope(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/permission/updateDataScope',
                method: 'put',
                data
            });
        },
        // 根据根节点获取地域树信息
        getRegionList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/region/getRegionList',
                method: 'get',
                params: query
            });
        },
        // 保存地域
        postAddRegion(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/region/addRegion',
                method: 'post',
                data: query
            });
        },
        // 编辑地域
        postUpdateRegion(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/region/updateRegion',
                method: 'post',
                data: query
            });
        },
        // 删除地域
        deleteRegion(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/region/delRegion',
                method: 'delete',
                params: query
            });
        },
        // 查询字典类型列表
        getDictionaryTypeList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/getDictTypeList',
                method: 'get',
                params: query
            });
        },
        // 导出字典类型列表
        getDictionaryTypeExport(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/exportAllDict',
                method: 'get',
                responseType: 'blob',
                params: query,
                timeout: downlaodTimeout
            });
        },
        // 添加字典类型
        postAddDictionaryType(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/addDictType',
                method: 'post',
                data
            });
        },
        // 修改字典类型
        postUpdateDictionaryType(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/updateDictType',
                method: 'post',
                data
            });
        },
        // 保存字典类型数据
        postSaveDictionaryType(data) {
            if (data.isUpdate === 1) {
                return this.postUpdateDictionaryType(data.dataInfo);
            }
            return this.postAddDictionaryType(data.dataInfo);
        },
        // 删除字典类型
        postRemoveDictionaryType(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/delDictType',
                method: 'delete',
                params: data
            });
        },
        // 根据字典值类型查询字典值所有列表，用于下拉列表选择框
        getDictionaryValueListAll(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/getDictValueList',
                method: 'get',
                params: query
            });
        },
        // 根据字典类型编码获取字典值列表。带分页的接口
        getDictionaryValueList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/getAllDictValue',
                method: 'get',
                params: query
            });
        },
        // 导出所有字典值
        getExportAllDict(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/exportLogs',
                method: 'get',
                responseType: 'blob',
                params: query,
                timeout: downlaodTimeout
            });
        },
        // 添加字典值
        postAddDictionaryValue(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/addDictValue',
                method: 'post',
                data
            });
        },
        //  更新字典值
        postUpdateDictionaryValue(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/updateDictValue',
                method: 'post',
                data
            });
        },
        // 保存或更新字典值
        postSaveDictionaryValue(fromData) {
            const data = Object.assign({}, fromData);
            if (!data.dataInfo.parentId) {
                delete data.dataInfo.parentId;
            }
            delete data.dataInfo.parentValueCode;

            if (data.isUpdate === 1) {
                return this.postUpdateDictionaryValue(data.dataInfo);
            }
            delete data.dataInfo.valueId;
            return this.postAddDictionaryValue(data.dataInfo);
        },
        // 删除字典值
        postRemoveDictionaryValue(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/dict/delDictValue',
                method: 'delete',
                params: data
            });
        },
        // 获取参数配置
        getParamByMapList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/param/getParamList',
                method: 'get',
                params: query
            });
        },
        // 获取参数类型值
        getParamTypeCodeList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/param/queryParamCode',
                method: 'get',
                params: query
            });
        },
        // 保存参数接口
        putSaveParam(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/param/addParam',
                method: 'put',
                data
            });
        },
        // 更新参数接口
        postUpdateParam(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/param/updateParam',
                method: 'put',
                data
            });
        },
        // 删除单个参数
        deleteParam(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/param/delParam',
                method: 'delete',
                params: query
            });
        },
        // 查询日志列表
        getLogList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/log/getLogList',
                method: 'get',
                params: query
            });
        },
        // 查询日志组织机构树
        getLogOrgTree() {
            return this.getOrgTree({ orgLevel: 0, orgId: '' });
        },
        // 导出日志列表
        getLogExport(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/log/exportLogs',
                method: 'get',
                responseType: 'blob',
                params: query,
                timeout: downlaodTimeout
            });
        },
        // 获取组织机构树
        getOrgTree(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/org/getOrgTree',
                method: 'get',
                params: query
            });
        },
        // 组织机构-获取用户列表
        getUserList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/userListPage',
                method: 'get',
                params: query
            });
        },
        // 组织机构-获取用户列表
        getUserById(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/getUserById',
                method: 'get',
                params: query
            });
        },
        // 组织机构-获取该组织机构用户
        getUserListForOrg(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/userList',
                method: 'get',
                params: query
            });
        },
        // 新增组织机构
        postAddOrg(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/org/addOrg',
                method: 'post',
                data: query
            });
        },
        // 更新组织机构
        putUpdateOrg(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/org/updateOrg',
                method: 'put',
                data: query
            });
        },
        // 删除组织机构
        deleteOrg(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/org/delOrg',
                method: 'delete',
                params: query
            });
        },
        // 拖拽组织机构排序
        putOrgSort(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/org/orgSort',
                method: 'put',
                data: query
            });
        },
        // 组织机构 - 新增用户
        putAddUserList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/addUser',
                method: 'put',
                data: query
            });
        },
        // 组织机构 - 编辑用户
        putUpdateUser(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updateUser',
                method: 'put',
                data: query
            });
        },
        // 组织机构 - 更改用户状态
        putChangeUserStatus(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/changeUserStatus',
                method: 'put',
                data: query
            });
        },
        // 组织机构 - 移除用户
        putRemoveUser2Org(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/removeUser2Org',
                method: 'put',
                data: query
            });
        },
        // 组织机构 - 多用户调整组织机构
        postAdjustDepartment(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/adjustDepartment',
                method: 'post',
                data: query
            });
        },
        // 组织机构 - 导入用户
        postImportUser(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/importUserNew',
                method: 'put',
                data: query
            });
        },
        // 组织机构 - 下载导入模板
        getDownloadUserTemplate(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/downloadUserTemplate',
                method: 'get',
                responseType: 'blob',
                params: query,
                timeout: downlaodTimeout
            });
        },
        // 组织机构 - 导出部分用户及部门
        getExportPartUser(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/exportPartUserNew',
                method: 'get',
                params: query
            });
        },
        //  组织机构 - 导出全部用户及部门
        getExportUser(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/exportUserNew',
                method: 'get',
                params: query
            });
        },
        //  根据接口判断当前用户是否是超管
        getUserRole(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/isAdminUser',
                method: 'get',
                params: query
            });
        },
        // 租户列表模块，用户获取所属客服、所属业务员接口
        getUserAllList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/userAllList',
                method: 'get',
                params: query
            });
        },
        // 组织机构获取角色列表  可以用getRoleList接口代替，此处为了本地调试
        getRoleListOrg(query) {
            return this.getRoleList(query);
        },
        // 获取角色列表
        getRoleList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getRoleList',
                method: 'get',
                params: query
            });
        },
        // 获取角色的关联的用户列表。roleId传空，获取未关联用户列表
        getRoleUserList(query) {
            const params = Object.assign(
                { status: query.roleId ? 0 : 1 },
                query
            );
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/userListPage',
                method: 'get',
                params
            });
        },
        // 获取角色的权限列表
        getRolePermissionList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getRolePermission',
                method: 'get',
                params: query
            });
        },
        // 获取当前用户所有权限列表
        getRoleAllPermissionList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getAllPermission',
                method: 'get',
                params: query
            });
        },
        // 获取角色信息及权限树
        getRoleInfoAndTree(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getroleinfo',
                method: 'get',
                params: query
            });
        },

        // 获角色取组织机构树
        getRoleOrgTree(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getOrgTree',
                method: 'get',
                params: query
            });
        },
        // 获取角色组织机构节点下的用户列表
        getRoleOrgUserList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getUserByOrg',
                method: 'get',
                params: query
            });
        },
        // 修改角色
        postAddRole(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/addRole',
                method: 'post',
                data: query
            });
        },
        // 修改角色
        postUpdateRole(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/updateRole',
                method: 'PUT',
                data: query
            });
        },
        // 删除角色
        postDeleteRole(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/delRole',
                method: 'DELETE',
                data: query
            });
        },
        // 角色关联权限-查询
        getRolePermission(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/getrolepermission',
                method: 'get',
                params: query
            });
        },
        // 添加用户角色关联关系
        postAddUserRoleRelation(query) {
            // eslint-disable-next-line no-param-reassign
            query = Object.assign({ status: 1 }, query);
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/roleRelationUser',
                method: 'PUT',
                data: query
            });
        },
        // 解除用户角色关联关系
        postRomveUserRoleRelation(query) {
            // eslint-disable-next-line no-param-reassign
            query = Object.assign({ status: 0 }, query);
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/roleRelationUser',
                method: 'PUT',
                data: query
            });
        },
        // 角色权限重置
        putPermissionReset(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/role/resetRole',
                method: 'PUT',
                data: query
            });
        },
        // 获取当前用户所属机构
        getCurrentOrgs(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/getUserOrgInfo4Role',
                method: 'GET',
                params: query
            });
        },
        // 获取租户列表信息
        getTenanList(query) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/tenant/getTenantList',
                method: 'POST',
                data: query
            });
        },
        // 根据租户id获取租户详情
        getTenantById(query) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/tenant/getTenantById',
                method: 'GET',
                params: query
            });
        },
        // 新增租户
        postAddTenant(data) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/tenant/addTenant',
                method: 'POST',
                data
            });
        },
        // 修改租户
        postUpdateTenant(data) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/tenant/updateTenant',
                method: 'POST',
                data
            });
        },
        // 保存租户信息
        postSaveTenant(fromData) {
            const data = Object.assign({}, fromData);
            if (data.isUpdate) {
                return this.postUpdateTenant(data.dataInfo);
            }
            delete data.dataInfo.valueId;
            return this.postAddTenant(data.dataInfo);
        },
        // 修改租户状态
        postUpdateTenantStatus(data) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/tenant/updateTenantStatus',
                method: 'POST',
                data
            });
        },
        // 查询租户日志列表
        gettenantLogList(query) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/log/getLogList',
                method: 'get',
                params: query
            });
        },
        // 导出租户日志列表后台算法
        gettenantLogExport(query) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/log/exportLogs',
                method: 'get',
                responseType: 'blob',
                params: query,
                timeout: downlaodTimeout
            });
        },
        // 导出租户日志列表前端算法
        gettenantLogExportData(query) {
            return http({
                baseDomain: basePath.bossapi.tenantmanager,
                url: '/console/log/exportLogsData',
                method: 'get',
                params: query,
                timeout: downlaodTimeoutEx
            });
        },
        getArea(param) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/area/getArea',
                method: 'GET',
                params: param
            });
        },
        // 获取导出文件列表
        getExportFilelist(param) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/file/exportFileList',
                method: 'GET',
                params: param
            });
        },
        // 删除导出文件列表
        deleteExportFile(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/file/deleteFile',
                method: 'DELETE',
                data: query
            });
        },
        // 展示日志清理时间
        showClearTime(param) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/param/getParamByCode',
                method: 'GET',
                params: param
            });
        },
        // 获取顶部导航菜单列表
        getMenuList() {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/navigation/list',
                method: 'GET'
            });
        },
        // 新增顶部菜单
        postAddTopMenu(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/navigation/add',
                method: 'POST',
                data
            });
        },
        // 编辑顶部菜单
        putEditTopMenu(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/navigation/edit',
                method: 'PUT',
                data
            });
        },
        // 删除顶部菜单
        deleteTopMenu(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/navigation/delete',
                method: 'DELETE',
                params: query
            });
        },
        // 保存导航菜单关系
        postSaveMenuRelation(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/navigation/saveRelationship',
                method: 'POST',
                data
            });
        },
        // 获取菜单关系设置
        getMenuRelation(param) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/navigation/getRelationship',
                method: 'GET',
                params: param
            });
        },
        // 信息公告获取人员列表
        getPersonList(param) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/notice/noticeUserList',
                method: 'GET',
                loading: false,
                params: param
            });
        },
        // 商户详情产品服务列表
        tenantDetailList(query) {
            return http({
                baseDomain: basePath.bossapi.productService,
                url: '/console/v1/boss/productservice/listbytenantid',
                method: 'get',
                params: query
            });
        },
        // 商户列表详情延期接口
        tenantDetailExtend(data) {
            return http({
                baseDomain: basePath.bossapi.productService,
                url: '/console/v1/boss/productservice/extendrecord',
                method: 'POST',
                data
            });
        },
        // 验证token接口
        verifyToken(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/api/superset/getuser',
                method: 'get',
                params: query
            });
        }
    };

    // 合并modules中的服务
    const fileService = require.context('./modules', false, /\.js/);
    fileService.keys().forEach((moduleFilePath) => {
        const moduleName = moduleFilePath.replace(/^\.\/(.*)\/.\w+$/, '$1');
        const tempService = fileService(moduleName);
        service = Object.assign(service, tempService.default(Vue));
    });

    return service;
};
