<template>
    <div>
        <el-button
            type="primary"
            @click="handleClick"
            class="add-button"
            v-permission="['businessSectorProjectUpdate']"
            >新增项目</el-button
        >
        <el-dialog
            title="新增项目"
            :visible.sync="dialogVisible"
            :before-close="dialogBeforeClose"
            class="add-project-dialog"
        >
            <el-form :model="form" ref="form">
                <el-form-item label="业务板块">
                    {{ businessUnit }}
                </el-form-item>
                <el-form-item
                    label="产品系列"
                    prop="productSet"
                    :rules="required"
                >
                    <el-select
                        v-model="form.productSet"
                        placeholder="请选择产品系列"
                        @change="handleProductSetChange"
                    >
                        <el-option
                            v-for="item in productTypeOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="产品分类"
                    prop="productType"
                    :rules="required"
                >
                    <el-select
                        v-model="form.productType"
                        placeholder="请选择产品分类"
                        filterable
                    >
                        <el-option
                            v-for="item in productClassOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- 国内网点等 -->
                <el-form-item
                    v-if="showSceneAndKeyModule"
                    label="场景分类"
                    prop="sceneType"
                    :rules="required"
                >
                    <el-select
                        v-model="form.sceneType"
                        placeholder="请选择场景分类"
                        @change="handleSceneTypeChange"
                    >
                        <el-option
                            v-for="item in sceneClassOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    v-if="showSceneAndKeyModule"
                    label="具体场景"
                    prop="sceneNameList"
                    :rules="required"
                >
                    <el-select
                        v-model="form.sceneNameList"
                        placeholder="请选择具体场景"
                        multiple
                    >
                        <el-option
                            v-for="item in specificSceneOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="战略标志"
                    prop="strategy"
                    :rules="required"
                >
                    <el-select
                        v-model="form.strategy"
                        placeholder="请选择战略标志"
                    >
                        <el-option
                            v-for="item in CONSTANTS.STRATEGY"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="关键模块"
                    v-if="
                        businessUnit === '金融机具' &&
                        showSceneAndKeyModule &&
                        showModule
                    "
                    prop="keyModuleList"
                    :rules="required"
                >
                    <el-select
                        v-model="form.keyModuleList"
                        placeholder="请选择关键模块"
                        multiple
                        filterable
                    >
                        <el-option
                            v-for="item in keyModulesOptions"
                            :key="item"
                            :label="item"
                            :value="item"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="项目"
                    prop="projectNumber"
                    :rules="required"
                >
                    <el-select
                        v-model="form.projectNumber"
                        placeholder="请选择项目"
                        class="project"
                        filterable
                    >
                        <el-option
                            v-for="item in projectList"
                            :key="item.projectNumber"
                            :label="item.projectName"
                            :value="item.projectNumber"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="handleCancel">取 消</el-button>
                <el-button type="primary" @click="addProject">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import {
    getFinanceSceneList,
    getKeyModuleList,
    getSortingSceneList
} from './tools';

const required = [
    {
        required: true,
        message: ' ',
        trigger: ['change', 'blur']
    }
];
const form = {
    // 产品系列
    productSet: '',
    // 产品分类
    productType: '',
    // 战略标志
    strategy: '',
    // 场景分类
    sceneType: '',
    // 具体场景
    sceneNameList: [],
    // 关键模块
    keyModuleList: [],
    // 项目编号
    projectNumber: ''
};
export default {
    name: 'AddProjectByScene',
    components: {},
    props: {
        businessUnit: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            CONSTANTS,
            dialogVisible: false,
            form: this.$tools.cloneDeep(form),
            projectList: [],
            financeScene: [],
            sortingScene: [],
            keyModulesOptions: [],
            // 项目编号与项目列表组成的映射
            projectListMap: [],
            // 金融机具--产品系列为核心模块时，有些东西需要隐藏
            showSceneAndKeyModule: true,
            showModule: true,
            required,
            productTypeOptions: [],
            productClassOptions: []
        };
    },
    computed: {
        // 各大场景(页签对应的场景)
        sceneClassOptions() {
            if (this.businessUnit === '金融机具') {
                return Object.keys(this.financeScene).map((i) => {
                    return {
                        value: i,
                        label: i
                    };
                });
            } else if (this.businessUnit === '物流分拣') {
                return Object.keys(this.sortingScene).map((i) => {
                    return {
                        value: i,
                        label: i
                    };
                });
            }
            return [];
        },
        // 具体场景
        specificSceneOptions() {
            if (this.businessUnit === '金融机具') {
                return this.financeScene[this.form.sceneType];
            } else if (this.businessUnit === '物流分拣') {
                return this.sortingScene[this.form.sceneType];
            }
            return [];
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getRestProjectList();
                this.getproductLine();
            }
        }
    },
    async created() {
        // 金融场景对应的所有下拉框选项
        if (this.businessUnit === '金融机具') {
            this.financeScene = await getFinanceSceneList(this);
            this.keyModulesOptions = await getKeyModuleList(this);
        } else {
            this.sortingScene = await getSortingSceneList(this);
        }
    },
    methods: {
        // 查询产品系列
        async getproductLine() {
            try {
                const params = {
                    paramName: '产品系列',
                    paramType: this.businessUnit
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productTypeOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        // 根据产品系列查询产品分类
        async getproductType() {
            try {
                const params = {
                    paramName: this.businessUnit,
                    paramType: this.form.productSet
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productClassOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        /**
         * 关闭弹窗前的处理
         * @param {Function} done 完成后的函数
         */
        dialogBeforeClose(done) {
            this.resetForm();
            this.dialogVisible = false;
            done();
        },
        /**
         * 产品系列变化
         */
        handleProductSetChange() {
            this.form.productType = '';
            this.form.sceneType = '';
            this.form.sceneNameList = [];
            this.form.keyModuleList = [];
            if (this.form.productSet === '核心模块') {
                this.showSceneAndKeyModule = false;
                this.showModule = false;
            } else if (this.form.productSet === '软件产品') {
                this.showSceneAndKeyModule = true;
                this.showModule = false;
            } else {
                this.showSceneAndKeyModule = true;
                this.showModule = true;
            }
            this.getproductType();
        },
        handleSceneTypeChange() {
            this.form.sceneNameList = [];
        },
        handleClick() {
            this.dialogVisible = true;
        },
        /**
         * 获取项目列表（已经过滤掉已经存在的项目）
         */
        async getRestProjectList() {
            const api = this.$service.scene.getRestProjectList;
            try {
                const res = await api();
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.projectList = res.body;
                // 创建对象映射
                this.projectList.forEach((item) => {
                    this.projectListMap[item.projectNumber] = item;
                });
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 新增项目
         */
        async addProject() {
            const valid = this.validateForm();
            if (!valid) {
                this.$message.error('请输入所有必填项');
                return;
            }
            const api = this.$service.scene.addProject;
            try {
                const params = {
                    ...this.form,
                    ...this.projectListMap[this.form.projectNumber],
                    businessUnit: this.businessUnit
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                const count = await this.$store.state.scene
                    .changeProjectCountSuccess;
                this.$store.dispatch(
                    'scene/changeProjectCountSuccess',
                    count + 1
                );
                this.resetForm();
                this.dialogVisible = false;
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 取消时的操作
         */
        handleCancel() {
            this.resetForm();
            this.dialogVisible = false;
        },
        /**
         * 表单验证
         * @returns {Boolean} 是否通过校验
         */
        validateForm() {
            let res = false;
            this.$refs.form.validate((valid) => {
                if (valid) {
                    res = true;
                }
            });
            return res;
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.$refs.form.resetFields();
        }
    }
};
</script>

<style lang="scss" scoped>
.add-button {
    position: absolute;
    z-index: 100;
    top: 15px;
    right: 170px;
}
.add-project-dialog {
    ::v-deep .el-form-item__label {
        font-weight: 700;
    }
    ::v-deep .el-select {
        width: 100%;
    }
}
</style>
