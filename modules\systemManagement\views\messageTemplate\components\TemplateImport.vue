<template>
    <!-- 导入用户 -->
    <el-dialog :title="$t('systemManagement.messageTemplate.templateImport')" :visible.sync="isShow">
        <span>
            <div class="import-title">
                <div class="import-box">
                    <span>{{ $t('systemManagement.messageTemplate.templateDescOne') }}</span>
                    <div class="download-template">
                        <el-link type="primary" @click.native.stop="downloadUserTemplate"> {{ $t('systemManagement.messageTemplate.messageTemplate') }} </el-link>
                    </div>
                    <span>{{ $t('systemManagement.messageTemplate.templateDescTwo') }}</span>
                </div>
            </div>
            <div class="import-contnent">
                {{ $t('systemManagement.messageTemplate.inportService') }}：
                <el-button class="btn-upload upload" plain size="small" onclick="chooseUser.click()">
                    {{ $t('systemManagement.messageTemplate.clickUpload') }}
                </el-button>
                <input id="chooseUser" name="files" type="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" @change="handleUpload()">
                <div v-show="oFiles.name" class="file-name">
                    <i class="el-icon-document" />
                    <span>{{ oFiles.name }}</span>
                </div>
            </div>
            <div class="remarks">
                {{ $t('systemManagement.messageTemplate.reMark') }}
            </div>
        </span>
        <span slot="footer">
            <el-button @click="isShow = false">{{
        $t("table.cancel")
      }}</el-button>
            <el-button type="primary" @click="importUser">{{
        $t("table.confirm")
      }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
import ExcelExport from '../../../mixins/excel-export';

export default {
    mixins: [ExcelExport],
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            oFiles: { name: '' }
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        }
    },
    methods: {
        // 导入
        handleUpload() {
            const oFiles = document.querySelector('#chooseUser').files;
            this.oFiles = oFiles[0];
        },
        // 下载导入模板
        downloadUserTemplate() {
            this.$service.systemManagement.downTemplate().then((response) => {
                this.download(
                    response,
                    `${this.$t(
                        'systemManagement.messageTemplate.messageTemplate'
                    )}.xlsx`
                );
            });
        },
        // 用户导入
        async importUser() {
            const formData = new FormData();
            formData.append('file', this.oFiles);
            this.isDownResult = false;
            // 上传文件
            await this.$service.systemManagement
                .templateImport(formData)
                .then((res) => {
                    //  如果body为null，则提示所有用户信息导入成功，下载失败的结果
                    if (res.head.code === '000000') {
                        this.isShow = false;
                        this.$message({
                            message: this.$t(
                                'systemManagement.messageTemplate.operation.operateSuccess'
                            ),
                            type: 'success'
                        });
                        // 触发父组件方法刷新列表
                        this.$emit('refreshList');
                    } else if (res.head.code === '111111') {
                        this.downResultList = res.body;
                        this.isDownResult = true;
                        this.isShow = false;
                        const msg = `frame.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    } else if (res.head.code === '950222') {
                        // 导入的用户数量大于1000条
                        this.$alert(
                            `${this.$t(
                                'systemManagement.messageTemplate.exceedMax'
                            )}\n${this.$t(
                                'systemManagement.messageTemplate.exceedMaxTip'
                            )}`,
                            this.$t(
                                'systemManagement.messageTemplate.importTip'
                            ),
                            {
                                type: 'warning',
                                customClass: 'message_box_alert'
                            }
                        ).then(() => {
                            // 弹出提示
                        });
                        this.isShow = false;
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 格式化JSON
        formatJson(list, filterVal) {
            return list.map((v) =>
                filterVal.map((j) => {
                    if (j === 'timestamp') {
                        return this.$tools.parseTime(v[j]);
                    }
                    return v[j];
                })
            );
        }
    }
};
</script>

<style lang="scss" scoped>
#chooseUser {
    display: none;
}
.import-title {
    margin-top: 26px;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    .el-icon-warning {
        color: #1890ff;
        margin-right: 5px;
        font-size: 18px !important;
    }
    .import-box {
        display: flex;
        align-items: center;
        .download-template {
            margin: 0 5px;
            span {
                font-weight: bold;
            }
        }
    }
}
.import-contnent {
    color: #000;
    display: flex;
    align-items: center;
    .file-name {
        margin-left: 10px;
    }
}
.remarks {
    color: #000;
    margin-top: 15px;
    font-size: 12px;
}
</style>

