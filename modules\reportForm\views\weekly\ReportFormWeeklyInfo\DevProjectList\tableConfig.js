import { getStatusArray } from './tableHeader';

const getTableConfig = (scope) => {
    return {
        // 查询api配置
        queryApi: scope.$service.reportForm.weekly.getDevelopProjectList,
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: getStatusArray('1', scope),
        // 头部按钮
        headerButtons: [],
        // 操作列
        operationColumnWidth: 80,
        operations: [],
        // 分页
        hasPage: false,
        // 固定表头
        elTableAttrs: {
            'height': 'calc(100vh - 120px)',
            'header-cell-style': '{text-align:center}'
        },
        hooks: {
            sortChangeHook: scope.sortChangeHook
        }
    };
};
export { getTableConfig };
