import { CONSTANTS } from '@/constants.js';
import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange } = CommonItems;
// 任务状态
const taskStatus = {
    ...select,
    name: '任务状态',
    modelKey: 'taskStatus',
    elOptions: CONSTANTS.TASK_STATUS.map((item) => ({
        label: item,
        value: item
    }))
};

// 任务计划完成时间
const taskPlanTime = {
    ...dateRange,
    name: '任务计划完成时间',
    modelKey: 'daterange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};

// 会议要求关键字
const meetingRequire = {
    ...input,
    name: '会议要求',
    modelKey: 'meetingRequire'
};

// 查询参数初始化
export const queryParams = {
    projectId: '',
    taskStatus: '未完成',
    daterange: [],
    meetingRequire: '',
    type: 4
};

// 关联项目（需要动态配置）
export const createProjectSelect = (searchOptions, remoteMethod) => ({
    ...select,
    name: '关联项目',
    modelKey: 'projectId',
    elSelectAttrs: {
        'placeholder': '关联项目',
        'clearable': true,
        'remote': true,
        'filterable': true,
        'remote-method': remoteMethod,
        'style': 'width:100%'
    },
    elOptions: searchOptions.map((item) => ({
        label: item.projectName,
        value: item.projectId
    }))
});

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '140px'
    },
    items: [taskStatus, taskPlanTime, meetingRequire]
};

// 导航栏配置项
export const navItems = [
    { field: '', name: '所有', queryField: 'type' },
    { field: 1, name: '我组织的', queryField: 'type' },
    { field: 2, name: '我参加的', queryField: 'type' },
    { field: 3, name: '我提出的', queryField: 'type' },
    { field: 4, name: '责任人是我的', queryField: 'type' }
];
