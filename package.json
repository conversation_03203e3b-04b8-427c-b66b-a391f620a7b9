{"name": "wtf-frame", "version": "0.0.1", "private": false, "author": "xue<PERSON>o", "homepage": "", "scripts": {"analyzer": "use_analyzer=true npm run build", "dev": "vue-cli-service serve --host 127.0.0.1", "lint": "eslint --fix --ext .js,.vue src/ modules/", "build": "vue-cli-service build", "build:module": "vue-cli-service build --target lib modules/icons/module_index.js", "build:test": "vue-cli-service build --mode test --target lib src/module_index.js", "new": "plop", "new:module": "plop module", "new:view": "plop view", "prepare": "husky install"}, "main": "dist/frame.umd.js", "license": "MIT", "repository": {"type": "git", "url": "http://*************/bbpf-platform-web/si-wtf-frame"}, "dependencies": {"@antv/g2plot": "^2.4.32", "@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "@opd/g2plot-vue": "^3.6.7", "@snbc/tools": "^1.0.0", "@vue/cli-plugin-babel": "^4.4.4", "@vue/composition-api": "^1.7.2", "core-js": "3.6.5", "echarts": "^5.5.1", "file-saver": "^2.0.5", "html2pdf.js": "^0.10.2", "moment": "^2.29.4", "qrcodejs2": "0.0.2", "quill": "^1.3.7", "uuid": "^8.3.2", "vue-cropper": "^0.5.8", "vue-demi": "^0.14.10", "vue-infinite-loading": "^2.4.5", "vue-organization-chart": "^1.1.6", "vue-quill-editor": "^3.0.6", "vuex": "3.1.0", "wtf-core-vue": "^2.3.6", "xlsx": "^0.17.4"}, "devDependencies": {"@babel/core": "^7.22.1", "@babel/eslint-parser": "^7.21.8", "@commitlint/cli": "^8.0.0", "@commitlint/config-conventional": "^8.0.0", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-service": "4.4.4", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-loader": "^8.0.5", "babel-plugin-dynamic-import-node": "2.3.3", "babel-polyfill": "^6.26.0", "code-inspector-plugin": "^0.20.1", "eslint": "^6.7.2", "eslint-config-prettier": "^8.8.0", "eslint-config-snbc": "^1.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-vue": "^6.2.2", "highlight.js": "^9.18.5", "html-webpack-plugin": "3.2.0", "husky": "^8.0.0", "lint-staged": "8.1.5", "plop": "^2.3.0", "postcss-preset-env": "^6.6.0", "prettier": "^2.8.8", "prettier-config-snbc": "^1.0.0", "runjs": "4.3.2", "sass": "1.39.0", "sass-loader": "10.1.0", "script-loader": "^0.7.2", "svg-sprite-loader": "4.1.3", "vue-eslint-parser": "^9.3.0", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.4.0"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "element-ui", "boilerplate", "admin-template", "management-system", "wtf-core-vue"]}