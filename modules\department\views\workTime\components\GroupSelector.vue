<template>
    <el-cascader
        class="selector"
        placeholder="请选择小组"
        @change="handleChange"
        :props="props"
        ref="cascaderRef"
        popper-class="topselector-cascader"
    >
        <div slot-scope="{ data }" @click="clickNode" class="span-click">
            {{ data.label }}
        </div>
    </el-cascader>
</template>
<script>
import { CONSTANTS } from '@/constants';

const { TECH_CENTER_CODE } = CONSTANTS;

export default {
    name: 'GroupSelector',
    props: {
        status: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            props: {
                // 配置动态加载
                lazy: true,
                lazyLoad: this.lazyLoadHandler,
                // 是否可以直接选中父节点
                checkStrictly: true,
                // 配置展开方式
                expandTrigger: 'hover',
                // 悬停状态保持时间，小于这个时间不会触发hover事件
                hoverThreshold: 150
            },
            // 获取部门信息的接口(无权限)
            getSubDepartmentListApi:
                this.$service.department.group.getAllSubDepartment,
            // 获取小组信息的接口
            getTeamListApi: this.$service.department.group.getGroupInfo
        };
    },
    computed: {},
    mounted() {
        // 挂载后向外传递ref
        this.$nextTick(() => {
            this.$emit('cascaderRef', this.$refs.cascaderRef);
        });
    },
    methods: {
        /**
         * 通过点击文字选中的处理函数
         * @param {Object} e 事件对象
         */
        clickNode(e) {
            // 模拟点击对应的radio
            e.target.parentElement.parentElement.firstElementChild.click();
        },
        /**
         * 选择的值发生变化时触发
         * @param {Array} value 选择的值
         */
        handleChange(value) {
            // 向父组件传递选中的值
            this.$emit('input', value);
            // 每次选择结束之后自动关闭
            if (this.$refs?.cascaderRef?.dropDownVisible) {
                this.$refs.cascaderRef.dropDownVisible = false;
            }
        },
        /**
         * 处理节点懒加载
         * @param {Object} node 当前节点
         * @param {Function} resolve 加载成功后的回调
         * @return {Function} 加载成功后的回调
         */
        lazyLoadHandler(node, resolve) {
            const { level, value } = node;
            // 确定菜单最大长度
            let maxLevel = 3;
            if (this.teamDisabled) {
                maxLevel = 2;
            }
            if (level >= maxLevel) {
                return resolve();
            }
            // 动态节点
            let nodes = [];
            let params = '';
            let api = this.getSubDepartmentListApi;
            if (level === 0) {
                // 第一级菜单默认查技术中心的orgCode
                params = { orgCode: TECH_CENTER_CODE };
            } else if (level === 1) {
                params = { orgCode: value };
            } else {
                // 第三级菜单替换为查询小组的接口
                api = this.getTeamListApi;
                params = { departmentCode: value };
            }
            api(params)
                .then((res) => {
                    if (res.head.code !== '000000') {
                        this.$message.error(res.head.message);
                        return;
                    }
                    // 构建节点数组
                    nodes = res.body.map((item) => {
                        let obj;
                        if (params?.orgCode) {
                            obj = {
                                value: item.orgCode,
                                label: item.orgName,
                                leaf: level >= maxLevel - 1,
                                disabled: true
                            };
                        } else {
                            obj = {
                                value: item.id,
                                label: item.teamName,
                                leaf: level >= maxLevel - 1
                            };
                        }
                        return obj;
                    });
                    // 加载成功，返回数据
                    resolve(nodes);
                })
                .catch((error) => {
                    console.error('Error:', error);
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.selector {
    margin: 0 10px;
    flex: 1;
    width: 350px;
    ::v-deep .el-input--mini .el-input__inner {
        line-height: 34px;
        height: 34px;
        font-weight: 400;
    }
}
// 利用placeholder进行数据回显，修改字体颜色
::v-deep .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685) !important;
}
.span-click {
    width: 100%;
}
</style>
<style lang="scss">
// 隐藏单选框
.el-cascader-panel .el-radio__input {
    display: none;
}
.el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
}
.topselector-cascader .el-cascader-menu__wrap {
    height: 340px;
}
</style>
