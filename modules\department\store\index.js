/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.department.xxx
 */

const state = {
    // 顶部级联组件部门的值
    departmentStore: []
};

const mutations = {
    CHANGE_DEPARTMENT(state, department) {
        state.departmentStore = department;
    }
};

const actions = {
    changeDepartment({ commit }, department) {
        commit('CHANGE_DEPARTMENT', department);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
