<template>
  <el-dialog :title="title" :visible.sync="isShow">
    <el-form
      ref="dataForm"
      :model="formDataInfo"
      :rules="rules"
      :inline="false"
      label-width="auto"
      size="normal"
    >
      <el-form-item
        :label="`${$t(
          'systemManagement.dictionary.columnName.typeName'
        )}`"
        prop="typeName"
      >
        <el-input
          v-model="formDataInfo.typeName"
          type="text"
          :placeholder="
            $t(
              'systemManagement.dictionary.message.placeholderPre'
            ) +
              $t('systemManagement.dictionary.columnName.typeName')
          "
          maxlength="20"
        />
      </el-form-item>
      <el-form-item
        :label="`${$t(
          'systemManagement.dictionary.columnName.typeCode'
        )}`"
        prop="typeCode"
      >
        <el-input
          v-model="formDataInfo.typeCode"
          :disabled="isEdit"
          :placeholder="
            $t(
              'systemManagement.dictionary.message.placeholderPre'
            ) +
              $t('systemManagement.dictionary.columnName.typeCode')
          "
          maxlength="40"
        />
      </el-form-item>
    </el-form>

    <span slot="footer">
      <el-button @click="cancel">{{
        $t("systemManagement.dictionary.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">{{
        $t("systemManagement.dictionary.save")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import userInfo from '../../../mixins/list-edit-info';

export default {
    name: 'DictTypeListEditInfo',
    mixins: [userInfo],
    props: {
        // 当前表单数据
        formData: {
            type: Object,
            default() {
                return {
                    typeName: '',
                    typeCode: ''
                };
            }
        }
    },
    data() {
        return {
        };
    },
    computed: {
        // 这里的title是根据编辑、新增的标识进行计算的
        title() {
            if (this.isEdit) {
                return this.$t('systemManagement.dictionary.editType');
            } 
                return this.$t('systemManagement.dictionary.addType');
            
        },
        // 是否编辑模式
        isEdit: {
            get() {
                return this.edit;
            }
        },
        // 表单验证规则
        rules() {
            return {
                typeName: [
                    {
                        required: true,
                        message:
							this.$t('systemManagement.dictionary.message.placeholderPre'
							) +
							this.$t('systemManagement.dictionary.columnName.typeName'
							),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,20}$/,
                        message: this.$t(
                            'systemManagement.dictionary.message.ruleName'
                        ),
                        trigger: ['blur', 'change']
                    }
                ],
                typeCode: [
                    {
                        required: true,
                        message:
						this.$t('systemManagement.dictionary.message.placeholderPre'
						) +
							this.$t('systemManagement.dictionary.columnName.typeCode'
							),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[0-9a-zA-Z-_]{1,40}$/,
                        message: this.$t(
                            'systemManagement.dictionary.message.ruleCode'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    methods: {
        // 调用接口保存数据
        postSave() {
            this.$service.systemManagement
                .postSaveDictionaryType({
                    dataInfo: this.formDataInfo,
                    // 这里通过标识调用新增、更新的接口
                    isUpdate: this.isEdit ? 1 : 0
                })
                .then((response) => {
                    // 调用mixins的方法，进行结果处理
                    this.doResponse(response);
                });
        }
    }
};
</script>
