<template>
    <div>
        <el-button
            type="primary"
            @click="handleClick"
            class="add-button"
            v-permission="['businessSectorProjectUpdate']"
            >删除项目</el-button
        >
        <el-dialog
            title="删除项目"
            :visible.sync="dialogVisible"
            :before-close="dialogBeforeClose"
            class="add-project-dialog"
        >
            <el-form :model="form" ref="form">
                <el-form-item label="业务板块">
                    {{ businessUnit }}
                </el-form-item>
                <el-form-item label="产品系列" prop="productSet">
                    <el-select
                        v-model="form.productSet"
                        placeholder="请选择产品系列"
                        @change="handleProductSetChange"
                    >
                        <el-option
                            v-for="item in productTypeOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="产品分类" prop="productType">
                    <el-select
                        v-model="form.productType"
                        placeholder="请选择产品分类"
                        filterable
                        @change="
                            () => {
                                this.form.projectNumber = '';
                            }
                        "
                    >
                        <el-option
                            v-for="item in productClassOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="项目"
                    prop="projectNumber"
                    :rules="required"
                >
                    <el-select
                        v-model="form.projectNumber"
                        placeholder="请选择项目"
                        class="project"
                        filterable
                    >
                        <el-option
                            v-for="item in projectList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="handleCancel">取 消</el-button>
                <el-button type="primary" @click="deleteProject"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';

const required = [
    {
        required: true,
        message: '请选择项目',
        trigger: ['change', 'blur']
    }
];
const form = {
    // 产品系列
    productSet: '',
    // 产品分类
    productType: '',
    // 项目编号
    projectNumber: ''
};
export default {
    name: 'DeleteNewRetails',
    components: {},
    props: {
        businessUnit: {
            type: String,
            default: ''
        },
        productData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            CONSTANTS,
            dialogVisible: false,
            form: this.$tools.cloneDeep(form),
            financeScene: [],
            sortingScene: [],
            keyModulesOptions: [],
            // 项目编号与项目列表组成的映射
            projectListMap: [],
            // 金融机具--产品系列为核心模块时，有些东西需要隐藏
            showSceneAndKeyModule: true,
            required,
            productTypeOptions: [],
            productClassOptions: []
        };
    },
    computed: {
        // 筛选出来的项目列表
        projectList() {
            // 产品系列和都不填的情况
            if (!this.form.productSet && !this.form.productType) {
                const allList = [];
                this.productData.forEach((item) => {
                    item.productTypeVos.forEach((i) => {
                        i.businessProjectVos.forEach((j) => {
                            allList.push({
                                label: j.projectName,
                                value: j.projectNumber
                            });
                        });
                    });
                });
                return this.$tools.uniqueBy(allList, 'value');
            }

            const productList = this.productData.filter(
                (i) => i.productSet === this.form.productSet
            );
            if (!productList || productList.length === 0) return [];

            // 只填了产品系列的情况
            if (productList.length > 0 && !this.form.productType) {
                const wholeList = [];
                productList[0].productTypeVos.forEach((i) => {
                    i.businessProjectVos.forEach((j) => {
                        wholeList.push({
                            label: j.projectName,
                            value: j.projectNumber
                        });
                    });
                });
                return wholeList;
            }
            const productTypeList = productList[0].productTypeVos.filter(
                (i) => i.productType === this.form.productType
            );
            if (!productTypeList || productTypeList.length === 0) {
                return [];
            }
            const list = [];
            productTypeList[0].businessProjectVos.forEach((j) => {
                list.push({
                    label: j.projectName,
                    value: j.projectNumber
                });
            });
            return list;
        }
    },
    mounted() {
        this.getproductLine();
    },
    methods: {
        // 查询产品系列
        async getproductLine() {
            try {
                const params = {
                    paramName: '产品系列',
                    paramType: this.businessUnit
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productTypeOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        // 根据产品系列查询产品分类
        async getproductType() {
            try {
                const params = {
                    paramName: this.businessUnit,
                    paramType: this.form.productSet
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productClassOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        /**
         * 产品系列变化
         */
        handleProductSetChange() {
            this.form.productType = '';
            this.form.projectNumber = '';
            this.getproductType();
        },
        /**
         * 关闭弹窗前的处理
         * @param {Function} done 完成后的函数
         */
        dialogBeforeClose(done) {
            this.resetForm();
            this.dialogVisible = false;
            done();
        },

        /**
         * 点击
         */
        handleClick() {
            this.dialogVisible = true;
        },
        /**
         * 删除项目
         */
        async deleteProject() {
            const valid = this.validateForm();
            if (!valid) {
                return;
            }
            const api = this.$service.scene.deleteProject;
            try {
                const params = this.form.projectNumber;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('删除成功');
                const count = await this.$store.state.scene
                    .changeProjectCountSuccess;
                this.$store.dispatch(
                    'scene/changeProjectCountSuccess',
                    count + 1
                );
                this.$emit('update');
                this.resetForm();
                this.dialogVisible = false;
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 取消时的操作
         */
        handleCancel() {
            this.resetForm();
            this.dialogVisible = false;
        },
        /**
         * 表单验证
         * @returns {Boolean} 是否通过校验
         */
        validateForm() {
            let res = false;
            this.$refs.form.validate((valid) => {
                if (valid) {
                    res = true;
                }
            });
            return res;
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.$refs.form.resetFields();
        }
    }
};
</script>

<style lang="scss" scoped>
.add-button {
    position: absolute;
    z-index: 100;
    top: 15px;
    right: 80px;
}
.add-project-dialog {
    ::v-deep .el-form-item__label {
        font-weight: 700;
    }
    ::v-deep .el-select {
        width: 100%;
    }
}
</style>
