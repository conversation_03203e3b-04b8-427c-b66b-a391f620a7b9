<template>
    <!-- tab切换 -->
    <div class="box-tabs">
        <div class="tabs-top">
            <div
                class="crad"
                v-for="(item, index) in cardArr"
                :key="index"
                @click="handleTabClick(index, item.componentId)"
            >
                <span :class="{ highLight: whichIndex == index }">{{
                    item.componentName
                }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        whichIndex: Number,
        componentId: String,
        cardArr: Array
    },
    data() {
        return {};
    },
    methods: {
        handleTabClick(index, componentId) {
            this.$emit('tab-click', index, componentId);
        }
    }
};
</script>

<style lang="scss" scoped>
.box-tabs {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    .tabs-top {
        width: 600px;
        display: flex;
        border-radius: 138px;
        background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0) 100%
        );
        border: 1px solid rgba(255, 255, 255, 1);
    }

    .crad {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: clamp(1.5rem, 0.9rem + 1.05vw, 1.5rem);
        font-weight: 500;
        color: rgba(34, 34, 34, 1);
        position: relative;

        &:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            width: 2px;
            height: 30%;
            background-color: #a5a8ab;
        }
    }
    .highLight {
        width: 85%;
        height: 80%;
        border-radius: 113px;
        background: rgba(54, 117, 255, 1);
        box-shadow: inset 0px 4px 4px rgba(255, 255, 255, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: clamp(1.5rem, 0.9rem + 1.05vw, 1.5rem);
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
    }
}
</style>
