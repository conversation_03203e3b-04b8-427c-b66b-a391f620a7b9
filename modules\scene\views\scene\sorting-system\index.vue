<template>
    <div class="page-view" id="scene-view" @scroll="onScroll">
        <MaintenanceProductVue businessUnit="物流分拣"></MaintenanceProductVue>
        <AddProjectByScene businessUnit="物流分拣"></AddProjectByScene>
        <DeleteProject
            businessUnit="物流分拣"
            :productData="productData"
        ></DeleteProject>
        <nav-tabs-vue
            class="tab-view"
            :which-index="whichIndex"
            :component-id="componentId"
            :cardArr="cardArr"
            @tab-click="handleTabClick"
        ></nav-tabs-vue>
        <component :is="componentId"></component>

        <el-button type="primary" @click="goback" class="goback-button"
            >返回</el-button
        >
    </div>
</template>
<script>
import NavTabsVue from '../components/NavTabs.vue';
import ProductOverview from './components/ProductOverview.vue';
import SortingCenterPage from './components/SortingCenterPage.vue';
import CountyBureauPage from './components/CountyBureauPage.vue';
import AddProjectByScene from 'scene/views/scene/components/AddProjectByScene';
import DeleteProject from 'scene/views/scene/components/DeleteProject.vue';
import MaintenanceProductVue from 'scene/views/scene/components/maintenanceProduct.vue';

export default {
    name: 'SortingSystem',
    components: {
        NavTabsVue,
        ProductOverview,
        SortingCenterPage,
        CountyBureauPage,
        AddProjectByScene,
        DeleteProject,
        MaintenanceProductVue
    },
    data() {
        return {
            whichIndex: 0,
            componentId: 'ProductOverview',
            cardArr: [
                {
                    componentName: '产品综述',
                    componentId: 'ProductOverview'
                },
                {
                    componentName: '分拣中心',
                    componentId: 'SortingCenterPage'
                },
                {
                    componentName: '县级局',
                    componentId: 'CountyBureauPage'
                }
            ]
        };
    },
    computed: {
        productData() {
            return this.$store.state.scene.sortingSystemProductStore;
        }
    },
    async mounted() {
        // 缓存页面
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    methods: {
        handleTabClick(index, componentId) {
            this.whichIndex = index;
            this.componentId = componentId;
        },
        goback() {
            this.$router.push('/dashboard-index');
        },
        /**
         * 滚动时清除弹窗
         */
        onScroll() {
            document
                .querySelectorAll('.resources-sceneNode--popper')
                .forEach((el) => el.remove());
            document
                .querySelectorAll('.resources-product-chart--popper')
                .forEach((el) => el.remove());
        }
    }
};
</script>
<style lang="scss" scoped>
.page-view {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    transform-origin: 0 0;
    background-color: #f3f5fc;
}
.tab-view {
    position: absolute;
    top: 10px;
    z-index: 1;
    cursor: pointer;
}
.goback-button {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}
</style>
