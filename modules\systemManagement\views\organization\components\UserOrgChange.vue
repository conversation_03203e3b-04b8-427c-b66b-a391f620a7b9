<template>
    <el-dialog
        :modal="showModal"
        :title="$t('systemManagement.organization.dialog.organization')"
        :visible.sync="isShow"
        custom-class="transfer-dialog org-dialog"
    >
        <el-card>
            <div slot="header">
                {{ $t('systemManagement.organization.dialog.organization') }}
            </div>
            <el-input
                v-model="filterTextCheck"
                maxlength="20"
                :placeholder="
                    $t('systemManagement.organization.dialog.placeholder.msg7')
                "
                for="msg7"
            >
                <i slot="suffix" class="el-input__icon el-icon-search" />
            </el-input>
            <el-tree
                ref="changeOrgTree"
                for="changeOrgTree"
                :data="treeSourceData"
                default-expand-all
                :default-checked-keys="treeDefaultCheckedKeys"
                check-strictly
                :props="defaultProps"
                node-key="orgId"
                :expand-on-click-node="false"
                check-on-click-node
                :filter-node-method="filterOrgCheck"
                @check="getCheckedNodes"
            />
        </el-card>
        <el-card>
            <div class="header">
                <span class="title">{{
                    $t('systemManagement.role.message.currentSelectRoleCount', [
                        checkOrgList.length
                    ])
                }}</span>
                <i
                    v-show="hasSelected"
                    class="el-icon-error text-close-all"
                    v-if="canRemoveList.length > 0"
                    @click.stop="removeAll()"
                />
            </div>
            <div class="list">
                <div
                    v-for="item in checkOrgListFinal"
                    :key="item.id"
                    class="block"
                    for="checkOrgList"
                >
                    <span class="block__text">{{ item.orgName }}</span>
                    <span class="block__btns">
                        <i
                            class="el-icon-error"
                            v-if="item.canRemove !== 'false'"
                            @click.stop="removeTargetOrg(item)"
                        />
                    </span>
                </div>
            </div>
        </el-card>
        <span slot="footer">
            <el-button @click="isShow = false">{{
                $t('table.cancel')
            }}</el-button>
            <el-button type="primary" @click="confirm">{{
                $t('table.confirm')
            }}</el-button>
        </span>
    </el-dialog>
</template>
<script>
export default {
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        },
        // 组织机构源数据
        orgSourceData: {
            type: Array,
            default() {
                return [];
            }
        },
        // 当前表单数据
        selection: {
            type: Array,
            default() {
                return [];
            }
        },
        // 不能移除的部门
        reserveOrgs: {
            type: Array,
            default() {
                return [];
            }
        },
        showModal: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            treeData: [],
            filterTextCheck: '',
            defaultProps: {
                children: 'children',
                label: 'orgName'
            },
            // 当前已选的数据列表
            checkOrgList: [],
            // 是否有选中的
            hasSelected: false,
            // 可移除的数据列表
            canRemoveList: []
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        treeSourceData() {
            const data = this.orgSourceData[0];
            data.disabled = true;
            return [data];
        },
        // 当前选中的列表数据，用于管理prop输入属性
        selectionData: {
            get() {
                return this.selection;
            },
            set(val) {
                this.$emit('update:selection', val);
            }
        },
        // 组织机构树默认选中项
        treeDefaultCheckedKeys() {
            return this.selectionData.map((item) => item.orgId);
        },
        // 展示在右侧的已选部门
        checkOrgListFinal() {
            const arrObj = this.checkOrgList.concat(this.reserveOrgs);
            // 数组对象去重
            const map = new Map();
            for (const item of arrObj) {
                if (!map.has(item.orgId)) {
                    map.set(item.orgId, item);
                }
            }
            const filterArr = [...map.values()];
            return filterArr;
        }
    },
    watch: {
        filterTextCheck(val) {
            this.$refs.changeOrgTree.filter((val || '').trim());
        },
        checkOrgList: {
            handler(newVal, oldVal) {
                if (newVal.length > 0) {
                    this.hasSelected = true;
                } else {
                    this.hasSelected = false;
                }
            }
        }
    },
    created() {
        this.checkOrgList = this.selectionData;
    },
    methods: {
        // 获取机构树
        getOrgTree() {
            const data = {
                // 返回的组织机构层级 0：全部 1：根节点下一级 2：根节点下两级
                orgLevel: 0
                // orgId: 0    // 组织机构id
            };
            this.$service.systemManagement.getOrgTree(data).then((res) => {
                if (res.head.code === '000000') {
                    this.treeData = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        // 树的过滤
        filterOrgCheck(value, data) {
            if (!value) {
                return true;
            }
            return data.orgName.indexOf(value) !== -1;
        },
        // 多用户调整组织机构 - check树节点
        getCheckedNodes(data, check) {
            const orgIds = this.reserveOrgs.map((item) => {
                return item.orgId;
            });
            this.checkOrgList = check.checkedNodes.filter((item) => {
                return orgIds.indexOf(item.orgId) === -1;
            });
            this.checkOrgList = this.checkOrgList.concat(this.reserveOrgs);
        },
        // 多用户 - 调整部门 - 确认
        confirm() {
            this.selectionData = this.checkOrgList;
            this.$emit('confirm-selection', this.checkOrgList);
        },
        // 移除已选中角色
        removeTargetOrg(data) {
            this.checkOrgList = this.checkOrgList.filter((item) => {
                return item.orgId !== data.orgId;
            });
            this.$refs.changeOrgTree.setChecked(data, false);
        },
        /**
         * 移除全部已选择用户
         */
        removeAll() {
            this.canRemoveList = this.checkOrgList.filter((item) => {
                return item.canRemove !== 'false';
            });
            if (this.canRemoveList.length > 0) {
                this.canRemoveList.forEach((data) => {
                    this.$refs.changeOrgTree.setChecked(data, false);
                });
                this.checkOrgList = [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.org-dialog {
    .header {
        .text-close-all {
            font-size: 15px;
            color: #8996a8;
            cursor: pointer;
        }
    }
    .list {
        .block {
            background: #eaf0ff;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 14px;
            .block__text {
                margin-left: 11px;
            }
            .block__btns {
                .el-icon-error {
                    color: #c7c7c7;
                }
            }
        }
    }
}
::v-deep.org-dialog {
    .el-tree {
        /*控制最顶级的选中状态 */
        & > .el-tree-node.is-expanded {
            .el-tree-node__content {
                background: transparent !important;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        }

        & > .el-tree-node.is-checked {
            & > .el-tree-node__content {
                background: #eaf0ff !important;
            }
        }
        /*控制子节点的选中状态 */
        .el-tree-node.is-expanded {
            .el-tree-node__children {
                & > .el-tree-node {
                    border-radius: 5px;
                    margin-bottom: 5px;
                }
                & > .is-checked {
                    & > .el-tree-node__content {
                        background: #eaf0ff !important;
                    }
                }
            }
        }
    }
}
</style>
