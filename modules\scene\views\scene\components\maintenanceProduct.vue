<template>
    <div>
        <el-button
            type="primary"
            @click="dialogVisible = true"
            class="add-button"
            v-permission="['businessSectorProjectUpdate']"
            >新增产品</el-button
        >
        <el-dialog
            title="新增产品"
            :visible.sync="dialogVisible"
            :before-close="dialogBeforeClose"
            class="add-project-dialog"
        >
            <el-form :model="form" ref="form">
                <el-form-item
                    label="产品系列"
                    prop="productSet"
                    :rules="required"
                >
                    <el-select
                        v-model="form.productSet"
                        placeholder="请选择产品系列"
                        @change="handleProductSetChange"
                    >
                        <el-option
                            v-for="item in productTypeOptions"
                            :key="item.paramCode"
                            :label="item.paramName"
                            :value="item.paramCode"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <div class="box-line">
                    <el-form-item
                        label="产品分类"
                        prop="productClassification"
                        :rules="required"
                    >
                        <el-input
                            v-model="form.productClassification"
                            placeholder="请输入产品分类"
                            maxlength="30"
                        />
                    </el-form-item>
                    <el-button
                        type="primary"
                        @click="addProject"
                        class="box-btn"
                        >保 存</el-button
                    >
                </div>
            </el-form>
            <el-table :data="tableList" style="width: 100%" border height="300">
                <el-table-column
                    label="序号"
                    type="index"
                    align="center"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    prop="paramName"
                    label="产品分类"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <div slot="footer">
                <el-button @click="handleCancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';

const required = [
    {
        required: true,
        message: ' ',
        trigger: ['change', 'blur']
    }
];
const form = {
    productSet: '',
    productClassification: ''
};
export default {
    name: 'MaintenanceProduct',
    components: {},
    props: {
        businessUnit: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            CONSTANTS,
            dialogVisible: false,
            form: this.$tools.cloneDeep(form),
            required,
            productTypeOptions: [],
            tableList: []
        };
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getproductLine();
            }
        }
    },
    methods: {
        // 查询产品系列
        async getproductLine() {
            try {
                const params = {
                    paramName: '产品系列',
                    paramType: this.businessUnit
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.productTypeOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        /**
         * 关闭弹窗前的处理
         * @param {Function} done 完成后的函数
         */
        dialogBeforeClose(done) {
            this.resetForm();
            this.dialogVisible = false;
            done();
        },
        /**
         * 产品系列变化
         */
        handleProductSetChange() {
            this.form.productClassification = '';
            this.getproductData();
        },
        /**
         * 新增项目
         */
        async addProject() {
            const valid = this.validateForm();
            if (!valid) {
                this.$message.error('请输入所有必填项');
                return;
            }
            const api = this.$service.scene.getmaintenanceData;
            try {
                const params = {
                    ...this.form,
                    productUnit: this.businessUnit
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success(res.head.message);
                this.getproductData();
            } catch (error) {
                console.error(error);
            }
        },
        // 查询项目列表
        async getproductData() {
            try {
                const params = {
                    paramName: this.businessUnit,
                    paramType: this.form.productSet
                };
                const res = await this.$service.scene.getproductLine(params);
                if (res.head.code === '000000') {
                    this.tableList = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        /**
         * 取消时的操作
         */
        handleCancel() {
            this.resetForm();
            this.dialogVisible = false;
        },
        /**
         * 表单验证
         * @returns {Boolean} 是否通过校验
         */
        validateForm() {
            let res = false;
            this.$refs.form.validate((valid) => {
                if (valid) {
                    res = true;
                }
            });
            return res;
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.$refs.form.resetFields();
            this.tableList = [];
        }
    }
};
</script>

<style lang="scss" scoped>
.add-button {
    position: absolute;
    z-index: 100;
    top: 15px;
    right: 260px;
}
.add-project-dialog {
    ::v-deep .el-form-item__label {
        font-weight: 700;
    }
    ::v-deep .el-select {
        width: 100%;
    }
    ::v-deep .el-input {
        width: 300px !important;
    }
}
.box-line {
    width: 100%;
    display: flex;
    justify-content: flex-start;
}
.box-btn {
    height: 40px;
    margin-left: 20px;
}
</style>
