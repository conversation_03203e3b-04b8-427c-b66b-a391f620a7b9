<template>
    <div class="maintenance-project-list-container">
        <el-button type="primary" class="go-back" @click="goBack()"
            >返回</el-button
        >
        <el-tabs>
            <el-tab-pane :label="`进行中(${projectNumbers})`"></el-tab-pane>
        </el-tabs>
        <el-table
            :data="tableData"
            class="maintenance-table"
            border
            :cell-style="{ border: '1px solid #8c8c8c!important' }"
            :header-cell-style="{ border: '1px solid #8c8c8c' }"
        >
            <el-table-column
                prop="index"
                label="序号"
                align="center"
                width="100"
            ></el-table-column>
            <el-table-column label="项目列表" header-align="center">
                <template slot-scope="scope">
                    <el-link
                        v-if="hasProjectLinkAccess"
                        type="primary"
                        @click="goDetail(scope.row)"
                        >{{ scope.row.projectName }}</el-link
                    >
                    <span v-else>{{ scope.row.projectName }}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'MaintenanceProjectList',
    props: {},
    data() {
        return { tableData: [], projectNumbers: 0 };
    },
    computed: {
        hasProjectLinkAccess() {
            return this.$store.state.permission.btnDatas.includes(
                'maintenanceProjectLinkAccess'
            );
        }
    },
    mounted() {
        this.getProjectList();
    },
    methods: {
        /**
         * 返回
         */
        goBack() {
            this.$router.back();
        },
        /**
         * 点击跳转
         * @param {Object} row 每行的数据
         */
        goDetail(row) {
            this.$store.dispatch(
                'maintenanceProject/changeMaintenanceProject',
                [row.id]
            );
            this.$router.push({
                path: '/maintenanceProject/maintenanceResource'
            });
        },
        /**
         * 获取列表数据
         */
        async getProjectList() {
            const api = this.$service.project.finance.getSelectOptions;
            try {
                const res = await api({
                    paramName: '产品线',
                    paramType: ''
                });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.tableData = this.handleProjectList(res.body);
                this.projectNumbers = res.body.length;
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 拼接项目列表
         * @param {Object} projectList 项目列表
         * @returns {Array} 处理后的项目列表
         */
        handleProjectList(projectList) {
            return projectList.map((item, i) => {
                return {
                    index: i + 1,
                    projectName: `${item.paramName}线维护项目`,
                    id: item.paramName
                };
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.maintenance-project-list-container {
    padding: 20px;
    position: relative;
}
.maintenance-table {
    margin-top: 20px;
    border: 1px solid #8c8c8c !important;
}
.go-back {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep .el-table .el-table__row {
    height: 35px !important;
}
</style>
