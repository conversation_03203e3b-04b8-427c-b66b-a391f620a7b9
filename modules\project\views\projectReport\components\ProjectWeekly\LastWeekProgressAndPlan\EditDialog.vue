<template>
    <div class="edit-task">
        <el-dialog
            title="编辑上周进展与本周计划"
            :visible.sync="dialogVisible"
            width="90%"
        >
            <el-table
                ref="planTable"
                :data="tableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                :row-key="getRowKey"
            >
                <el-table-column
                    type="selection"
                    width="40"
                    header-align="center"
                    :selectable="(row) => isProjectManagerOrPQA"
                    :reserve-selection="true"
                ></el-table-column>
                <!-- 模块 -->
                <el-table-column
                    prop="module"
                    label="模块"
                    align="center"
                    width="110"
                >
                    <template slot-scope="scope">
                        <el-input
                            v-if="scope.row.creationType === '手动'"
                            v-model="scope.row.module"
                            maxlength="6"
                            autoresize
                            placeholder="请输入模块"
                            :clearable="false"
                            :disabled="
                                !isProjectMemberEditable(scope.row) &&
                                !isProjectManagerOrPQA
                            "
                        >
                        </el-input>
                        <span v-else>{{ scope.row.module }}</span>
                    </template>
                </el-table-column>

                <!-- 任务名称 -->
                <el-table-column
                    prop="taskName"
                    label="任务名称"
                    header-align="center"
                    align="left"
                >
                    <template slot-scope="scope">
                        <el-input
                            v-if="scope.row.creationType === '手动'"
                            v-model="scope.row.taskName"
                            maxlength="50"
                            autoresize
                            type="textarea"
                            placeholder="请输入任务名称"
                            :disabled="
                                !isProjectMemberEditable(scope.row) &&
                                !isProjectManagerOrPQA
                            "
                        >
                        </el-input>
                        <span v-else>{{ scope.row.taskName }}</span>
                    </template>
                </el-table-column>

                <!-- 计划完成时间 -->
                <el-table-column
                    prop="planFinishDate"
                    label="计划完成时间"
                    align="center"
                    width="130"
                >
                    <template slot-scope="scope">
                        <el-date-picker
                            v-if="scope.row.creationType === '手动'"
                            v-model="scope.row.planFinishDate"
                            type="date"
                            size="small"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期"
                            style="width: 100%"
                            :clearable="false"
                            :disabled="
                                !isProjectMemberEditable(scope.row) &&
                                !isProjectManagerOrPQA
                            "
                        >
                        </el-date-picker>
                        <span v-else>{{ scope.row.planFinishDate }}</span>
                    </template>
                </el-table-column>

                <!-- 责任人 -->
                <el-table-column
                    prop="responsiblePerson"
                    label="责任人"
                    align="center"
                    width="110"
                >
                    <template slot-scope="scope">
                        <PeopleSelector
                            v-if="scope.row.creationType === '手动'"
                            placeholder=""
                            v-model="scope.row.responsiblePerson"
                            :clearable="false"
                            :options="projectMembersOptions"
                            :isMultipled="false"
                            :ref="`people_selector_${scope.row.id}`"
                            :disabled="!isProjectManagerOrPQA"
                        ></PeopleSelector>
                        <span v-else>{{
                            scope.row.responsiblePersonName
                        }}</span>
                    </template>
                </el-table-column>

                <!-- 上周进展 -->
                <el-table-column label="上周进展" align="center">
                    <el-table-column
                        prop="lastWeekProgress"
                        label="上周进度"
                        align="center"
                        width="90"
                    >
                        <template slot-scope="scope">
                            <div class="progress-input-container">
                                <!-- lastWeekProgressEdit 这个字段，用于前端手动编辑的字段，从接口获取数据的时候，
                                 需要判断如果有手动编辑的，使用手动编辑的字段，没有的话就用lastweekProgress，这个就是
                                 禅道的字段，同样的，外面页面的（周报展示页面）也要一并修改
                                -->
                                <el-input-number
                                    v-model="scope.row.computedLastProgress"
                                    :min="0"
                                    :max="100"
                                    :controls="false"
                                    style="width: 100%"
                                    :disabled="
                                        !isProjectMemberEditable(scope.row) &&
                                        !isProjectManagerOrPQA
                                    "
                                >
                                </el-input-number>
                                <span class="percent-sign">%</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="progressSupplement"
                        label="进展补充"
                        header-align="center"
                        align="left"
                        width="180"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.progressSupplement"
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                placeholder="请输入进展补充"
                                maxlength="200"
                                :disabled="
                                    !isProjectMemberEditable(scope.row) &&
                                    !isProjectManagerOrPQA
                                "
                            >
                            </el-input>
                        </template>
                    </el-table-column>
                </el-table-column>

                <!-- 本周计划 -->
                <el-table-column label="本周计划" align="center">
                    <el-table-column
                        prop="thisWeekProgress"
                        label="本周进度"
                        align="center"
                        width="90"
                    >
                        <template slot-scope="scope">
                            <div class="progress-input-container">
                                <el-input-number
                                    v-model="scope.row.thisWeekProgress"
                                    :min="0"
                                    :max="100"
                                    size="small"
                                    :controls="false"
                                    style="width: 70px"
                                    :disabled="
                                        !isProjectMemberEditable(scope.row) &&
                                        !isProjectManagerOrPQA
                                    "
                                >
                                </el-input-number>
                                <span class="percent-sign">%</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="planSupplement"
                        label="计划补充"
                        header-align="center"
                        align="left"
                        width="180"
                    >
                        <template slot-scope="scope">
                            <el-input
                                v-model="scope.row.planSupplement"
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                placeholder="请输入计划补充"
                                maxlength="200"
                                :disabled="
                                    !isProjectMemberEditable(scope.row) &&
                                    !isProjectManagerOrPQA
                                "
                            >
                            </el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column
                    v-if="isProjectManagerOrPQA"
                    header-align="center"
                    align="center"
                    label="操作"
                    width="70"
                >
                    <template slot-scope="scope">
                        <div v-if="scope.row.creationType === '手动'">
                            <el-button
                                type="text"
                                @click="handleAdd(scope.row)"
                                icon="el-icon-plus"
                                :disabled="!isProjectManagerOrPQA"
                            ></el-button>
                            <el-button
                                type="text"
                                @click="handleDelete(scope.row, scope.$index)"
                                icon="el-icon-delete"
                                :disabled="!isProjectManagerOrPQA"
                            ></el-button>
                        </div>
                        <div v-else>
                            <el-button
                                type="text"
                                @click="handleAdd(scope.row)"
                                icon="el-icon-plus"
                                :disabled="!isProjectManagerOrPQA"
                            ></el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="confirm-button">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import PeopleSelector from 'Components/PeopleSelector';
import {
    getUserAccount,
    getSelectedLabel,
    getUuid
} from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'EditDialog',
    components: { PeopleSelector },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        projectMembersOptions: {
            type: Array,
            default: () => []
        },
        isProjectManagerOrPQA: {
            type: Boolean,
            default: false
        },
        weeklyId: {
            type: String,
            default: ''
        },
        // 项目经理是否确认过，确认过就使用checkedFlag字段，
        // 没确认过就使用defaultCheckedFlag字段来控制是否勾选
        isConfirmed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tableData: [],
            selectedRows: [],
            // 禅道任务ID列表
            proTaskIdList: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    watch: {
        visible: {
            immediate: true,
            async handler(val) {
                if (val) {
                    this.tableData = [];
                    // 先获取数据
                    await this.getLastWeekProgressAndPlanList();

                    // 等数据加载和DOM更新完成后再处理选择
                    this.$nextTick(() => {
                        this.applyRowSelections();
                    });
                }
            }
        }
    },
    methods: {
        closeDialog() {
            this.tableData = [];
            this.dialogVisible = false;
        },
        /**
         * 统一处理表格行选择逻辑
         */
        applyRowSelections() {
            if (!this.$refs.planTable) return;
            // 清除之前的选择
            this.$refs.planTable.clearSelection();
            // 根据isConfirmed字段决定使用checkedFlag还是defaultCheckedFlag
            if (this.tableData && this.tableData.length > 0) {
                this.tableData.forEach((row) => {
                    // 没有确认过，直接使用defaultCheckedFlag的值
                    if (!this.isConfirmed) {
                        row.checkedFlag = row.defaultCheckedFlag;
                    }

                    if (row.checkedFlag) {
                        this.$refs.planTable.toggleRowSelection(row, true);
                    }
                });
            }
        },
        /**
         * 选择行
         * @param {Array} selection 选择行
         */
        handleSelectionChange(selection) {
            this.selectedRows = selection;
        },
        /**
         * 确认
         */
        async handleSubmit() {
            if (!this.validate()) {
                return;
            }
            const selectedIds = this.selectedRows.map((row) => row.id);
            this.tableData.forEach((row) => {
                this.$set(row, 'checkedFlag', selectedIds.includes(row.id));
            });
            // 为编辑的责任人补充中文名
            this.tableData.forEach((i) => {
                // 只要点了保存，无论是否修改过上周进展，上周进展就固定为当前的百分比
                i.lastWeekProgressEdit = i.computedLastProgress;
                if (i.creationType === '手动') {
                    i.responsiblePersonName = getSelectedLabel(
                        this.$refs[`people_selector_${i.id}`]
                    );
                }
            });
            // 只传有权限编辑的部分
            const projectTaskProgressList = this.tableData.filter((i) => {
                if (this.isProjectManagerOrPQA) return true;
                return this.isProjectMemberEditable(i);
            });
            // 没数据,并且不是项目经理（项目经理才有删除权限）直接退出
            if (
                projectTaskProgressList.length === 0 &&
                !this.isProjectManagerOrPQA
            ) {
                this.$message.success('保存成功');
                this.$emit('success');
                this.dialogVisible = false;
                return;
            }
            const params = {
                proTaskIdList: this.proTaskIdList,
                weekReportId: this.weeklyId,
                projectTaskProgressList
            };
            const api =
                this.$service.project.weekly.editLastWeekProgressAndPlanList;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.$emit('success');
                this.dialogVisible = false;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 添加行
         * @param {Object} row 行数据
         */
        handleAdd(row) {
            const newRow = {
                id: getUuid(),
                module: row.module,
                taskName: '',
                planFinishDate: null,
                responsiblePerson: '',
                lastWeekProgress: null,
                computedLastProgress: null,
                progressSupplement: '',
                thisWeekProgress: null,
                planSupplement: '',
                checkedBtnDisabled: false,
                checkedFlag: false,
                creationType: '手动',
                weekReportId: this.weeklyId,
                // 随机7位禅道ID，用于给后端作为联合主键
                proTaskId: Math.floor(Math.random() * 10 ** 8)
            };
            const index = this.tableData.findIndex(
                (item) => item.id === row.id
            );
            this.tableData.splice(index + 1, 0, newRow);

            // 更新已选中行的checkedFlag状态
            this.recoverSelection();
        },
        /**
         * 添加首行
         */
        addFirstRow() {
            const newRow = {
                id: getUuid(),
                module: '',
                taskName: '',
                planFinishDate: null,
                responsiblePerson: '',
                lastWeekProgress: null,
                computedLastProgress: null,
                progressSupplement: '',
                thisWeekProgress: null,
                planSupplement: '',
                checkedBtnDisabled: false,
                checkedFlag: false,
                creationType: '手动',
                weekReportId: this.weeklyId,
                // 随机7位禅道ID，用于给后端作为联合主键
                proTaskId: Math.floor(Math.random() * 10 ** 8)
            };
            this.tableData.push(newRow);
        },
        /**
         * 删除行
         * @param {Object} row 行数据
         * @param {Number} index 行索引
         */
        async handleDelete(row, index) {
            await this.$confirm('确认删除该行数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });
            this.proTaskIdList.push(row.proTaskId);
            this.tableData.splice(index, 1);
            this.recoverSelection();
        },
        /**
         * 判断项目团队成员是否可以编辑
         * 责任人为此项目团队成员
         * @param {Object} row 行数据
         * @returns {boolean} 是否可以编辑
         */
        isProjectMemberEditable(row) {
            return getUserAccount(this) === row.responsiblePerson;
        },
        /**
         * 校验
         * @returns {boolean} 是否校验通过
         */
        validate() {
            let isValid = true;
            let errorMessage = '';
            for (const row of this.selectedRows) {
                if (row.creationType === '手动') {
                    if (!row.taskName) {
                        isValid = false;
                        errorMessage = '请填写任务名称';
                        break;
                    }

                    if (!row.planFinishDate) {
                        isValid = false;
                        errorMessage = '请选择计划完成时间';
                        break;
                    }

                    if (!row.responsiblePerson) {
                        isValid = false;
                        errorMessage = '请选择责任人';
                        break;
                    }

                    if (
                        row.computedLastProgress === null ||
                        row.computedLastProgress === undefined
                    ) {
                        isValid = false;
                        errorMessage = '请填写上周进度';
                        break;
                    }

                    if (
                        (row.thisWeekProgress === null ||
                            row.thisWeekProgress === undefined) &&
                        row.lastWeekProgress !== 100
                    ) {
                        isValid = false;
                        errorMessage = '请填写本周进度';
                        break;
                    }
                }
            }
            if (!isValid) {
                this.$message.warning(errorMessage);
                return false;
            }
            return true;
        },
        /**
         * 上周进度和本周计划列表（包含未勾选）
         */
        async getLastWeekProgressAndPlanList() {
            const api =
                this.$service.project.weekly.getLastWeekProgressAndPlanList;
            const params = { weekReportId: this.weeklyId, checkedFlag: null };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (res.body.length === 0) {
                    // 为空，默认新增一行
                    this.addFirstRow();
                    return;
                }
                this.tableData = res.body.map((i) => ({
                    ...i,
                    id: getUuid(),
                    // 这里如果用户手动编辑过上周进展，就用用户编辑过的
                    // 如果用户没有编辑过，就用禅道过来的
                    computedLastProgress:
                        i.lastWeekProgressEdit !== null
                            ? i.lastWeekProgressEdit
                            : i.lastWeekProgress
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取表格每行数据的key
         * @param {Obejct} row 每行数据
         * @returns {String} key
         */
        getRowKey(row) {
            return row.id;
        },
        /**
         * 重新给选择框赋值
         */
        recoverSelection() {
            this.tableData.forEach((i) => {
                const isSelected =
                    this.selectedRows.findIndex((j) => j.id === i.id) !== -1;
                i.checkedFlag = isSelected;
            });
            // 等待数据更新后再应用选择
            this.$nextTick(() => {
                this.applyRowSelections();
            });
        }
    }
};
</script>

<style lang="scss" scoped>
// 预先定义表格行最小高度，避免布局抖动
::v-deep .el-table .el-table__row {
    min-height: 60px;
}

.progress-input-container {
    display: flex;
    align-items: center;
    justify-content: center;

    .percent-sign {
        margin-left: 2px;
        font-size: 14px;
    }
}
.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
}
.edit-task {
    ::v-deep .el-input--suffix .el-input__inner {
        padding-right: 0;
    }
}
</style>
