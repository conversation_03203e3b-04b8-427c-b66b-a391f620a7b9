<template>
    <div class="milestone-bar">
        <div
            class="straight-bar"
            :style="{
                'background-color': barColor
            }"
        >
            <svg
                class="before-arrow right"
                viewBox="0 0 10 15"
                preserveAspectRatio="none"
            >
                <polygon
                    points="0,0 10,7.5 0,15"
                    :style="{ 'background-color': barColor }"
                />
            </svg>
            <el-popover
                placement="bottom"
                width="600"
                trigger="click"
                :disabled="data.projectDetailStatus !== '进行中'"
                popper-class="base-info-milestone-popover"
            >
                <div
                    v-for="(i, index) in data.projectDetailKeyTaskVoList"
                    :key="index"
                    class="key-task"
                >
                    <div class="flex">
                        <svg-icon
                            icon-class="project-clock"
                            class="prefix-circle"
                            :style="{
                                fill:
                                    i.taskStatus === '已延期'
                                        ? 'red'
                                        : i.taskStatus === '有延期风险'
                                        ? 'orange'
                                        : '#00d156'
                            }"
                        ></svg-icon>
                    </div>
                    <el-tooltip
                        class="key-task-name"
                        effect="dark"
                        :content="i.taskName"
                        placement="bottom-start"
                        :visible-arrow="false"
                    >
                        <div>
                            {{ i.taskName }}
                        </div>
                    </el-tooltip>
                    <div class="key-task-time">
                        {{ i.startDate }} - {{ i.endDate }}
                    </div>
                    <div class="key-task-owner">
                        {{ i.responsiblePerson }}
                    </div>
                    <div class="key-task-rest">
                        剩余天数：{{ i.residueDays }}
                    </div>
                </div>
                <el-empty
                    description="暂无数据"
                    :image-size="50"
                    v-show="data.projectDetailKeyTaskVoList.length === 0"
                ></el-empty>
                <div
                    slot="reference"
                    v-if="data.projectDetailStatus === '进行中'"
                >
                    <svg-icon
                        icon-class="project-clock"
                        class="clock-flag"
                        :style="{
                            fill:
                                data.projectDetailScheduleStatus === '已延期'
                                    ? 'red'
                                    : data.projectDetailScheduleStatus ===
                                      '有延期风险'
                                    ? 'orange'
                                    : '#00d156'
                        }"
                    />
                </div>
                <div
                    v-else-if="data.projectDetailStatus === '待启动'"
                    slot="reference"
                ></div>
                <div v-else slot="reference">
                    <svg-icon
                        icon-class="project-check-mark"
                        class="check-mark-flag"
                    />
                </div>
            </el-popover>
            <svg
                class="end-arrow right"
                viewBox="0 0 10 15"
                preserveAspectRatio="none"
            >
                <polygon points="0,0 10,7.5 0,15" :style="{ fill: barColor }" />
            </svg>
        </div>
        <div style="margin-top: 1px">
            <div class="info">
                <b>{{ data.projectDetailName }}</b>
            </div>
            <div class="info">
                <div class="info-ring info-ring-plan"></div>
                <span class="info-plan-time">{{ data.planEndDate }}</span>
            </div>
            <div class="info">
                <div class="info-ring info-ring-actual"></div>
                <span class="info-actual-time">{{
                    data.actualityEndDate
                }}</span>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'MilestoneBar',
    props: {
        data: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        barColor() {
            if (this.data.projectDetailStatus === '已完成') {
                return '#0064f0';
            } else if (this.data.projectDetailStatus === '进行中') {
                return '#0dbd04';
            }
            return '#f0f0f0';
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.milestone-bar {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100px;
    position: relative;
    .straight-bar {
        height: 15px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        .clock-flag {
            width: 30px;
            height: 30px;
            cursor: pointer;
            border-radius: 50%;
            background-color: #fff;
            fill: #0dbd04;
            &:focus {
                outline: 0;
            }
        }
        .circle-flag {
            width: 13px;
            height: 13px;
            background-color: #bfbfbf;
            border-radius: 50%;
        }
        .check-mark-flag {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #0064f0;
            border: 2px solid #fff;
        }
        .before-arrow {
            position: absolute;
            width: 10px;
            height: 15px;
            left: 0px;
            fill: #fff;
        }
        .end-arrow {
            position: absolute;
            width: 10px;
            height: 15px;
            background-color: #fff;
        }
    }
    .info {
        max-width: 100%;
        margin: 10px auto 0 auto;
        display: flex;
        .info-ring {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: transparent;
            margin: auto 5px auto 0;
            position: absolute;
        }
        .info-ring-plan {
            border: 2px solid #0dbd04;
            bottom: 16px;
        }
        .info-ring-actual {
            border: 2px solid #0064f0;
            bottom: -4px;
        }
        .info-plan-time {
            position: absolute;
            bottom: 13px;
            left: 15px;
        }
        .info-actual-time {
            position: absolute;
            bottom: -7px;
            left: 16px;
        }
    }
}
.right {
    right: 0px;
}
.left {
    left: 0px;
    transform: rotate(180deg);
}
.key-task {
    display: flex;
    height: 25px;
    margin: 5px;
    line-height: 25px;
    .key-task-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 33%;
    }
    .key-task-time {
        width: 30%;
        margin-left: 10px;
    }
    .key-task-owner {
        width: 10%;
        margin-left: 10px;
    }
    .key-task-rest {
        margin-left: 10px;
    }
    .prefix-circle {
        width: 20px;
        height: 20px;
        margin: auto;
    }
}
</style>
<style>
.base-info-milestone-popover {
    padding: 10px 5px;
    max-height: 300px;
    overflow-y: auto;
}
</style>
