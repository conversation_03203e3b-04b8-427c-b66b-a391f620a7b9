import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        riskControl: {
            // 查询工期工时预警程度下拉选项
            getWaringLevelOptions(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/work_day_warning/get_work_day_warning_level',
                    method: 'get',
                    params: query
                });
            },
            // 工期工时预警查询
            getWaringList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/work_day_warning/get_work_day_warning_list',
                    method: 'post',
                    data
                });
            },
            // 导出风控列表
            exportRiskControlExcel(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/work_day_warning/download_work_day_warning_level',
                    method: 'post',
                    responseType: 'blob',
                    data
                });
            },
            // 指派预警任务
            assignWarningTask(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/work_day_warning/insertTransferWarningTask',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
