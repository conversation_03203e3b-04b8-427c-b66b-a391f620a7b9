<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-cascader
            ref="cascader"
            v-model="config.modelObj[config.modelKey]"
            v-bind="elCascaderAttrs"
            :show-all-levels="false"
            :options="options"
            :props="props"
            @change="changeHandler"
        />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormMultiCitySelect',
    props: {
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    // 值示例: ['北京市', '北京市', '东城区']
                    modelKey: '',
                    // province|city|area
                    type: 'area',
                    elFormItemAttrs: {},
                    elCascaderAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-cascader组件默认属性设置
            defaultElCascaderAttrs: {
                clearable: true
            },
            // 省市数据
            options: [],
            props: {
                multiple: true,
                emitPath: false
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-cascader组件应用属性
        elCascaderAttrs() {
            return {
                ...this.defaultElCascaderAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elCascaderAttrs || {})
            };
        },
        // 区域选择类型 province|city|area
        type() {
            return this.config.type || 'area';
        }
    },
    watch: {
        'options': {
            handler() {
                this.changeHandler(this.config.modelObj[this.config.modelKey]);
            },
            deep: true
        },
        'config.modelObj': {
            handler(newVal, oldVal) {
                if (!newVal[this.config.modelKey] || !oldVal[this.config.modelKey]) {
                    return;
                }
                if (newVal[this.config.modelKey].join('') !== oldVal[this.config.modelKey].join('')) {
                    this.changeHandler(newVal[this.config.modelKey]);
                }
            },
            deep: true
        }
    },
    mounted() {
        this.getCityData();
    },
    methods: {
        // 获取省市区数据
        async getCityData() {
            try {
                const res = await this.$service.providerManagement.region.getRegionData();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 格式化数据
                this.options = result.map((province) => {
                    if (this.type === 'city') {
                        province.children = province.childrenList.map((city) => {
                            return {
                                ...city,
                                label: city.nodeName,
                                value: city.nodeName
                            };
                        });
                    }
                    return {
                        ...province,
                        label: province.nodeName,
                        value: province.nodeName
                    };
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        changeHandler(value) {
            if (!value) return;
            const element = this.$refs.cascader.$el;
            if (value.length < 3) {
                element.removeAttribute('style');
            } else {
                setTimeout(() => {
                    const height = element.children[1].offsetHeight;
                    element.style.height = `${height + 6}px`;
                });
            }
        }
    }
};
</script>
