// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/project',
        redirect: 'noRedirect',
        name: 'project',
        useLayout: true,
        meta: {
            title: '项目',
            icon: 'el-icon-box'
        },
        children: [
            {
                path: 'schedule',
                component: () => import('../views/schedule'),
                name: 'Schedule',
                meta: {
                    title: '进度',
                    icon: 'el-icon-share'
                }
            },
            {
                path: 'resources',
                component: () => import('../views/resources'),
                name: 'Resources',
                meta: {
                    title: '资源',
                    icon: 'el-icon-data-analysis'
                }
            },
            {
                path: 'projectList',
                component: () => import('../views/projectList'),
                name: 'ProjectList',
                hidden: true,
                meta: {
                    title: '列表'
                }
            },
            {
                path: 'baseInfo',
                component: () => import('../views/baseInfo'),
                name: 'BaseInfo',
                meta: {
                    title: '概况'
                }
            },
            {
                path: 'addProject',
                component: () => import('../views/projectForm'),
                name: 'AddProject',
                hidden: true,
                meta: {
                    title: '新增项目',
                    noCache: true
                }
            },
            {
                path: 'editProject',
                component: () => import('../views/projectForm'),
                name: 'ProjectForm',
                hidden: true,
                meta: {
                    title: '编辑项目',
                    noCache: true
                }
            },
            {
                path: 'finance',
                component: () => import('../views/finance'),
                name: 'Finance',
                meta: {
                    title: '财务'
                }
            },
            {
                path: 'projectReview',
                component: () => import('../views/projectReview'),
                name: 'ProjectReview',
                meta: {
                    title: '评审'
                }
            },
            {
                path: 'projectRisk',
                component: () => import('project/views/projectRisk'),
                name: 'ProjectRisk',
                meta: {
                    title: '风控'
                }
            },
            {
                path: 'projectRiskDetail',
                component: () =>
                    import(
                        'maintenanceProject/views/maintenanceRisk/components/maintenanceRiskDetail'
                    ),
                name: 'ProjectRiskDetail',
                hidden: true,
                meta: {
                    title: '风控详情'
                }
            },
            {
                path: 'projectOrder',
                component: () => import('project/views/projectOrder'),
                name: 'ProjectOrder',
                meta: {
                    title: '订单'
                }
            },
            {
                path: 'projectOrderDetail',
                component: () =>
                    import(
                        'maintenanceProject/views/maintenanceOrder/components/maintenanceOrderDetail'
                    ),
                name: 'ProjectOrderDetail',
                meta: {
                    title: '订单详情'
                },
                hidden: true
            },
            {
                path: 'projectReport',
                component: () => import('project/views/projectReport'),
                name: 'ProjectReport',
                meta: {
                    title: '汇报'
                }
            }
        ]
    }
];
