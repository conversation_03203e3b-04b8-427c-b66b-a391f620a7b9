/**
 * 中文国际化配置：项目模块国际化配置文件route和project必填、httpCode可选
 * @param {Object} route 项目模块路由国际化
 * @param {Object} httpCode 项目模块httpCode国际化
 * @param {Object} project 项目模块除路由、httpCode外，其他信息国际化
 */
export default {
    route: {
        dashboard: '首页',
        documentation: '文档',
        userInfo: '用户信息',
        baseUserInfo: '基本信息',
        changePwd: '修改密码'
    },
    project: {
        systmeTitle: '项目管理信息化平台',
        title: '系统登录',
        logIn: '登录',
        username: '账号',
        password: '密码',
        usernamePlaceholder: '请输入账号',
        passwordPlaceholder: '请输入密码',
        checkWindowTitle: '校验',
        capsTooltip: '大写已开启',
        goBack: '返回首页',
        accountLogin: '密码登录',
        scanningLogin: '扫码登录',
        mobileLogin: '手机登录',
        phonePlaceholder: '请输入手机号',
        pleaseSelect: '请选择',
        verCodePlaceholder: '请输入验证码',
        getVerificationCode: '获取验证码',
        forgetPassword: '忘记密码',
        nextStep: '下一步',
        pleaseEnterNewPassword: '请输入新密码',
        confirmPassword: '请再次输入密码',
        changeSucc: '修改完成',
        changeSuccText: '您已成功设置密码，请试用新密码登录',
        loginNow: '马上登录',
        weChat: '微信',
        dingding: '钉钉',
        lang: {
            zh: '简体中文',
            en: 'English'
        },
        tip: {
            scanning: '扫码快捷登录，快来试试吧~',
            accountOrMobile: '短信登录或者账密登录',
            des: '请使用微信【扫一扫】'
        },
        msg: {
            loginSuccess: '登录成功',
            loginFailed: '登录失败',
            loginOutSuccess: '登出成功',
            loginOutFailed: '登出失败',
            errorPage: '抱歉，您访问的页面500错误',
            errorPageMsg1: '抱歉，您暂无权限访问该页面内容',
            errorPageMsg2: '如您需要访问，请联系管理员',
            checkPhone: '请输入正确的手机号码',
            validVerCode: '请输入验证码',
            checkPasswordLength: '密码长度8-20位',
            checkPasswordMiddle: '需要包含字母和数字',
            checkPasswordHigh: '需要包含大小写字母、数字和特殊字符',
            checkPasswordSame: '两次密码输入不一致',
            handling: '正在处理,请稍后...',
            interfaceError: '系统出现异常，请与管理员联系！'
        },
        bgReturnError: {
            991701: '用户名或密码错误',
            991702: '用户被冻结',
            990706: '账号已在其他端登录',
            991705: '验证码错误',
            990303: '密码强度不符合,请重新输入',
            990304: '您输入的验证码有误，请重新输入',
            990305: '验证码不存在或已失效，请重新获取',
            990714: '密码解密失败',
            991312: '手机号码未注册',
            111111: '操作失败',
            990302: '确认密码与新密码不一致',
            990312: '此号码已绑定用户，请更换其他手机号',
            990001: '缺少关键参数',
            990201: '您登录失败已达4次,再次失败账号将被冻结',
            990202: '因超过登录失败次数账号已冻结，{0}小时后恢复正常',
            900001: '用户绑定未扫码',
            900002: '用户绑定已超时',
            900003: '用户已经绑定其它账号',
            900011: '您未绑定微信账号，请使用其他方式登录。',
            900016: '该用户绑定已解除',
            900012: '该用户已禁用',
            900013: '二维码超时，请刷新二维码',
            900015: '未识别该登录类型',
            990310: '您输入的验证码错误或已失效，请重新输入',
            990311: '您输入的验证码次数过多，请重新发送验证码',
            900017: '您短信验证失败已达4次，再次失败账号将被冻结',
            900018: '因超过短信验证失败次数账号已被冻结，{0}小时后恢复正常',
            920101: '用户Id错误',
            920102: '文件路径错误',
            920103: '文件错误，或空文件',
            920104: '文件后缀错误,不能上传此类型文件',
            920105: '不能上传此类型文件',
            920301: '上传频率过高',
            920302: '下载频率过高',
            920303: '文件超过{0}，无法上传',
            920304: '文件格式错误',
            920305: '文件已经存在',
            920306: '文件不存在',
            920307: '断点续传错误'
        },
        // 用户个人信息
        userInfo: {
            title: '基本信息',
            thirdBindTitle: '第三方账号绑定',
            infoName: {
                name: '姓名',
                phone: '手机号',
                jobNumber: '员工ID'
            },
            columnName: {
                workStatus: '工作状态',
                merchant: '所属商户',
                department: '所属部门',
                role: '所属角色',
                weChat: '微信',
                dingding: '钉钉',
                bind: '绑定',
                Unbound: '未绑定',
                Unbinding: '解绑'
            },
            message: {
                filtTypeErrorMsg: '上传头像只能是 JPG/PNG 格式',
                imgSizeErrorMsg: '上传头像只能小于等于50kb',
                imgPixelErrorMsg: '上传的头像必须是64*64像素',
                uploadFailure: '上传失败',
                notEmpty: '手机号不能为空'
            },
            change: {
                title: '提示',
                cancel: '取消',
                tipContent: '先获取当前验证码-然后输入变更后的手机号码和验证码',
                next: '下一步',
                verification: '验证码',
                placeholderPhone: '请输入验证码',
                getVerification: '获取验证码'
            },
            reselect: '重新选择',
            changePhone: {
                change: '更换',
                securityVerify: '安全验证',
                setNewPhone: '设置新手机号',
                newPhone: '新手机号',
                finished: '完成',
                msg: {
                    msg1: '您正在对',
                    msg2: '用户进行更换安全手机操作，请先进行安全验证：',
                    msg3: '点击获取验证码，短信发送至安全手机',
                    msg4: '请输入手机号码',
                    msg5: '新手机号只能是由5-16位的数字组成',
                    msg6: '请输入新手机号'
                }
            },
            bind: {
                title: '手机验证',
                tip: '请先进行当前手机号验证，再进行第三方账号绑定！',
                phone: '手机号',
                veritify: '验证码',
                getVerification: '获取验证码',
                verificationMsg: '请输入6位数验证码',
                confirm: '确定',
                wxLogin: '微信登录',
                confirmBox: {
                    title: '解绑提示',
                    msg: '您确定解除和微信平台的三方账号绑定吗？'
                },
                msg: {
                    success: '解绑成功',
                    canceled: '已取消'
                }
            }
        },
        // 首页
        dashboard: {
            successTip: '修改密码成功，将重新登录系统！',
            welcome: '欢迎访问'
        },
        // 修改密码界面
        changePwd: {
            save: '确认修改',
            confirm: '确定',
            sendCodeButton: '发送验证码',
            sendCountdown: '{0}秒后重新发送',
            columnName: {
                newPwd: '新密码',
                confirmNewPwd: '再次输入新密码',
                smsCaptcha: '手机验证码'
            },
            placeholder: {
                newPwd: '请输入新密码',
                confirmNewPwd: '请再次输入新密码',
                smsCaptcha: '请输入验证码'
            },
            message: {
                ruleEmptyNewPwd: '密码不能为空',
                ruleNewPwd:
                    '输入内容包含数字、英文字母和特殊字符且大于等于8位，最大长度20。',
                ruleEqualPwd: '两次输入密码不一致',
                ruleEmptySmsCaptcha: '验证码不能为空',
                ruleSmsCaptcha: '验证码格式不正确，只能是6位数字',
                ruleNoPass: '请正确填写输入项内容！',
                saveSuccess: '密码修改成功，请重新登录！',
                saveFailure: '密码修改失败'
            },
            changePassword: '修改密码',
            // 重新选择
            reselect: '重新选择'
        },
        // 图片选择框
        selectPicBox: {
            title: '图片选择框',
            selectPic: '选择图片...',
            picPreview: '图片预览',
            picDownload: '下载图片',
            picSave: '保存图片',
            message: {
                picErrorMsg: '上传图片长度不能超过 {0} 字节',
                picFormatErrorMsg: '图片类型必须是.gif,jpeg,jpg,png,bmp中的一种'
            }
        },
        login: {
            welcome: '欢迎访问',
            passwordTips:
                '你使用的是初始密码，密码有被盗风险，请立即修改密码！',
            expireTitle: '密码修改提示',
            expirePasswordTips:
                '您使用的密码已到期，根据管理规定，请立刻修改所用密码！非常感谢。'
        }
    },
    httpCode: {
        http400: '接口签名错误，系统拒绝处理！',
        http401: '您的账号登录时效已过期，请重新登录！',
        http404: '网址无法访问，请联系系统管理员！',
        http406:
            '您的账号在其他设备上登录，如果不是您本人操作，请尽快修改密码！',
        http500: '服务器出现异常，请与管理员联系！'
    }
};
