<template>
    <div>
        <MaintenanceSelector
            :key="projectSelectorKey"
            :statusDisabled="false"
        ></MaintenanceSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="需求周报" name="demand" :lazy="true">
                    <Demand></Demand>
                </el-tab-pane>
                <el-tab-pane label="订单周报" name="order" :lazy="true">
                    <Order></Order>
                </el-tab-pane>
                <el-tab-pane label="缺陷周报" name="defect" :lazy="true">
                    <Defect></Defect>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import MaintenanceSelector from 'maintenanceProject/components/MaintenanceSelector';
import Demand from './Demand';
import Order from './Order';
import Defect from './Defect';

export default {
    name: 'ProjectReport',
    components: {
        MaintenanceSelector,
        Demand,
        Order,
        Defect
    },
    data() {
        return {
            activeName: 'demand',
            // 顶部级联组件key
            projectSelectorKey: 0
        };
    },
    watch: {
        activeName(newVal) {
            this.$store.dispatch('maintenanceProject/setActiveName', newVal);
        }
    },
    async created() {
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    mounted() {
        this.projectSelectorKey += 1;
    },
    activated() {
        this.projectSelectorKey += 1;
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.box-main {
    width: 100%;
    padding: 0px 20px 0px 20px;
    background-color: #ffffff;
    overflow: auto;
}
</style>
