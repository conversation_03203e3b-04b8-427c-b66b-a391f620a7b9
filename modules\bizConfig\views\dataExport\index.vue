<template>
    <div class="export-outer">
        <el-button type="primary" @click="estimateWorkHour"
            >预计工时导出</el-button
        >
        <el-button type="primary" @click="riskWarning"
            >工期工时预警导出</el-button
        >
    </div>
</template>

<script>
export default {
    name: 'DataExport',
    data() {
        return {};
    },
    methods: {
        /**
         * 预计工时分配
         */
        async estimateWorkHour() {
            try {
                const api = this.$service.bizConfig.dataExport.estimateWorkHour;
                const stream = await api();
                this.$tools
                    .downloadExprotFile(stream, '预计工时数据', 'xlsx')
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 工期工时预警
         */
        async riskWarning() {
            try {
                const api = this.$service.bizConfig.dataExport.riskWarning;
                const stream = await api();
                this.$tools
                    .downloadExprotFile(stream, '工期工时预警数据', 'xlsx')
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.export-outer {
    padding: 10px;
}
</style>
