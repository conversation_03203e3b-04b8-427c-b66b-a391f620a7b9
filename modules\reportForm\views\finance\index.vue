<template>
    <div class="finance-container">
        <el-tabs class="tabs" v-model="activeName">
            <el-tab-pane
                label="研发年度支出监控（汇总）"
                name="annualSpendSummary"
                v-if="hasAnnualSpendSummaryPermisson"
            >
                <AnnualSpendSummary
                    :activeName="activeName"
                ></AnnualSpendSummary>
            </el-tab-pane>
            <el-tab-pane
                label="研发年度支出监控（分项）"
                name="annualSpendSubentry"
                :lazy="true"
                v-if="hasAnnualSpendSubentryPermisson"
            >
                <AnnualSpendSubentry
                    :activeName="activeName"
                ></AnnualSpendSubentry>
            </el-tab-pane>
            <el-tab-pane
                label="开发项目概算监控"
                name="devProjectEstimateMonitor"
                :lazy="true"
                v-if="hasDevProjectEstimateMonitorPermisson"
            >
                <DevProjectEstimateMonitor
                    :activeName="activeName"
                ></DevProjectEstimateMonitor>
            </el-tab-pane>
            <el-tab-pane
                label="开发项目预算监控"
                name="devProjectBudgetMonitor"
                :lazy="true"
                v-if="hasDevProjectBudgetMonitorPermisson"
                ><DevProjectBudgetMonitor
                    :activeName="activeName"
                ></DevProjectBudgetMonitor>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import AnnualSpendSummary from './components/spendSummary/AnnualSpendSummary.vue';
import AnnualSpendSubentry from './components/spendSubentry/AnnualSpendSubentry.vue';
import DevProjectEstimateMonitor from './components/estimateMonitor/DevProjectEstimateMonitor.vue';
import DevProjectBudgetMonitor from './components/budgetMonitor/DevProjctBudgetMonitor.vue';

export default {
    name: 'ReportFormFinance',
    components: {
        AnnualSpendSummary,
        AnnualSpendSubentry,
        DevProjectEstimateMonitor,
        DevProjectBudgetMonitor
    },
    data() {
        return { activeName: 'annualSpendSummary' };
    },
    computed: {
        // 是否有研发年度支出监控（汇总）页签权限
        hasAnnualSpendSummaryPermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'AnnualSpendSummary'
            );
        },
        // 是否有研发年度支出监控（分项）页签权限
        hasAnnualSpendSubentryPermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'AnnualSpendSubentry'
            );
        },
        // 是否有开发项目概算监控页签权限
        hasDevProjectEstimateMonitorPermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'DevProjectEstimateMonitor'
            );
        },
        // 是否有开发项目预算监控页签权限
        hasDevProjectBudgetMonitorPermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'DevProjectBudgetMonitor'
            );
        }
    },
    mounted() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
    }
};
</script>

<style scoped lang="scss">
.finance-container {
    height: 100vh;
    overflow-y: auto;
    padding: 10px 16px;
    .tabs {
        height: 100%;
    }
    // 修改排序箭头样式
    ::v-deep .el-table .ascending .sort-caret.ascending {
        border-bottom-color: #ffdc37;
    }
    ::v-deep .el-table .descending .sort-caret.descending {
        border-top-color: #ffdc37;
    }
}
</style>
