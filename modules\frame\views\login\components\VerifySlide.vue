<template>
    <div style="position: relative;">
        <el-dialog :title="$t('frame.checkWindowTitle')" width="450px" :visible.sync="loginCheckDialogVisible" @close="closeWindowHandler">
            <div style="position: relative;">
                <div v-if="type === '2'" class="verify-img-out" :style="{height: (parseInt(setSize.imgHeight) + vSpace) + 'px'}">
                    <div class="verify-img-panel" :style="{width: setSize.imgWidth, height: setSize.imgHeight,}">
                        <div v-if="!backImgBase" class="verify-img-loading">
                            <span>正在获取... </span><i class="el-icon-loading" :style="{color: iconColor}" />
                        </div>
                        <img v-if="backImgBase" :src="'data:image/png;base64,' + backImgBase" alt="" style="width:100%;height:100%;display:block">
                        <div v-show="showRefresh" class="verify-refresh" @click="refresh"><i class="el-icon-refresh icon-refresh" />
                        </div>
                        <transition name="tips">
                            <span v-if="tipWords" class="verify-tips" :class="passFlag ?'suc-bg':'err-bg'">{{ tipWords }}</span>
                        </transition>
                    </div>
                </div>
                <!-- 公共部分 -->
                <div class="verify-bar-area" :style="{width: setSize.imgWidth, height: barSize.height, 'line-height':barSize.height}">
                    <span class="verify-msg" v-text="text" />
                    <div class="verify-left-bar" :style="{width: (leftBarWidth!==null)?leftBarWidth: barSize.height, height: barSize.height, 'border-color': leftBarBorderColor, transaction: transitionWidth}">
                        <span class="verify-msg" v-text="finishText" />
                        <div class="verify-move-block" :style="{width: barSize.height, height: barSize.height, 'background-color': moveBlockBackgroundColor, left: moveBlockLeft, transition: transitionLeft}" @touchstart="start" @mousedown="start">
                            <i :class="['verify-icon iconfont', iconClass]" :style="{color: iconColor}" />
                            <div v-if="type === '2'" class="verify-sub-block" :style="{'width':Math.floor(parseInt(setSize.imgWidth)*47/310)+ 'px',
                         'height': setSize.imgHeight,
                         'top':'-' + (parseInt(setSize.imgHeight) + vSpace + 20) + 'px',
                         'background-size': setSize.imgWidth + ' ' + setSize.imgHeight,
                }">
                                <img :src="'data:image/png;base64,'+blockBackImgBase" alt="" style="width:100%;height:100%;display:block">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script type="text/babel">
import { resetSize } from '../utils/util';

export default {
    name: 'VerifySlide',
    props: {
        captchaType: {
            type: String,
            default: 'blockPuzzle'
        },
        type: {
            type: String,
            default: '1'
        },
        // 弹出式pop，固定fixed
        mode: {
            type: String,
            default: 'fixed'
        },
        vSpace: {
            type: Number,
            default: 5
        },
        explain: {
            type: String,
            default: '向右滑动完成验证'
        },
        imgSize: {
            type: Object,
            default() {
                return {
                    width: '310px',
                    height: '155px'
                };
            }
        },
        blockSize: {
            type: Object,
            default() {
                return {
                    width: '50px',
                    height: '50px'
                };
            }
        },
        barSize: {
            type: Object,
            default() {
                return {
                    width: '310px',
                    height: '55px'
                };
            }
        },
        // 是否显示弹窗
        showWindow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 是否通过的标识
            passFlag: '',
            // 验证码背景图片
            backImgBase: '',
            // 验证滑块的背景图片
            blockBackImgBase: '',
            // 后端返回的唯一验证值
            captchaMark: '',
            // 移动开始的时间
            startMoveTime: '',
            // 移动结束的时间
            endMovetime: '',
            // 提示词的背景颜色
            tipsBackColor: '',
            tipWords: '',
            text: '',
            finishText: '',
            setSize: {
                imgHeight: '210px',
                imgWidth: '100%',
                barHeight: 0,
                barWidth: 0
            },
            top: 0,
            left: 0,
            moveBlockLeft: undefined,
            leftBarWidth: null,
            // 移动中样式
            moveBlockBackgroundColor: undefined,
            leftBarBorderColor: '#ddd',
            iconColor: undefined,
            iconClass: 'el-icon-arrow-right',
            // 鼠标状态
            status: false,
            // 是够验证完成
            isEnd: false,
            showRefresh: true,
            transitionLeft: '',
            transitionWidth: '',
            // 弹窗控制变量
            loginCheckDialogVisible: false,
            // 是否第一次打开
            isFirst: true
        };
    },
    computed: {
        barArea() {
            return this.$el.querySelector('.verify-bar-area');
        },
        resetSize() {
            return resetSize;
        }
    },
    watch: {
        showWindow(val) {
            if (val) {
                if (this.isFirst) {
                    this.isFirst = false;
                    this.init();
                } else {
                    this.refresh();
                }
                this.loginCheckDialogVisible = true;
            } else {
                this.loginCheckDialogVisible = false;
            }
        }
    },
    mounted() {
        // 禁止拖拽
        this.$el.onselectstart = function () {
            return false;
        };
    },
    methods: {
        // 关闭窗口处理
        closeWindowHandler() {
            this.treeDialogVisible = false;
            this.$emit('update:showWindow', false);
        },
        // 获取验证图片或者验证码
        getLoginCheckInfo() {
            this.loading = true;
            // 验证码类型  clickWord：文字点选、blockPuzzle：滑动验证码、calculation：数字验证码
            const data = {
                // 目前暂时只支持滑动验证码
                captchaType: 'blockPuzzle'
            };
            this.$service.frame
                .getLoginCheckInfo(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.backImgBase = res.body.originalImageBase64;
                        this.blockBackImgBase = res.body.jigsawImageBase64;
                        this.captchaMark = res.body.captchaMark;
                        // 获取成功后，重置验证信息
                        this.tipWords = '';
                        this.loading = false;
                        this.$nextTick(() => {
                            // 重新设置宽度高度
                            const setSize = this.resetSize(this);
                            // eslint-disable-next-line guard-for-in
                            for (const key in setSize) {
                                this.$set(this.setSize, key, setSize[key]);
                            }
                            // 通知父容器，图片已经加载完毕
                            this.$emit('imgIsLoaded');
                        });
                    } else {
                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        // 去后端验证是否正确
        checkRequest() {
            this.loading = true;
            // 验证前修改icon
            this.iconClass = 'el-icon-loading';
            this.endMovetime = +new Date();
            let moveLeftDistance = parseInt(
                (this.moveBlockLeft || '').replace('px', '')
            );
            moveLeftDistance =
                (moveLeftDistance * 310) / parseInt(this.setSize.imgWidth);
            const data = {
                // 验证码类型  目前暂时只支持滑动验证码
                captchaType: 'blockPuzzle',
                // 验证码唯一标识（获取验证码接口获取）
                captchaMark: this.captchaMark,
                // 坐标信息（加密传输）明文示例：{      "x": 111,   "y": 5 }
                pointJson: this.$tools.encrypt(
                    JSON.stringify({ x: moveLeftDistance, y: 5.0 }),
                    '3DES'
                )
            };
            this.$service.frame
                .postLoginCheck(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.moveBlockBackgroundColor = '#5cb85c';
                        this.leftBarBorderColor = '#5cb85c';
                        this.iconColor = '#fff';
                        // 验证成功修改icon
                        this.iconClass = 'el-icon-check';
                        this.showRefresh = false;
                        this.isEnd = true;
                        this.passFlag = true;
                        this.tipWords = `${(
                            (this.endMovetime - this.startMoveTime) /
                            1000
                        ).toFixed(2)}s验证成功`;
                        setTimeout(() => {
                            // 关闭窗口
                            this.closeWindowHandler();
                            this.$emit(
                                'checkSuccess',
                                this.$tools.encrypt(this.captchaMark, '3DES')
                            );
                        }, 1000);
                    } else {
                        this.moveBlockBackgroundColor = '#d9534f';
                        this.leftBarBorderColor = '#d9534f';
                        this.iconColor = '#fff';
                        this.iconClass = 'el-icon-close';
                        this.passFlag = false;
                        this.$emit('checkError');
                        this.tipWords = res.head.message || '验证失败';
                        setTimeout(() => {
                            // 刷新图片
                            this.refresh();
                        }, 1000);
                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.moveBlockBackgroundColor = '#d9534f';
                    this.leftBarBorderColor = '#d9534f';
                    this.iconColor = '#fff';
                    this.iconClass = 'el-icon-close';
                    this.passFlag = false;
                    this.$parent.$emit('error', this);
                    this.tipWords = '服务器链接错误';
                    setTimeout(() => {
                        this.refresh();
                    }, 1000);
                    this.loading = false;
                    this.loading = false;
                });
        },
        init() {
            this.text = this.explain;
            this.getLoginCheckInfo();
            const _this = this;

            window.removeEventListener('touchmove', (e) => {
                _this.move(e);
            });
            window.removeEventListener('mousemove', (e) => {
                _this.move(e);
            });

            // 鼠标松开
            window.removeEventListener('touchend', () => {
                _this.end();
            });
            window.removeEventListener('mouseup', () => {
                _this.end();
            });

            window.addEventListener('touchmove', (e) => {
                _this.move(e);
            });
            window.addEventListener('mousemove', (e) => {
                _this.move(e);
            });

            // 鼠标松开
            window.addEventListener('touchend', () => {
                _this.end();
            });
            window.addEventListener('mouseup', () => {
                _this.end();
            });
        },
        // 鼠标按下
        start(e) {
            // eslint-disable-next-line no-param-reassign
            e = e || window.event;
            let x = '';
            if (!e.touches) {
                // 兼容PC端
                x = e.clientX;
            } else {
                // 兼容移动端
                x = e.touches[0].pageX;
            }
            this.startLeft = Math.floor(
                x - this.barArea.getBoundingClientRect().left
            );
            // 开始滑动的时间
            this.startMoveTime = +new Date();
            if (!this.isEnd) {
                this.text = '';
                this.moveBlockBackgroundColor = '#337ab7';
                this.leftBarBorderColor = '#337AB7';
                this.iconColor = '#fff';
                e.stopPropagation();
                this.status = true;
            }
        },
        // 鼠标移动
        move(e) {
            // eslint-disable-next-line no-param-reassign
            e = e || window.event;
            if (this.status && !this.isEnd) {
                let x = '';
                if (!e.touches) {
                    // 兼容PC端
                    x = e.clientX;
                } else {
                    // 兼容移动端
                    x = e.touches[0].pageX;
                }
                // eslint-disable-next-line camelcase
                const bar_area_left = this.barArea.getBoundingClientRect().left;

                // eslint-disable-next-line camelcase
                let move_block_left = x - bar_area_left;
                if (
                    // eslint-disable-next-line camelcase
                    move_block_left >=
                    this.barArea.offsetWidth -
                        parseInt(parseInt(this.blockSize.width) / 2) -
                        2
                ) {
                    // eslint-disable-next-line camelcase
                    move_block_left =
                        this.barArea.offsetWidth -
                        parseInt(parseInt(this.blockSize.width) / 2) -
                        2;
                }
                // eslint-disable-next-line camelcase
                if (move_block_left <= 0) {
                    // eslint-disable-next-line camelcase
                    move_block_left = parseInt(
                        parseInt(this.blockSize.width) / 2
                    );
                }

                // eslint-disable-next-line camelcase
                this.moveBlockLeft = `${move_block_left - this.startLeft}px`;
                // eslint-disable-next-line camelcase
                this.leftBarWidth = `${move_block_left - this.startLeft}px`;
            }
        },
        // 鼠标松开
        end() {
            this.endMovetime = +new Date();
            // 判断是否重合
            if (this.status && !this.isEnd) {
                this.checkRequest();
                this.status = false;
            }
        },
        // 刷新
        refresh() {
            this.showRefresh = true;
            this.finishText = '';

            this.transitionLeft = 'left .3s';
            this.moveBlockLeft = 0;

            this.leftBarWidth = null;
            this.transitionWidth = 'width .3s';

            this.leftBarBorderColor = '#ddd';
            this.moveBlockBackgroundColor = '#fff';
            this.iconColor = '#000';
            this.iconClass = 'el-icon-arrow-right';
            this.isEnd = false;

            this.getLoginCheckInfo();
            setTimeout(() => {
                this.transitionWidth = '';
                this.transitionLeft = '';
                this.text = this.explain;
            }, 300);
        }
    }
};
</script>

<style lang="scss">
.verify-tips {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 100%;
    height: 30px;
    line-height: 30px;
    color: #fff;
}
.suc-bg {
    background-color: rgba(92, 184, 92, 0.5);
    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7f5CB85C, endcolorstr=#7f5CB85C);
}
.err-bg {
    background-color: rgba(217, 83, 79, 0.5);
    filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#7fD9534F, endcolorstr=#7fD9534F);
}
.tips-enter,
.tips-leave-to {
    bottom: -30px;
}
.tips-enter-active,
.tips-leave-active {
    transition: bottom 0.5s;
}

.verify-bar-area {
    position: relative;
    background: #ffffff;
    text-align: center;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    border: 1px solid #ddd;
    -webkit-border-radius: 4px;
    margin-top: 20px;
}

.verify-bar-area .verify-move-block {
    position: absolute;
    top: 0px;
    left: 0;
    background: #fff;
    cursor: pointer;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    box-shadow: 0 0 2px #888888;
    -webkit-border-radius: 1px;
}

.verify-bar-area .verify-move-block:hover {
    background-color: #337ab7;
    color: #ffffff;
}

.verify-bar-area .verify-left-bar {
    position: absolute;
    top: -1px;
    left: -1px;
    background: #f0fff0;
    cursor: pointer;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    border: 1px solid #ddd;
}

.verify-img-panel {
    margin: 0;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    border: 1px solid #ddd;
    border-radius: 3px;
    position: relative;
}

.verify-img-loading {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.verify-img-panel .verify-refresh {
    width: 25px;
    height: 25px;
    text-align: center;
    padding: 5px;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
}

.verify-img-panel .icon-refresh {
    font-size: 20px;
    color: #fff;
}

.verify-img-panel .verify-gap {
    background-color: #fff;
    position: relative;
    z-index: 2;
    border: 1px solid #fff;
}

.verify-bar-area .verify-move-block .verify-sub-block {
    position: absolute;
    text-align: center;
    z-index: 3;
}

.verify-bar-area .verify-move-block .verify-icon {
    font-size: 18px;
}

.verify-bar-area .verify-msg {
    z-index: 3;
}
// dialog 弹框的样式
.el-dialog {
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    .el-dialog__header {
        display: flex;
        align-items: center;
        padding: 18px 20px;
        height: 50px;
        background: #f5f6fa;
        .el-dialog__title {
            font-size: 14px;
            font-weight: bold;
            color: #000000;
            &::before {
                content: '';
                display: inline-block;
                width: 5px;
                height: 15px;
                background: #3370ff;
                margin-right: 10px;
                position: relative;
                top: 2px;
            }
        }
        .el-dialog__close {
            font-size: 15px;
            color: #000000;
            font-weight: bold;
        }
    }
    .el-dialog__body {
        box-sizing: border-box;
        padding: 20px;
        .el-input .el-input__inner {
            height: 40px;
            line-height: 40px;
            border: 1px solid #ececec;
            &:active,
            &:hover {
                border: 1px solid #3370ff;
            }
        }
        .el-form-item.is-error {
            .el-input__inner {
                border: 1px solid #ff0000;
            }
        }
        .el-form-item__label {
            padding: 0 17px 0 0;
            font-weight: 400;
        }
        .el-switch__core {
            width: 42px;
        }
        .el-form-item:last-child {
            margin-bottom: 0;
        }
        .el-row .el-form-item:last-child {
            margin-bottom: 22px;
        }
        .active {
            background: #3370ff;
            color: #ffffff;
        }
    }
}
</style>
