<template>
    <div class="product-cards">
        <div
            v-for="product in productLineList"
            :key="product.productLineName"
            class="product-card"
        >
            <div class="product-card-content">
                <el-button
                    size="mini"
                    class="export-button"
                    type="text"
                    @click.stop="handleExport(product)"
                >
                    <svg-icon
                        icon-class="reportForm-export"
                        class="export-icon"
                /></el-button>
                <div class="product-card-title">
                    {{ product.productLineName }}
                </div>
                <div class="product-card-actions">
                    <el-button
                        v-for="(action, index) in product.actions"
                        :key="index"
                        type="primary"
                        class="product-card-action"
                        @click="handleProductAction(product, action)"
                    >
                        {{ action }}
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'ProductCardGrid',
    props: {
        productLineList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {};
    },
    methods: {
        // 处理产品线操作按钮点击
        handleProductAction(product, action) {
            const isDevelopWeekly = action === '开发项目';
            this.$store.dispatch(
                'reportForm/setIsDevelopWeekly',
                isDevelopWeekly
            );

            // 构建下个页面选中时的store
            const pathValue = this.$store.state.reportForm.weeklyValue;
            pathValue.push(product.productLineName);
            this.$store.dispatch('reportForm/selectedWeeklyValue', pathValue);
            const option = this.$store.state.reportForm.weeklyOption;
            option.push({
                productLineName: product.productLineName,
                value: product.productLineName,
                label: product.productLineName
            });
            this.$store.dispatch('reportForm/selectedWeeklyOption', option);
            // 跳转到开发项目列表
            this.$router.push({
                path: '/reportForm/reportFormWeeklyInfo'
            });
        },
        /**
         * @description: 导出周报
         * @param {object} product 产品信息
         */
        async handleExport(product) {
            const projectMap = {
                开发项目: this.exportDev,
                维护工作: this.exportMaintain
            };
            const exportPromises = product.actions.map((i) => {
                const exportFunc = projectMap[i];
                if (exportFunc) {
                    return exportFunc(product);
                }
                return Promise.resolve();
            });

            try {
                await Promise.all(exportPromises);
            } catch (error) {
                console.error('导出过程中发生错误:', error);
            }
        },
        /**
         * @description: 导出开发项目周报
         * @param {object} product 产品信息
         */
        async exportDev(product) {
            const { weeklyOption } = this.$store.state.reportForm;
            const { weekNumber } = weeklyOption[0];
            const yearVal = weeklyOption[0].year;
            const { label } = weeklyOption[0];
            try {
                const exportList =
                    this.$service.reportForm.weekly.exportDevWeekly;
                const params = {
                    productLine: product.productLineName,
                    weekNumber,
                    yearVal
                };
                const res = await exportList(params);

                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);

                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                await this.$tools
                    .downloadExprotFile(
                        res,
                        `${product.productLineName}产品线${yearVal}年${label
                            .split('/')
                            .join('')}开发项目周报-${moment().format(
                            'yyyyMMDD'
                        )}`,
                        'xlsx'
                    )
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * @description: 导出维护工作周报
         * @param {object} product 产品信息
         */
        async exportMaintain(product) {
            const { weeklyOption } = this.$store.state.reportForm;
            const { weekNumber } = weeklyOption[0];
            const yearVal = weeklyOption[0].year;
            const { label } = weeklyOption[0];
            try {
                const exportList =
                    this.$service.reportForm.weekly.exportMaintainWeekly;
                const params = {
                    productLine: product.productLineName,
                    weekNumber,
                    yearVal
                };
                const res = await exportList(params);
                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);
                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                await this.$tools.downloadExprotFile(
                    res,
                    `${product.productLineName}产品线${yearVal}年${label
                        .split('/')
                        .join('')}维护工作周报-${moment().format('yyyyMMDD')}`,
                    'xlsx'
                );
            } catch (err) {
                console.error(err);
            }
        }
    }
};
</script>

<style scoped lang="scss">
.product-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 30px;
}

.product-card {
    background: linear-gradient(135deg, #4169e1 0%, #0066ff 100%);
    color: white;
    padding: 20px 20px 10px 20px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transition: transform 0.2s ease;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
            135deg,
            transparent,
            transparent 2px,
            rgba(255, 255, 255, 0.1) 2px,
            rgba(255, 255, 255, 0.1) 4px
        );
    }
}

.product-card-content {
    position: relative;
    z-index: 1;
}

.export-button {
    position: absolute;
    top: -20px;
    right: -14px;
    z-index: 2;
    color: white;
}

.product-card-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
}

.product-card-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.product-card-action {
    flex: 1;
    padding: 8px 16px;
    background: #3d33ff;
    border-radius: 4px;
    font-size: 14px;
    border: none;
    &:hover {
        background: rgba(61, 51, 255, 0.6);
    }
}
.export-icon {
    width: 15px;
    height: 15px;
    padding: 0;
    &:hover {
        transform: scale(1.1);
    }
}
</style>
