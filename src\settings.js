module.exports = {
    /**
     * @type {String}
     * @description 系统名称，用于浏览器title，和侧边栏LOGO
     */
    title: 'frame.systmeTitle',

    /**
     * @type {String}
     * @description 系统logo图标地址，用于侧边栏LOGO
     */
    // logoPath: require('../modules/frame/assets/logo.svg'),
    logoPath: '',

    /**
     * @type {boolean} true | false
     * @description 是否开启权限过滤
     */
    permissionSettings: true,
    /**
     * @type {string} 配置值：空(默认)/local/server
     * @description 路由菜单数据优先级。
     * 配置值：
     *  空(默认)：根据 permission* 配置（必配项）自动识别服务器值。permission* 配置值为空，则忽略服务器值
     *  local：本地服务器都配置情况下，本地配置数据优先。忽略 permission* 配置
     *  server：本地服务器都配置情况下，服务器数据优先。忽略 permission* 配置
     */
    permissionDataPriority: 'server',
    //  permissionDataPriority: '',

    /**
     * @type {String}
     * @description 权限过滤对比的key，对比时会根据key值来对比，如果permissionSettings为true则该字段不能为空
     */
    permissionKey: 'permissionCode',

    /**
     * @type {String}
     * @description 权限排序提取字段，默认是orderBy，如permissionSettings为true有效，值为空则不排序
     */
    permissionOrderKey: 'orderBy',

    /**
     * @type {String}
     * @description icon图标的提取字段，默认是orderBy，如permissionSettings为true有效，值为空则不判断图标
     */
    permissionIconKey: 'permissionIcon',

    /**
     * @type {boolean} true | false
     * @description Whether show the settings right-panel
     */
    showSettings: false,

    /**
     * @type {boolean} true | false
     * @description 是否需要面包屑导航
     */
    tagsView: false,

    /**
     * @type {boolean} true | false
     * @description Whether fix the header
     */
    fixedHeader: true,

    /**
     * @type {boolean} true | false
     * @description Whether show the logo in sidebar
     */
    sidebarLogo: true,

    /**
     * @type {boolean} true | false
     * @description Whether support pinyin search in headerSearch
     * Bundle size minified 47.3kb,minified + gzipped 63kb
     */
    supportPinyinSearch: true,

    /**
     * @type {string | array} 'production' | ['production', 'development']
     * @description Need show err logs component.
     * The default is only used in the production env
     * If you want to also use it in dev, you can pass ['production', 'development']
     */
    errorLog: 'production',
    /**
     * @type {number} 5000
     * @description timeout of request
     */
    // timeout: 12000,
    timeout: 5000,
    /**
     * @type {Object}  {token_name: 'Authorization', token_prefix: 'Bearer ', token_suffix: 'Bearer ',}
     * @description token of request
     */
    token: {
        token_name: 'Authorization',
        token_prefix: 'Bearer ',
        token_suffix: ''
    },

    /**
     * @description:配置公钥 用于RSA加密
     * @param {*} string
     * @return {*}
     */
    rsaPubKey:
        'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAifm6PtL8La/GxrmkbZsxkjbWScSWbiodnYt7GDHuIrI9NYsc/tQkDyNu/BBxXltY1LhDw3PqVvQQVo7D/oroDb3syWyZb4QB7+BkN9WNQbBKoYGISZbc8I8ttBtq4B85vaWtAKqzOZA4BVLj3wz//q7OqN09ZjY/rTP95zrQOg7hCoXCl9wECFsw86ZzVCOB21t+z4HqzPIQjj5qDEU7lbMOsGdWjo5w1boJq4/mdQUKVOjjWiqa2nVOL8jjXeXFnXuF89vDADxsOeUFVAlX5ULVxxhjowNTRID28tSJxukCHcsQRIbFKFcOy7nwekppNcQJtN1AhUcYuH7GpAlxRQIDAQAB',

    /**
     * @description:配置公钥 用于DES加密 key
     * @param {*} string
     * @return {*}
     */
    desKey: 'YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4',

    /**
     * @description:配置公钥 用于DES加密 keyiv
     * @param {*} string
     * @return {*}
     */
    desKeyIV: 'snbcbbpf',

    /**
     * 配置layout框架的右上角功能区域，支持自定义组件，组件排序，系统默认组件显示隐藏
     * 数组中对象属性说明：
     *      component: 组件对象，支持三种方式。
     *                 1、component:()=>import('../logo.vue')
     *                 2、全局注册的组件名称。即通过Vue.component('xxx',()=>import('xxx'))
     *                 3、系统默认功能组件名称。支持组件有：search(搜索)、screenfull(全屏)、size(调整字体大小)、lang(多语言选择)
     *      show: 取值true/false,标识组件是否显示()。默认true
     * 示例配置：
     *      navbar: [
                {
                    component: 'lang',
                    show: false
                },
                { component: () => import('../modules/frame/components/democp.vue') }
            ]
     */
    navbar: [
        {
            component: 'lang',
            show: false
            // component: () => import('../modules/frame/components/LangSelect/index.vue')
        },
        {
            component: 'log',
            show: false
        }
    ],
    interfaceWhiteList: [],
    timeoutList: [
        '/console/user/exportUserNew',
        '/console/role/getUserByOrg',
        '/console/log/exportLogs'
    ],
    layoutName: 'noheadMenu',
    terminal: 'boss',
    // 项目是否需要国际化
    isNeedLang: false,
    modifyPassword: false,
    // 是否需要通过后台配置修改菜单名称，如果不需要则为空字符串
    permissionTitleKey: 'permissionName'
};
