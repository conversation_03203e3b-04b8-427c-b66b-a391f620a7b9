<template>
    <div>
        <ProjectSelector
            @input="handleChange"
            :key="projectSelectorKey"
            :statusDisabled="false"
            :projectStatus="projectStatus"
        ></ProjectSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="项目周报" name="projectWeekly" :lazy="true">
                    <ProjectWeekly />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector.vue';
import ProjectWeekly from './components/ProjectWeekly';

export default {
    name: 'ProjectReport',
    components: {
        ProjectSelector,
        ProjectWeekly
    },
    data() {
        return {
            activeName: 'projectWeekly',
            // 顶部级联组件key
            projectSelectorKey: 0,
            projectId: ''
        };
    },
    computed: {
        projectStatus() {
            const { projectStatus } = this.$store.state.project.projectInfo;
            return projectStatus;
        }
    },
    async created() {
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    mounted() {
        this.projectSelectorKey += 1;
    },
    activated() {
        this.projectSelectorKey += 1;
    },
    methods: {
        /**
         * 选择后的回调
         * @param {Array} value  选中的值
         */
        handleChange(value) {
            this.projectId = value[value.length - 1];
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 0px 20px;
    background-color: #ffffff;
    overflow: auto;
}
</style>
