<template>
    <div style="margin-top: 10px">
        <div class="tabGroup">
            <el-radio-group v-model="view" style="align-self: flex-start">
                <el-radio-button label="day">日视图</el-radio-button>
                <el-radio-button label="month">月视图</el-radio-button>
            </el-radio-group>
            <div class="filter" v-show="view === 'day'">
                <el-button icon="el-icon-arrow-left" @click="prevMonth"
                    >上月</el-button
                >
                <el-date-picker
                    v-model="selectedDate"
                    type="month"
                    placeholder="选择月"
                    :clearable="false"
                >
                </el-date-picker>
                <el-button @click="nextMonth"
                    >下月<i class="el-icon-arrow-right"></i
                ></el-button>
            </div>
            <div class="filter" v-show="view === 'month'">
                <el-button icon="el-icon-arrow-left" @click="prevYear"
                    >上一年</el-button
                >
                <el-date-picker
                    v-show="view === 'month'"
                    v-model="selectedDate"
                    type="year"
                    placeholder="选择年"
                    :clearable="false"
                >
                </el-date-picker>
                <el-button @click="nextYear"
                    >下一年<i class="el-icon-arrow-right"></i
                ></el-button>
            </div>
            <!-- 切换视图类型 -->
            <el-radio-group
                style="align-self: flex-end"
                v-model="showType"
                v-show="view === 'month'"
            >
                <el-radio-button label="title"> 职称 </el-radio-button>
                <el-radio-button label="group" :disabled="isTopLevelDepart">
                    小组
                </el-radio-button>
            </el-radio-group>
        </div>
        <div id="resourceChart" class="resourceChart"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import {
    getResoureLoadChartByDay,
    getResoureLoadChartByMonth
} from './resourceOverviewChart';
import { Loading } from 'element-ui';
import i18n from 'wtf-core-vue/src/lang';

const curDate = new Date();
export default {
    name: 'ResourceLoadChart',
    props: {
        orgCode: { type: String, default: '' },
        isTopLevelDepart: { type: Boolean, default: false }
    },
    data() {
        return {
            dateType: '全年',
            date: curDate,
            view: 'day',
            selectedDate: curDate,
            dividedNumber: 13,
            hourDataList: [],
            showType: 'title',
            getChartByDay:
                this.$service.department.naturalResources.getChartByDay,
            getTitleChartByMonth:
                this.$service.department.naturalResources.getTitleChartByMonth,
            getGroupChartByMonth:
                this.$service.department.naturalResources.getGroupChartByMonth
        };
    },
    computed: {
        year() {
            return this.selectedDate.getFullYear();
        },
        month() {
            return this.selectedDate.getMonth() + 1;
        },
        // 每个月的天数
        daysNumInMonth() {
            return this.getDaysInMonth(this.year, this.month).length;
        }
    },
    watch: {
        view(newVal) {
            newVal && this.handleChange();
        },
        selectedDate(newVal) {
            newVal && this.handleChange();
        },
        orgCode(newVal) {
            newVal && this.handleChange();
        },
        showType(newVal) {
            newVal && this.handleChange();
        }
    },
    mounted() {
        this.handleChange();
    },
    methods: {
        // 研发项目及需求数量图
        setResourceChart(res) {
            const myChart = echarts.init(
                document.getElementById('resourceChart')
            );
            // 清除上次的option，避免option合并
            myChart.clear();
            let option;
            if (this.view === 'day') {
                option = getResoureLoadChartByDay(
                    res.resourceOverviewDetailVoDayList,
                    Number(res.expectedStartDate),
                    this.daysNumInMonth
                );
            } else {
                option = getResoureLoadChartByMonth(
                    res.resourceOverviewDetailVoMonthList,
                    Number(res.expectedStartDate)
                );
            }
            myChart.setOption(option);
            window.addEventListener('resize', () => {
                myChart.resize();
            });
        },
        /**
         * 获取选择的日期
         * @param {String} type 日视图或月视图
         * @return {String} 选择的日期
         */
        getSelectedDate(type) {
            let selectDate;
            if (type === 'day' && this.month <= 9) {
                selectDate = `${this.year}-0${this.month}`;
            } else if (type === 'day' && this.month > 9) {
                selectDate = `${this.year}-${this.month}`;
            } else if (type === 'month') {
                selectDate = `${this.year}`;
            }
            return selectDate;
        },
        /**
         * 获取当月天数
         * @param {*} year 当年
         * @param {*} month 当月
         * @returns {Array} 当月天数的数组
         */
        getDaysInMonth(year, month) {
            const days = new Date(year, month, 0).getDate();
            return Array.from({ length: days }, (v, k) => k + 1);
        },
        /**
         * 上一月
         */
        prevMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 0) {
                this.selectedDate = new Date(currentYear - 1, 11, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth - 1, 1);
            }
        },
        /**
         * 下一月
         */
        nextMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 11) {
                this.selectedDate = new Date(currentYear + 1, 0, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth + 1, 1);
            }
        },
        /**
         * 上一年
         */
        prevYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear - 1, 0, 1);
        },
        /**
         * 下一年
         */
        nextYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear + 1, 0, 1);
        },
        /**
         * 处理参数变更
         */
        handleChange() {
            if (!this.orgCode) {
                return;
            }
            const loadingInstance = Loading.service({
                text: i18n.t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            const selectedDate = this.getSelectedDate(this.view);
            const splitDate = selectedDate.split('-');
            const requestData = {
                code: this.orgCode || '',
                year: splitDate[0],
                month: splitDate[1]
            };
            let api;
            if (this.view === 'day') {
                api = this.getChartByDay;
            } else {
                if (this.isTopLevelDepart === true) {
                    this.showType = 'title';
                }
                api =
                    this.showType === 'title'
                        ? this.getTitleChartByMonth
                        : this.getGroupChartByMonth;
            }
            api(requestData)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.setResourceChart(res.body);
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                })
                .finally(() => {
                    loadingInstance && loadingInstance.close();
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.resourceChart {
    margin: 30px 20px 20px 20px;
    border: 1px solid;
    border-radius: 10px;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
    height: 540px;
    overflow: hidden;
}
.tabGroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    .filter {
        flex: 1;
        display: flex;
        justify-content: center;
    }
}
.select-icon {
    height: 20px;
    width: 20px;
}
</style>
