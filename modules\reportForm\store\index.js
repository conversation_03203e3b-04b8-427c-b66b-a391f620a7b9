/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.reportForm.xxx
 */

const state = {
    weeklyValue: [],
    weeklyOption: [],
    // 这个字段用来判断，开发周报/维护周报是否从外部引入的，如果有外部的周报id，跳过更新，直接查询
    outsideWeekly: null,
    // 是否为开发项目周报
    isDevelopWeekly: true
};

const mutations = {
    SET_WEEKLY_VALUE(state, value) {
        state.weeklyValue = value;
    },
    SET_WEEKLY_OPTION(state, option) {
        state.weeklyOption = option;
    },
    SET_OUTSIDE_WEEKLY(state, option) {
        state.outsideWeekly = option;
    },
    SET_IS_DEVELOP_WEEKLY(state, option) {
        state.isDevelopWeekly = option;
    }
};

const actions = {
    selectedWeeklyValue({ commit }, value) {
        commit('SET_WEEKLY_VALUE', value);
    },
    selectedWeeklyOption({ commit }, option) {
        commit('SET_WEEKLY_OPTION', option);
    },
    setOutsideWeekly({ commit }, value) {
        commit('SET_OUTSIDE_WEEKLY', value);
    },
    setIsDevelopWeekly({ commit }, value) {
        commit('SET_IS_DEVELOP_WEEKLY', value);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
