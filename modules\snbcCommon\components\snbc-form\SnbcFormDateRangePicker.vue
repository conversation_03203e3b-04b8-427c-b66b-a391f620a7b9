<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-date-picker
            v-model="config.modelObj[config.modelKey]"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            v-bind="elDatePickerAttrs"
            @change="handleChange"
            @focus="handleFocus"
            @blur="handleBlur"
            class="date-picker"
        />
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormDatePicker',
    props: {
        /**
         * SnbcFormDatePicker组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elDatePickerAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-date-picker组件默认属性设置
            defaultElDatePickerAttrs: {
                'type': 'daterange',
                'range-separator': '至',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                'value-format': 'yyyy-MM-dd HH:mm:ss',
                'default-time': ['00:00:00', '23:59:59']
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-date-picker组件应用属性
        elDatePickerAttrs() {
            return {
                ...this.defaultElDatePickerAttrs,
                ...(this.config.elDatePickerAttrs || {})
            };
        }
    },
    methods: {
        // 变化事件 处理clearable操作将数组格式的属性值设置为null的场景
        handleChange() {
            const { modelObj, modelKey } = this.config;
            if (modelObj[modelKey] === null) {
                this.$nextTick(() => {
                    modelObj[modelKey] = [];
                });
            }
        },
        // 获取焦点事件
        handleFocus() {
            this.config.focusHandler && this.config.focusHandler();
        },
        // 失去焦点事件
        handleBlur() {
            this.config.blurHandler && this.config.blurHandler();
        }
    }
};
</script>
<style scoped>
.date-pick {
    font-weight: initial;
}
</style>
