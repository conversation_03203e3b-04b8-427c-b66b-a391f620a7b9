import CommonItems from 'snbcCommon/common/form-items.js';
import { CONSTANTS } from '@/constants';

const { select, input, radio } = CommonItems;
const riskType = {
    ...select,
    name: '风险类型',
    modelKey: 'riskType',
    elOptions: []
};
const riskLevel = {
    ...select,
    name: '风险等级',
    modelKey: 'riskLevel',
    elOptions: CONSTANTS.RISK_LEVEL.map((i) => ({ label: i, value: i }))
};
const riskStatus = {
    ...select,
    name: '风险状态',
    modelKey: 'riskStatus',
    elOptions: CONSTANTS.RISK_STATUS.map((i) => ({ label: i, value: i }))
};
const productModel = {
    ...select,
    name: '产品型号',
    modelKey: 'productModel',
    elOptions: []
};

const riskPlanType = {
    ...radio,
    name: '风险应对计划',
    modelKey: 'riskPlanType',
    elRadios: [
        { label: '需要创建', value: '需要创建' },
        { label: '不需要创建', value: '不需要创建' }
    ]
};

const hasNoCloseSupportType = {
    ...radio,
    name: '存在未关闭的需支持事项',
    modelKey: 'hasNoCloseSupportType',
    elRadios: [
        { label: '有', value: '有' },
        { label: '无', value: '无' }
    ]
};
const riskTitle = {
    ...input,
    name: '风险标题',
    modelKey: 'riskTitle'
};

// 查询参数初始化
export const queryParams = {
    faultPlanType: '',
    riskType: '',
    riskLevel: '',
    riskTitle: '',
    productModel: '',
    riskPlanType: '',
    hasNoCloseSupportType: ''
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '170px'
    },
    items: [
        riskTitle,
        riskType,
        riskLevel,
        riskStatus,
        productModel,
        riskPlanType,
        hasNoCloseSupportType
    ]
};

export const navItems = [
    { field: '', name: '所有', queryField: '' },
    { field: '进行中', name: '未关闭的', queryField: 'riskStatus' },
    {
        field: '高',
        name: '高等级的',
        queryField: 'riskLevel'
    },
    {
        name: '需要支持的',
        field: '有',
        queryField: 'hasNoCloseSupportType'
    }
];
