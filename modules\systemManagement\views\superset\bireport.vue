<template>
    <div class="dashboard">
        <iframe :src="iframeUrl" width="100%" height="100%"></iframe>
    </div>
</template>

<script>
import generateSignature from './generateSignature';

export default {
    data() {
        return {
            iframeUrl: ''
        };
    },
    mounted() {
        // 先验证一下token是否过期
        this.$service.systemManagement.verifyToken().then((response) => {
            console.log(response);
        });
        // 截取id参数
        const indexlast = this.$route.path.lastIndexOf('/');
        const reportId = this.$route.path.slice(indexlast + 1);
        // 截取环境
        const { origin } = window.location;
        const startIndex = origin.indexOf('//');
        const endIndex = origin.indexOf('-');
        const envName = origin.slice(startIndex + 2, endIndex + 1);
        const baseUrl = `https://${envName}bi.xinbeiyang.info`;
        const url = `/superset/dashboard/${reportId}/?standalone=2&expand_filters=0&sysType=1&token=${this.$tools.getToken()}`;
        const config = {
            url,
            method: 'GET'
        };
        const signature = generateSignature(config);
        this.iframeUrl = `${baseUrl}${url}&signature=${signature}`;
    }
};
</script>
<style type="text/css">
html,
body,
.dashboard {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

.dashboard iframe {
    width: 100%;
    height: 100%;
    border: 0;
}
</style>
./sign
./generateSignature