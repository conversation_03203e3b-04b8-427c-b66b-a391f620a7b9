import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        workTime: {
            // 查询工时
            getWorkTime(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/zentao/hours_assignment/getHoursAssignmentList',
                    method: 'post',
                    data
                });
            },
            // 修改预计工时
            editWorkTime(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/zentao/hours_assignment/updateHoursAssignment',
                    method: 'post',
                    data
                });
            },
            // 获取团队资源负载日视图
            getResourceLoadByDay(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource_load/resource_day_view',
                    method: 'post',
                    data
                });
            },
            // 获取团队资源负载月视图
            getResourceLoadByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource_load/resource_month_view',
                    method: 'post',
                    data
                });
            },
            // 预计/异常任务指派
            assignTask(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/zentao/hours_assignment/taskAssign',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
