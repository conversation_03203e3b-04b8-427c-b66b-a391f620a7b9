<template>
    <div>
        <el-dialog
            :title="title"
            :visible.sync="dialogVisible"
            :before-close="reset"
            width="80%"
            append-to-body
        >
            <el-form
                ref="form"
                :model="form"
                class="form"
                size="small"
                label-width="150px"
                :rules="rules"
            >
                <el-divider>基本信息</el-divider>
                <el-form-item v-if="type === 'edit'" label="ID" prop="riskId">
                    {{ form.riskId }}
                </el-form-item>
                <el-form-item label="风险标题" prop="riskTitle">
                    <el-input
                        v-model="form.riskTitle"
                        placeholder="请输入风险标题"
                    ></el-input>
                </el-form-item>
                <div class="flex">
                    <el-form-item
                        label="风险状态"
                        prop="riskStatus"
                        class="w-50"
                    >
                        <el-select
                            v-model="form.riskStatus"
                            placeholder="请选择风险状态"
                        >
                            <el-option
                                v-for="item in CONSTANTS.RISK_STATUS"
                                :label="item"
                                :key="item"
                                :value="item"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                    <el-form-item label="项目经理" prop="projectManager">
                        <PeopleSelector
                            ref="projectManagerName"
                            v-model="form.projectManager"
                            :isAll="1"
                            :isMultipled="false"
                            :disabled="true"
                        ></PeopleSelector>
                    </el-form-item>
                </div>
                <template v-if="innerProjectType === 'maintenance'">
                    <div class="flex" style="margin-bottom: 18px; height: 40px">
                        <el-form-item
                            label="风险关联对象"
                            prop="riskAssObjectType"
                            class="w-50"
                        >
                            <el-select
                                v-model="form.riskAssObjectType"
                                placeholder="请选择风险关联对象"
                                :disabled="type === 'edit' || !!relatedObject"
                                @change="handleRiskObejctChange"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.RISK_RELATED_OBJECT"
                                    :label="item"
                                    :key="item"
                                    :value="item"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                        <el-form-item
                            v-if="form.riskAssObjectType === '维护项目缺陷'"
                            label="缺陷"
                            prop="assObjectId"
                            class="w-50"
                        >
                            <el-select
                                v-model="form.assObjectId"
                                placeholder="请选择缺陷"
                                class="w-100"
                                ref="assObjectName"
                                :disabled="type === 'edit'"
                                @change="handleAssObjectChange"
                            >
                                <el-option
                                    v-for="item in defectList"
                                    :label="item.faultTitle"
                                    :key="item.id"
                                    :value="item.id"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                        <el-form-item
                            v-if="form.riskAssObjectType === '维护项目需求'"
                            label="需求"
                            prop="assObjectId"
                            class="w-50"
                        >
                            <el-select
                                v-model="form.assObjectId"
                                placeholder="请选择需求"
                                class="w-100"
                                ref="assObjectName"
                                :disabled="type === 'edit'"
                                @change="handleAssObjectChange"
                            >
                                <el-option
                                    v-for="item in demandList"
                                    :label="item.demandName"
                                    :key="item.id"
                                    :value="item.id"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                        <el-form-item
                            v-if="form.riskAssObjectType === '订单'"
                            label="订单"
                            prop="assObjectId"
                            class="w-50"
                        >
                            <el-select
                                v-model="form.assObjectId"
                                placeholder="请选择订单"
                                ref="assObjectName"
                                :disabled="type === 'edit'"
                                class="w-100"
                                @change="handleAssObjectChange"
                            >
                                <el-option
                                    v-for="item in orderList"
                                    :label="item.flowNo"
                                    :key="item.orderId"
                                    :value="item.orderId"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                    </div>
                    <div
                        class="flex"
                        v-if="form.riskAssObjectType === '维护项目任务'"
                    >
                        <el-form-item
                            label="项目"
                            prop="assProjectId"
                            class="w-50"
                        >
                            <el-select
                                class="w-100"
                                v-model="form.assProjectId"
                                placeholder="请选择项目"
                                @change="getOngoingZentaoTaskList"
                                :disabled="type === 'edit'"
                                filterable
                                ref="assProjectName"
                            >
                                <el-option
                                    v-for="item in zentaoProjectList"
                                    :label="item.name"
                                    :key="item.id"
                                    :value="item.id"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                        <el-form-item
                            label="任务"
                            prop="assTaskId"
                            class="w-50"
                            :rules="required"
                        >
                            <el-select
                                placeholder="请选择任务"
                                class="w-100"
                                v-model="form.assTaskId"
                                filterable
                                :disabled="type === 'edit'"
                                ref="assTaskName"
                                @change="handleAssTaskChange"
                            >
                                <el-option
                                    v-for="item in ongoingZentaoTaskList"
                                    :label="`ID${item.id}：${item.name}`"
                                    :key="item.id"
                                    :value="item.id"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                    </div>
                </template>
                <!-- 开发项目 -->
                <template v-else>
                    <div class="flex">
                        <el-form-item
                            label="风险关联对象"
                            prop="riskAssObjectType"
                            class="w-50"
                        >
                            <el-select
                                v-model="form.riskAssObjectType"
                                placeholder="请选择风险关联对象"
                                :disabled="type === 'edit' || !!relatedObject"
                                @change="handleRiskObejctChange"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.RISK_RELATED_OBJECT_DEVELOP"
                                    :label="item"
                                    :key="item"
                                    :value="item"
                                ></el-option
                            ></el-select>
                        </el-form-item>
                        <el-form-item
                            class="w-50"
                            label="项目"
                            prop="assProjectName"
                        >
                            {{ form.assProjectName }}
                        </el-form-item>
                    </div>
                    <el-form-item
                        v-if="form.riskAssObjectType === '开发项目任务'"
                        label="任务"
                        prop="assTaskId"
                        class="w-50"
                    >
                        <el-select
                            class="w-100"
                            v-model="form.assTaskId"
                            :disabled="type === 'edit'"
                            filterable
                            ref="assTaskName"
                            clearable
                        >
                            <el-option
                                v-for="item in developZentaoTaskList"
                                :label="`ID${item.id}：${item.name}`"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                    <el-form-item
                        v-if="form.riskAssObjectType === '开发项目订单'"
                        label="订单"
                        prop="assObjectId"
                        class="w-50"
                    >
                        <el-select
                            v-model="form.assObjectId"
                            placeholder="请选择订单"
                            :disabled="type === 'edit'"
                            ref="assObjectName"
                            class="w-100"
                            @change="handleAssObjectChange"
                        >
                            <el-option
                                v-for="item in orderList"
                                :label="item.flowNo"
                                :key="item.orderId"
                                :value="item.orderId"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                </template>

                <el-form-item class="w-50" label="产品型号" prop="productModel">
                    <div class="flex">
                        <el-input
                            v-model="form.productModel"
                            :disabled="type === 'edit' || productModelDisabled"
                            placeholder="请输入产品型号"
                            @blur="handleproductModelBlur"
                        ></el-input>
                    </div>
                </el-form-item>

                <div class="flex">
                    <el-form-item
                        label="归属产品线"
                        prop="productLine"
                        key="productLine"
                        class="w-50"
                    >
                        <el-select
                            v-model="form.productLine"
                            placeholder="请选择归属产品线"
                            :disabled="type === 'edit' || productLineDisabled"
                            size="medium"
                            @change="handleProductLineChange"
                        >
                            <el-option
                                v-for="item in productLineList"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="归属细分产品线"
                        prop="subProductLine"
                        key="subProductLine"
                    >
                        <el-select
                            v-model="form.subProductLine"
                            :disabled="
                                type === 'edit' || subProductLineDisabled
                            "
                            placeholder="请选择归属细分产品线"
                            size="medium"
                        >
                            <el-option
                                v-for="item in subProductLineOptions"
                                :key="item.paramName"
                                :label="item.paramName"
                                :value="item.paramName"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </div>

                <el-divider>风险识别</el-divider>
                <div class="flex">
                    <el-form-item label="风险识别日期" prop="riskIdentifyDate">
                        <el-date-picker
                            v-model="form.riskIdentifyDate"
                            type="date"
                            placeholder="请选择风险识别日期"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="风险等级" prop="riskLevel">
                        <el-select
                            v-model="form.riskLevel"
                            placeholder="请选择风险等级"
                        >
                            <el-option
                                v-for="item in CONSTANTS.RISK_LEVEL"
                                :label="item"
                                :key="item"
                                :value="item"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                    <el-form-item label="风险类型" prop="riskType">
                        <el-select
                            v-model="form.riskType"
                            placeholder="请选择风险类型"
                        >
                            <el-option
                                v-for="item in riskTypeOptions"
                                :label="item.paramName"
                                :key="item.paramName"
                                :value="item.paramName"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                </div>
                <el-form-item label="风险描述及影响" prop="riskDesc">
                    <el-input
                        type="textarea"
                        v-model="form.riskDesc"
                        placeholder="请输入风险描述及影响"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>

                <el-divider>风险应对</el-divider>
                <el-form-item label="应对措施" prop="solutionMeasures">
                    <el-input
                        type="textarea"
                        v-model="form.solutionMeasures"
                        placeholder="请输入应对措施"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>
                <div class="flex">
                    <el-form-item
                        label="计划完成时间"
                        prop="planFinishDate"
                        class="w-50"
                    >
                        <el-date-picker
                            v-model="form.planFinishDate"
                            type="date"
                            placeholder="请选择计划完成时间"
                            value-format="yyyy-MM-dd"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item
                        label="责任人"
                        prop="responsiblePersonAccountList"
                        class="w-50"
                    >
                        <PeopleSelector
                            class="w-100"
                            ref="responsiblePersonList"
                            v-model="form.responsiblePersonAccountList"
                            :isAll="1"
                        ></PeopleSelector>
                    </el-form-item>
                </div>

                <el-divider>风险应对计划</el-divider>
                <el-form-item label="风险应对计划" prop="riskPlanType">
                    <el-radio-group
                        v-model="form.riskPlanType"
                        :disabled="faultPlanTypeDisabled"
                    >
                        <el-radio label="不需要创建">不需要创建</el-radio>
                        <el-radio label="需要创建">需要创建</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="关联禅道项目"
                    prop="proProjectId"
                    v-if="form.riskPlanType === '需要创建'"
                    :rules="form.riskPlanType === '需要创建' ? required : {}"
                >
                    <el-select
                        class="w-100"
                        v-model="form.proProjectId"
                        placeholder="请选择关联禅道项目"
                        :disabled="riskPlanTypeDisabled"
                        @change="getZentaoTaskList"
                        filterable
                    >
                        <el-option
                            v-for="item in zentaoProjectList"
                            :label="item.name"
                            :key="item.id"
                            :value="item.id"
                        ></el-option
                    ></el-select>
                </el-form-item>
                <el-form-item
                    label="关联禅道任务"
                    prop="proTaskId"
                    v-if="form.riskPlanType === '需要创建' && form.proTaskId"
                >
                    <div class="flex space-between">
                        <el-select
                            v-if="form.newProTaskId && form.newProTaskId !== 0"
                            class="w-100"
                            v-model="form.newProTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                        <div v-else-if="form.newProTaskId === 0"></div>
                        <el-select
                            v-else-if="form.proTaskId"
                            class="w-100"
                            v-model="form.proTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                        <div v-else></div>
                        <ChangeTaskDialog
                            v-if="form.proProjectId"
                            :hasTaskHour="consumed !== '0.0'"
                            :zentaoProjectList="zentaoProjectList"
                            @save="handleChangeTaskSave"
                            class="pl-20"
                        ></ChangeTaskDialog>
                    </div>
                </el-form-item>

                <el-divider>风险应对需支持事项</el-divider>
                <el-form-item
                    label="风险应对需支持事项"
                    prop="riskSupportType"
                    label-width="160px"
                >
                    <el-radio-group v-model="form.riskSupportType">
                        <el-radio label="有">有</el-radio>
                        <el-radio label="无">无</el-radio>
                    </el-radio-group>
                </el-form-item>
                <SupportMatter
                    v-show="form.riskSupportType === '有'"
                    v-model="form.riskSupportObject"
                    ref="supportMatterRef"
                ></SupportMatter>
                <el-divider>风险控制</el-divider>
                <el-form-item
                    label="风险落实情况"
                    prop="riskImplementInfo"
                    :rules="rulesByStatus"
                >
                    <el-input
                        type="textarea"
                        v-model="form.riskImplementInfo"
                        placeholder="请输入风险落实情况"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-if="this.rulesForFinishDate"
                    label="实际完成日期"
                    prop="finishDate"
                >
                    <el-date-picker
                        v-model="form.finishDate"
                        type="date"
                        placeholder="请选择实际完成日期"
                        value-format="yyyy-MM-dd"
                    ></el-date-picker>
                </el-form-item>
            </el-form>
            <div class="footer" slot="footer">
                <el-button
                    v-if="type === 'edit' && !isWeekly"
                    type="danger"
                    @click="handleDelete"
                    >删 除</el-button
                >
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';
import {
    getSelectedLabel,
    getUserAccount
} from 'feature/views/meetingManagement/commonFunction';
import ChangeTaskDialog from 'maintenanceProject/components/ChangeTaskDialog/index.vue';
import SupportMatter from 'maintenanceProject/components/RiskUpdateDialog/SupportMatter.vue';
import moment from 'moment';
import { omit } from 'lodash';
import { rules } from './formInit';

const oriForm = {
    riskTitle: '',
    productModel: '',
    riskLevel: '',
    riskType: '',
    riskIdentifyDate: moment().format('YYYY-MM-DD'),
    riskStatus: '',
    projectManager: getUserAccount(this),
    riskAssObjectType: '',
    assProjectId: '',
    assTaskId: '',
    assObjectId: '',
    riskSupportType: '无',
    finishDate: '',
    riskImplementInfo: '',
    riskPlanType: '不需要创建',
    proProjectId: '',
    newProTaskId: '',
    proTaskId: '',
    productLine: '',
    subProductLine: '',
    riskDesc: '',
    solutionMeasures: '',
    responsiblePersonAccountList: [],
    riskSupportList: [],
    riskSupportObject: {
        riskSupportList: [
            {
                supportItem: '',
                expectedDate: '',
                responsiblePersonAccountList: [],
                responsibleOrgCodeList: '',
                supportStatus: ''
            }
        ]
    }
};

const longLengthLimit = {
    min: 0,
    max: 2000,
    message: '最多输入2000个字符',
    trigger: ['change', 'blur']
};

export default {
    name: 'RiskUpdateDialog',
    components: { PeopleSelector, ChangeTaskDialog, SupportMatter },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'add'
        },
        // 风险关联对象
        relatedObject: {
            type: String,
            default: ''
        },
        // 风险关联对象对应的id
        relatedObjectId: {
            type: String,
            default: ''
        },
        // 风险ID，用于编辑风险
        id: {
            type: String,
            default: ''
        },
        // 项目类型：develop/maintenance
        projectType: {
            type: String,
            default: 'maintenance'
        },
        // 开发项目名称
        assProjectName: {
            type: String,
            default: ''
        },
        // 开发项目名称
        assProjectId: {
            type: String,
            default: ''
        },
        // 开发项目对应的禅道Id
        proProjectId: {
            type: [String, Number],
            default: ''
        },
        // 是否从项目周报点进来的
        isWeekly: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            productLineList: this.$store.state.project.productLine,
            form: {
                riskTitle: '',
                productModel: '',
                riskLevel: '',
                riskType: '',
                riskIdentifyDate: moment().format('YYYY-MM-DD'),
                riskStatus: '',
                projectManager: getUserAccount(this),
                riskAssObjectType: '',
                assProjectId: '',
                assTaskId: '',
                riskSupportType: '无',
                finishDate: null,
                riskImplementInfo: '',
                riskPlanType: '不需要创建',
                proProjectId: '',
                newProTaskId: '',
                proTaskId: '',
                productLine: '',
                subProductLine: '',
                riskDesc: '',
                solutionMeasures: '',
                responsiblePersonAccountList: [],
                riskSupportList: [],
                riskSupportObject: {
                    riskSupportList: [
                        {
                            supportItem: '',
                            expectedDate: '',
                            responsiblePersonAccountList: [],
                            responsibleOrgCodeList: '',
                            supportStatus: ''
                        }
                    ]
                }
            },
            CONSTANTS,
            required: {
                required: true,
                // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            },
            subProductLineOptions: [],
            zentaoProjectList: [],
            zentaoTaskList: [],
            riskPlanTypeDisabled: false,
            riskTypeOptions: [],
            rules,
            consumed: '0.0',
            demandList: [],
            defectList: [],
            orderList: [],
            faultPlanTypeDisabled: false,
            // 仅适用于维护项目风险编辑界面，任务状态为wait和doing的禅道任务列表
            ongoingZentaoTaskList: [],
            // 仅适用于开发项目风险编辑界面，任务状态为wait和doing的禅道任务列表
            developZentaoTaskList: [],
            innerProjectType: 'maintenance',
            productModelDisabled: false,
            productLineDisabled: false,
            subProductLineDisabled: false,
            listNumbers: 0
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        },
        title() {
            return this.type === 'add' ? '新建风险' : '编辑风险';
        },
        rulesByStatus() {
            const riskStatusList = ['已关闭', '转为非风险', '转为问题'];
            return riskStatusList.includes(this.form.riskStatus)
                ? [this.required, longLengthLimit]
                : [longLengthLimit];
        },
        rulesForFinishDate() {
            return this.form.riskStatus === '已关闭';
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                if (this.type === 'add') {
                    this.form.riskId = '';
                }
                this.innerProjectType = this.projectType;

                if (this.innerProjectType === 'develop') {
                    this.form.assProjectName = this.assProjectName;
                    this.form.assProjectId = this.assProjectId;
                }

                if (this.relatedObject) {
                    this.form.riskAssObjectType = this.relatedObject;
                    this.form.assObjectId = this.relatedObjectId;
                }
                if (this.id) {
                    this.getDetail();
                } else {
                    this.getZentaoProjectList();
                    this.getOngoingZentaoTaskList();
                }
                this.getDemandList();
                this.getDefectList();
                this.getOrderList();
                this.getRiskTypeOptions();
            }
        },
        // 项目列表查询完成的数量
        listNumbers(newVal) {
            if (newVal === 3) {
                // 等待项目列表查询完毕
                this.relatedObjectId &&
                    this.handleAssObjectChange(this.relatedObjectId);
            }
        }
    },
    methods: {
        resetDialogState() {
            this.listNumbers = 0;
            this.$refs.form && this.$refs.form.resetFields();
            this.riskPlanTypeDisabled = false;
            this.form = this.$tools.cloneDeep(oriForm);

            // 重置所有下拉列表选项
            Object.keys(this.$data).forEach((key) => {
                if (key.includes('List') || key.includes('Options')) {
                    this[key] = [];
                }
            });
            this.consumed = '0.0';
        },
        closeDialog() {
            this.resetDialogState();
            this.dialogVisible = false;
            this.$emit('update');
        },
        reset(done) {
            this.resetDialogState();
            done();
        },
        /**
         * 参数预处理
         * @returns {Object} 处理后的参数
         */
        handleParams() {
            // 编辑的时候，如果此时没有创建任务，并且选择了需要创建
            // 将 newProTaskId 改为 0
            if (!this.form.proTaskId && this.form.riskPlanType === '需要创建') {
                this.form.newProTaskId = 0;
            }
            if (this.form.riskPlanType === '不需要创建') {
                this.form.proProjectId = '';
            }

            // 如果实际完成日期字段未显示，清空该值
            if (!this.rulesForFinishDate) {
                this.form.finishDate = null;
            }

            let params = this.form;

            if (this.form.riskSupportType === '无') {
                this.form.riskSupportList = [];
                params = omit(this.form, ['riskSupportObject']);
            } else {
                // 获取SupportMatter组件中的责任人label信息
                const { supportMatterRef } = this.$refs;
                const riskSupportList =
                    this.form.riskSupportObject.riskSupportList.map(
                        (item, index) => {
                            const responsibleOrgList = supportMatterRef
                                .getRiskSupportListLabel(index)
                                .map((i) => i.join('/'));
                            const responsiblePersonList =
                                supportMatterRef.getResponsiblePersonLabel(
                                    index
                                );
                            // 如果清除了责任人，
                            if (responsiblePersonList.length === 0) {
                                item.responsiblePerson = '';
                                item.responsiblePersonAccount = '';
                            }
                            return {
                                ...item,
                                // 部门
                                responsibleOrgCodeList:
                                    item.responsibleOrgCodeList.map((i) =>
                                        i.join('/')
                                    ),
                                responsibleOrgList,
                                // 责任人
                                responsiblePersonAccountList:
                                    item.responsiblePersonAccountList,
                                responsiblePersonList
                            };
                        }
                    );
                riskSupportList.forEach((i) => {
                    i.responsibleOrgShortList = this.getAbbreviation(
                        i.responsibleOrgList
                    );
                });

                params = {
                    ...params,
                    riskSupportList
                };
            }
            return params;
        },
        /**
         * 获取提供支持的责任部门简称列表
         * @param {Array} list 提供支持的责任部门列表
         * @returns {Array} 责任部门简称列表
         */
        getAbbreviation(list) {
            return list.map((i) => {
                const arr = i.split('/');
                // 长度为1，就直接返回
                if (arr.length === 1) {
                    return arr[0];
                }
                // 这里不关心新北洋以外的多级部门
                if (arr[0] !== '新北洋') {
                    return arr[0];
                }
                // 长度为2，返回二级
                if (arr.length === 2) {
                    return arr[1];
                }
                // 长度为3，分为技术中心和非技术中心两种
                if (arr.length === 3) {
                    if (arr[1] === '技术中心') {
                        return arr[2];
                    }
                    return `${arr[1]}/${arr[2]}`;
                }
                // 长度为4，也分为技术中心和非技术中心两种
                if (arr.length === 4) {
                    if (arr[1] === '技术中心') {
                        return `${arr[2]}/${arr[3]}`;
                    }
                    return `${arr[1]}/${arr[2]}`;
                }
                return i;
            });
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            const api = this.$service.maintenanceProject.risk.update;
            const projectManagerName = getSelectedLabel(
                this.$refs.projectManagerName
            );
            const responsiblePersonList = getSelectedLabel(
                this.$refs.responsiblePersonList
            );
            const params = this.handleParams();

            let assObjectName = '';
            let assProjectName = '';
            let assTaskName = '';
            if (this.innerProjectType === 'develop') {
                assProjectName = this.form.assProjectName;
                if (params.riskAssObjectType === '开发项目任务') {
                    assTaskName = this.$refs.assTaskName.selected.label || '';
                } else {
                    assObjectName = this.$refs.assObjectName.selected.label;
                }
            } else if (params.riskAssObjectType === '维护项目任务') {
                assProjectName = this.$refs.assProjectName.selected.label;
                assTaskName = this.$refs.assTaskName.selected.label;
            } else {
                assObjectName = this.$refs.assObjectName.selected.label;
            }
            try {
                const res = await api({
                    ...params,
                    projectManagerName,
                    responsiblePersonList,
                    assProjectName,
                    assTaskName,
                    assObjectName,
                    riskStageType:
                        this.innerProjectType === 'develop' ? '开发' : '维护'
                });
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                if (this.form.riskSupportType === '有') {
                    const supportValid =
                        await this.$refs.supportMatterRef.validateForm();
                    return supportValid;
                }
                return true;
            } catch (error) {
                console.error(error);
                return false;
            }
        },
        /**
         * 产品型号失焦时，进行查询获取产品线/细分产品线/产品名称
         * 获取到之后，将对应的空的选项填入获取到的值
         * 非空选项不变
         */
        async handleproductModelBlur() {
            if (!this.form.productModel) return;
            const api =
                this.$service.maintenanceProject.common
                    .getProductInfoByProductModel;
            const params = {
                productModel: this.form.productModel
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    const { productLine, subProductLine, productName } =
                        res.body;
                    if (productLine) {
                        this.form.productLine = productLine;
                    }
                    if (subProductLine) {
                        this.form.subProductLine = subProductLine;
                    }
                    if (productName) {
                        this.form.productName = productName;
                    }
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 删除缺陷
         */
        async handleDelete() {
            await this.$confirm('确定删除吗?', '提示', { type: 'warning' });
            const api = this.$service.maintenanceProject.risk.delete;
            const params = {
                riskId: this.form.riskId,
                proTaskId: this.form.proTaskId
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.$message.success('删除成功');
                    const { name } = this.$route;
                    if (
                        name === 'MaintenanceRiskDetail' ||
                        name === 'ProjectRiskDetail'
                    ) {
                        history.go(-1);
                        return;
                    }
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 产品线变更后，根据产品线查询细分产品线
         * @param {String} value 产品线
         */
        handleProductLineChange(value) {
            this.form.subProductLine = '';
            this.getSubProductLineOptions(value);
        },
        /**
         * 获取细分产品线对应的选项
         * @param {String} value 产品线
         */
        async getSubProductLineOptions(value) {
            const api = this.$service.project.finance.getSelectOptions;
            const params = { paramName: '细分产品线', paramType: value };
            const res = await api(params);
            if (res.head.code !== '000000') {
                this.$message.error(res.head.message);
                return;
            }
            this.subProductLineOptions = res.body;
        },
        /**
         * 获取禅道项目下拉列表
         */
        async getZentaoProjectList() {
            const api =
                this.$service.maintenanceProject.zentao.getZentaoProjectList;
            const params = {
                projectCode: this.innerProjectType === 'develop' ? '' : '维护',
                projectManagerAccount: this.id
                    ? this.form.projectManager
                    : getUserAccount(this)
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.zentaoProjectList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 根据项目获取子任务接口
         * @param {String} value id
         */
        async getZentaoTaskList() {
            if (!this.form.proTaskId) return;
            const api =
                this.$service.maintenanceProject.zentao.getZentaoTaskList;
            try {
                const res = await api({ proProjectId: this.form.proProjectId });
                if (res.head.code === '000000') {
                    this.zentaoTaskList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 根据项目获取子任务接口（只包含wait和doing这两种状态的）
         * @param {String} value id
         */
        async getOngoingZentaoTaskList() {
            if (!this.form.assProjectId) return;
            const api =
                this.$service.maintenanceProject.zentao.getZentaoTaskList;
            const params = {
                proProjectId:
                    this.innerProjectType === 'develop'
                        ? this.proProjectId
                        : this.form.assProjectId,
                proTaskStatusList: ['wait', 'doing']
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    if (this.innerProjectType === 'develop') {
                        this.developZentaoTaskList = res.body;
                    } else {
                        this.ongoingZentaoTaskList = res.body;
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleChangeTaskSave(value) {
            this.form.proProjectId = value.proProjectId;
            this.form.newProTaskId = value.proTaskId;
            this.getZentaoTaskList();
        },
        /**
         * 获取风险类型对应的选项
         */
        async getRiskTypeOptions() {
            const api = this.$service.project.finance.getSelectOptions;
            const params = { paramName: '风险类型', paramType: '' };
            const res = await api(params);
            if (res.head.code !== '000000') {
                this.$message.error(res.head.message);
                return;
            }
            this.riskTypeOptions = res.body;
        },
        /**
         * 获取风险详情
         */
        async getDetail() {
            const api = this.$service.maintenanceProject.risk.getDetail;
            const params = {
                riskId: this.id
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    // 使用 $set 确保响应式更新
                    Object.keys(res.body).forEach((key) => {
                        this.$set(this.form, key, res.body[key]);
                    });
                    this.$nextTick(() => {
                        this.innerProjectType =
                            this.form.riskStageType === '开发'
                                ? 'develop'
                                : 'maintenance';
                    });

                    // 如果已经关联了禅道项目，就禁用掉
                    this.riskPlanTypeDisabled =
                        this.form.riskPlanType === '需要创建';
                    this.form.proProjectId && this.getTaskList();
                    this.getZentaoProjectList();
                    this.getZentaoTaskList();
                    this.getOngoingZentaoTaskList();

                    if (this.innerProjectType === 'develop') {
                        this.form.assProjectName = this.assProjectName;
                        this.form.assProjectId = this.assProjectId;
                    }
                    if (this.form.riskSupportList.length !== 0) {
                        this.form.riskSupportObject.riskSupportList =
                            this.form.riskSupportList.map((i) => {
                                // 部门
                                i.responsibleOrgCodeList =
                                    i.responsibleOrgCodeList.map((item) => {
                                        return item.split('/');
                                    });
                                return i;
                            });
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取禅道任务列表
         */
        async getTaskList() {
            const api =
                this.$service.maintenanceProject.zentao
                    .getZentaoTaskListInProject;
            const params = {
                id: this.form.proTaskId,
                proProjectId: this.form.proProjectId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 获取该任务消耗工时
                if (res.body.length > 0) {
                    const { consumed = null } = res.body[0];
                    this.consumed = consumed;
                } else {
                    this.consumed = '0.0';
                }
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 获取需求下拉列表选项
         */
        async getDemandList() {
            const api = this.$service.maintenanceProject.risk.getDemandList;
            const params = {};
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.demandList = res.body;
                this.listNumbers += 1;
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 获取缺陷下拉列表选项
         */
        async getDefectList() {
            const api = this.$service.maintenanceProject.risk.getDefectList;
            const params = { faultStatus: '激活' };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.defectList = res.body;
                this.listNumbers += 1;
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 获取订单下拉列表选项
         */
        async getOrderList() {
            const api = this.$service.maintenanceProject.risk.getOrderList;
            const params = {
                orderStageType:
                    this.innerProjectType === 'develop' ? '开发' : '维护'
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.orderList = res.body;
                this.listNumbers += 1;
            } catch (error) {
                console.error(error, 'error');
            }
        },
        handleRiskObejctChange() {
            this.form.productModel = '';
            this.form.productLine = '';
            this.form.subProductLine = '';
            if (
                this.form.riskAssObjectType === '维护项目任务' ||
                this.form.riskAssObjectType === '开发项目任务'
            ) {
                if (this.form.riskAssObjectType === '维护项目任务') {
                    this.form.assProjectId = '';
                }
                this.form.assTaskId = '';

                // 重置产品信息字段禁用状态
                this.productModelDisabled = false;
                this.productLineDisabled = false;
                this.subProductLineDisabled = false;
            } else {
                this.form.assObjectId = '';
            }
        },
        /**
         * 根据选择的关联对象填充产品信息
         * @param {String} id 关联对象ID
         * @param {String} type 关联对象类型
         */
        async fillProductInfoFromRelatedObject(id, type) {
            if (!id) return;

            let selectedItem = null;

            // 根据类型找到选中的对象
            if (type === '维护项目缺陷') {
                selectedItem = this.defectList.find((item) => item.id === id);
            } else if (type === '维护项目需求') {
                selectedItem = this.demandList.find((item) => item.id === id);
            } else if (type === '订单' || type === '开发项目订单') {
                selectedItem = this.orderList.find(
                    (item) => item.orderId === id
                );
            } else {
                return;
            }

            if (!selectedItem) return;

            // 独立处理每个字段
            if (selectedItem.productModel) {
                this.form.productModel = selectedItem.productModel;
                this.productModelDisabled = true;
            } else {
                this.productModelDisabled = false;
            }

            if (selectedItem.productLine) {
                this.form.productLine = selectedItem.productLine;
                this.productLineDisabled = true;

                // 如果更新了产品线，重新获取细分产品线选项
                this.getSubProductLineOptions(selectedItem.productLine);
            } else {
                this.productLineDisabled = false;
            }

            if (selectedItem.subProductLine) {
                this.form.subProductLine = selectedItem.subProductLine;
                this.subProductLineDisabled = true;
            } else {
                this.subProductLineDisabled = false;
            }
        },
        // 监听缺陷/需求/订单选择变化
        async handleAssObjectChange(value) {
            await this.fillProductInfoFromRelatedObject(
                value,
                this.form.riskAssObjectType
            );
        },
        // 监听任务选择变化
        async handleAssTaskChange(value) {
            // 任务可能不包含产品信息，如有需要可以添加相应逻辑
            // 这里重置禁用状态以允许用户手动输入
            this.productModelDisabled = false;
            this.productLineDisabled = false;
            this.subProductLineDisabled = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.w-50 {
    width: 50%;
}
.w-100 {
    width: 100%;
}
.space-between {
    justify-content: space-between;
}
.ml-10 {
    margin-left: 10px;
}
.pl-20 {
    padding-left: 20px;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}

::v-deep.form .el-form-item__label {
    font-weight: bold;
}
</style>
