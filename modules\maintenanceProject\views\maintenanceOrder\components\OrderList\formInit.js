import CommonItems from 'snbcCommon/common/form-items.js';
import { CONSTANTS } from '@/constants';

const { select, radio } = CommonItems;
const flowStatus = {
    ...select,
    name: 'OA流程状态',
    modelKey: 'flowStatus',
    elOptions: CONSTANTS.OA_FLOW_STATUS.map((i) => ({ label: i, value: i }))
};
const customerName = {
    ...select,
    name: '客户名称',
    modelKey: 'customerName',
    elOptions: []
};
const productModel = {
    ...select,
    name: '产品型号',
    modelKey: 'productModel',
    elOptions: []
};
const orderPlanType = {
    ...radio,
    name: '订单应对计划',
    modelKey: 'orderPlanType',
    elRadios: [
        { label: '需要创建', value: '需要创建' },
        { label: '不需要创建', value: '不需要创建' }
    ]
};
const whetherExistRisk = {
    ...radio,
    name: '存在风险',
    modelKey: 'whetherExistRisk',
    elRadios: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
    ]
};

// 查询参数初始化
export const queryParams = {
    orderPlanType: '',
    flowStatus: '',
    customerName: '',
    productModel: '',
    whetherExistRisk: ''
};

// 查询区域配置项
export const queryConfig = {
    elFormAttrs: {
        'size': 'small',
        'inline': true,
        'label-width': '160px'
    },
    items: [
        flowStatus,
        customerName,
        productModel,
        orderPlanType,
        whetherExistRisk
    ]
};

// 导航栏配置项
export const navItems = [
    { field: '', name: '所有', queryField: '' },
    {
        field: '是',
        name: '存在风险的',
        queryField: 'whetherExistRisk'
    },
    {
        field: '需要创建',
        name: '关联禅道任务的',
        queryField: 'orderPlanType'
    }
];
