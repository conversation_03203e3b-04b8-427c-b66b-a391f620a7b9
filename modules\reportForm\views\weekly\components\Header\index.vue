<template>
    <div
        class="top-selector-container"
        :style="{
            '--container-width': containerWidth,
            '--container-info-width': containerInfoWidth
        }"
    >
        <div class="info">
            <!-- 级联选择器组件，用于进行项目选择操作 -->
            <div class="info-tag middle-tag">
                <el-date-picker
                    class="year-selector"
                    v-model="yearVal"
                    type="year"
                    placeholder="选择年"
                    :clearable="false"
                    value-format="yyyy"
                    :filterable="false"
                    @change="getOptions"
                    :picker-options="pickerOptions"
                >
                </el-date-picker>
                <el-cascader
                    class="selector"
                    @change="handleChange"
                    :placeholder="placeholder"
                    :props="props"
                    v-model="value"
                    :options="options"
                    ref="cascaderRef"
                    popper-class="MaintenanceSelector-cascader"
                    filterable
                >
                    <div
                        slot-scope="{ data }"
                        @click="clickNode"
                        class="span-click"
                    >
                        {{ data.label }}
                    </div>
                </el-cascader>
            </div>
        </div>
    </div>
</template>
<script>
/**
 * UnifiedSelector 组件
 * @desc 该组件用于展示信息的级联选择，支持开发和维护两种模式
 * @param {String} [placeholder] - 级联选择器的占位符文本
 * @param {String} [type] - 选择器类型：'develop' 或 'maintain'
 * @example 调用示例
 * */
import moment from 'moment';

export default {
    name: 'UnifiedSelector',
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        }
    },
    data() {
        return {
            pickerOptions: {
                disabledDate(time) {
                    const year = time.getFullYear();
                    const currentYear = new Date().getFullYear();
                    // 禁用2025年以前的年份，以及未来所有年份
                    return year < 2025 || year > currentYear;
                }
            },
            props: {
                // 是否可以直接选中父节点
                checkStrictly: true,
                // 配置展开方式
                expandTrigger: 'hover',
                // 悬停状态保持时间，小于这个时间不会触发hover事件
                hoverThreshold: 150
            },
            options: [],
            // 当前项目状态
            currentStatus: '',
            yearVal: moment().format('yyyy')
        };
    },
    computed: {
        // 开发/维护
        type() {
            return this.$store.state.reportForm.isDevelopWeekly
                ? 'develop'
                : 'maintain';
        },
        value: {
            get() {
                return this.$store.state.reportForm.weeklyValue || [];
            },
            set(val) {
                this.$emit('input', val);
            }
        },
        containerWidth() {
            if (this.type === 'develop') {
                return this.level === 1 ? '600px' : '700px';
            }
            return '500px';
        },
        containerInfoWidth() {
            if (this.type === 'develop') {
                return this.level === 1 ? '400px' : '700px';
            }
            return this.level === 1 ? '400px' : '500px';
        },
        level() {
            return this.$store.state.reportForm.weeklyValue.length || 1;
        },
        // 根据类型获取对应的API
        apiMethod() {
            return this.type === 'develop'
                ? this.$service.reportForm.weekly.getDevelopHeaderData
                : this.$service.reportForm.weekly.getMaintainHeaderData;
        }
    },
    mounted() {
        const { path } = this.$route;
        // 如果我们回到了周报首页，但是选项还是多级，说明是浏览器回退
        // 此时，需要将选项值重置为一级
        if (path === '/reportForm/reportFormWeekly' && this.level > 1) {
            const levelOneValue = this.value.slice(0, 1);
            this.$store.dispatch(
                'reportForm/selectedWeeklyValue',
                levelOneValue
            );
        }
        this.getOptions();
    },
    activated() {
        const { path } = this.$route;
        // 如果我们回到了周报首页，但是选项还是多级，说明是浏览器回退
        // 此时，需要将选项值重置为一级
        if (path === '/reportForm/reportFormWeekly' && this.level > 1) {
            const levelOneValue = this.value.slice(0, 1);
            this.$store.dispatch(
                'reportForm/selectedWeeklyValue',
                levelOneValue
            );
        }
        this.getOptions();
    },
    methods: {
        /**
         * 通过点击文字选中的处理函数
         * @param {Object} e 事件对象
         */
        clickNode(e) {
            // 模拟点击对应的radio
            e.target.parentElement.parentElement.firstElementChild.click();
        },
        /**
         * 选择的值发生变化时触发
         * @param {Array} value 选择的值
         */
        handleChange(value) {
            this.$store.dispatch('reportForm/selectedWeeklyValue', value);
            const nodesOnPath = this.getNodeByValue(value);

            if (nodesOnPath && nodesOnPath.length > 0) {
                this.$store.dispatch(
                    'reportForm/selectedWeeklyOption',
                    nodesOnPath
                );
            }

            // 对应的选项，按选择的级分别跳转至对应的页面
            this.routerJump();
            // 向父组件通知页头值变更
            this.$emit('select-change');
            // 每次选择结束之后自动关闭
            if (this.$refs?.cascaderRef?.dropDownVisible) {
                this.$refs.cascaderRef.dropDownVisible = false;
            }
        },
        /**
         * 根据路径（value）在options树中查找节点
         * @param {Array} values 路径数组
         * @returns {Array} 找到的节点对象数组
         */
        getNodeByValue(values) {
            if (!values || values.length === 0) {
                return [];
            }
            let currentOptions = this.options;
            const nodesOnPath = [];
            for (let i = 0; i < values.length; i++) {
                const value = values[i];
                const foundNode = currentOptions.find(
                    (option) => option.value === value
                );
                if (foundNode) {
                    // 不需要这些属性，因为都是选项
                    // eslint-disable-next-line no-unused-vars
                    const {
                        children,
                        productLineList,
                        projectList,
                        ...nodeInfo
                    } = foundNode;
                    nodesOnPath.push(nodeInfo);

                    if (foundNode.children) {
                        currentOptions = foundNode.children;
                    } else if (i < values.length - 1) {
                        return [];
                    }
                } else {
                    return [];
                }
            }
            return nodesOnPath;
        },
        /**
         * 获取选择项
         * 每次选择项变更之后都会进行一次查询
         */
        async getOptions() {
            const params = { yearVal: Number(this.yearVal) };
            try {
                const res = await this.apiMethod(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (res.body.length === 0) {
                    this.options = [];
                    return;
                }
                const newOptions = this.handleOptions(res.body);
                if (newOptions.length > 0) {
                    this.options = newOptions;
                }
                if (this.level === 1) {
                    // 在报表首页，如果当前没有值，用默认值查询
                    if (this.value.length === 0) {
                        this.setDefaultValue();
                    } else {
                        // 如果有值（例如，从详情页返回），则使用现有值并触发数据加载
                        this.handleChange(this.value);
                    }
                } else if (this.value.length !== 0) {
                    // 如果值有效，直接查询
                    this.handleChange(this.value);
                } else {
                    // 没有值，就设置默认值
                    this.setDefaultValue();
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 处理option数据
         * @param {Array} data 接口返回的数据
         * @returns {Array} 处理后的数据
         */
        handleOptions(data) {
            if (!Array.isArray(data)) return [];

            // 只有最初一级周报
            if (this.level === 1) {
                return data.map((week) => ({
                    ...week,
                    value: week.weekNumber,
                    label: week.reportTime,
                    year: Number(this.yearVal)
                }));
            }

            return data.map((week) => ({
                ...week,
                value: week.weekNumber,
                label: week.reportTime,
                year: Number(this.yearVal),
                children: (week.productLineList || []).map((productLine) => ({
                    ...productLine,
                    value: productLine.productLine,
                    label: productLine.productLine,
                    // 开发模式下才有项目列表
                    ...(this.type === 'develop' && {
                        children: (productLine.devProjectList || []).map(
                            (devProject) => ({
                                ...devProject,
                                value: devProject.projectId,
                                label: devProject.projectName
                            })
                        )
                    })
                }))
            }));
        },
        /**
         * 设置默认值
         * 只有在只有1级（报表首页）时才会设置，默认为最上面的周报
         */
        setDefaultValue() {
            const { value } = this.options[0];
            this.$store.dispatch('reportForm/selectedWeeklyValue', [value]);
            const nodesOnPath = this.getNodeByValue([value]);
            if (nodesOnPath && nodesOnPath.length > 0) {
                this.$store.dispatch(
                    'reportForm/selectedWeeklyOption',
                    nodesOnPath
                );
            }
            // 向父组件通知页头值变更
            this.$emit('select-change');
        },
        /**
         * 路由跳转规则
         * 如果选择的是一级菜单，跳转到报表首页
         * 如果选择的是二级/三级菜单，跳转到开发、维护项目周报列表/详情（共用一个界面）
         */
        routerJump() {
            const { path } = this.$route;
            // 根据路径长度判断选中的哪一级
            if (this.level === 1 && path !== '/reportForm/reportFormWeekly') {
                this.$router.push({ path: '/reportForm/reportFormWeekly' });
                return;
            }
            if (this.level > 1 && path !== '/reportForm/reportFormWeeklyInfo') {
                this.$router.push({
                    path: '/reportForm/reportFormWeeklyInfo'
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.top-selector-container {
    height: 45px;
    display: flex;
    align-items: center;
    margin: 10px 0 7px 16px;
    width: var(--container-width);
}
.year-selector.el-date-editor.el-input {
    width: 80px;
    margin-left: 10px;
    ::v-deep .el-input__inner {
        padding-right: 0;
        height: 34px;
    }
}
.project-list-button {
    height: 34px;
    margin: 0 10px;
    flex: 1;
    font-weight: 400;
}
.info {
    background-color: #fff;
    margin-right: 10px;
    margin-left: 4px;
    width: var(--container-info-width);
}
.primary-tag {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}
.info-tag {
    height: 45px;
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.12),
        0px 0 3px 0 rgba(0, 0, 0, 0.04);
    justify-content: center;
    align-items: center;
    position: relative;
    .selector {
        margin: 0 10px 0 10px;
        flex: 1;
        ::v-deep .el-input--mini .el-input__inner {
            line-height: 34px;
            height: 34px;
            font-weight: 400;
        }
    }
}
.info-tag::after {
    content: '';
    position: absolute;
    right: -14px;
    top: 0px;
    width: 0;
    height: 0;
    border-top: 23px solid transparent;
    border-bottom: 23px solid transparent;
    border-left: 15px solid #f0f0f0;
    z-index: 2;
}
.middle-tag::before {
    content: '';
    position: absolute;
    top: 0px;
    width: 0;
    height: 0;
}
// 利用placeholder进行数据回显，修改字体颜色
::v-deep .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685) !important;
}
.span-click {
    width: 100%;
}
</style>
<style lang="scss">
// 隐藏单选框
.MaintenanceSelector-cascader .el-cascader-panel .el-radio__input {
    display: none;
}
.MaintenanceSelector-cascader .el-cascader-panel .el-cascader-node__postfix {
    top: 10px;
}
.MaintenanceSelector-cascader .el-cascader-menu__wrap {
    height: 300px;
}
</style>
