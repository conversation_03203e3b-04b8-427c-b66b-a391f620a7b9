<template>
    <div>
        <el-dialog
            title="编辑需求"
            :visible.sync="dialogVisible"
            :before-close="reset"
            width="70%"
        >
            <!-- 基本信息 -->
            <el-divider>基本信息</el-divider>
            <el-descriptions :column="4" border>
                <el-descriptions-item label="需求名称" :span="4">
                    {{ form.demandName }}
                </el-descriptions-item>
                <el-descriptions-item label="客户名称" :span="2">
                    {{ form.customerName }}
                </el-descriptions-item>
                <el-descriptions-item label="OA流程状态">
                    {{ form.flowStatus }}
                </el-descriptions-item>
                <el-descriptions-item label="OA流程ID" :span="2">
                    {{ form.flowNo }}
                </el-descriptions-item>
                <el-descriptions-item label="产品型号">
                    {{ form.productModel }}
                </el-descriptions-item>
                <el-descriptions-item label="产品线">
                    {{ form.productLine }}
                </el-descriptions-item>
                <el-descriptions-item label="细分产品线">
                    {{ form.subProductLine }}
                </el-descriptions-item>
            </el-descriptions>

            <!-- 需求识别 -->
            <el-divider>需求识别</el-divider>
            <el-descriptions :column="4" border class="mt-20">
                <el-descriptions-item label="优先级别" :span="4">
                    {{ form.priorityLevel }}
                </el-descriptions-item>
                <el-descriptions-item label="需求背景" :span="4">
                    <span class="pre-wrap-text">{{
                        form.demandBackground
                    }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="需求要点" :span="4">
                    <span class="pre-wrap-text">{{
                        form.demandKeyRequire
                    }}</span>
                </el-descriptions-item>
            </el-descriptions>

            <el-divider>需求响应结果</el-divider>
            <el-form ref="form" :model="form" class="form" size="small">
                <el-form-item
                    label="需求应对计划"
                    prop="demandPlanType"
                    :rules="required"
                >
                    <el-radio-group
                        v-model="form.demandPlanType"
                        :disabled="faultPlanTypeDisabled"
                    >
                        <el-radio label="不需要创建">不需要创建</el-radio>
                        <el-radio label="需要创建">需要创建</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="关联禅道项目"
                    prop="proProjectId"
                    v-show="form.demandPlanType === '需要创建'"
                    :rules="form.demandPlanType === '需要创建' ? required : {}"
                >
                    <el-select
                        class="w-100"
                        v-model="form.proProjectId"
                        placeholder="请选择关联禅道项目"
                        :disabled="faultPlanTypeDisabled"
                        @change="getZentaoTaskList"
                        filterable
                    >
                        <el-option
                            v-for="item in zentaoProjectList"
                            :label="item.name"
                            :key="item.id"
                            :value="item.id"
                        ></el-option
                    ></el-select>
                </el-form-item>
                <el-form-item
                    label="关联禅道任务"
                    prop="proTaskId"
                    v-if="form.demandPlanType === '需要创建' && form.proTaskId"
                >
                    <div class="flex space-between">
                        <el-select
                            v-if="form.newProTaskId && form.newProTaskId !== 0"
                            class="w-100"
                            v-model="form.newProTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                        <div v-else-if="form.newProTaskId === 0"></div>
                        <el-select
                            v-else-if="form.proTaskId"
                            class="w-100"
                            v-model="form.proTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                        <div v-else></div>
                        <ChangeTaskDialog
                            v-if="form.proProjectId"
                            :hasTaskHour="consumed !== '0.0'"
                            :zentaoProjectList="zentaoProjectList"
                            @save="handleChangeTaskSave"
                            class="pl-20"
                        ></ChangeTaskDialog>
                    </div>
                </el-form-item>
            </el-form>
            <div class="footer" slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';
import ChangeTaskDialog from 'maintenanceProject/components/ChangeTaskDialog/index.vue';

export default {
    name: 'DemandUpdateDialog',
    components: { ChangeTaskDialog },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            form: {
                demandName: '',
                customerName: '',
                projectManager: '',
                demandStage: '',
                flowStatus: '',
                flowNo: '',
                productModel: '',
                productName: '',
                productLine: '',
                subProductLine: '',
                demandReceiveDate: '',
                priorityLevel: '',
                developFeedbackDate: '',
                demandBackground: '',
                demandKeyRequire: '',
                demandScale: '',
                whetherAssBatchOrder: '',
                demandConfirmResult: '',
                demandPlanType: ''
            },
            CONSTANTS,
            required: {
                required: true,
                // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            },
            zentaoProjectList: [],
            zentaoTaskList: [],
            consumed: '0.0',
            faultPlanTypeDisabled: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        },
        productModelOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.productModel) {
                    res.push({ value: i.productModel, label: i.productModel });
                }
            });
            return res;
        },
        customerNameOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.customerName) {
                    res.push({ value: i.customerName, label: i.customerName });
                }
            });
            return res;
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                // 编辑的时候先查详情，后根据详情的项目经理获取禅道项目
                if (this.id) {
                    this.getDetail();
                } else {
                    this.getZentaoProjectList();
                }
            }
        }
    },
    mounted() {
        this.getZentaoProjectList();
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.$refs.form.resetFields();
            this.dialogVisible = false;
            this.$emit('update');
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            // 编辑的时候，如果此时没有创建任务，并且选择了需要创建
            // 将 newProTaskId 改为 0
            if (
                !this.form.proTaskId &&
                this.form.demandPlanType === '需要创建'
            ) {
                this.form.newProTaskId = 0;
            }
            if (this.form.demandPlanType === '不需要创建') {
                this.form.proProjectId = '';
            }
            const api = this.$service.maintenanceProject.demand.updateDemand;
            try {
                const res = await api({
                    ...this.form,
                    id: this.id,
                    // 流程软硬件归属，固定为硬件
                    flowSoftHardBelong: '硬件'
                });
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 关闭弹窗前的回调
         * @param {Function} done 关闭弹窗的函数
         */
        reset(done) {
            this.$refs.form.resetFields();
            done();
        },

        /**
         * 获取禅道项目下拉列表
         */
        async getZentaoProjectList() {
            const api =
                this.$service.maintenanceProject.zentao.getZentaoProjectList;
            const params = {
                projectCode: '维护',
                projectManagerAccount: this.id
                    ? this.form.projectManager
                    : getUserAccount(this)
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.zentaoProjectList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },

        /**
         * 根据项目获取子任务接口
         */
        async getZentaoTaskList() {
            if (!this.form.proProjectId) return;
            const api =
                this.$service.maintenanceProject.zentao.getZentaoTaskList;
            try {
                const res = await api({ proProjectId: this.form.proProjectId });
                if (res.head.code === '000000') {
                    this.zentaoTaskList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取缺陷详情
         */
        async getDetail() {
            const api = this.$service.maintenanceProject.demand.getDetail;
            const params = {
                id: this.id
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.form = res.body;
                    // 如果已经关联了禅道项目，就禁用掉
                    this.faultPlanTypeDisabled =
                        this.form.demandPlanType === '需要创建';
                    this.form.proProjectId && this.getTaskList();
                    this.getZentaoProjectList();
                    this.getZentaoTaskList();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleChangeTaskSave(value) {
            this.form.proProjectId = value.proProjectId;
            this.form.newProTaskId = value.proTaskId;
            this.getZentaoTaskList();
        },
        /**
         * 获取禅道任务列表
         */
        async getTaskList() {
            const api =
                this.$service.maintenanceProject.zentao
                    .getZentaoTaskListInProject;
            const params = {
                id: this.form.proTaskId,
                proProjectId: this.form.proProjectId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 获取该任务消耗工时
                if (res.body.length > 0) {
                    const { consumed = null } = res.body[0];
                    this.consumed = consumed;
                } else {
                    this.consumed = '0.0';
                }
            } catch (error) {
                console.error(error, 'error');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.space-between {
    justify-content: space-between;
}
.ml-10 {
    margin-left: 10px;
}
.w-100 {
    width: 100%;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
}
.pl-20 {
    padding-left: 20px;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
}
.pre-wrap-text {
    white-space: pre-wrap;
}
</style>
