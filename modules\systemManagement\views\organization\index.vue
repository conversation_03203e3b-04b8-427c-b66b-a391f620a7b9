<template>
    <div class="view">
        <el-container>
            <el-aside>
                <org-tree ref="orgTree" :is-enable="isEnable" :org-source-data.sync="orgTreeData" @org-node-click="orgNodeClick" @org-edit-finish="orgEditFinish" />
            </el-aside>
            <el-main>
                <div class="header">
                    <div>{{ supervisor.orgName }}</div>
                    <el-button v-if="currentOrgNodeData && currentOrgNodeData.orgId !== '0'" size="middle" icon="fa fa-pencil" type="primary" @click="editOrg">
                        {{ $t("table.edit") }}
                    </el-button>
                </div>
                <el-row type="flex" justify="space-between" class="info">
                    <el-col :span="12">
                        <div class="desc-info">
                            <i class="fa fa-user-o color-blue" />
                            <span>{{ $t('systemManagement.organization.departmentHead') }}</span>
                            <el-tooltip class="item" effect="dark" :content="supervisor.supervisorName" placement="top-start">
                                <span class="desc-text">{{ supervisor.supervisorName }}</span>
                            </el-tooltip>
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <div class="desc-info">
                            <i class="fa fa-mobile-phone color-blue color-blue-phone" />
                            <span>{{ $t('systemManagement.organization.telephoneNumber') }}</span>
                            <el-tooltip class="item" effect="dark" :content="supervisor.supervisorPhone" placement="top-start">
                                <span class="desc-text">{{ supervisor.supervisorPhone }}</span>
                            </el-tooltip>
                        </div>
                    </el-col>
                </el-row>
                <div class="line" />
                <div class="header">
                    <div class="header__title">{{ $t('systemManagement.organization.memberInformation') }}</div>
                    <span class="header__body">
                        <el-input v-model="listQuery.queryParam" type="text" :placeholder="$t('systemManagement.organization.placeholder.msg1')" autocomplete="text" @keyup.enter.native="getUserList">
                            <i slot="suffix" class="el-input__icon fa fa-search" @click="getUserList" />
                        </el-input>
                    </span>
                    <div class="header__btns">
                        <el-button v-permission="['Org_Add_User']" icon="fa fa-plus-square-o" size="middle" type="primary" @click="showAdd">
                            {{ $t('systemManagement.organization.addUser') }}
                        </el-button>
                        <el-button v-permission="['Org_Remove_User']" icon="fa fa-trash-o" size="middle" type="primary" :disabled="multipleSelection.length === 0" @click="showMultiDelete">
                            {{ $t('systemManagement.organization.removeUser') }}
                        </el-button>
                        <el-button v-permission="['org_adjust_department']" icon="fa fa-exchange" size="middle" :disabled="multipleSelection.length <= 0" @click="showMultiOrgChange">
                            {{ $t('systemManagement.organization.adjustmentDepartment') }}
                        </el-button>
                        <el-dropdown trigger="click" class="btn-more" @command="handleCommand">
                            <el-button size="middle" type="primary" class="button more-menu">
                                {{ $t('systemManagement.organization.moreMenu') }}
                                <i class="fa fa-angle-down" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-permission="['org_import_user']" command="handleImportUser" for="enterUser">{{ $t('systemManagement.organization.enterUser') }}</el-dropdown-item>
                                <el-dropdown-item v-permission="['org_export_user']" command="handleExportPart" :disabled="multipleSelection.length === 0" for="outputUser">{{ $t('systemManagement.organization.outputUser') }}</el-dropdown-item>
                                <el-dropdown-item v-permission="['org_export_all']" size="mini" command="handleExportAll" for="exportUser">{{ $t('systemManagement.organization.exportUser') }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>
                <el-table id="organizationTable" :data="tableData" :header-cell-style="{ background: '#F5F6FA' }" border @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="userName" :label="$t('systemManagement.organization.table.name')" width="150" />
                    <el-table-column prop="jobNumber" :label="$t('systemManagement.organization.table.number')" width="160" />
                    <el-table-column prop="phone" :label="$t('systemManagement.organization.table.phone')" width="160" />
                    <el-table-column prop="email" :label="$t('systemManagement.organization.table.mail')" width="180" show-overflow-tooltip />
                    <el-table-column prop="orgNames" :label="$t('systemManagement.organization.table.depart')" show-overflow-tooltip />
                    <el-table-column prop="roleNames" :label="$t('systemManagement.organization.table.role')" show-overflow-tooltip />
                    <el-table-column :label="$t('systemManagement.organization.table.status')" width="100">
                        <template slot-scope="{ row }">
                            <el-switch v-model="row.hasLock" v-permission="['organization_enable']" :active-value="1" :inactive-value="0" @change="changeSwitch(row)" />
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')" width="98px">
                        <template slot-scope="{ row, $index }">
                            <!-- 编辑用户禁止修改角色：分配角色暂时注释，后期在进行讨论 -->
                            <!-- <el-button
                                type="primary"
                                icon="el-icon-user"
                                size="mini"
                                @click="handleClickRole(row)"
                            >
                                {{ "分配角色" }}
                            </el-button> -->
                            <el-button v-permission="['organization_edit']" icon="fa fa-pencil" size="mini" type="primary" @click="showEdit(row, $index)">
                                {{ $t("table.edit") }}
                            </el-button>
                            <!-- 离职功能前期暂不开发，下个开发周期进行 -->
                            <!-- <el-button
                                size="mini"
                                type="danger"
                                icon="el-icon-delete"
                                @click="handleDimission(row, $index)"
                            >
                                {{ "离职" }}
                            </el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background for="pagination" :current-page.sync="listQuery.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size.sync="listQuery.pageSize" layout="total, prev, pager, next,sizes,  jumper" :total="totalCount" class="right" @size-change="getUserList" @current-change="getUserList" />
            </el-main>
        </el-container>

        <!-- 用户信息编辑窗口 -->
        <user-list-edit-info v-if="editInfo.isShow" :org-source-data="orgTreeData" :show.sync="editInfo.isShow" :edit="editInfo.isEdit" :userid="currentUserId" :org-tag-list="orgTagList" @view-save-finish="userEditFinish" />
        <!-- 调整组织结构窗口 -->
        <user-org-change v-if="dialogMultiOrgChange.isShow" :org-source-data="orgTreeData" :show.sync="dialogMultiOrgChange.isShow" @confirm-selection="handleMultiOrgChange" />
        <!-- 用户信息导入窗口 -->
        <user-list-import v-if="dialogUserImport.isShow" :show.sync="dialogUserImport.isShow" />
    </div>
</template>

<script>
import OrgTree from './components/OrgTree.vue';
import list from '../../mixins/list';
import ExcelExport from '../../mixins/excel-export';
import UserListEditInfo from './components/UserListEditInfo.vue';
import UserOrgChange from './components/UserOrgChange.vue';
import UserListImport from './components/UserListImport.vue';

export default {
    name: 'Organization',
    components: { OrgTree, UserListEditInfo, UserOrgChange, UserListImport },
    mixins: [list, ExcelExport],
    data() {
        return {
            // 用于orgtree组件的重新刷新渲染
            orgTreeRender: true,
            // 组织机构树，数据源。用于获取orgTree组件数据源
            orgTreeData: [],
            // 当前组织机构选择节点
            currentOrgNodeData: null,
            // 部门主管信息
            supervisor: {
                orgName: '',
                supervisor: '',
                supervisorPhone: ''
            },
            // 查询、分页表单
            listQuery: {
                orgId: '0',
                queryParam: '',
                pageSize: 10,
                pageNum: 1
            },
            // 总记录数
            totalCount: 0,
            // 表格数据
            tableData: [],
            multipleSelection: [],
            editInfo: {
                // 这里的是将mixins的重新了，定义编辑表单的数据对象
                formData: {
                    userName: '',
                    userId: '',
                    jobNumber: '',
                    phone: '',
                    email: '',
                    belongOrgIds: '',
                    belongRoleIds: '',
                    hasLock: '',
                    orgInfo: [],
                    roleInfo: []
                }
            },
            currentUserId: '',
            // 多用户调整部门
            dialogMultiOrgChange: {
                isShow: false
            },
            // 用户模板导入
            dialogUserImport: {
                isShow: false
            },
            // 添加用户时自动带出所选部门
            orgTagList: [],
            isDownlist: false,
            downlist: [],
            isDownAlllist: false,
            downAlllist: [],
            // 编辑按钮操作相关数据
            editInfoTree: {
                // 是否显示窗口
                isShow: false,
                // 窗口模式，true：编辑，false：新增
                isEdit: false,
                // 编辑数据,一般由组件进行重写
                formData: {
                    orgName: '',
                    parentId: '',
                    orgDesc: '',
                    supervisor: '',
                    supervisorPhone: '',
                    orgId: ''
                }
            },
            isEnable: false
        };
    },
    methods: {
        // 编辑部门
        editOrg() {
            this.$refs.orgTree.editOrg(this.currentOrgNodeData);
        },
        // 获取用户列表
        getUserList() {
            this.$service.systemManagement
                .getUserList(this.listQuery)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.tableData = res.body.list;
                        // 保持与服务器接口分页信息一致
                        this.totalCount = res.body.total;
                        this.listQuery.pageNum = res.body.pageNum;
                        this.listQuery.pageSize = res.body.pageSize;
                        this.isEnable = true;
                    } else {
                        const { code } = res.head;
                        const msg = `systemManagement.bgReturnError[${code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 移除用户
        postDeleteUser() {
            const params = {
                // 将当前选中的用户id拼接为字符串
                userIds: this.multipleSelection
                    .map((user) => user.userId)
                    .join(','),
                userNames: this.multipleSelection
                    .map((user) => user.userName)
                    .join(','),
                orgId: this.currentOrgNodeData.orgId,
                orgNames: this.currentOrgNodeData.orgName
            };
            this.$service.systemManagement
                .putRemoveUser2Org(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        // 移除用户，刷新树节点的，主管信息
                        this.refreshOrgTree();
                        // 刷新当前列表
                        this.getUserList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 多用户 - 调整部门 - 确认
        postChangeOrg(checkOrgList) {
            const params = {
                oldOrgId: this.currentOrgNodeData.orgId,
                // 需要调整的用户
                userIds: this.multipleSelection
                    .map((item) => item.userId)
                    .join(','),
                // 要调整的目标部门
                newOrgIds: checkOrgList.map((item) => item.orgId).join(','),
                userNames: this.multipleSelection
                    .map((item) => item.userName)
                    .join(','),
                orgNames: checkOrgList.map((item) => item.orgName).join(',')
            };
            this.$service.systemManagement
                .postAdjustDepartment(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        // 把选择的组织机构树列表保存到用户的所属机构里边去
                        this.dialogMultiOrgChange.isShow = false;
                        // 调整就有可能涉及到部门主管信息，所以这里需要刷新orgtree
                        this.refreshOrgTree();
                        // 刷新当前列表
                        this.getUserList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 重新加载组织机构，进行数据刷新
        refreshOrgTree() {
            // bugfix:55472,强制刷新tree，解决用户禁用，左侧树主管信息未更新问题
            this.$refs.orgTree.getOrgTree();
        },
        // 组织机构树点击事件
        orgNodeClick(nodeData, isEdit) {
            this.currentOrgNodeData = nodeData;
            Object.assign(this.supervisor, nodeData);

            if (isEdit !== true) {
                // 切换列表数据，当前分页要切换到首页。否则可能存在不存在对应页问题
                Object.assign(this.listQuery, this.$options.data().listQuery, {
                    pageSize: this.listQuery.pageSize
                });
            }
            // 这里可以解决，节点删除后。数据查询问题，默认查询根节点
            this.listQuery.orgId = nodeData.orgId || '0';

            this.getUserList();
        },
        // tree编辑、删除完成回调
        orgEditFinish(nodeData, isEdit) {
            this.orgNodeClick(nodeData, isEdit);
        },
        // 用户编辑保存完成后回调
        userEditFinish() {
            this.refreshOrgTree();
            this.getUserList();
        },
        // 用户列表check选择
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 显示新增窗口
        showAdd() {
            const currDepartment = {
                orgId: this.currentOrgNodeData.orgId,
                orgName: this.currentOrgNodeData.orgName
            };
            this.orgTagList = [currDepartment];
            // 清空数据
            this.currentUserId = '';

            this.editInfo.isShow = true;
            // 指定弹出窗口为新增模式
            this.editInfo.isEdit = false;
        },
        // 显示编辑窗口
        showEdit(row) {
            this.currentUserId = row.userId;

            this.editInfo.isShow = true;
            // 指定弹出窗口为编辑模式
            this.editInfo.isEdit = true;
        },
        // 显示删除确认提示
        showMultiDelete() {
            this.$confirm(
                this.$t('systemManagement.organization.dialog.removeUserMsg'),
                this.$t('systemManagement.organization.dialog.tips'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.postDeleteUser();
            });
        },
        // 列表启用禁用用户
        changeSwitch(user) {
            const formData = new FormData();
            formData.append('userId', user.userId);
            formData.append('status', user.hasLock);
            formData.append('userName', user.userName);
            formData.append('phone', user.phone);
            // 调用禁用启用用户的接口
            this.$service.systemManagement
                .putChangeUserStatus(formData)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message.success(
                            this.$t(
                                'systemManagement.organization.message.success'
                            )
                        );
                    } else {
                        this.$message.error(
                            this.$t(
                                'systemManagement.organization.message.failture'
                            )
                        );
                    }
                    // 成功、失败都刷新列表，获取真是状态
                    this.refreshOrgTree();
                    this.getUserList();
                })
                .catch(() => {
                    // 失败了，ui状态已经变更。这里进行状态还原显示
                    this.getUserList();
                });
        },
        // 给多个用户调整组织机构 - 调整部门按钮
        showMultiOrgChange() {
            this.dialogMultiOrgChange.isShow = true;
        },
        // 处理多用户调整组织机构
        handleMultiOrgChange(checkOrgList) {
            const _this = this;
            if (checkOrgList && checkOrgList.length > 0) {
                this.postChangeOrg(checkOrgList);
            } else {
                this.$message.error(
                    _this.$t('systemManagement.organization.dialog.tipMsg.msg2')
                );
            }
        },
        // 用户 - 更多操作
        handleCommand(command) {
            // eslint-disable-next-line default-case
            switch (command) {
                case 'handleImportUser':
                    this.dialogUserImport.isShow = true;
                    break;
                case 'handleExportPart':
                    this.handleExportPart();
                    break;
                case 'handleExportAll':
                    this.handleExportAll();
                    break;
            }
        },
        // 导出所选用户及部门 前端导出
        handleExportPart() {
            this.isDownlist = false;
            this.$confirm(
                `<span> ${this.$t(
                    'systemManagement.organization.message.exportMsgOne'
                )}</span><br/>` +
                    `<span> ${this.$t(
                        'systemManagement.organization.message.exportMsgTwo'
                    )}</span><br/>` +
                    `<span>${this.$t(
                        'systemManagement.organization.message.exportMsgThree'
                    )}</span>`,
                this.$t('systemManagement.organization.message.exportTip'),
                {
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            ).then(() => {
                const params = {
                    orgId: this.currentOrgNodeData.orgId,
                    // 导出当前选中的用户
                    userIds: this.multipleSelection
                        .map((item) => item.userId)
                        .join(',')
                };
                this.$service.systemManagement
                    .getExportPartUser(params)
                    .then((response) => {
                        this.isDownlist = true;
                        if (response.head.code !== '000000') {
                            this.isDownlist = true;
                            const msg = `productService.bgReturnError[${response.head.code}]`;
                            this.$message({
                                message: this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
            });
        },
        // 导出全部用户及部门
        handleExportAll() {
            this.isDownAlllist = false;
            this.$confirm(
                `<span> ${this.$t(
                    'systemManagement.organization.message.exportMsgOne'
                )}</span><br/>` +
                    `<span> ${this.$t(
                        'systemManagement.organization.message.exportMsgTwo'
                    )}</span><br/>` +
                    `<span>${this.$t(
                        'systemManagement.organization.message.exportMsgThree'
                    )}</span>`,
                this.$t('systemManagement.organization.message.exportTip'),
                {
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            ).then(() => {
                const params = {
                    orgId: this.currentOrgNodeData.orgId
                };
                this.$service.systemManagement
                    .getExportUser(params)
                    .then((response) => {
                        this.isDownAlllist = true;
                        if (response.head.code !== '000000') {
                            const msg = `productService.bgReturnError[${response.head.code}]`;
                            this.$message({
                                message: this.$t(msg),
                                type: 'error'
                            });
                        }
                    });
            });
        },
        // 格式化JSON
        formatJson(data, filterVal) {
            return data.map((v) =>
                filterVal.map((j) => {
                    if (j === 'timestamp') {
                        return this.$tools.parseTime(v[j]);
                    }
                    return v[j];
                })
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.info {
    height: 60px;
    display: flex;
    align-items: center;
    background: #f5f6fa;
    box-sizing: border-box;
    padding: 22px 20px;
    margin-bottom: 20px;
    .desc-info {
        display: flex;
        align-items: center;
        .color-blue {
            color: #3370ff;
            width: 13px;
            margin-right: 10px;
        }
        .color-blue-phone {
            font-size: 18px;
        }
        .desc-text {
            margin-left: 5px;
            width: 465px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
