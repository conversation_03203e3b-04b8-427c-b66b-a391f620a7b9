<template>
    <div class="resource-load">
        <div class="tabGroup">
            <el-radio-group v-model="view" style="align-self: flex-start">
                <el-radio-button label="day">日视图</el-radio-button>
                <el-radio-button label="month">月视图</el-radio-button>
            </el-radio-group>
            <div class="status-info">
                <span class="status-info--consumed">项目消耗工时</span>
                <span class="status-info--plan">项目预计工时</span>
                <span class="status-info--whole">个人预计总工时</span>
            </div>
            <div class="filter" v-show="view === 'day'">
                <el-button icon="el-icon-arrow-left" @click="prevMonth"
                    >上月</el-button
                >
                <el-date-picker
                    v-model="selectedDate"
                    type="month"
                    placeholder="选择月"
                    @change="handleChange()"
                    :clearable="false"
                    style="width: 150px"
                >
                </el-date-picker>
                <el-button @click="nextMonth"
                    >下月<i class="el-icon-arrow-right"></i
                ></el-button>
            </div>
            <div class="filter" v-show="view === 'month'">
                <el-button icon="el-icon-arrow-left" @click="prevYear"
                    >上一年</el-button
                >
                <el-date-picker
                    v-show="view === 'month'"
                    v-model="selectedDate"
                    type="year"
                    placeholder="选择年"
                    @change="handleChange()"
                    :clearable="false"
                    style="width: 150px"
                >
                </el-date-picker>
                <el-button @click="nextYear"
                    >下一年<i class="el-icon-arrow-right"></i
                ></el-button>
            </div>
            <div style="width: 316px; visibility: hidden"></div>
        </div>
        <ElTableVirtualScroll
            :data="allData"
            :height="30"
            @change="(renderData) => (staffList = renderData)"
            keyProp="loginName"
            :throttleTime="2"
            :slowOnMousewheelTime="1"
            :buffer="500"
            :virtualized="false"
        >
            <el-table
                ref="resourceLoadRef"
                :data="staffList"
                :header-cell-style="{
                    'text-align': 'center'
                }"
                border
                :height="'calc(100vh - 220px)'"
                row-key="loginName"
                class="resourceTable"
                :row-style="{ height: '30px!important' }"
                :header-cell-class-name="hanlderTableHeader"
                :span-method="arraySpanMethod"
            >
                <el-table-column
                    label="人员"
                    min-width="100px"
                    :resizable="false"
                    prop="person"
                    align="left"
                    class-name="table-person"
                >
                    <template slot-scope="scope">
                        <div v-if="scope.row.person !== '汇总'">
                            <el-badge
                                :value="scope.row.area"
                                type="warning"
                                class="area-badge"
                            >
                                <div
                                    style="
                                        display: flex;
                                        width: 100%;
                                        padding: 0 10px;
                                    "
                                >
                                    <span
                                        v-if="showTitlePermission"
                                        class="title-prefix"
                                    >
                                        {{ scope.row.titleVal }}
                                    </span>
                                    <span v-else style="width: 20px"></span>
                                    <el-link
                                        type="primary"
                                        @click="
                                            showPersonResourceLoad(scope.row)
                                        "
                                    >
                                        {{ scope.row.person }}
                                    </el-link>
                                </div>
                            </el-badge>
                        </div>
                        <div v-else>
                            <span style="margin-left: 16px">{{
                                scope.row.person
                            }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="模块"
                    min-width="80px"
                    :resizable="false"
                    :show-overflow-tooltip="true"
                    class-name="module"
                    align="center"
                >
                    <template slot-scope="scope">
                        {{ scope.row.moduleVal }}
                        <span v-if="scope.row.numbers !== 1">
                            <br />({{ scope.row.numbers }}人)</span
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    v-for="(item, index) in this.view === 'day'
                        ? getDaysInMonth(year, month)
                        : getMonthsInYear()"
                    :key="index"
                    :label="item.toString()"
                    :resizable="false"
                    :min-width="columnWidth"
                    :class-name="getHeaderClassName(index)"
                >
                    <template slot-scope="scope">
                        <WorkTimeBar
                            :taskList="
                                scope.row.hoursViewShowVoList[item - 1] || {}
                            "
                            :view="view"
                            @click="
                                workTimeBarClick(
                                    [
                                        scope.row,
                                        scope.row.hoursViewShowVoList[item - 1],
                                        index >= dividedNumber - 1
                                    ],
                                    $event
                                )
                            "
                            :isLast="scope.row.person === '汇总'"
                            :isPlanHour="index >= dividedNumber - 1"
                        ></WorkTimeBar>
                    </template>
                </el-table-column>
            </el-table>
        </ElTableVirtualScroll>
    </div>
</template>

<script>
import WorkTimeBar from '../resouceLoad/workTimeBar.vue';
import ElTableVirtualScroll from 'Components/ElTableVirtualScroll.vue';
import { Loading } from 'element-ui';
import i18n from 'wtf-core-vue/src/lang';

export default {
    name: 'ResourceLoad',
    components: { WorkTimeBar, ElTableVirtualScroll },
    props: {
        // 产品线
        productLine: { type: String, default: '' },
        // 细分产品线
        subProductLine: { type: String, default: '' },
        // 项目经理
        projectManager: { type: String, default: '' },
        // 是否为预计工时
        isPlanHour: { type: Boolean, default: false }
    },
    data() {
        return {
            // 是否正在加载
            isLoading: false,
            // 表格可视区域渲染的数据
            staffList: [],
            // 存储所有表格数据
            allData: [],
            // 日期
            selectedDate: new Date(),
            // 日/月视图
            view: 'day',
            // 可视列表的开始索引
            firstItemIndex: 0,
            // 可视列表的结束索引
            lastItemIndex: 20,
            // 从这天开始是预计工时
            dividedNumber: 32,
            // 用于记录上一个合并单元格的字段
            lastRowSpan: 1,
            // 日视图的接口
            getTableListByDay:
                this.$service.maintenanceProject.resourceLoad
                    .getResoureLoadDataByDay,
            // 月视图的接口
            getTableListByMonth:
                this.$service.maintenanceProject.resourceLoad
                    .getResoureLoadDataByMonth,
            // 项目资源负载-日视图-任务列表查询
            getTaskDetailByDay:
                this.$service.maintenanceProject.resourceLoad
                    .getResoureLoadTaskListByDay,
            // 项目资源负载-月视图-任务列表查询
            getTaskDetailByMonth:
                this.$service.maintenanceProject.resourceLoad
                    .getResoureLoadTaskListByMonth
        };
    },
    computed: {
        // 是否有查看职称的权限
        showTitlePermission() {
            return this.$store.state.permission.btnDatas.includes(
                'showTitlePermission'
            );
        },
        // 表格列宽
        columnWidth() {
            if (this.view === 'month') {
                return 70;
            }
            return 30;
        },
        daysNumInMonth() {
            return this.getDaysInMonth(this.year, this.month).length;
        },
        year() {
            return this.selectedDate.getFullYear();
        },
        month() {
            return this.selectedDate.getMonth() + 1;
        }
    },
    watch: {
        productLine() {
            this.handleChange();
        },
        subProductLine() {
            this.handleChange();
        },
        projectManager() {
            this.handleChange();
        },
        selectedDate() {
            this.handleChange();
        },
        view() {
            this.handleChange();
        }
    },
    mounted() {
        this.handleChange();
    },
    methods: {
        /**
         * 获取选择的日期
         * @param {String} type 日视图或月视图
         * @return {String} 选择的日期
         */
        getSelectedDate(type) {
            let selectDate;
            if (type === 'day' && this.month <= 9) {
                selectDate = `${this.year}-0${this.month}`;
            } else if (type === 'day' && this.month > 9) {
                selectDate = `${this.year}-${this.month}`;
            } else if (type === 'month') {
                selectDate = `${this.year}`;
            }
            return selectDate;
        },
        /**
         * 获取当月天数
         * @param {*} year 当年
         * @param {*} month 当月
         * @returns {Array} 当月天数的数组
         */
        getDaysInMonth(year, month) {
            const days = new Date(year, month, 0).getDate();
            return Array.from({ length: days }, (v, k) => k + 1);
        },
        /**
         * 获取一年中的月份
         * @returns {Array} 一年中月份的数组
         */
        getMonthsInYear() {
            return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
        },
        /**
         * 处理参数变更
         * @param {String} prop 排序字段
         * @param {String} order 排序方式
         */
        handleChange(prop = 'moduleVal', order = 'descending') {
            if (!this.productLine) return;
            // 避免重复请求
            if (this.isLoading) return;
            this.isLoading = true;
            this.dividedNumber = 32;
            const loadingInstance = Loading.service({
                text: i18n.t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            this.allData = [];
            const selectDate = this.getSelectedDate(this.view);
            const requestData = {
                productLine: this.productLine || '',
                subProductLine: this.subProductLine || '',
                projectManager: this.projectManager || '',
                selectDate,
                sortType: order === 'descending' ? '1' : '0',
                sortName: prop
            };
            let api;
            if (this.view === 'day') {
                api = this.getTableListByDay;
            } else {
                api = this.getTableListByMonth;
            }
            api(requestData)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.allData = res.body.map((i) => {
                            i.hoursViewShowVoList.map((j) => {
                                j.taskShowVoList = [];
                                return j;
                            });
                            return i;
                        });
                        this.allData = this.handleTableData(this.allData);
                        this.dividedNumber = res.body[0].dividedNumber;
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                })
                .finally(() => {
                    this.isLoading = false;
                    loadingInstance && loadingInstance.close();
                });
        },
        /**
         * 上一月
         */
        prevMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 0) {
                this.selectedDate = new Date(currentYear - 1, 11, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth - 1, 1);
            }
        },
        /**
         * 下一月
         */
        nextMonth() {
            const currentYear = this.selectedDate.getFullYear();
            const currentMonth = this.selectedDate.getMonth();
            // 处理跨年情况
            if (currentMonth === 11) {
                this.selectedDate = new Date(currentYear + 1, 0, 1);
            } else {
                this.selectedDate = new Date(currentYear, currentMonth + 1, 1);
            }
        },
        /**
         * 上一年
         */
        prevYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear - 1, 0, 1);
        },
        /**
         * 下一年
         */
        nextYear() {
            const currentYear = this.selectedDate.getFullYear();
            this.selectedDate = new Date(currentYear + 1, 0, 1);
        },
        /**
         * 点击工时获取任务详情
         * @param {Array} value 工时信息
         * @param {Object} $event 事件对象
         */
        workTimeBarClick(value, $event) {
            const { isNeedProductLine } = $event;
            const { loginName, person } = value[0];
            const { time } = value[1];
            if (person === '汇总') {
                return;
            }
            const api =
                this.view === 'day'
                    ? this.getTaskDetailByDay
                    : this.getTaskDetailByMonth;
            const params = {
                loginNames: [loginName],
                selectDate: time,
                isQueryPlanHour: value[2],
                productLine: isNeedProductLine ? this.productLine : ''
            };
            api(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        value[1].taskShowVoList = res.body;
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
        /**
         * 查看个人资源负载
         * @param {Object} data 数据
         */
        async showPersonResourceLoad(data) {
            const date = this.getSelectedDate(this.view);
            const { loginName, person } = data;
            // 缓存姓名
            this.$store.dispatch('frame/setResourceName', person);
            // 缓存页面
            await this.$store.dispatch('tagsView/addView', this.$route);
            this.$router.push({
                path: '/dashboard-index',
                query: {
                    name: loginName,
                    view: this.view,
                    date
                }
            });
        },
        /**
         * 当前日期高亮
         * @param {*} param 行列
         * @returns {String} 类名
         */
        hanlderTableHeader({ row, column, rowIndex, columnIndex }) {
            let curDayOrMonth;
            if (this.selectedDate.getFullYear() !== new Date().getFullYear()) {
                return;
            }
            if (this.view === 'day') {
                if (this.selectedDate.getMonth() !== new Date().getMonth()) {
                    return;
                }
                curDayOrMonth = new Date().getDate() + 1;
            } else {
                curDayOrMonth = new Date().getMonth() + 2;
            }
            if (columnIndex === curDayOrMonth) {
                return 'curDate';
            }
        },
        /**
         * 合并单元格
         * @param {Object} param 行列参数
         * @returns {Object} 合并单元格的格式
         */
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            if (columnIndex === 1) {
                return {
                    rowspan: row.numbers,
                    colspan: 1
                };
            }
        },
        /**
         * 新增每个模块多少人的字段(numbers)，用于显示与合并单元格
         * @param {Array} arr table列表
         * @returns {Array} 表格数据
         */
        handleTableData(arr) {
            let i = 0;
            while (i < arr.length) {
                // 初始化计数器
                let count = 1;
                for (let j = i + 1; j < arr.length; j++) {
                    if (arr[i].moduleSort === arr[j].moduleSort) {
                        // 如果moduleSort相同，计数器加1
                        count += 1;
                    } else {
                        // 如果moduleSort不同，跳出内层循环
                        break;
                    }
                }
                if (count > 1) {
                    for (let k = i; k < i + count; k++) {
                        // eslint-disable-next-line max-depth
                        if (k === i) {
                            // 在第一个相同的对象上赋值相同的moduleSort的数量
                            arr[k].numbers = count;
                        } else {
                            // 剩余的赋值0
                            arr[k].numbers = 0;
                        }
                    }
                    // 跳过已经处理过的相同moduleSort的对象
                    i += count - 1;
                } else {
                    // 如果moduleSort不同，赋值1
                    arr[i].numbers = 1;
                }
                i += 1;
            }
            return arr;
        },
        /**
         * 获取每一列的样式
         * @param {Number} index 序号
         * @returns {String} 样式
         */
        getHeaderClassName(index) {
            // 只有汇总这一列，就不展示节假日
            if (this.allData.length <= 1 || this.view === 'month') return '';
            return this.allData[0]?.hoursViewShowVoList[index]?.workDay
                ? ''
                : 'rest-day';
        }
    }
};
</script>

<style lang="scss" scoped>
.resource-load {
    width: 100%;
    height: 100%;
}
.tabGroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    white-space: nowrap;

    .filter {
        flex: 1;
        display: flex;
        justify-content: center;
    }
}
// 突出显示当天/当月
::v-deep .curDate .cell {
    height: 30px;
    width: 30px !important;
    background-color: rgb(235, 58, 19);
    display: grid;
    place-items: center;
    text-align: center;
    border-radius: 50%;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    line-height: 30px;
}

@mixin status-info($bg-color) {
    width: 100px;
    height: 16px;

    &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 20px;
        margin-right: 5px;
        border-radius: 50%;
        background-color: $bg-color;
    }
}

.status-info--plan {
    @include status-info(#85b67a);
}

.status-info--whole {
    @include status-info(#4a9ef7);
}

.status-info--consumed {
    @include status-info(#999);
}
.area-badge {
    font-size: 12px;
    margin-top: 8px;
    // 修改职称背景颜色
    ::v-deep .el-badge__content {
        background-color: #ff860d;
        font-size: 11px;
    }
}

.title-prefix {
    margin-top: 0.5px;
    width: 20px;
    height: 20px;
    border: 1px solid #5a9ef8;
    border-radius: 50%;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
}
::v-deep td:not(.table-person) .cell {
    padding: 0 auto !important;
    display: flex;
    justify-content: center;
}
::v-deep .rest-day:not(th) .cell {
    background-color: rgb(245, 245, 245, 0.4);
}
::v-deep .el-table__cell.rest-day {
    background-color: rgb(245, 245, 245, 0.4);
}
// 去除斑马纹
::v-deep .el-table tr.el-table__row:nth-child(even) {
    background-color: #fff !important;
}
::v-deep th.rest-day {
    position: relative;
}
::v-deep .module .cell {
    display: flex;
    justify-content: center;
    flex-direction: column;
}
// 修改排序箭头样式
::v-deep .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #ffdc37;
}
::v-deep .el-table .descending .sort-caret.descending {
    border-top-color: #ffdc37;
}
// 统一表头高度，修正操作列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}
// 留给人员展示更大空间
::v-deep .table-person .cell {
    padding-left: 0 !important;
}
::v-deep .el-table--mini .el-table__cell {
    padding: 3px 0 !important;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
