import { CONSTANTS } from '@/constants';

const { COLOR_LIST } = CONSTANTS;
const handleLineData = (data, typeList) => {
    if (data.length === 0) return [];
    return typeList.map((i) => {
        return data.map((j) => {
            const index = j.financeDetailShowVos.findIndex((l) => l.item === i);
            if (index !== -1) {
                return (
                    Math.trunc(j.financeDetailShowVos[index].value / 100) / 100
                ).toFixed(2);
            }
            return '0.00';
        });
    });
};

const handlePieData = (data, typeList) => {
    return typeList.map((i) => {
        let value = data.find((el) => el.item === i)?.value;
        if (!value) {
            value = 0;
        }
        return {
            value: Math.trunc(value / 100) / 100,
            name: i
        };
    });
};

const formatTooltip = (params) => {
    let tooltipContent = '';
    const sortedParams = params.toSorted((a, b) => b.data - a.data);
    sortedParams.forEach((param) => {
        tooltipContent += `${param.marker}${param.seriesName}：${param.data}万元</br>`;
    });
    return tooltipContent;
};

const calculateSum = (arr) => {
    let res = Array.from({ length: 12 }, () => 0);
    arr.forEach((subArray) => {
        subArray.forEach((i, index) => {
            res[index] += parseFloat(i);
        });
    });
    res = res.map((i) => i.toFixed(2));
    return res;
};

// 人工费：按项目类别 折线图
// eslint-disable-next-line max-lines-per-function
export const getLaborCostProductCategoryLineChartOptions = (data) => {
    const typeList = ['开发', '维护'];
    const options = handleLineData(data, typeList);
    const calculateData = calculateSum(options);
    return {
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params);
            },
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['开发项目', '维护项目', '总费用']
        },
        color: COLOR_LIST,
        xAxis: [
            {
                type: 'category',
                data: [
                    '1月',
                    '2月',
                    '3月',
                    '4月',
                    '5月',
                    '6月',
                    '7月',
                    '8月',
                    '9月',
                    '10月',
                    '11月',
                    '12月'
                ],
                axisTick: {
                    alignWithLabel: true
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: '开发/维护费用：万元',
                position: 'left',
                axisLabel: {
                    formatter: (value) => {
                        return value.toFixed(2);
                    }
                }
            },
            {
                type: 'value',
                name: '总费用：万元',
                position: 'right',
                splitLine: {
                    show: false
                },
                // y轴自适应坐标轴
                scale: true,
                axisLabel: {
                    formatter: (value) => {
                        return value.toFixed(2);
                    }
                }
            }
        ],
        series: [
            ...options.map((i, index) => {
                return {
                    name: `${typeList[index]}项目`,
                    type: 'bar',
                    data: i
                };
            }),
            {
                name: '总费用',
                type: 'line',
                yAxisIndex: 1,
                data: calculateData,
                label: {
                    show: true
                }
            }
        ]
    };
};

// 人工费：按项目类别 饼图
export const getLaborCostProductCategoryPieChartOptions = (data) => {
    const options = data.map((i) => {
        return {
            value: Math.trunc(i.total / 100) / 100,
            name: `${i.xdata}项目`
        };
    });
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}: ${
                    params.value
                } 万(${params.percent.toFixed(1)}%)`;
            }
        },
        color: COLOR_LIST,
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: ['30%', '85%'],
                center: ['50%', '50%'],
                data: options,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};

// 人工费：按级别 折线
export const getLaborCostLineChartOptions = (data) => {
    const typeList = ['助理', '初级', '中级', '高级', '资深'];
    const options = handleLineData(data, typeList);
    return {
        legend: {
            data: typeList
        },
        color: COLOR_LIST,
        xAxis: {
            type: 'category',
            data: [
                '1月',
                '2月',
                '3月',
                '4月',
                '5月',
                '6月',
                '7月',
                '8月',
                '9月',
                '10月',
                '11月',
                '12月'
            ]
        },
        yAxis: {
            type: 'value',
            name: '金额: 万元',
            axisLabel: {
                show: true,
                formatter: (value) => {
                    return value.toFixed(2);
                }
            }
        },
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params);
            }
        },
        series: [
            {
                name: '助理',
                type: 'line',
                data: options[0]
            },
            {
                name: '初级',
                type: 'line',
                data: options[1]
            },
            {
                name: '中级',
                type: 'line',
                data: options[2]
            },
            {
                name: '高级',
                type: 'line',
                data: options[3]
            },
            {
                name: '资深',
                type: 'line',
                data: options[4]
            }
        ]
    };
};

// 人工费：按级别 饼图
export const getLaborCostPieChartOptions = (data) => {
    const typeList = ['助理', '初级', '中级', '高级', '资深'];
    const options = handlePieData(data, typeList);
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}: ${
                    params.value
                } 万(${params.percent.toFixed(1)}%)`;
            }
        },
        color: COLOR_LIST,
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: ['30%', '85%'],
                center: ['50%', '50%'],
                data: options,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};

// 人工费：按产品线细分
export const getLaborCostProductLineChartOptions = (data) => {
    const typeList = ['助理', '初级', '中级', '高级', '资深'];
    const options = handleLineData(data, typeList);
    return {
        legend: {
            data: typeList
        },
        color: COLOR_LIST,
        xAxis: {
            type: 'category',
            data: data.map((i) => i.xdata)
        },
        yAxis: {
            type: 'value',
            name: '金额: 万元',
            axisLabel: {
                show: true,
                formatter: (value) => {
                    return value.toFixed(2);
                }
            }
        },
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params);
            }
        },
        series: [
            {
                name: '助理',
                type: 'line',
                data: options[0]
            },
            {
                name: '初级',
                type: 'line',
                data: options[1]
            },
            {
                name: '中级',
                type: 'line',
                data: options[2]
            },
            {
                name: '高级',
                type: 'line',
                data: options[3]
            },
            {
                name: '资深',
                type: 'line',
                data: options[4]
            }
        ]
    };
};

// 物料消耗费 折线
export const getMaterialConsumptionCostLineChartOptions = (data, typeList) => {
    const options = handleLineData(data, typeList);
    return {
        legend: {
            data: typeList
        },
        color: COLOR_LIST,
        xAxis: {
            type: 'category',
            data: [
                '1月',
                '2月',
                '3月',
                '4月',
                '5月',
                '6月',
                '7月',
                '8月',
                '9月',
                '10月',
                '11月',
                '12月'
            ]
        },
        yAxis: {
            type: 'value',
            name: '金额: 万元',
            axisLabel: {
                show: true,
                formatter: (value) => {
                    return value.toFixed(2);
                }
            }
        },
        tooltip: {
            show: true,
            trigger: 'axis',
            confine: true,
            formatter: (params) => {
                return formatTooltip(params);
            }
        },
        series: typeList.map((i, index) => {
            return {
                name: i,
                type: 'line',
                data: options[index]
            };
        })
    };
};

// 物料消耗费 饼图
export const getMaterialConsumptionCostPieChartOptions = (data, typeList) => {
    const options = handlePieData(data, typeList);
    return {
        tooltip: {
            trigger: 'item',
            formatter: (params) => {
                return `${params.name}: ${
                    params.value
                } 万(${params.percent.toFixed(1)}%)`;
            }
        },
        color: COLOR_LIST,
        series: [
            {
                name: '级别',
                type: 'pie',
                radius: ['30%', '85%'],
                center: ['50%', '50%'],
                data: options,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    // 设置超出之后自动换行
                    overflow: 'break',
                    // 设置标签的最大宽度
                    width: 100,
                    ellipsis: false,
                    formatter: (params) => {
                        return `${params.name}(${params.percent.toFixed(1)}%)`;
                    }
                },
                labelLine: {
                    length: 5,
                    lineStyle: {
                        width: 1
                    }
                }
            }
        ]
    };
};
