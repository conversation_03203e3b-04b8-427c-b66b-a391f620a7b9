import { getStatusArray } from './tableHeader';

const getTableConfig = (scope) => {
    return {
        // 查询api配置
        queryApi: scope.$service.reportForm.finance.getSpendSummaryTable,
        // 列表各列配置,默认展示表一：汇总表
        elTableColumns: getStatusArray(0, scope),
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        hooks: {
            sortChangeHook: scope.sortChangeHook
        }
    };
};
export { getTableConfig };
