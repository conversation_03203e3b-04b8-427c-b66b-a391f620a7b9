import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        // 获取日历
        getWorkDays(query) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/workDays/getNonWorkDays',
                method: 'get',
                params: query
            });
        },
        // 保存日历
        addWorkDays(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/workDays/addNonWorkDaysByYear',
                method: 'post',
                data
            });
        }
    };

    return service;
};
