<template>
    <div>
        <SnbcBaseTable class="table" ref="tableRef" :table-config="tableConfig">
            <template #cost="{ row }">
                ￥{{
                    row.calculateAmount.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
            <template #flowName="{ row }">
                <el-link
                    v-if="row.flowName === '人工周费用核算'"
                    type="primary"
                    @click="handleClick('人工周费用核算', row.flowId, row)"
                    >{{ row.flowName }}</el-link
                >
                <el-link
                    v-else-if="row.flowName === '样机试制备料申请'"
                    type="primary"
                    @click="handleClick('样机试制备料申请', row.flowId, row)"
                    >{{ row.flowName }}</el-link
                >
                <el-link
                    v-else-if="row.flowName === '内部样机转销售扣减'"
                    type="primary"
                    @click="handleClick('内部样机转销售扣减', row.flowId, row)"
                    >{{ row.flowName }}</el-link
                >
                <span v-else> {{ row.flowName }}</span>
            </template>
        </SnbcBaseTable>
        <EmpolyeeCostDetail
            :visible.sync="employeeVisble"
            :data="employeeData"
            @employee-cost-confirm="handleConfirm('人工周费用核算')"
        ></EmpolyeeCostDetail>
        <SampleCostDetail
            :visible.sync="sampleVisble"
            :data="sampleData"
            @sample-cost-confirm="handleConfirm"
        ></SampleCostDetail>
    </div>
</template>
<script>
import { getTableConfig } from './tableConfig';
import EmpolyeeCostDetail from './EmpolyeeCostDetail.vue';
import SampleCostDetail from './SampleCostDetail.vue';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';

export default {
    name: 'ExpenseDetails',
    components: { SnbcBaseTable, EmpolyeeCostDetail, SampleCostDetail },
    props: {
        projectId: {
            type: String,
            default: ''
        },
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 列表各项配置
            tableConfig: getTableConfig(this),
            employeeVisble: false,
            sampleVisble: false,
            employeeData: {},
            sampleData: {},
            // 当前弹窗的流程ID
            curFlowId: '',
            curRow: {}
        };
    },
    watch: {
        projectId(newVal) {
            if (newVal) {
                this.tableConfig.queryParams = {
                    flowName: '',
                    costSubject: '',
                    projectStage: '',
                    flowId: '',
                    calculateDateRange: []
                };
                this.handleQuery();
            }
        },
        activeName(newVal) {
            if (newVal && newVal === 'expenseDetails') {
                this.handleQuery();
            }
        }
    },
    mounted() {
        this.getQueryOptions('费用科目');
        this.getQueryOptions('费用流程简称');
        this.handleQuery();
    },
    methods: {
        /**
         * 查询
         */
        handleQuery() {
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 排序前的钩子
         * @param {Object} params 参数
         */
        sortChangeHook(params) {
            const { prop: sortKey, order: sortOrder } = params;
            const order = sortOrder === 'ascending' ? 'asc' : 'desc';
            this.tableConfig.queryParams.sortStr = `${sortKey} ${order}`;
        },
        /**
         * 查询前的钩子
         * @param {Object} params 参数
         */
        queryParamsHook(params) {
            const { calculateDateRange } = params;
            params.applyStartDate = calculateDateRange[0] || '';
            params.applyEndDate = calculateDateRange[1] || '';
            params.projectId = this.projectId;
            if (!params.sortKey) {
                params.sortStr = 'applyDate desc';
            }
        },
        /**
         * 获取下拉列表选项
         * @param {String} type 哪种option
         */
        async getQueryOptions(type) {
            try {
                const api = this.$service.project.finance.getSelectOptions;
                const res = await api({ paramName: type, paramType: '' });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 处理选项并去重
                const options = res.body
                    .map((i) => {
                        return {
                            value: i.paramName,
                            label: i.paramName
                        };
                    })
                    .filter(
                        (item, index, self) =>
                            index ===
                            self.findIndex((t) => t.value === item.value)
                    );
                if (type === '费用科目') {
                    this.tableConfig.queryConfig.items[1].elOptions = options;
                } else if (type === '费用流程简称') {
                    this.tableConfig.queryConfig.items[0].elOptions = options;
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 处理流程的点击，显示弹窗
         * @param {String} type 类型
         * @param {String} flowId 流程id
         * @param {Object} row 行数据
         */
        async handleClick(type, flowId, row) {
            // 查扣减信息
            try {
                this.curRow = row;
                this.curFlowId = flowId;
                let api;
                if (type === '人工周费用核算') {
                    api = this.$service.project.finance.getEmpolyeeCostByWeek;
                } else {
                    api = this.$service.project.finance.getSampleCostInfo;
                }
                const res = await api({ flowId });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (type === '人工周费用核算') {
                    this.employeeData = {
                        projectName: row.projectName,
                        projectId: this.projectId,
                        ...res.body
                    };
                    this.employeeVisble = true;
                } else {
                    this.sampleVisble = true;
                    this.sampleData = {
                        projectName: row?.projectName,
                        title: row?.flowName,
                        ...res.body
                    };
                }
            } catch (err) {
                console.error('Rrror', err);
            }
        },
        /**
         * 确认操作
         * @param {Object} data 数据
         */
        async handleConfirm(data) {
            try {
                let api;
                let params;
                if (data === '人工周费用核算') {
                    params = { flowId: this.curFlowId };
                    api = this.$service.project.finance.confirmEmoloyeeCost;
                } else {
                    params = {
                        ...data
                    };
                    api = this.$service.project.finance.confirmSampleCost;
                }
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('提交成功');
                this.handleQuery();
                this.employeeVisble = false;
                this.sampleVisble = false;
            } catch (err) {
                console.error('Rrror', err);
            }
        },
        /**
         * 人工费用导出（各阶段的人工费）
         */
        async handleEmployeeFeeExport() {
            if (!this.projectId) {
                this.$message.warning('请选择项目');
                return;
            }
            try {
                const api = this.$service.project.finance.exportEmployeeFee;
                const res = await api({
                    projectId: this.projectId
                });
                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);
                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                this.$tools
                    .downloadExprotFile(res, '人工费用数据', 'xlsx')
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
// 保证选择框宽度
::v-deep .el-select {
    width: 100%;
}
// 修改排序箭头样式
::v-deep .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #ffdc37;
}
::v-deep .el-table .descending .sort-caret.descending {
    border-top-color: #ffdc37;
}
.table {
    // 设置最小高度，避免下拉框的option显示不全
    min-height: 500px;
}
</style>
