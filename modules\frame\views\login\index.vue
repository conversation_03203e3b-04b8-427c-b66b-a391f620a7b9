eslint-disable max-lines
<template>
    <div v-show="isNormalLogin" class="login-container">
        <div class="title-part">
            <div>
                <svg-icon icon-class="logo" class="sidebar-logo" />
                <span>{{ $t('frame.systmeTitle') }}</span>
            </div>
            <!-- <div class="lang-part">
                <Lang :is-show="isShowName" />
            </div> -->
        </div>
        <el-row>
            <el-col :span="15">
                <div class="carousel">
                    <el-carousel
                        style="width: 100%"
                        trigger="click"
                        :height="bannerHeight + 'px'"
                        arrow="never"
                        indicator-position="none"
                    >
                        <el-carousel-item
                            v-for="(item, index) in carouselList"
                            :key="index"
                        >
                            <img :src="item" class="bannerImg" />
                        </el-carousel-item>
                    </el-carousel>
                </div>
            </el-col>
            <el-col :span="1" class="login-form-col">
                <div style="position: relative">
                    <el-tooltip
                        v-show="!onlyQrcode && qrCodeLogin"
                        class="item"
                        effect="light"
                        :content="
                            showThirdLogin
                                ? $t('frame.tip.accountOrMobile')
                                : $t('frame.tip.scanning')
                        "
                        placement="left"
                    >
                        <img
                            class="code-tip"
                            :src="imgSrc"
                            @click="switchThirdLogin()"
                        />
                    </el-tooltip>
                    <div v-if="!showThirdLogin">
                        <el-form
                            v-show="
                                (accountLogin && showAccountLogin) ||
                                (ldapLogin && showAccountLogin)
                            "
                            ref="loginForm"
                            :model="loginForm"
                            :rules="loginRules"
                            class="login-form"
                            autocomplete="off"
                            label-position="left"
                            label-width="0px"
                            :validate-on-rule-change="false"
                        >
                            <div class="title-container">
                                <div
                                    class="tab-box"
                                    :class="{ 'tab-box-new': isActive }"
                                >
                                    <div
                                        class="tab-item"
                                        :class="
                                            isActive && loginTabIndex === 0
                                                ? 'tab-acitve'
                                                : ''
                                        "
                                        @click="switchLoginType(0)"
                                    >
                                        {{ $t('frame.accountLogin') }}
                                    </div>
                                    <div
                                        v-if="smsLogin"
                                        class="tab-item"
                                        :class="
                                            isActive && loginTabIndex === 1
                                                ? 'tab-acitve'
                                                : ''
                                        "
                                        @click="switchLoginType(1)"
                                    >
                                        {{ $t('frame.mobileLogin') }}
                                    </div>
                                </div>
                            </div>
                            <el-form-item prop="username">
                                <span class="svg-container">
                                    <svg-icon icon-class="user" />
                                </span>
                                <el-input
                                    ref="username"
                                    v-model="loginForm.username"
                                    :validate-event="false"
                                    :placeholder="
                                        $t('frame.usernamePlaceholder')
                                    "
                                    autocomplete="off"
                                    name="username"
                                    type="text"
                                    tabindex="1"
                                />
                            </el-form-item>

                            <el-tooltip
                                v-model="capsTooltip"
                                :content="$t('frame.capsTooltip')"
                                placement="right"
                                manual
                            >
                                <el-form-item prop="password">
                                    <span class="svg-container">
                                        <svg-icon icon-class="password" />
                                    </span>
                                    <el-input
                                        :key="passwordType"
                                        ref="password"
                                        v-model="loginForm.password"
                                        :validate-event="false"
                                        :type="passwordType"
                                        :placeholder="
                                            $t('frame.passwordPlaceholder')
                                        "
                                        name="password"
                                        tabindex="2"
                                        autocomplete="off"
                                        :clearable="false"
                                        @paste.native.capture.prevent="
                                            handlePaste
                                        "
                                        @keyup.native="checkCapslock"
                                        @blur="capsTooltip = false"
                                    />
                                    <span class="show-pwd" @click="showPwd">
                                        <svg-icon
                                            :icon-class="
                                                passwordType === 'password'
                                                    ? 'eye'
                                                    : 'eye-open'
                                            "
                                        />
                                    </span>
                                </el-form-item>
                            </el-tooltip>

                            <el-button
                                :loading="loading"
                                type="primary"
                                class="login-btn"
                                @click.native.prevent="loginButtonClickHandler"
                            >
                                {{ $t('frame.logIn') }}
                            </el-button>
                            <!-- <div class="forget-password">
                                <span class="forget-password-text" @click="forgetPassword">{{ $t("frame.forgetPassword") }}</span>
                            </div> -->
                        </el-form>
                        <el-form
                            v-show="smsLogin && showMobileLogin"
                            ref="phoneForm"
                            :model="phoneForm"
                            :rules="phoneFormRules"
                            class="login-form"
                            autocomplete="off"
                            label-position="left"
                            label-width="0px"
                            :validate-on-rule-change="false"
                        >
                            <div class="title-container">
                                <div
                                    class="tab-box"
                                    :class="{ 'tab-box-new': isActive }"
                                >
                                    <div
                                        v-if="accountLogin || ldapLogin"
                                        class="tab-item"
                                        :class="
                                            isActive && loginTabIndex === 0
                                                ? 'tab-acitve'
                                                : ''
                                        "
                                        @click="switchLoginType(0)"
                                    >
                                        {{ $t('frame.accountLogin') }}
                                    </div>
                                    <div
                                        class="tab-item"
                                        :class="
                                            isActive && loginTabIndex === 1
                                                ? 'tab-acitve'
                                                : ''
                                        "
                                        @click="switchLoginType(1)"
                                    >
                                        {{ $t('frame.mobileLogin') }}
                                    </div>
                                </div>
                            </div>
                            <!-- 解决错误信息切换登录类型时依然存在的问题 key="phoneInput" -->
                            <el-form-item
                                key="phoneInput"
                                prop="phone"
                                class="phone-login-item phone-item"
                            >
                                <span class="split-line" />
                                <!-- 解决输入时检验的问题:validate-event="false" -->
                                <el-input
                                    v-model="phoneForm.phone"
                                    :placeholder="$t('frame.phonePlaceholder')"
                                    class="input-with-select"
                                    type="text"
                                    maxlength="11"
                                    clearable
                                    :validate-event="false"
                                    @input="phoneInput"
                                >
                                    <el-select
                                        slot="prepend"
                                        v-model="areaCode"
                                        :placeholder="$t('frame.pleaseSelect')"
                                    >
                                        <el-option label="+86" value="1" />
                                        <el-option label="+852" value="2" />
                                        <el-option label="+853" value="3" />
                                        <el-option label="+886" value="4" />
                                    </el-select>
                                </el-input>
                            </el-form-item>
                            <el-form-item
                                key="verCodeInput"
                                prop="verificationCode"
                                class="phone-login-item code-item"
                            >
                                <el-input
                                    v-model="phoneForm.verificationCode"
                                    :placeholder="
                                        $t('frame.verCodePlaceholder')
                                    "
                                    type="text"
                                    maxlength="6"
                                    clearable
                                    :validate-event="false"
                                    @input="verCodeInput"
                                />
                                <span class="split-line" />
                                <span
                                    v-if="loginCountDown <= 0"
                                    class="get-code-nomal get-code"
                                    @click="getVerCode"
                                    >{{ $t('frame.getVerificationCode') }}</span
                                >
                                <!-- 这样来实现动态国际化 -->
                                <span
                                    v-else
                                    class="get-code-nomal code-count"
                                    >{{
                                        $t('frame.changePwd.sendCountdown', [
                                            loginCountDown
                                        ])
                                    }}</span
                                >
                            </el-form-item>
                            <el-button
                                :loading="loading"
                                type="primary"
                                class="login-btn"
                                @click.native.prevent="phoneLogin"
                            >
                                {{ $t('frame.logIn') }}
                            </el-button>
                        </el-form>
                        <forget-password
                            v-if="showForgetPassword"
                            @passwordBack="passwordBack"
                        />
                    </div>
                    <div v-else>
                        <qr-code
                            v-show="showQrCode"
                            :is-active="isActive"
                            @toLogin="toLogin"
                        />
                    </div>
                </div>
            </el-col>
        </el-row>
        <verify-slide
            :show-window.sync="loginCheckDialogVisible"
            :captcha-type="'blockPuzzle'"
            :img-size="{ width: '410px', height: '200px' }"
            :type="'2'"
            @imgIsLoaded="imgIsLoadedHandler"
            @checkSuccess="checkPass"
        />
    </div>
</template>

<script>
import { mapState } from 'vuex';
// 图片滑块组件(拼图)
import VerifySlide from './components/VerifySlide';
import Lang from 'frame/components/LangSelect/index';
// localStorage加密存储
import storage from 'wtf-core-vue/src/methods/storage';
import settings from '@/settings.js';
import QrCode from './components/QrCode.vue';
import ForgetPassword from './components/ForgetPassword.vue';

export default {
    name: 'Login',
    components: {
        VerifySlide,
        QrCode,
        ForgetPassword,
        Lang
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        const validateUsername = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t('frame.usernamePlaceholder')));
            } else {
                callback();
            }
        };
        const validatePassword = (rule, value, callback) => {
            if (!value) {
                callback(new Error(this.$t('frame.passwordPlaceholder')));
            } else {
                callback();
            }
        };
        // 校验手机号
        const validatePhone = (rule, value, callback) => {
            if (!value) {
                return callback(new Error(this.$t('frame.msg.checkPhone')));
            }
            const phoneReg = /^[1][3,4,5,7,8,9][0-9]{9}$/;
            if (!phoneReg.test(value)) {
                return callback(new Error(this.$t('frame.msg.checkPhone')));
            }
            callback();
        };
        // 校验验证码
        const validateVerCode = (rule, value, callback) => {
            if (!value) {
                return callback(new Error(this.$t('frame.msg.validVerCode')));
            }
            callback();
        };
        return {
            // 轮播图计算当前屏幕高度
            screenHeight:
                window.innerHeight ||
                document.documentElement.clientHeight ||
                document.body.clientHeight,
            // 轮播图根据当前屏幕高度计算的自适应高度
            bannerHeight: '',
            // 轮播图数据源
            carouselList: [require('../../assets/1.png')],
            loginRules: {
                username: [
                    {
                        required: true,
                        validator: validateUsername
                    }
                ],
                password: [
                    {
                        required: true,
                        validator: validatePassword
                    }
                ]
            },
            loginForm: {
                username: '',
                password: ''
            },
            // 手机登录
            phoneForm: {
                phone: '',
                verificationCode: ''
            },
            // 手机登录，检验规则
            phoneFormRules: {
                phone: [
                    {
                        required: true,
                        validator: validatePhone,
                        // 解决改变区号时触发校验的问题
                        trigger: 'blur'
                    }
                ],
                verificationCode: [
                    {
                        required: true,
                        validator: validateVerCode,
                        trigger: 'blur'
                    }
                ]
            },
            // 手机登录区号
            areaCode: '1',
            passwordType: 'password',
            capsTooltip: false,
            loading: false,
            redirect: undefined,
            otherQuery: {},
            // 滑动验证组件，图片是否加载完成的标识符
            imgIsLoaded: false,
            // 是否通过参数
            isPassing: false,
            // 通过校验后的加密参数，需要在登录时传递给后台
            captchaVerification: false,
            // 校验弹窗变量，点击登录按钮后弹出，校验通过后关闭弹窗
            loginCheckDialogVisible: false,
            // 用于判断是否显示登录输入框，即是否正常登录。false，则指利用缓存token自动登录
            isNormalLogin: !this.$store.getters.token,
            // 当前登录类型index
            loginTabIndex: 0,
            // 手机登录 验证码倒计时计时器
            loginCountDown: 0,
            // 手机登录 验证码倒计时
            loginCountTimer: null,
            // 忘记密码 验证码倒计时
            passCountTimer: null,
            // 显示账密登录
            showAccountLogin: true,
            // 显示手机登录
            showMobileLogin: false,
            // 显示第三方登录
            showThirdLogin: false,
            // 图标路径
            imgSrc: require('../../assets/ercode.png'),
            // 当前顶部菜单的id
            currentMenuId: '',
            // 是否支持账密登录
            accountLogin: true,
            //  是否支持ldap登录
            ldapLogin: true,
            // 是否支持二维码登录
            qrCodeLogin: true,
            // 是否支持手机号登录
            smsLogin: true,
            // 是否显示两个时的样式
            isActive: false,
            // 只有二维码登录
            onlyQrcode: false,
            // 展示二维码
            showQrCode: true,
            // 展示忘记密码
            showForgetPassword: false,
            // 是否展示国际化的名称
            isShowName: true
        };
    },
    // 带命名空间获取
    computed: {
        ...mapState('user', {
            user_name: (state) => state.name
        }),
        ...mapState('settings', {
            langEnabled: (state) => {
                const langOption =
                    state.navbar.filter((v) => v.component === 'lang')[0] || {};
                return langOption.show !== false;
            }
        })
    },
    watch: {
        $route: {
            handler(route) {
                const { query } = route;
                if (query) {
                    this.redirect = query.redirect;
                    this.otherQuery = this.getOtherQuery(query);
                }
            },
            immediate: true
        }
    },
    mounted() {
        // 获取登录方式
        this.getLoginTypes();
        // 初始化获取屏幕尺寸，监听浏览器尺寸变化事件
        this.setSize();
        const that = this;
        window.addEventListener(
            'resize',
            () => {
                that.screenHeight =
                    window.innerHeight ||
                    document.documentElement.clientHeight ||
                    document.body.clientHeight;
                that.setSize();
            },
            false
        );
        // 绑定enter事件
        this.enterKeyup();
        if (this.loginForm.username === '') {
            this.$refs.username.focus();
        } else if (this.loginForm.password === '') {
            this.$refs.password.focus();
        }
        // 页面初始化监听头像下拉菜单的事件，便于之后的处理
        this.initNativeDrop();

        // 根据token判断是否自动登录
        const { token } = this.$store.getters;
        if (token) {
            this.$store.commit('user/SET_HADLOGIN', true);
            if (settings.layoutName === 'mixedMenu') {
                this.getMenuFuncList();
            } else {
                this.getPermissionList();
            }
        }
        this.setIsNormalLogin(!token);

        //  浏览器窗口切换激活事件
        document.addEventListener(
            'visibilitychange',
            () => {
                // eslint-disable-next-line no-shadow
                const { token } = this.$store.getters;
                const cookieToken = that.$tools.getToken();
                if (cookieToken !== undefined && token !== cookieToken) {
                    location.reload();
                }
            },
            false
        );
        if (settings.layoutName === 'mixedMenu') {
            const _this = this;
            this.$eventBus.on('topMenuChange', (data) => {
                if (data) {
                    _this.currentMenuId = data;
                    _this.getPermissionList('fresh');
                }
            });
        }
        const menuId = sessionStorage.getItem('currentId');
        if (menuId) {
            this.currentMenuId = menuId;
        }
        // 重新登录时清除项目选择与部门选择中vuex的状态
        this.$store.dispatch('department/changeDepartment', []);
        this.$store.dispatch('project/changeProject', []);
    },
    destroyed() {
        // 销毁enter事件
        this.enterKeyupDestroyed();
        // 销毁定时器
        window.clearTimeout(this.loginCountTimer);
        this.loginCountTimer = null;
        window.clearTimeout(this.passCountTimer);
        this.passCountTimer = null;
    },
    methods: {
        // 改变isNormalLogin的值
        toLogin() {
            // 如果是混合菜单获取顶部菜单，否则直接获取左侧菜单
            if (settings.layoutName === 'mixedMenu') {
                this.getMenuFuncList();
            } else {
                this.getPermissionList();
            }
        },
        // 判断账号登录次数
        loginAmount() {
            const param = {
                loginAccount: this.loginForm.username
            };
            this.$service.frame.loginAmountGet(param).then((res) => {
                if (res.body === true) {
                    this.loginCheckDialogVisible = true;
                } else {
                    const params = {
                        userName: this.loginForm.username,
                        userPwd: this.loginForm.password,
                        loginType: '0'
                    };
                    this.handleLogin(params);
                }
            });
        },
        // 页面初始化监听头像下拉菜单的事件，便于之后的处理
        initNativeDrop() {
            // 监听修改密码
            if (!this.$eventBus.hadEvent('changePWDEvent')) {
                this.$eventBus.on('changePWDEvent', (_this) => {
                    _this.$router.push(`/userinfo/changepwd`);
                });
            }
            // 监听基本信息
            if (!this.$eventBus.hadEvent('profileEvent')) {
                this.$eventBus.on('profileEvent', (_this) => {
                    _this.$router.push(`/userinfo/base`);
                });
            }
            // 监听退出登录
            if (!this.$eventBus.hadEvent('logOutEvent')) {
                this.$eventBus.on('logOutEvent', (e) => {
                    this.loginOutHandler(e);
                });
            }
        },
        // 设置轮播图的高度，根据分辨率来计算
        setSize() {
            this.bannerHeight = (450 / 974) * this.screenHeight;
            if (this.bannerHeight > 450) this.bannerHeight = 450;
            if (this.bannerHeight < 360) this.bannerHeight = 360;
        },
        /**
         * 设置是否正常登录标识
         * @param {Boolean} flag 是否正常登录标识，true: 走正常登录  false：刷新登录，不显示登录页
         */
        setIsNormalLogin(flag) {
            if (flag) {
                this.$store.dispatch('user/logout');
            }
            this.isNormalLogin = flag;
        },
        handlePaste() {
            // 禁止密码复制粘贴，所以该函数为空即可
        },
        // 监听回车处理函数
        enterKey(event) {
            const componentName = this.$options.name;
            if (componentName === 'Login') {
                let code;
                if (event.keyCode) {
                    code = event.keyCode;
                } else {
                    code = event.which ? event.which : event.charCode;
                }
                if (code === 13) {
                    if (this.loginTabIndex === 0 && this.showAccountLogin) {
                        // 账密登录
                        this.loginButtonClickHandler();
                    } else if (
                        this.loginTabIndex === 1 &&
                        this.showMobileLogin
                    ) {
                        // 手机登录
                        this.phoneLogin();
                    }
                }
            }
        },
        // 销毁回车监听
        enterKeyupDestroyed() {
            document.removeEventListener('keyup', this.enterKey);
        },
        // 创建回车监听
        enterKeyup() {
            document.addEventListener('keyup', this.enterKey);
        },
        // 滑动验证组件，图片加载完毕事件触发
        imgIsLoadedHandler() {
            this.imgIsLoaded = true;
        },
        // 滑动验证组件，通过验证回调函数
        checkPass(backData) {
            this.captchaVerification = backData;
            this.loginCheckDialogVisible = false;
            // 去后端验证
            const params = {
                userName: this.loginForm.username,
                userPwd: this.loginForm.password,
                loginType: '0'
            };
            this.handleLogin(params);
        },
        checkCapslock(e) {
            const { key } = e;
            this.capsTooltip =
                key && key.length === 1 && key >= 'A' && key <= 'Z';
        },
        // 显示密码按钮
        showPwd() {
            if (this.passwordType === 'password') {
                this.passwordType = '';
            } else {
                this.passwordType = 'password';
            }
            this.$nextTick(() => {
                this.$refs.password.focus();
            });
        },
        // 对密码进行加密的处理函数
        pwdEncrypt(pwd) {
            // des3加密后的密码
            const des3Encrypt = this.$tools.encrypt(pwd, '3DES');
            // 对des3加密后的密码，再次使用RAS加密
            const rasEncrypt = this.$tools.encryptByRsa(des3Encrypt);
            return rasEncrypt;
        },
        // 点击登录按钮，先进行表单校验，通过后弹窗进行滑动校验，通过后去后端验证
        loginButtonClickHandler() {
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    // 调用配置接口，判断当前是否需要弹出滑动验证
                    this.$service.frame.getCheckCaptcha().then((res) => {
                        if (res.head.code === '000000') {
                            //  如果不需要滑动验证直接调用登录接口
                            if (res.body === 'false') {
                                // 去后端验证
                                const params = {
                                    userName: this.loginForm.username,
                                    userPwd: this.loginForm.password,
                                    loginType: '0'
                                };
                                this.handleLogin(params);
                            } else {
                                // 否则根据接口判断账号是否达到登录次数，达到了需要滑动验证
                                this.loginAmount();
                            }
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        // 获取按钮权限数据
        getFunctionList(fresh) {
            // 获取权限按钮
            const btnData = {
                // 系统标识 0：web系统 1:移动端 2：终端
                sysType: '0',
                langKey: 'cn',
                // 权限类型  1系统，2菜单，3按钮，4接口
                permissionType: '3',
                navId: this.currentMenuId
            };
            this.$service.frame.getMenuList(btnData).then((resBtnList) => {
                if (resBtnList.head.code === '000000') {
                    const datas = resBtnList.body
                        ? resBtnList.body.map((item) => {
                              return item.permissionCode;
                          })
                        : [];
                    this.$store.dispatch('permission/btnPermissionData', datas);
                    // 登录成功后续处理
                    if (!fresh) {
                        this.loginSuccess();
                    }
                } else if (!fresh) {
                    this.loginFailed(resBtnList.head.code);
                }
            });
        },
        // 接口获取菜单数据+按钮权限数据
        getPermissionList(fresh) {
            const menuData = {
                // 系统标识 0：web系统 1:移动端 2：终端
                sysType: '0',
                langKey: 'cn',
                // 权限类型  1系统，2菜单，3按钮，4接口
                permissionType: '2',
                navId: this.currentMenuId
            };
            // 获取菜单信息
            this.$service.frame
                .getMenuList(menuData)
                .then((resMenuList) => {
                    if (resMenuList.head.code === '000000') {
                        this.$store.dispatch(
                            'permission/generateRoutes',
                            resMenuList.body
                        );

                        this.getFunctionList(fresh);
                    } else if (!fresh) {
                        this.loginFailed(
                            this.$t(
                                `frame.bgReturnError[${resMenuList.head.code}]`
                            )
                        );
                    }
                })
                .catch(() => {
                    // 这里异常后，设置为正常登录，显示登录页面。解决刷新登录空白页问题
                    this.setIsNormalLogin(true);
                });
        },
        // 弹窗滑动校验通过以后调用该方法，去后端验证
        handleLogin(params) {
            this.loading = true;
            const data = {
                captchaVerification: this.captchaVerification,
                captchaType: 'blockPuzzle',
                sysType: '0'
            };
            data.userName = params.userName.trim();
            data.userPwd = this.pwdEncrypt(params.userPwd);
            data.loginType = params.loginType;
            // 开始登录
            this.$service.frame.postLogin(data).then((res) => {
                if (res.head.code === '000000') {
                    //  存储isOriginPwd,用于判断是否需要提示修改密码
                    localStorage.setItem('isOriginPwd', res.body.isOriginPwd);
                    localStorage.setItem('isPwdExpired', res.body.isPwdExpired);
                    this.$store.dispatch('user/login', res.body.token);
                    /* 存储angular项目登录成功后需要存储的数据start */
                    localStorage.setItem('token', res.body.token);
                    /* 存储angular项目登录成功后需要存储的数据end */

                    this.$store.dispatch('user/setUserInfo', res.body);
                    this.$service.frame.getUserInfo({}).then((response) => {
                        if (response.head.code === '000000') {
                            // 存一下用户id
                            storage.setLocalStorage(
                                'userId',
                                response.body.userId || ''
                            );
                            // 这里本来有针对Angularjs项目的本地存储，
                            // 但是这些存储没有加密，因此删除
                        }
                    });
                    // 如果是混合菜单获取顶部菜单，否则直接获取左侧菜单
                    if (settings.layoutName === 'mixedMenu') {
                        this.getMenuFuncList();
                    } else {
                        this.getPermissionList();
                    }
                } else {
                    this.loginFailed(res.head.code, res.head.message);
                }
            });
        },
        // 登录成功后处理函数
        loginSuccess() {
            if (this.isNormalLogin) {
                this.$message({
                    title: this.$t('common.success'),
                    message: this.$t('frame.msg.loginSuccess'),
                    type: 'success',
                    duration: 2000
                });
            }
            // 登录成功后重置滑块状态
            this.isPassing = false;
            // 关闭loading
            this.loading = false;

            // 正常退出登录，跳转对应页。刷新登录跳转首页
            if (this.isNormalLogin) {
                // 跳转进入页面
                this.$router.push({
                    path: this.redirect || '/',
                    query: this.otherQuery
                });
                if (
                    localStorage.getItem('isOriginPwd') === 'true' ||
                    localStorage.getItem('isPwdExpired') === 'true'
                ) {
                    // 进入首页
                    this.$router.push({ path: '/' });
                } else if (this.redirect !== '/login') {
                    // 跳转进入页面
                    this.$router.push({
                        path: this.redirect || '/',
                        query: this.otherQuery
                    });
                } else {
                    this.$router.push({ path: '/' });
                }
            } else {
                // 刷新登录
                this.$router.push({
                    path: this.redirect || '/',
                    query: this.otherQuery
                });
            }
        },
        // 登录失败后处理函数
        loginFailed(code, message) {
            if (this.isNormalLogin) {
                const msg = `frame.bgReturnError[${code}]`;
                this.$message({
                    message:
                        code === '990202'
                            ? this.$t(msg).replace('{0}', message)
                            : this.$t(msg),
                    type: 'error'
                });
            }
            // 刷新自动登录失败，显示登录页，允许重新登录
            this.isNormalLogin = true;
            // 关闭loading
            this.loading = false;
        },
        // 登出处理函数
        loginOutHandler(_this) {
            // 获取权限按钮
            const params = {
                // 系统标识 0：web系统 1:移动端 2：终端
                sysType: '0'
            };
            this.$service.frame
                .putlogOut(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.loginOutSuccessHandler(_this);
                    } else {
                        this.loginOutFailedHandler(_this);
                    }
                })
                .catch(() => {
                    this.loginOutFailedHandler(_this);
                });
        },
        // 登出成功处理函数
        loginOutSuccessHandler(_this) {
            _this.$message({
                title: _this.$t('common.success'),
                message: _this.$t('frame.msg.loginOutSuccess'),
                type: 'success',
                duration: 2000
            });
            _this.$store.dispatch('user/logout');
            // 将当前选中的顶部菜单索引归零
            this.$store.dispatch('user/setCurrentIndex', 0);
            // 移除顶部菜单及顶部菜单id
            storage.removeLocalStorage('topMenu');
            sessionStorage.removeItem('currentId');
            _this.$router.push(`/login`);
        },
        // 登出失败处理函数
        loginOutFailedHandler(_this) {
            _this.$message({
                title: _this.$t('common.failed'),
                message: _this.$t('frame.msg.loginOutFailed'),
                type: 'error',
                duration: 2000
            });
            _this.$store.dispatch('user/logout');
            _this.$router.push(`/login`);
        },
        // 重定向跳转参数处理
        getOtherQuery(query) {
            return Object.keys(query).reduce((acc, cur) => {
                if (cur !== 'redirect') {
                    acc[cur] = query[cur];
                }
                return acc;
            }, {});
        },
        // 切换登录类型
        switchLoginType(index) {
            if (this.loginTabIndex !== index) {
                this.loginTabIndex = index;
                if (index === 0) {
                    this.showMobileLogin = false;
                    this.showAccountLogin = true;
                } else {
                    this.showAccountLogin = false;
                    this.showMobileLogin = true;
                }
            }
        },
        // 点击忘记密码
        forgetPassword() {
            // 隐藏账密登录，展示忘记密码框
            this.showAccountLogin = false;
            this.showForgetPassword = true;
        },
        // 从忘记密码返回来
        passwordBack() {
            this.showAccountLogin = true;
            this.showForgetPassword = false;
        },
        // 手机登录 手机号输入监听 只能输入数字 ，如输入字母或符号时，输入框内不显示
        phoneInput() {
            if (this.phoneForm.phone.length === 1) {
                this.phoneForm.phone = this.phoneForm.phone.replace(
                    /[^0-9]/g,
                    ''
                );
            } else {
                this.phoneForm.phone = this.phoneForm.phone.replace(/\D/g, '');
            }
        },
        // 手机登录 验证码输入监听 只能输入数字
        verCodeInput() {
            if (this.phoneForm.verificationCode.length === 1) {
                this.phoneForm.verificationCode =
                    this.phoneForm.verificationCode.replace(/[^0-9]/g, '');
            } else {
                this.phoneForm.verificationCode =
                    this.phoneForm.verificationCode.replace(/\D/g, '');
            }
        },
        // 手机号登录
        phoneLogin() {
            this.$refs.phoneForm.validate((valid) => {
                if (valid) {
                    const params = {
                        userName: this.phoneForm.phone,
                        userPwd: this.phoneForm.verificationCode,
                        loginType: '1'
                    };
                    this.handleLogin(params);
                }
            });
        },
        // 手机登录 获取验证码
        getVerCode() {
            // 前端验证手机号
            this.$refs['phoneForm'].validateField(['phone'], (error) => {
                if (!error) {
                    // 执行倒计时
                    this.loginCountDown = 60;
                    // 开启倒计时之前先关闭倒计时
                    clearInterval(this.loginCountTimer);
                    this.loginCountTimer = setInterval(() => {
                        if (this.loginCountDown === 0) {
                            clearInterval(this.loginCountTimer);
                        }
                        // eslint-disable-next-line no-plusplus
                        this.loginCountDown--;
                    }, 1000);
                    const { phone } = this.phoneForm;
                    this.getVerCodeRq(0, phone);
                }
            });
        },
        // 手机登录，请求获取验证码接口
        getVerCodeRq(msgType, phone) {
            const params = {
                userPhone: phone,
                msgType
            };
            this.$service.frame.getLoginVerifCode(params).then((res) => {
                const { code } = res.head;
                if (code !== '000000') {
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        /**
         * 切换扫码登录
         */
        switchThirdLogin() {
            this.showThirdLogin = !this.showThirdLogin;
            this.imgSrc = this.showThirdLogin
                ? require('../../assets/pc.png')
                : require('../../assets/ercode.png');
        },
        /**
         * 获取顶部菜单列表
         */
        getMenuFuncList() {
            this.$service.systemManagement
                .getMenuList()
                .then((res) => {
                    if (res.head.code === '000000') {
                        if (res.body.length > 0) {
                            this.currentMenuId = res.body[0].navigationId;
                            sessionStorage.setItem(
                                'currentId',
                                this.currentMenuId
                            );
                            // 存储当前的顶部菜单
                            this.$store.dispatch('user/setTopMenu', res.body);
                        }
                        this.getPermissionList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                })
                .catch(() => {
                    // 这里异常后，设置为正常登录，显示登录页面。解决刷新登录空白页问题
                    this.setIsNormalLogin(true);
                });
        },
        /**
         * 获取登录方式
         */
        getLoginTypes() {
            this.$service.frame.getLoginType().then((res) => {
                if (res.head.code === '000000') {
                    this.accountLogin = res.body.accountLogin;
                    this.ldapLogin = res.body.ldapLogin;
                    this.smsLogin = res.body.smsLogin;
                    this.qrCodeLogin = res.body.qrCodeLogin;
                    // 如果只有手机号登录，则将showMobileLogin置为true
                    if (!this.accountLogin && !this.ldapLogin) {
                        this.showMobileLogin = true;
                    }
                    if (this.smsLogin) {
                        if (this.accountLogin || this.ldapLogin) {
                            this.isActive = true;
                        }
                    } else {
                        this.isActive = false;
                    }
                    // 如果只有二维码登录，直接调用点击右上角二维码的方法
                    if (
                        this.accountLogin === false &&
                        this.ldapLogin === false &&
                        this.smsLogin === false &&
                        this.qrCodeLogin === true
                    ) {
                        this.onlyQrcode = true;
                        // 展示二维码
                        this.switchThirdLogin();
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #d6d6d6;
// $light_gray: #fff;
$light_gray: #373d41;
// $cursor: #fff;
$cursor: #373d41;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
    .login-container .el-input input {
        color: $cursor;
    }
}

/* reset element-ui css */
.login-container {
    .title-part {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #fff;
        height: 60px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        box-sizing: border-box;
        padding: 0 30px;
        font-size: 16px;
        .lang-part {
            cursor: pointer;
            .avatar-wrapper {
                display: flex;
                align-items: center;
                color: #fff;
                font-size: 16px;
                .current-lang {
                    width: 30px;
                    height: 20px;
                }
            }
        }
        .sidebar-logo {
            width: 65px;
            height: 65px;
            vertical-align: middle;
            margin-right: 8px;
            margin-bottom: 2px;
        }
    }
    .bannerImg {
        /*width: 100%;*/
        height: inherit;
        max-height: 600px;
        max-width: 822px;
    }

    .login-form-col {
        height: 100vh;
        display: flex;
        align-items: center;
    }
    .code-tip {
        position: absolute;
        cursor: pointer;
        top: 5px;
        right: 5px;
        z-index: 1;
        width: 55px;
        height: 55px;
    }
    .el-input {
        display: inline-block;
        height: 60px;
        width: 85%;

        input {
            background: transparent;
            border: 0px;
            -webkit-appearance: none;
            border-radius: 0px;
            padding: 12px 5px 12px 15px;
            color: $light_gray;
            height: 60px;
            caret-color: $cursor;

            &:-webkit-autofill {
                box-shadow: 0 0 0px 1000px #fff inset !important;
                -webkit-text-fill-color: $cursor !important;
            }

            &:-webkit-autofill::first-line {
                font-size: 18px;
            }
        }
    }

    .el-form-item {
        border: 1px solid #d8d8d8;
        border-radius: 5px;
        color: #454545;
    }
}
</style>

<style lang="scss" scoped>
// $bg: #2d3a4b;
$bg: #121217;
$dark_gray: #889aa4;
// $light_gray: #eee;
$light_gray: #373d41;

.login-container {
    background: url('../../assets/login-bg.png') #121217 no-repeat 50px 350px;
    min-height: 100%;
    width: 100%;
    background-color: $bg;
    overflow: hidden;

    .carousel {
        height: 100vh;
        display: flex;
        align-items: center;

        .el-carousel__item {
            text-align: center;
        }
    }

    .login-form {
        background: #fff;
        position: relative;
        width: 460px;
        height: 460px;
        border-radius: 10px;
        box-sizing: border-box;
        padding: 40px;

        .login-btn {
            width: 100%;
            height: 60px;
            font-size: 24px;
            font-weight: normal;
            margin-top: 10px;
            background: linear-gradient(0deg, #3370ff, #6b91f8);
            border-radius: 33px;
        }

        ::v-deep.el-form-item {
            margin-bottom: 30px;
        }
    }

    .tips {
        font-size: 14px;
        color: #373d41;
        margin-bottom: 10px;

        span {
            &:first-of-type {
                margin-right: 16px;
            }
        }
    }

    .svg-container {
        padding: 10px 5px 6px 15px;
        color: $dark_gray;
        vertical-align: middle;
        width: 45px;
        display: inline-block;

        .svg-icon {
            width: 1.5em;
            height: 1.9em;
        }
    }

    .title-container {
        position: relative;
        margin-bottom: 40px;

        .title {
            font-size: 26px;
            color: $light_gray;
            margin: 0px auto 20px auto;
            text-align: center;
            font-weight: bold;
        }
    }

    .show-pwd {
        position: absolute;
        right: 10px;
        top: 14px;
        font-size: 16px;
        color: $dark_gray;
        cursor: pointer;
        user-select: none;
    }

    .thirdparty-button {
        position: absolute;
        right: 0;
        bottom: 6px;
    }

    @media only screen and (max-width: 470px) {
        .thirdparty-button {
            display: none;
        }
    }
}

::v-deep .el-form-item--medium {
    .el-form-item__content {
        height: 60px;

        .el-form-item__error {
            margin-right: 40px;
        }
    }
}

::v-deep .el-input--medium {
    .el-input__inner {
        font-size: 18px;

        // 自动填充文本样式
        &:-webkit-autofill::first-line {
            font-size: 18px;
        }
    }
}

.tab-box {
    display: flex;
    padding: 0 52px;
    justify-content: center;
    font-size: 26px;
    font-family: PingFang SC;
    font-weight: bold;
    box-sizing: border-box;

    .tab-item {
        color: #7f7f7f;
        padding-bottom: 10px;
        border-bottom: 4px solid transparent;
        cursor: pointer;
    }

    .tab-acitve {
        color: #3370ff;
        border-bottom: 4px solid #3370ff;
    }
}
.tab-box-new {
    justify-content: space-between;
}

.phone-login-item {
    position: relative;

    .el-input {
        display: inline-table;
    }

    .split-line {
        position: absolute;
        display: block;
        width: 1px;
        height: 30px;
        background-color: #d8d8d8;
    }
}

.phone-item {
    .el-select {
        width: 90px;
        margin: -20px -20px;

        ::v-deep .el-input__suffix {
            right: -8px;
            top: 5px;
        }
    }

    .input-with-select {
        width: 98%;

        /*解决右边边框显示异常问题*/
        ::v-deep .el-input-group__prepend {
            background-color: #fff;
            border-left: 0;
            border-top: 0;
            border-bottom: 0;
        }
    }

    .split-line {
        left: 90px;
        top: 14px;
    }
}

.code-item {
    .el-input {
        width: 65%;

        ::v-deep .el-input-group__append {
            background-color: #fff;
            border-left: 0;
            border-top: 0;
            border-bottom: 0;
            border-right: 0;
        }
    }

    .get-code-nomal {
        position: absolute;
        color: #3370ff;
        top: 12px;
    }

    .get-code {
        right: 25px;
        cursor: pointer;
    }

    .code-count {
        right: 12px;
    }

    .split-line {
        right: 120px;
        top: 14px;
    }
}

.forget-password {
    text-align: right;
    margin-top: 12px;

    .forget-password-text {
        color: #2f2f2f;
        cursor: pointer;
    }

    .forget-password-text:hover {
        color: #3370ff;
    }
}
</style>
