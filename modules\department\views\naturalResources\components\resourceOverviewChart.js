/* eslint-disable */
let dayArr;
const MonthArr = Array.from({ length: 12 }, (item, index) => index + 1);
let minSaturation;
// 图例配置
const getLegend = (data, selected) => {
    return {
        type: 'scroll',
        show: true,
        padding: 15,
        width: 900,
        textStyle: {
            color: '#666666'
        },
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 25,
        top: 55,
        left: 30,
        selected,
        data
    };
};
// x轴配置
const getXAxis = (view) => {
    return {
        type: 'category',
        data: view === 'day' ? dayArr : MonthArr,
        axisLine: {
            lineStyle: {
                color: '#333'
            }
        },
        axisLabel: {
            textStyle: {
                color: '#333'
            },
            color: '#333',
            interval: 0
        },
        name: `单位（${view === 'day' ? '天' : '月'}）`
    };
};
// y轴配置
const getYAxis = () => {
    return {
        type: 'value',
        axisLine: {
            show: false
        },
        splitLine: {
            show: false
        },
        axisLabel: {
            textStyle: {
                color: '#333'
            },
            formatter: '{value}%'
        },
        name: '饱和度（%)',
        nameTextStyle: {
            padding: [0, 70, 0, 0]
        },
        min: minSaturation
    };
};
// 图标内容位置
const getGrid = () => {
    return {
        x: 30,
        y: 80,
        x2: 70,
        y2: 30,
        containLabel: true
    };
};

/**
 * 部门资源概况折线图配置（日视图）
 * @param {Array} data 数据
 * @param {Number} dividedNumber 从那天开始是预计负载
 * @returns {Array} 配置
 */
const getResoureLoadChartByDay = function (res, dividedNumber, daysNumInMonth) {
    dayArr = Array.from({ length: daysNumInMonth }, (item, index) => index + 1);
    // 部门总工时
    const departHourList = res.map((i) => {
        return i.actualOrExpectedHours;
    });
    // 饱和度
    const saturationList = res.map((i) => {
        return i.actualOrExpectedSaturation;
    });
    // 过滤掉null，求最小值作为下标
    minSaturation = Math.min(...saturationList.filter((i) => i !== null));
    // 标准工时
    const standardHoursList = res.map((i) => {
        return i.standardWorkingHours;
    });
    // 总人数
    const totalEmployees = res.map((i) => {
        return i.personTotal;
    });
    const chartDataList = saturationList.map((value, index) => {
        let positon = index % 2 === 0 ? 'top' : 'bottom';
        if (value < 100) {
            positon = 'top';
        }
        return {
            value: value,
            label: {
                show: true,
                distance: 4,
                color: '#333',
                formatter: '{c}%',
                position: positon
            }
        };
    });
    const seriesData = [
        {
            data: chartDataList,
            type: 'line',
            symbolSize: 11,
            markLine: {
                symbol: 'none',
                data: [
                    {
                        silent: false,
                        lineStyle: {
                            type: 'dashed',
                            color: '#006400'
                        },
                        yAxis: 100
                    }
                ],
                label: {
                    formatter: '{c}%'
                },
                silent: true
            }
        }
    ];
    const option = {
        grid: {
            top: 120
        },
        title: {
            text: '资源概况',
            left: 'center',
            padding: [20, 0, 0, 0],
            margin: 40,
            subtext: '饱和度 : 实际（预计）工时/标准工时',
            subtextStyle: {
                color: '#333'
            }
        },
        tooltip: {
            confine: true,
            trigger: 'item',
            show: true,
            // 让鼠标可以移入tooltip中，否则tooltip无法滚动
            enterable: true,
            formatter: function (params) {
                return `总计：${totalEmployees[params.dataIndex]}人</br>${
                    params.dataIndex < dividedNumber - 1 ? '实际' : '预计'
                }工时：${departHourList[params.dataIndex]}h<br/>标准工时：${
                    standardHoursList[params.dataIndex]
                }h`;
            }
        },
        visualMap: {
            y: 'top',
            x: 'right',
            padding: 15,
            type: 'piecewise',
            showLabel: true,
            dimension: 0,
            seriesIndex: 0,
            selectedMode: false,
            pieces: [
                {
                    min: -10,
                    max: dividedNumber - 2,
                    color: '#4169E1',
                    label: '实际饱和度',
                    lineStyle: {
                        normal: {
                            type: 'dashed'
                        }
                    }
                },
                {
                    min: dividedNumber - 2,
                    max: 50,
                    color: '#7effb2',
                    label: '预计饱和度',
                    lineStyle: { normal: { type: 'dashed' } }
                }
            ],
            lineStyle: {
                normal: {
                    type: 'dashed'
                }
            },
            textStyle: {
                color: '#333',
                fontSize: 12
            }
        },
        xAxis: getXAxis('day'),
        yAxis: getYAxis(),
        series: seriesData
    };
    return option;
};
/**
 * 个人资源负载折线图配置（月视图）
 * @param {Object} res 拿到的数据
 * @param {Number} dividedNumber 从这天开始是预计负载
 * @returns {Array} 配置
 */
const getResoureLoadChartByMonth = function (res, dividedNumber) {
    let list = [];
    res.forEach((item, index) => {
        list[index] = {};
        list[index].name = item[0].companyTitle;
        list[index].departHourList = item.map((i) => {
            return i.actualOrExpectedHours;
        });
        list[index].saturationList = item.map((i) => {
            return i.actualOrExpectedSaturation;
        });
        list[index].standardHoursList = item.map((i) => {
            return i.standardWorkingHours;
        });
        list[index].totalEmployees = item.map((i) => {
            return i.personTotal;
        });
        list[index].resignedEmployees = item.map((i) => {
            return i.leavingCount;
        });
        list[index].newEmployees = item.map((i) => {
            return i.onBoardCount;
        });
    });
    const whole = list.filter((i) => i.name === '总计');
    const other = list.filter((i) => i.name !== '总计');
    list = [...other, ...whole];
    minSaturation = Math.min(
        ...list.map((i) => {
            const a = i.saturationList.filter((j) => j !== null);
            return Math.min(...a);
        })
    );
    const colors = ['#4a1711', '#ebbf14', '#0d96e5', '#940ba3 '];

    const seriesData = list.map((i, index) => {
        const data = {
            data: i.saturationList,
            name: i.name,
            type: 'line',
            symbolSize: 11,
            lineStyle: {
                type: i.name === '总计' ? 'dashed' : 'solid'
            },
            label: {
                show: i.name === '总计' ? true : false,
                distance: 4,
                color: '#333',
                formatter: '{c}%',
                position: 'top'
            }
        };
        if (index === list.length - 1) {
            data.markLine = {
                symbol: 'none',
                data: [
                    {
                        silent: false,
                        lineStyle: {
                            type: 'dashed',
                            color: '#006400'
                        },
                        yAxis: 100
                    }
                ],
                label: {
                    formatter: '{c}%'
                },
                silent: true
            };
        }
        return data;
    });

    const nameList = list.map((i) => i.name);
    nameList.pop();
    let selected = {};
    nameList.forEach((item) => {
        selected[item] = false;
    });
    const legend = getLegend(nameList, selected);
    const option = {
        grid: {
            top: 125
        },
        title: {
            text: '资源概况',
            subtext: '饱和度 : 实际（预计）工时/标准工时',
            left: 'center',
            padding: [20, 0, 10, 0],
            subtextStyle: {
                color: '#333'
            }
        },
        tooltip: {
            confine: true,
            trigger: 'item',
            show: true,
            // 让鼠标可以移入tooltip中，否则tooltip无法滚动
            enterable: true,
            formatter: function (params) {
                const curItme = list.filter(
                    (i) => i.name === params.seriesName
                )[0];
                return `总计：${
                    curItme.totalEmployees[params.dataIndex]
                }人 (离职：${
                    curItme.resignedEmployees[params.dataIndex]
                }人，入职：${curItme.newEmployees[params.dataIndex]}人）</br>${
                    params.dataIndex < dividedNumber - 1 ? '实际' : '预计'
                }工时：${
                    curItme.departHourList[params.dataIndex]
                }h<br/>标准工时：${
                    curItme.standardHoursList[params.dataIndex]
                }h<br/>饱和度：${curItme.saturationList[params.dataIndex]}%`;
            }
        },
        visualMap: {
            y: 'top',
            x: 'right',
            padding: 15,
            type: 'piecewise',
            dimension: 0,
            seriesIndex: list.length - 1,
            selectedMode: false,
            pieces: [
                {
                    min: -10,
                    max: dividedNumber - 2,
                    color: '#4169E1',
                    label: '实际饱和度'
                },
                {
                    min: dividedNumber - 2,
                    max: 50,
                    color: '#7effb2',
                    label: '预计饱和度'
                }
            ],
            textStyle: {
                color: '#333',
                fontSize: 12,
                padding: [15, 20]
            }
        },
        xAxis: getXAxis('month'),
        yAxis: getYAxis(),
        series: seriesData,
        legend,
        color: colors
    };
    return option;
};

export { getResoureLoadChartByDay, getResoureLoadChartByMonth };
