<template>
    <div :style="boxStyle">
        <div :style="outScale">
            <div ref="sceneViewRef" class="scene-view"></div>
        </div>
    </div>
</template>
<script>
import sceneMixin from 'scene/mixins/scene-mixin.js';
import sceneConfig from 'scene/views/scene/config/overseas-retail-scene-config.js';
import sceneBg from 'scene/assets/overseas-retail-scene-bg.png';

export default {
    name: 'DomesticFinanceScene',
    mixins: [sceneMixin],
    props: {
        // 父节点宽度
        parentWidth: {
            type: Number,
            default: 0
        },
        // scale
        scale: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            // 背景图
            sceneBg,
            // 场景按钮展开
            expand: false,
            // 底部按钮展开
            expandBottom1: false,
            expandBottom2: false,
            // 场景数据对象
            sceneData: {
                前台结账场景: [],
                后台现金管理: []
            },
            sceneType: '商业零售'
        };
    },
    computed: {
        // 外层容器的缩放比例
        outScale() {
            if (!this.expand) {
                return {
                    'height': '620px',
                    'transform': 'scale(1.2)',
                    'transform-origin': 'center 100px',
                    'overflow': 'hidden'
                };
            }
            if (this.expandBottom2) {
                return {
                    height: '825px',
                    overflow: 'hidden'
                };
            }
            if (this.expandBottom1) {
                return {
                    'height': '660px',
                    'overflow-y': 'hidden'
                };
            }
            return {
                'height': '530px',
                'overflow-y': 'hidden'
            };
        },
        boxStyle() {
            const { height } = this.outScale;
            return {
                width: '100%',
                height,
                overflow: 'hidden'
            };
        },
        // 新增项目时的标志
        count() {
            return this.$store.state.scene.changeProjectCountSuccess;
        }
    },
    watch: {
        count(newVal) {
            if (newVal) {
                this.querySceneProductData().then(this.resetAllNodes());
            }
        }
    },
    mounted() {
        // 初始化场景按钮
        this.initSceneButton(sceneConfig);
        // 获取场景产品数据
        this.querySceneProductData();
    },
    methods: {
        // 获取场景产品数据
        querySceneProductData() {
            return new Promise((resolve, reject) => {
                // 参数设置
                const params = [
                    {
                        productSet: '整机终端',
                        sceneType: this.sceneType,
                        sceneNameList: ['前台结账场景', '后台现金管理']
                    }
                ];
                this.$service.scene.business_project_finance
                    .get_product_type_by_scene_name(params)
                    .then((res) => {
                        if (res.head.code !== '000000') {
                            this.$message.error(res.head.message);
                            reject();
                        }
                        const list = res.body || [];
                        list.forEach((item) => {
                            const { sceneName } = item;
                            this.sceneData[sceneName] =
                                item.productTypeVoList.map((product) => {
                                    return {
                                        ...product.calculateFieldVo,
                                        name: product.productType,
                                        sceneName: item.sceneName,
                                        iconName: product.iconName,
                                        productSet: '整机终端',
                                        sceneType: '商业零售',
                                        businessUnit: '金融机具'
                                    };
                                });
                        });
                        resolve();
                    })
                    .catch((err) => {
                        this.$message.error('场景产品数据获取失败!');
                        reject();
                    });
            });
        },
        // 点击场景按钮事件
        handleSceneButtonNode(node) {
            const res = this.graph.getSuccessors(node);
            if (res.length > 0) {
                this.graph.removeCells(res);
            } else {
                const { buttonName } = node.data;
                const { [buttonName]: config } = sceneConfig;
                const { [buttonName]: data } = this.sceneData;
                this.initSceneNodes(sceneConfig[buttonName].node, config, data);
            }
        },
        // 节点点击事件
        handleNodeClick(node) {
            if (node.shape === 'scene-button-node') {
                this.checkExpandStatus(node);
                this.checkExpandBottom1Status();
            }
            // 一级产品节点点击事件
            if (node.shape === 'scene-product-node' && node.level === 1) {
                this.checkExpandBottom2Status(node);
            }
        },
        // 检测场景是否展开
        checkExpandStatus() {
            for (let i = 0; i < this.sceneButtonNodes.length; i++) {
                const node = this.sceneButtonNodes[i];
                const res = this.graph.getSuccessors(node);
                if (res.length > 0) {
                    this.expand = true;
                    return;
                }
            }
            this.expand = false;
            this.expandBottom1 = false;
            this.expandBottom2 = false;
        },
        // 检测底部一级节点是否展开
        checkExpandBottom1Status() {
            const nodes = this.sceneButtonNodes.filter((node) => {
                const { buttonName } = node.data;
                return ['大堂场景', '外拓营销'].includes(buttonName);
            });
            for (let i = 0; i < nodes.length; i++) {
                const node = nodes[i];
                const res = this.graph.getSuccessors(node);
                if (res.length > 0) {
                    this.expandBottom1 = true;
                    return;
                }
            }
            this.expandBottom1 = false;
        },
        // 检测底部二级节点是否展开
        checkExpandBottom2Status(node) {
            const { buttonName } = node.parentNode.data;
            const bool = ['大堂场景', '外拓营销'].includes(buttonName);
            if (bool) {
                const res = this.graph.getSuccessors(node);
                if (res.length > 0) {
                    this.expandBottom2 = true;
                    return;
                }
            }
            this.expandBottom2 = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.scene-view {
    width: 1250px;
    height: 825px;
    transform: scale(1);
    transform-origin: 0 0;
    position: relative;
}
</style>
