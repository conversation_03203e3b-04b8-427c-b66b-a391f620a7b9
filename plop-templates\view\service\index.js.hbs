,
        get{{pascalCase viewName}}List(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/getList',
                method: 'get',
                params: query
            });
        },
        get{{pascalCase viewName}}Export(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/export',
                method: 'get',
                responseType: 'blob',
                params: query,
                timeout: downlaodTimeout
            });
        },
{{#if isAdd }}
        post{{pascalCase viewName}}Add(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/add',
                method: 'post',
                data: data
            });
        },
{{/if}}
{{#if isEdit }}
        post{{pascalCase viewName}}Update(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/update',
                method: 'post',
                data: data
            });
        },
{{/if}}
{{#if isDelete }}
        post{{pascalCase viewName}}Remove(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/delete',
                method: 'delete',
                data: data
            });
        },
{{/if}}
{{#if isTreeTable }}
        get{{pascalCase viewName}}TreeList(query) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/getTreeList',
                method: 'get',
                params: query
            });
        },
{{/if}}
{{#if isTreeAdd }}
        post{{pascalCase viewName}}TreeAdd(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/add',
                method: 'post',
                data: data
            });
        },
{{/if}}
{{#if isTreeEdit }}
        post{{pascalCase viewName}}TreeUpdate(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/update',
                method: 'post',
                data: data
            });
        },
{{/if}}
{{#if isTreeDelete }}
        post{{pascalCase viewName}}TreeRemove(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/delete',
                method: 'delete',
                data: data
            });
        },
{{/if}}
{{#if isTreeSort }}
        post{{pascalCase viewName}}TreeSort(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/{{viewName}}/sort',
                method: 'post',
                data: data
            });
        }
{{/if}}
    };

    return service;
};
