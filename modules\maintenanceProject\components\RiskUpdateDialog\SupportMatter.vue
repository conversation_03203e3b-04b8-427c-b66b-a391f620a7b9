<template>
    <div>
        <el-form ref="supportForm" :model="value" class="minutes-form">
            <div class="button-line">
                <el-button type="primary" size="small" @click="addRow"
                    >新增</el-button
                >
            </div>
            <el-table
                :data="value.riskSupportList"
                class="minutes-table"
                :row-style="{ background: '#fff' }"
                empty-text="无需支持的事项"
                style="width: 100%"
            >
                <el-table-column
                    header-align="center"
                    align="center"
                    prop="supportItem"
                >
                    <template #header>
                        <RedStar class="required" />需支持的事项和目标
                    </template>
                    <template #default="scope">
                        <el-form-item
                            :prop="`riskSupportList.${scope.$index}.supportItem`"
                            :rules="required"
                        >
                            <el-input
                                type="textarea"
                                :autosize="{ minRows: 4 }"
                                maxlength="2000"
                                v-model="scope.row.supportItem"
                                @input="handleChange"
                            ></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                    align="center"
                    header-align="center"
                    prop="expectedDate"
                    width="120"
                >
                    <template #header>
                        <RedStar class="required" />期望达成日期
                    </template>
                    <template #default="scope">
                        <el-form-item
                            :prop="`riskSupportList.${scope.$index}.expectedDate`"
                            :rules="required"
                        >
                            <el-date-picker
                                v-model="scope.row.expectedDate"
                                type="date"
                                placeholder=""
                                value-format="yyyy-MM-dd"
                                @change="handleChange"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                    label="提供支持的具体责任人"
                    prop="responsiblePersonAccountList"
                    header-align="center"
                    align="center"
                    width="150"
                >
                    <template #default="scope">
                        <el-form-item
                            :prop="`riskSupportList.${scope.$index}.responsiblePersonAccountList`"
                        >
                            <PeopleSelector
                                v-model="scope.row.responsiblePersonAccountList"
                                placeholder=""
                                :isAll="1"
                                @change="handleChange"
                                data-id="peopleSelector"
                                :ref="`peopleSelectorRef${scope.$index}`"
                                :clearable="true"
                            >
                            </PeopleSelector>
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                    label="提供支持的责任部门"
                    prop="responsibleOrgCodeList"
                    header-align="center"
                    align="center"
                >
                    <template #header>
                        <RedStar class="required" />提供支持的责任部门
                    </template>
                    <template #default="scope">
                        <el-form-item
                            :prop="`riskSupportList.${scope.$index}.responsibleOrgCodeList`"
                            :rules="required"
                        >
                            <el-cascader
                                :ref="`departmentRef${scope.$index}`"
                                class="selector"
                                v-model="scope.row.responsibleOrgCodeList"
                                :options="supportDepartmentOptions"
                                :props="{
                                    checkStrictly: true,
                                    label: 'orgName',
                                    value: 'orgCode',
                                    expandTrigger: 'hover',
                                    multiple: true
                                }"
                                @change="handleCascaderChange(scope.$index)"
                                popper-class="org-cascader"
                                filterable
                            >
                                <div
                                    slot-scope="{ data }"
                                    @click="clickNode"
                                    class="span-click"
                                >
                                    {{ data.orgName }}
                                </div>
                            </el-cascader>
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                    label="状态"
                    prop="supportStatus"
                    header-align="center"
                    align="center"
                    width="120"
                >
                    <template #header>
                        <RedStar class="required" />状态
                    </template>
                    <template #default="scope">
                        <el-form-item
                            :prop="`riskSupportList.${scope.$index}.supportStatus`"
                            :rules="required"
                        >
                            <el-select
                                v-model="scope.row.supportStatus"
                                placeholder=""
                                @change="handleChange"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.RISK_SUPPORT_STATUS"
                                    :label="item"
                                    :key="item"
                                    :value="item"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                    label="操作"
                    width="100"
                    header-align="center"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="danger"
                            @click="deleteRow(scope.$index)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
    </div>
</template>

<script>
import RedStar from 'feature/components/redStar';
import PeopleSelector from 'Components/PeopleSelector';
import { CONSTANTS } from '@/constants';
import { getSelectedLabel } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'SupportMatter',
    components: { RedStar, PeopleSelector },
    props: {
        value: {
            type: Object,
            required: true,
            default: () => ({
                riskSupportList: [
                    {
                        supportItem: '',
                        expectedDate: '',
                        responsiblePersonAccountList: [],
                        responsibleOrgCodeList: '',
                        supportStatus: ''
                    }
                ]
            })
        }
    },
    data() {
        return {
            required: {
                required: true,
                message: ' ',
                trigger: ['change', 'blur']
            },
            supportDepartmentOptions: [],
            CONSTANTS
        };
    },
    created() {
        this.getSupportDepartmentOptions();
    },
    methods: {
        /**
         * 获取指定行的责任人标签
         * @param {Number} index 行索引
         * @returns {String} 责任人标签，多个责任人用逗号分隔
         */
        getResponsiblePersonLabel(index) {
            const refName = `peopleSelectorRef${index}`;
            const peopleSelectorRef = this.$refs[refName];
            return getSelectedLabel(peopleSelectorRef);
        },
        /**
         * 获取指定行的部门标签
         * @param {Number} index 行索引
         * @returns {String} 责任人标签，多个责任人用逗号分隔
         */
        getRiskSupportListLabel(index) {
            const refName = `departmentRef${index}`;
            const departmentRef = this.$refs[refName];
            return departmentRef.getCheckedNodes().map((i) => i.pathLabels);
        },
        /**
         * 通过点击文字选中的处理函数
         * @param {Object} e 事件对象
         */
        clickNode(e) {
            // 模拟点击对应的radio
            e.target.parentElement.parentElement.firstElementChild.click();
        },
        handleCascaderChange(index) {
            // 每次选择结束之后自动关闭
            if (this.$refs[`departmentRef${index}`]?.dropDownVisible) {
                this.$refs[`departmentRef${index}`].dropDownVisible = false;
            }
        },
        addRow() {
            this.value.riskSupportList.push({
                supportItem: '',
                expectedDate: '',
                responsiblePersonAccountList: [],
                responsibleOrgCodeList: '',
                supportStatus: ''
            });
            this.handleChange();
        },
        deleteRow(index) {
            this.value.riskSupportList.splice(index, 1);
            this.handleChange();
        },
        handleChange() {
            // 触发v-model更新
            this.$emit('input', { ...this.value });
        },
        validateForm() {
            return new Promise((resolve) => {
                if (!this.$refs.supportForm) {
                    console.error('表单引用不存在');
                    resolve(false);
                    return;
                }

                this.$refs.supportForm.validate((valid, invalidFields) => {
                    if (!valid) {
                        console.error('表单验证失败:', invalidFields);
                    }
                    resolve(valid);
                });
            });
        },
        /**
         * 获取部门选项
         */
        async getSupportDepartmentOptions() {
            const api =
                this.$service.maintenanceProject.common.getCompanyOrgInfo;
            try {
                const res = await api();
                if (res.head.code === '000000') {
                    this.supportDepartmentOptions = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.button-line {
    margin-bottom: 10px;
    text-align: right;
}

.minutes-form {
    ::v-deep .el-table__row > td {
        padding: 0px;
    }
    ::v-deep .cell {
        padding: 0 !important;
    }
    .el-form-item {
        margin-bottom: 0px;
    }
    ::v-deep .el-form-item__error {
        top: calc(50% - 10px);
        left: 16px;
    }
    ::v-deep .el-form-item__content {
        margin: 0 !important;
    }

    // 令输入框无边框
    ::v-deep .el-input__inner {
        border: none !important;
    }

    // 令输入框无边框
    ::v-deep .el-textarea__inner {
        border: none;
        resize: none;
    }
    // 令鼠标移入之后不变色
    ::v-deep .el-table tbody tr:hover > td {
        background-color: #fff !important;
    }

    .minutes-table {
        border: 1px solid #8c8c8c !important;
    }
}
.selector {
    width: 100%;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
<style>
.org-cascader .el-cascader-menu__wrap {
    height: 365px;
}
</style>
