<template>
    <div class="view">
        <el-container>
            <el-aside>
                <el-button v-permission="['region_manage_increase_region']" icon="fa fa-plus-square-o" class="margin-left" size="middle" type="primary" @click="addProvince">{{ $t('systemManagement.area.addRegion') }}</el-button>
                <el-tree v-if="areaTreeFlag" ref="areaTree" node-key="id" :props="defaultProps" :load="loadNode" lazy :expand-on-click-node="false" :default-expanded-keys="defaultTreeExpandedKeys" :current-node-key="currentTreeNodeKey" auto-expand-parent @node-click="handleNodeClick" @node-expand="handleNodeExpand">
                    <el-tooltip slot-scope="{ node }" :content="node.label" placement="right">
                        <span class="text-ellipsis tree-node-text">{{ node.label }}</span>
                    </el-tooltip>
                </el-tree>
            </el-aside>
            <el-main>
                <div class="header">
                    {{ $t("systemManagement.listTitle") }}
                </div>
                <el-table id="areaTable" ref="tableArea" :header-cell-style="{ background: '#F5F6FA' }" :data="tableData" border>
                    <el-table-column prop="regionName" :label="$t('systemManagement.area.regionName')" width="300" />
                    <el-table-column prop="regionCode" :label="$t('systemManagement.area.regionCode')" width="240" />
                    <el-table-column prop="regionDesc" :label="$t('systemManagement.area.describe')">
                        <template slot-scope="{ row}">
                            <span class="column-desc" :title="row.regionDesc">
                                {{ row.regionDesc }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')" width="320px">
                        <template slot-scope="{ row, $index }">
                            <el-button v-permission="['add_region_management']" size="mini" type="primary" icon="fa fa-plus-square-o" @click="handleAdd(row)">
                                {{ $t("table.add") }}
                            </el-button>
                            <el-button v-permission="['region_manage_edit']" icon="fa fa-pencil" size="mini" type="primary" @click="handleEdit(row)">
                                {{ $t("table.edit") }}
                            </el-button>
                            <el-button v-permission="['region_manage_delete']" size="mini" icon="fa fa-trash-o" @click="handleDelete(row, $index)">
                                {{ $t("table.delete") }}
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-main>
        </el-container>
        <el-dialog :title="$t('systemManagement.area.addArea')" :visible.sync="addProvinceDialogVisible" @closed="clearAreaProvinceItem">
            <el-form ref="areaProvinceItem" label-position="right" label-width="130px" :model="areaProvinceItem" :rules="areaAddRules">
                <el-form-item :label="$t('systemManagement.area.regionName')" prop="regionName">
                    <el-input v-model="areaProvinceItem.regionName" :placeholder="$t('systemManagement.area.placeholder.msg1')" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.regionCode')" prop="regionCode">
                    <el-input v-model="areaProvinceItem.regionCode" :placeholder="$t('systemManagement.area.placeholder.msg2')" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.describe')" prop="regionDesc">
                    <el-input v-model="areaProvinceItem.regionDesc" type="textarea" :placeholder="$t('systemManagement.area.placeholder.msg3')" maxlength="200" />
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="addProvinceDialogVisible = false">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="saveAreaProvince">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('systemManagement.area.addArea')" :visible.sync="addDialogVisible" @closed="clearAreaItem">
            <el-form ref="areaItem" label-position="right" label-width="130px" :model="areaItem" :rules="areaAddRules">
                <el-form-item :label="$t('systemManagement.area.fatherName')" prop="fatherName">
                    <el-input v-model="areaFather.regionName" :placeholder="$t('systemManagement.area.placeholder.msg4')" :disabled="true" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.regionName')" prop="regionName">
                    <el-input v-model="areaItem.regionName" :placeholder="$t('systemManagement.area.placeholder.msg1')" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.regionCode')" prop="regionCode">
                    <el-input v-model="areaItem.regionCode" :placeholder="$t('systemManagement.area.placeholder.msg2')" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.describe')" prop="regionDesc">
                    <el-input v-model="areaItem.regionDesc" type="textarea" :placeholder="$t('systemManagement.area.placeholder.msg3')" maxlength="200" />
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="addDialogVisible = false">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="saveArea">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="$t('systemManagement.area.editArea')" :visible.sync="editDialogVisible">
            <el-form ref="areaItemEdit" label-position="right" label-width="130px" :model="areaItemEdit" :rules="areaAddRules">
                <el-form-item :label="$t('systemManagement.area.regionName')" prop="regionName">
                    <el-input v-model="areaItemEdit.regionName" :placeholder="$t('systemManagement.area.placeholder.msg1')" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.regionCode')" prop="regionCode">
                    <el-input v-model="areaItemEdit.regionCode" :disabled="editDialogVisible === true" :placeholder="$t('systemManagement.area.placeholder.msg2')" maxlength="20" />
                </el-form-item>
                <el-form-item :label="$t('systemManagement.area.describe')" prop="regionDesc">
                    <el-input v-model="areaItemEdit.regionDesc" type="textarea" :placeholder="$t('systemManagement.area.placeholder.msg3')" maxlength="200" />
                </el-form-item>
            </el-form>
            <span slot="footer">
                <el-button @click="editDialogVisible = false">{{
          $t("table.cancel")
        }}</el-button>
                <el-button type="primary" @click="updateArea">{{
          $t("table.confirm")
        }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'Area',
    data() {
        return {
            areaTreeFlag: true,
            // 表格数据
            tableData: [],
            // 树结构数据
            treeData: [],
            defaultProps: {
                children: 'children',
                label: 'regionName',
                isLeaf: 'leaf'
            },
            // 控制添加省份的弹窗变量
            addProvinceDialogVisible: false,
            // 添加省份的弹窗表单数据对象
            areaProvinceItem: {
                regionName: '',
                regionCode: '',
                regionDesc: ''
            },
            // 保存地域弹层
            addDialogVisible: false,
            // 保存地域 - 父级地域名称
            areaFather: {
                regionName: '',
                regionCode: '',
                regionDesc: ''
            },
            // 保存地域 - 地域数据
            areaItem: {
                regionName: '',
                regionCode: '',
                regionDesc: ''
            },
            // 编辑地域弹层
            editDialogVisible: false,
            // 编辑弹窗表单数据对象
            areaItemEdit: {
                regionName: '',
                regionCode: '',
                regionDesc: '',
                parentCode: '',
                id: ''
            },
            // 删除的数据对象
            areaItemDetete: {},

            // 记录树展开的节点标识
            defaultTreeExpandedKeys: [],
            // 记录当前树默认选择节点
            currentTreeNodeKey: '',
            // 记录当前表格数据子节点列表，还是当前点击项
            isTableList: true
        };
    },
    computed: {
        // 区域的验证规则
        areaAddRules() {
            // 区域名称规则，非空验证
            return {
                regionName: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.area.inputRegionName'
                        ),
                        trigger: ['blur', 'change']
                    }
                ],
                // 区域编码，使用非空验证
                regionCode: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.area.inputRegionCode'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    methods: {
        // 地域表格数据获取,这里是提供给tree进行异步调用的方法
        loadNode(node, resolve) {
            // 显示右侧树列表
            if (node && node.data) {
                this.tableData = [node.data];
            }
            // 如果节点不是跟节点，并且节点没有子节点，则不加载数据
            if (node.level !== 0 && !node.data.hasChild) {
                resolve([]);
                return;
            }
            // 如果是父级根节点，regionCode需要传空
            const data = {
                regionCode: node.level === 0 ? '' : node.data.regionCode
            };

            this.$service.systemManagement.getRegionList(data).then((res) => {
                if (res.head.code === '000000') {
                    // 将数据转换为书节点所需数据。leaf是标识是否有子节点
                    const leafData = res.body.map((item) => {
                        const leaf = { leaf: !item.hasChild };
                        return { ...item, ...leaf };
                    });
                    resolve(leafData);

                    // 如果没有默认选中，则加载省份，即根数据
                    if (this.isTableList || !this.currentTreeNodeKey) {
                        this.tableData = res.body || [];
                    }
                } else {
                    // 这里给空数据，避免加载失败，treenode节点一直处于加载提示问题
                    resolve([]);
                    this.$message.error(res.head.message);
                }
            });
        },
        // 设置tree节点的选中
        setTreeCurrentNodeKey(nodeKey) {
            // 这里主要是为了tree刷新后，能保持当前选中节点
            this.currentTreeNodeKey = nodeKey || '';
            this.$refs.areaTree.setCurrentKey(nodeKey);
        },
        // 获取地域子节点数据
        handleNodeClick(data, node, nodeCom) {
            // 如果是点击左侧tree节点，右侧表格则展示单条数据
            this.isTableList = false;
            this.currentTreeNodeKey = data.id;
            this.tableData = [data];
        },
        // 节点被展开的数据
        handleNodeExpand(data, node) {
            // 如果是点击左侧tree展开图标，右侧表格则展示展开节点的所有子节点
            this.isTableList = true;
            this.setTreeCurrentNodeKey(data.id);

            // 给右侧表格对象赋值
            this.tableData = node.childNodes.map((item) => {
                return item.data;
            });
        },
        // 增加省
        addProvince() {
            this.addProvinceDialogVisible = true;
        },
        // 增加地域
        handleAdd(area) {
            this.addDialogVisible = true;
            this.areaFather = area;
        },
        // 编辑地域
        handleEdit(area) {
            this.editDialogVisible = true;
            this.areaItemEdit.oldParam = JSON.parse(JSON.stringify(area));
            Object.assign(this.areaItemEdit, area);
        },
        // 删除地域
        handleDelete(area) {
            // 这里赋值，在删除方法里面使用
            this.areaItemDetete = area;
            const _this = this;
            this.$confirm(
                this.$t('systemManagement.area.message.deleteTypeTip'),
                this.$t('systemManagement.area.message.deleteTipTitle'),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.deleteArea(area);
                this.$message.success(
                    _this.$t(
                        'systemManagement.configuration.message.deleteSuccess'
                    )
                );
            });
        },
        // 获取当前节点的所有父节点
        getTreeNodeParent(treeNode) {
            if (!treeNode) {
                return [];
            }
            let keys = [];
            if (treeNode.data && treeNode.data.id) {
                keys.push(treeNode.data.id);
            }
            // 如果有节点有父节点，继续递归
            if (treeNode.parent) {
                const temp = this.getTreeNodeParent(treeNode.parent);
                // 递归的父节点，全部存在一个数组里面
                keys = keys.concat(temp);
            }
            return keys;
        },
        // 重新渲染区域tree
        renderAreaTree() {
            // 获取当前选中的节点，用于刷新保持选中状态
            const nodeKey = this.$refs.areaTree.getCurrentKey() || '';
            this.currentTreeNodeKey = nodeKey;

            if (nodeKey) {
                const node = this.$refs.areaTree.getNode(nodeKey);
                // 这里是保存当前节点的父节点，用于刷新后逐级定位到当前选中节点
                this.defaultTreeExpandedKeys = this.getTreeNodeParent(node);
            }

            // 重置了tree的vif绑定areaTreeFlag变量值，用于刷新树
            this.areaTreeFlag = false;
            this.$nextTick(() => {
                this.areaTreeFlag = true;
            });
        },
        // 保存地域 - 省
        saveAreaProvince() {
            const params = {
                regionName: this.areaProvinceItem.regionName,
                regionCode: this.areaProvinceItem.regionCode,
                regionDesc: this.areaProvinceItem.regionDesc
            };
            // 验证表单
            this.$refs.areaProvinceItem.validate((valid) => {
                if (valid) {
                    // 调用新增区域接口
                    this.$service.systemManagement
                        .postAddRegion(params)
                        .then((res) => {
                            if (res.head.code === '000000') {
                                this.addProvinceDialogVisible = false;
                                // 重新渲染树
                                this.renderAreaTree();
                            } else {
                                const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                                this.$message({
                                    title: this.$t(
                                        'systemManagement.msg.failed'
                                    ),
                                    message: this.$t(msg),
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        // 保存地域
        saveArea() {
            const params = {
                parentCode: this.areaFather.regionCode,
                regionName: this.areaItem.regionName,
                regionCode: this.areaItem.regionCode,
                regionDesc: this.areaItem.regionDesc
            };
            this.$refs.areaItem.validate((valid) => {
                if (valid) {
                    this.$service.systemManagement
                        .postAddRegion(params)
                        .then((res) => {
                            if (res.head.code === '000000') {
                                this.addDialogVisible = false;
                                // 重新渲染树
                                this.renderAreaTree();
                            } else {
                                const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                                this.$message({
                                    title: this.$t(
                                        'systemManagement.msg.failed'
                                    ),
                                    message: this.$t(msg),
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        // 编辑地域
        updateArea() {
            const params = {
                regionName: this.areaItemEdit.regionName,
                regionCode: this.areaItemEdit.regionCode,
                regionDesc: this.areaItemEdit.regionDesc,
                parentCode: this.areaItemEdit.parentCode,
                id: this.areaItemEdit.id,
                oldParam: this.areaItemEdit.oldParam
            };
            this.$refs.areaItemEdit.validate((valid) => {
                if (valid) {
                    this.$service.systemManagement
                        .postUpdateRegion(params)
                        .then((res) => {
                            if (res.head.code === '000000') {
                                this.editDialogVisible = false;
                                // 重新渲染树
                                this.renderAreaTree();
                            } else {
                                const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                                this.$message({
                                    title: this.$t(
                                        'systemManagement.msg.failed'
                                    ),
                                    message: this.$t(msg),
                                    type: 'error',
                                    duration: 2000
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        // 关闭弹窗的回调
        clearAreaItem() {
            this.$refs.areaItem.resetFields();
        },
        // 关闭弹窗的回调
        clearAreaProvinceItem() {
            this.$refs.areaProvinceItem.resetFields();
        },
        // 删除地域
        deleteArea(area) {
            const params = {
                regionCode: this.areaItemDetete.regionCode,
                regionName: area.regionName
            };

            // 尝试获取当前节点父节点
            const node = this.$refs.areaTree.getNode(this.areaItemDetete.id);

            this.$service.systemManagement.deleteRegion(params).then((res) => {
                if (res.head.code === '000000') {
                    // 清空列表旧数据,重置默认选中节点为空
                    this.tableData = [];

                    // 重新渲染树
                    this.renderAreaTree();

                    // 删除成功，尝试选中父节点
                    if (node && node.parent && node.parent.data) {
                        // 记录当前删除节点的父节点id，用于重新选中
                        this.currentTreeNodeKey = node.parent.data.id || '';
                    } else {
                        // 如果删除的是根节点，则不进行默认选中
                        this.currentTreeNodeKey = '';
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.column-desc{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>

