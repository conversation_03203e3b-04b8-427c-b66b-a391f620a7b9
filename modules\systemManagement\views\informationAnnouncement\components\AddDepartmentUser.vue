<template>
  <el-dialog
    :title="$t('systemManagement.informationAnnounce.addPage.addUser')"
    :visible.sync="isShow"
    width="50%"
    custom-class="transfer-dialog org-dialog"
    @close="cancel"
  >
    <el-card>
      <!-- 树结构 -->
      <el-tree
        ref="tree"
        :data="dataOrgTreeList"
        default-expand-all
        :default-checked-keys="treeDefaultCheckedKeys"
        check-strictly
        :props="orgTreeProps"
        node-key="orgId"
        :expand-on-click-node="false"
        check-on-click-node
        height="350px"
        @check="getCheckedNodes"
      >
        <el-tooltip slot-scope="{ node,data}" :content="node.label" placement="right">
          <span class="block">
            <span class="block__text">{{
              node.label
            }}</span>
            <span class="block__btns">
              <el-button type="text" size="mini" v-show="!data.disabled" @click.stop="addOrg(data)">
                <span
                  style="color:blue"
                >{{ $t('systemManagement.informationAnnounce.selectDept') }}</span>
              </el-button>
            </span>
          </span>
        </el-tooltip>
      </el-tree>
    </el-card>
    <el-card>
      <div class="header">
        <el-input
          v-model="userFilterText"
          suffix-icon="fa fa-search"
          :placeholder="$t('systemManagement.informationAnnounce.message.searchPerson')"
        />
      </div>
      <div class="content">
        <div
          v-for="row in dataUserSourceFilterList"
          :key="row.userId"
          class="user-item"
          :class="{ 'card-active': isSelected(row) }"
          @click="addTargetUserItem(row)"
        >
          <img src="../../../assets/default-img.png" alt="" class="user-item-img">
          <div class="user-item-text">
            <div class="left">
              <div class="text-title text-ellipsis">
                {{ row.userName }}
              </div>
              <div class="text-desc">
                <span>{{ row.phone }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="content">
        <h4>{{ $t('systemManagement.informationAnnounce.addPage.selectedPart') }}</h4>
        <div v-for="row in checkOrgListFinal" :key="row.orgId" class="user-item user-item-selected">
          <img src="../../../assets/default-img.png" alt="" class="user-item-img">
          <span class="user-item-text">
            <div class="text-title-chk">
              <el-tooltip :content="row.orgName" placement="right">
                <span class="text-content text-ellipsis">{{
                  row.orgName
                }}</span>
              </el-tooltip>
            </div>
            <i class="el-icon-error text-close" v-if="row.canRemove !== 'false'" @click.stop="removeOrgrItem(row)" />
          </span>
        </div>
        <h4>{{ $t('systemManagement.informationAnnounce.addPage.selectedPerson') }}</h4>
        <div v-for="row in checkUserListFinal" :key="row.userId" class="user-item user-item-selected">
          <img src="../../../assets/default-img.png" alt="" class="user-item-img">
          <span class="user-item-text">
            <div class="text-title-chk">
              <el-tooltip :content="row.userName" placement="right">
                <span class="text-content text-ellipsis">{{
                  row.userName
                }}</span>
              </el-tooltip>
            </div>
            <i class="el-icon-error text-close" v-if="row.canRemove !== 'false'" @click.stop="removeTargetUserItem(row)" />
          </span>
        </div>
      </div>
    </el-card>
    <span slot="footer">
      <el-button @click="cancel">{{
        $t("systemManagement.role.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">{{
        $t("systemManagement.components.treeSelectOrg.confirm")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        },
        orgList: {
            type: Array,
            default() {
                return [];
            }
        },
        personList: {
            type: Array,
            default() {
                return [];
            }
        },
         // 不能移除的部门
         reserveOrgs:{
            type:Array,
            default(){
                return [];
            }
        },
         // 不能移除的部门
         reserveUsers:{
            type:Array,
            default(){
                return [];
            }
        }
    },
    data() {
        return {
            // 定义tree显示的名称字段
            orgTreeProps: {
                children: 'children',
                label: 'orgName'
            },
            // 当前选中的keys
            currentCheckedKeys: [],
            // 当前选中的nodes
            currentCheckedNodes: [],
            // 当前已选的数据列表
            checkOrgList: [],
            // 用户列表过滤
            userFilterText: '',
            // 接口返回的用户信息列表，用于数据展示
            dataSourceUserList: [],
            // 已选择用户列表
            dataTargetUserList: [],
            // 组织机构树
            dataOrgTreeList: [],
            // 默认展开的树列表
            defaultExpandOrgTree: [],
            // 是否显示全部删除按钮
            hasSelected: false
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        // 表格数据过滤
        dataUserSourceFilterList() {
            // 忽略空格
            const filterText = (this.userFilterText || '').trim();
            return this.dataSourceUserList.filter((item) => {
                // 根据用户名称、手机号、工号进行本地搜索
                return (
                    (item.userName || '').includes(filterText) ||
					(item.phone || '').includes(filterText) ||
					(`${  item.jobNumber || ''}`).includes(filterText)
                );
            });
        },
        // 组织机构树默认选中项
        treeDefaultCheckedKeys() {
            return this.orgList.map((item) => item.orgId);
        },
        // 展示在右侧的已选部门
        checkOrgListFinal(){
            const arrObj = this.checkOrgList.concat(this.reserveOrgs);
            // 数组对象去重
            const map = new Map();
            for (const item of arrObj) {
                if (!map.has(item.orgId)) {
                    map.set(item.orgId, item);
                }
            }
            const filterArr = [...map.values()];
            return filterArr;
        },
        // 展示在右侧的已选人员
        checkUserListFinal(){
            const arrObj = this.dataTargetUserList.concat(this.reserveUsers);
            // 数组对象去重
            const map = new Map();
            for (const item of arrObj) {
                if (!map.has(item.userId)) {
                    map.set(item.userId, item);
                }
            }
            const filterArr = [...map.values()];
            return filterArr;
        }
    },
    watch: {
        dataTargetUserList: {
            handler(newVal, oldVal) {
                if (newVal.length > 0) {
                    this.hasSelected = true;
                } else {
                    this.hasSelected = false;
                }
            }
        }
    },
    created() {
        this.dataTargetUserList = this.personList;
        this.checkOrgList = this.orgList.filter((item) => item.orgId !== '');
        // 先查左侧组织机构树
        this.getOrgTreeList();
        // 默认选中根节点
        this.getUserList();
    },
    methods: {
        // 用户原有列表选中，如果选中显示深色背景
        isSelected(dataItem) {
            return this.dataTargetUserList.some((item) => {
                return dataItem.userId === item.userId;
            });
        },
        // 添加选择部门
        addOrg(data) {
            if (
                this.checkOrgList.filter((item) => item.orgId === data.orgId)
                    .length === 0
            ) {
                this.checkOrgList.push(data);
            }
        },
        // 获取组织机构数据
        getOrgTreeList() {
            this.$service.systemManagement
                .getRoleOrgTree({ orgLevel: 0, orgId: '' })
                .then((response) => {
                    if (response.head.code === '000000') {
                        const dataTree = response.body || [];

                        this.defaultExpandOrgTree.splice(0);
                        if (dataTree.length > 0) {
                            // 默认展开第一个节点
                            const firstNodeOrgId = dataTree[0].orgId || '0';
                            this.defaultExpandOrgTree.push(firstNodeOrgId);
                        }
                        this.dataOrgTreeList = dataTree;
                    } else {
                        this.$message({
                            message:
                              response.head.message ||
                                this.$t(
                                    'systemManagement.role.message.queryListFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 获取角色对应用户列表
        getUserList(id = '') {
            let orgIds = '';
            if (id) {
                orgIds = id;
            }
            const param = {
                orgIds,
                userNamePhone: this.userFilterText
            };
            this.$service.systemManagement
                .getPersonList(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.dataSourceUserList = response.body || [];
                    } else {
                        this.$message({
                            message:
                               response.head.message ||
                              this.$t(
                                  'systemManagement.role.message.queryListFailure'
                              ),
                            type: 'error'
                        });
                    }
                });
        },
        // 组织机构节点点击事件
        getCheckedNodes(data, check) {
            // node 该节点所对应的对象、list 树目前的选中状态对象;选中事件在选中后执行，当lis中有两个选中时，使用setCheckedKeys方法，选中一个节点
            if (check.checkedKeys.length === 2) {
                // 单选实现
                this.$refs.tree.setCheckedKeys([data.orgId]);
            }
            // 更新人员列表
            this.getUserList(data.orgId);
        },
        // 点击选择用户
        addTargetUserItem(row) {
            // 判断列表数据是否已经存在，存在则不重复添加
            const isExist = this.dataTargetUserList.some((item) => {
                return item.userId === row.userId;
            });
            if (isExist) {
                this.removeTargetUserItem(row);
                return;
            }

            const tempData = this.dataSourceUserList.filter((item) => {
                return item.userId === row.userId;
            });
            if (tempData.length >= 1) {
                this.dataTargetUserList.push(tempData[0]);
            }
        },
        // 移除已选择部门列表
        removeOrgrItem(data) {
            this.checkOrgList = this.checkOrgList.filter((item) => {
                return item.orgId !== data.orgId;
            });
            this.$refs.tree.setChecked(data, false);
        },
        // 移除已选择用户列表
        removeTargetUserItem(row) {
            this.dataTargetUserList = this.dataTargetUserList.filter((item) => {
                return item.userId !== row.userId;
            });
        },
        // 取消
        cancel() {
            this.isShow = false;
        },
        // 保存
        save() {
            this.isShow = false;
            this.$emit('rangeList', this.checkOrgList, this.dataTargetUserList);
        }
    }
};
</script>
  <style lang="scss" scoped>
// 弹框中的面板样式eg. 角色管理中的添加用户弹框
.user-item {
	display: flex;
	align-items: center;
	border-radius: 5px;
	box-sizing: border-box;
	padding: 6px 10px;
	color: #8b8b8b;
	cursor: pointer;
	font-size: 12px;
	font-weight: 400;
	margin-bottom: 10px;
	box-sizing: border-box;
	border: 1px solid #dfdfdf;
	.user-item-img {
		margin-right: 11px;
		width: 30px;
		height: 30px;
	}
	.user-item-text {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 30px;
		flex: 1;
		.text-desc {
			margin-top: 5px;
		}
		.text-jobnumber {
			margin-right: 11px;
		}
		.text-close {
			font-size: 15px;
			color: #c7c7c7;
		}
	}
	&:hover {
		background: #eaf0ff;
		color: #000000;
		border: 1px solid transparent;
	}
}
// 选中样式
.user-item-selected {
	background: #eaf0ff;
	color: #2f2f2f;
	border: 1px solid transparent;
}
// 高亮样式
.card-active {
	background: #eaf0ff;
	color: #000000;
	border: 1px solid transparent;
}
.transfer-dialog {
	.header {
		.text-close-all {
			font-size: 15px;
			color: #8996a8;
			cursor: pointer;
		}
	}
	.already-selected {
		box-sizing: border-box;
		border-bottom: 1px solid #eaf0ff;
		padding-bottom: 19px;
	}
}
.org-dialog {
	.header {
		.text-close-all {
			font-size: 15px;
			color: #8996a8;
			cursor: pointer;
		}
	}
	.list {
		.block {
			background: #eaf0ff;
			margin-bottom: 5px;
			cursor: pointer;
			border-radius: 5px;
			font-size: 14px;
			.block__text {
				margin-left: 11px;
			}
			.block__btns {
				.el-icon-error {
					color: #c7c7c7;
				}
			}
		}
	}
}
::v-deep .org-dialog {
	.el-tree {
		/*控制最顶级的选中状态 */
		& > .el-tree-node.is-expanded {
			.el-tree-node__content {
				background: transparent !important;
				border-radius: 5px;
				margin-bottom: 5px;
			}
		}

		& > .el-tree-node.is-checked {
			& > .el-tree-node__content {
				background: #eaf0ff !important;
			}
		}
		/*控制子节点的选中状态 */
		.el-tree-node.is-expanded {
			.el-tree-node__children {
				& > .el-tree-node {
					border-radius: 5px;
					margin-bottom: 5px;
				}
				& > .is-checked {
					& > .el-tree-node__content {
						background: #eaf0ff !important;
					}
				}
			}
		}
	}
}
</style>
