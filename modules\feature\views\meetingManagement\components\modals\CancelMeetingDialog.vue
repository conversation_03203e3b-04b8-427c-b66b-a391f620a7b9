<template>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="600px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleClose"
    >
        <el-form
            ref="cancelForm"
            :model="formData"
            :rules="rules"
            label-width="120px"
        >
            <el-form-item label="取消原因" prop="cancelReason">
                <el-input
                    v-model="formData.cancelReason"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入取消原因"
                    show-word-limit
                />
            </el-form-item>
            <el-form-item label="后续计划" prop="nextAction">
                <el-input
                    v-model="formData.nextAction"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入后续计划"
                    show-word-limit
                />
            </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm"> 确 定 </el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'CancelMeetingDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '取消会议'
        },
        meetingId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            formData: {
                cancelReason: '',
                nextAction: ''
            },
            rules: {
                cancelReason: [
                    {
                        required: true,
                        message: '请输入取消原因',
                        trigger: 'blur'
                    }
                ],
                nextAction: [
                    {
                        required: true,
                        message: '请输入后续计划',
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    methods: {
        handleClose() {
            this.resetForm();
            this.$emit('update:visible', false);
        },
        resetForm() {
            this.formData = {
                cancelReason: '',
                nextAction: ''
            };
            if (this.$refs.cancelForm) {
                this.$refs.cancelForm.clearValidate();
            }
        },
        async handleConfirm() {
            try {
                await this.$refs.cancelForm.validate();
                const api = this.$service.feature.meeting.cancelMeeting;
                const res = await api({
                    meetingId: this.meetingId,
                    operateName: '取消会议',
                    cancelReason: this.formData.cancelReason,
                    nextStep: this.formData.nextAction
                });

                if (res.head.code === '000000') {
                    this.$message.closeAll();
                    this.$message.success('会议已取消');
                    this.handleClose();
                    this.$emit('success');
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error('取消会议失败:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
    text-align: right;
}

::v-deep .el-dialog__body {
    padding: 20px;
}

::v-deep .el-form-item__label {
    font-weight: 500;
}

::v-deep .el-textarea__inner {
    resize: none;
}
</style>
