import { Graph } from '@antv/x6';
import { register } from '@antv/x6-vue-shape';
import SceneButtonNode from 'scene/views/scene/components/SceneButtonNode.vue';
import SceneProductNode from 'scene/views/scene/components/SceneProductNode.vue';
import SceneProductToggleNode from 'scene/views/scene/components/SceneProductToggleNode.vue';

// 定义一些常量
export const BTN_W = 80;
export const BTN_H = 24;
export const PRO_W = 70;
export const PRO_H = 70;

/**
 * 注册场景按钮节点
 */
export function registerSceneButtonNode() {
    register({
        shape: 'scene-button-node',
        width: BTN_W,
        height: BTN_H,
        component: SceneButtonNode
    });
}

/**
 * 注册场景产品节点
 */
export function registerSceneProductNode() {
    register({
        shape: 'scene-product-node',
        width: PRO_W,
        height: PRO_H,
        component: SceneProductNode
    });
}

/**
 * 注册数据切换节点
 */
export function registerSceneToggleNode() {
    register({
        shape: 'scene-toggle-node',
        width: 20,
        height: 20,
        component: SceneProductToggleNode
    });
}

/**
 * 注册场景连线
 */
export function registerSceneEdge() {
    const options = {
        zIndex: -1,
        attrs: {
            line: {
                fill: 'none',
                strokeLinejoin: 'round',
                strokeWidth: 1,
                stroke: 'rgba(0, 119, 255, 0.75)',
                sourceMarker: null,
                targetMarker: null
            }
        }
    };
    Graph.registerEdge('scene-edge', options, true);
}

/**
 * 添加场景按钮节点
 * @param {*} graph graph
 * @param {*} options options
 * @returns {*} 场景节点
 */
export function addSceneButtonNode(graph, options) {
    const shape = 'scene-button-node';
    const { x, y, buttonName } = options;
    return graph.addNode({ shape, x, y, data: { buttonName } });
}

/**
 * 添加场景切换节点
 * @param {*} graph graph
 * @param {*} options options
 * @returns {*} 节点
 */
export function addSceneToggleNode(graph, options) {
    const shape = 'scene-toggle-node';
    const { x, y, moreData } = options;
    return graph.addNode({ shape, x, y, data: { moreData } });
}

/**
 * 添加场景产品节点
 * @param {*} graph graph
 * @param {*} options options
 * @returns {*} 场景节点
 */
export function addSceneProductNode(graph, options) {
    const shape = 'scene-product-node';
    const { x, y, name, iconName, level, position, moreData } = options;
    const data = {
        position,
        name,
        moreData,
        iconName,
        hrCost: options.hrCost,
        counts: {
            planProjectNum: options.planProjectNum,
            doingProjectNum: options.doingProjectNum,
            publishProjectNum: options.publishProjectNum,
            strategyNum: options.strategyNum,
            noStrategyNum: options.noStrategyNum
        }
    };
    const node = graph.addNode({ shape, x, y, data });
    node.level = level;
    return node;
}

/**
 * 添加边线
 * @param {*} graph graph
 * @param {*} options options
 * @returns {*} 边线
 */
export function addEdgeLink(graph, options) {
    const { source, target, vertices } = options;
    if (source.childNodes) {
        source.childNodes.push(target);
    } else {
        source.childNodes = [target];
    }
    target.parentNode = source;
    let attrs = {};
    if (options.toggle) {
        attrs = { line: { strokeWidth: 0, stroke: 'rgba(255, 255, 255, 0)' } };
    }
    return graph.addEdge({
        vertices,
        attrs,
        source: { cell: source },
        target: { cell: target },
        shape: 'scene-edge'
    });
}

/**
 * 根据iconName返回图片
 * @param {string} name 名称
 * @returns {string} 图片地址
 */
export function getDeviceImg(name) {
    let imgUrl = '';
    try {
        const { origin, hostname } = window.location;
        let ori = origin;
        if (hostname === '127.0.0.1' || hostname === 'localhost') {
            ori = 'http://dev-pmis.xtjc.net';
        }
        imgUrl = `${ori}/upload/${name}.png`;
    } catch (error) {
        console.warn(`${name}图片缺失`);
    }
    return imgUrl;
}
