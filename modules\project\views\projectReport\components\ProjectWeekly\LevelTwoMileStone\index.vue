<template>
    <div class="progress-situation">
        <div class="title-container">
            <div class="title">二级里程碑控制点进度情况</div>
        </div>
        <div class="button-group">
            <el-radio-group
                v-model="stage"
                @change="getTableData"
                class="stage-selector"
            >
                <el-radio-button
                    v-for="phase in levelTwoMilestonePhase"
                    :key="phase"
                    :label="phase"
                >
                    {{ phase }}
                </el-radio-button>
            </el-radio-group>
            <el-radio-group
                v-if="stage === computedCurrentMilestone"
                v-model="type"
                @change="getTableData"
            >
                <el-radio label="近期里程碑">近期里程碑</el-radio>
                <el-radio label="当前阶段全部里程碑"
                    >当前阶段全部里程碑</el-radio
                >
            </el-radio-group>
        </div>
        <el-table
            :data="milestones"
            class="milestone-table"
            :span-method="objectSpanMethod"
        >
            <el-table-column
                prop="module"
                label="模块"
                align="center"
                width="80"
            >
            </el-table-column>
            <el-table-column
                prop="detailName"
                label="二级控制点"
                min-width="180"
                header-align="center"
                align="left"
            />
            <el-table-column
                prop="responsiblePerson"
                label="责任人"
                width="100"
                align="center"
            />
            <el-table-column
                prop="currentBaseline"
                :label="currentBaselineTitle"
                width="120"
                align="center"
            />
            <el-table-column
                prop="adjustedBaseline"
                label="调整后目标"
                width="110"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="endDate"
                label="实际完成时间"
                width="110"
                align="center"
            />
            <el-table-column
                prop="progress"
                label="进度"
                width="100"
                align="center"
            >
                <template slot-scope="scope">
                    {{ scope.row.progress }}%
                </template>
            </el-table-column>
            <el-table-column
                prop="completionStatus"
                label="完成状态"
                width="100"
                align="center"
            >
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'LevelTwoMileStone',
    components: {},
    props: {
        weeklyId: {
            type: String,
            default: ''
        },
        phases: {
            type: Array,
            default: () => []
        },
        currentMilestone: {
            type: String,
            default: ''
        },
        // 项目周报更新之后的标记
        changeMark: {
            type: Number,
            default: 0
        },
        // 当前项目周报生命周期内的项目名称
        projectName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            stage: '',
            // 没有一级里程碑的二级里程碑列表
            phaseWithoutLevelOne: [],
            milestones: [],
            type: '近期里程碑'
        };
    },
    computed: {
        // 当前基线的标题
        currentBaselineTitle() {
            if (!this.projectName) return '';
            return this.projectName.includes('审批中')
                ? '当前基线(审批中)'
                : '当前基线';
        },
        // 二级里程碑阶段
        levelTwoMilestonePhase() {
            if (this.phases.length > 0) return this.phases;
            return this.phaseWithoutLevelOne;
        },
        // 二级里程碑当前阶段，如果没有一级里程碑,就取二级里程碑的第一个
        computedCurrentMilestone() {
            if (this.phases.length > 0) return this.currentMilestone;
            return this.phaseWithoutLevelOne[0] || '';
        }
    },
    watch: {
        changeMark(newVal) {
            if (newVal) {
                this.stage = this.computedCurrentMilestone || '';
                this.getTableData();
            }
        }
    },
    methods: {
        /**
         * 获取二级里程碑列表
         */
        async getTableData() {
            if (!this.stage) {
                await this.getLevelTwoMileStonePhase();
            }
            const api = this.$service.project.weekly.getLevelTwoMileStone;
            const params = {
                weekReportId: this.weeklyId,
                detailStage: this.stage,
                type:
                    this.stage === this.computedCurrentMilestone
                        ? this.type
                        : '当前阶段全部里程碑'
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.milestones = res.body || [];
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 即使没有一级里程碑，也可能会有二级里程碑，有时候一级里程碑的流还没过来，
         * 就会有这种情况。此时就使用这个接口获取二级里程碑的阶段
         */
        async getLevelTwoMileStonePhase() {
            const api = this.$service.project.weekly.getLevelTwoMileStonePhase;
            const params = {
                weekReportId: this.weeklyId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject();
                }
                if (res.body.length === 0) return Promise.reject();
                this.phaseWithoutLevelOne = res.body;
                this.stage = res.body[0];
                Promise.resolve();
            } catch (error) {
                console.error('Error:', error);
                Promise.reject();
            }
        },
        /**
         * 合并单元格
         * @param {Object} param { row, column, rowIndex, columnIndex }
         * @returns {Object} 单元格合并方式
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只在“模块”这一列进行合并, 其他保持现状
            if (columnIndex !== 0) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }
            // 如果当前行的“模块”与上一行的“模块”相同，则合并
            if (
                rowIndex > 0 &&
                row.module === this.milestones[rowIndex - 1].module
            ) {
                return {
                    // 隐藏当前行的单元格
                    rowspan: 0,
                    colspan: 0
                };
            }
            // 计算当前“项”需要合并的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.milestones.length; i++) {
                if (row.module === this.milestones[i].module) {
                    rowspan += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.title {
    @include section-title;
}
.progress-situation {
    width: 100%;
}
.button-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    border-bottom: 1px solid #8c8c8c;
    height: 28px;
}
</style>
