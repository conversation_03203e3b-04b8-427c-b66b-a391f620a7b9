<template>
    <div class="snbc-header table-header">
        <span class="header__title">{{ headerTitle }}</span>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <template v-if="headerButtons.length > 0">
                    <template v-for="(button, index) in headerButtons">
                        <el-button
                            v-if="
                                button.handleShow ? button.handleShow() : true
                            "
                            :key="index"
                            :type="button.type"
                            :disabled="isDisabledBtn(button)"
                            v-bind="button.elButtonAttrs"
                            @click.native.prevent="button.handleClick"
                            >{{ button.name }}</el-button
                        >
                    </template>
                </template>
                <!--设置之后弹窗不会插入body,注意设置table最小高度,保证弹窗能完整显示 -->
                <el-popover
                    v-model="visible"
                    :placement="placement"
                    class="popover"
                >
                    <div
                        v-for="(column, index) in columns"
                        :key="index"
                        class="item"
                    >
                        <span class="column-label" ref="columnLabel">
                            {{ column.label }}</span
                        >
                        <el-switch
                            v-model="column.show"
                            :disabled="isColSelectDisabled && column.show"
                        />
                    </div>
                    <el-button
                        slot="reference"
                        icon="el-icon-setting"
                        v-bind="defaultElButtonAttrs"
                        class="column-button"
                        >列选择</el-button
                    >
                </el-popover>
            </el-col>
        </el-row>
    </div>
</template>
<script>
export default {
    name: 'SnbcTableHeaderButtons',
    props: {
        config: {
            type: Object,
            default() {
                return {
                    headerButtons: [],
                    headerTitle: '查询列表'
                };
            }
        },
        // 多选选中项
        selections: {
            type: Array,
            default() {
                return [];
            }
        },
        // 列配置
        columns: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            // el-button组件默认属性设置
            defaultElButtonAttrs: {
                size: 'mini'
            },
            // popover显示控制
            visible: false,
            // popover出现位置
            placement: 'bottom-end'
        };
    },
    computed: {
        // 表头标题
        headerTitle() {
            return this.config.headerTitle || '查询列表';
        },
        // 按钮配置，默认按钮属性和自定义属性合并
        headerButtons() {
            return (
                (this.config.headerButtons || [])
                    // 按钮权限过滤
                    .filter((button) => {
                        return (
                            button.permissionCode === undefined ||
                            this.$store.state.permission.btnDatas.includes(
                                button.permissionCode
                            )
                        );
                    })
                    .map((button) => {
                        return {
                            ...button,
                            elButtonAttrs: {
                                ...this.defaultElButtonAttrs,
                                ...(button.elButtonAttrs || {})
                            }
                        };
                    })
            );
        },
        // 按钮可用
        isDisabledBtn() {
            return (button) => {
                return button.needSelections && !this.selections.length;
            };
        },
        // 对于每列的选择是否禁用，当只剩最后一列时禁用
        isColSelectDisabled() {
            return this.columns.filter((item) => item.show).length === 1;
        }
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.mb8 {
    margin: 8px;
}
.snbc-header {
    display: flex;
    margin: 0;
    align-items: center;
}
.header__title {
    display: flex !important;
    align-items: center;
    flex: 1;
    font-weight: bold;
    &::before {
        display: inline-block;
        content: '';
        height: 14px;
        padding-left: 8px;
        border-left: 3px solid #3370ff;
    }
}
.column-button {
    margin-left: 8px;
}
.item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 3px;
}

.column-label {
    flex: 1;
    white-space: nowrap;
    margin-right: 10px;
}

.popover {
    ::v-deep .el-popover {
        max-height: calc(100vh - 320px);
        overflow-y: auto;
    }
}
</style>
