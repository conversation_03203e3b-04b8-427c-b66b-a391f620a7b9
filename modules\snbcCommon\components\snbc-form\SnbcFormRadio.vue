<template>
  <el-form-item v-bind="elFormItemAttrs">
    <el-radio-group v-model="config.modelObj[config.modelKey]" v-bind="elRadioAttrs" @change="handleChange">
      <el-radio
        v-for="(radio, index) in config.elRadios"
        :key="index"
        :label="radio.value"
        :disabled="radio.disabled"
      >
        {{ radio.label }}
      </el-radio>
    </el-radio-group>
  </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormSelect',
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elOptions: [],
                    elFormItemAttrs: {},
                    elRadioAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElRadioAttrs: {
                size: 'small'
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elRadioAttrs() {
            return {
                ...this.defaultElRadioAttrs,
                ...(this.config.elRadioAttrs || {})
            };
        }
    },
    methods: {
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
