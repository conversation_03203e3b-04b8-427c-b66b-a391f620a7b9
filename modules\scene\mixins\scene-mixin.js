import { Graph } from '@antv/x6';
import {
    registerSceneButtonNode,
    registerSceneProductNode,
    registerSceneEdge,
    registerSceneToggleNode,
    addSceneButtonNode,
    addSceneProductNode,
    addEdgeLink,
    addSceneToggleNode
} from 'scene/views/scene/scene-tools.js';

export default {
    data() {
        return {
            // 画布实例
            graph: null,
            // 场景按钮节点集合
            sceneButtonNodes: [],
            // 上一次展示二级的节点
            lastShowNode: null
        };
    },

    mounted() {
        // 初始化画布
        this.initGraph();
        // 节点事件处理
        this.handleNodeEvents();
        // 注册定义的节点
        this.registerNodes();
    },

    methods: {
        // 初始化画布
        initGraph() {
            const container = this.$refs.sceneViewRef;
            this.graph = new Graph({
                grid: { size: 1 },
                container,
                scroller: true,
                connecting: { anchor: 'orth' },
                background: { image: this.sceneBg, size: 'contain' },
                interacting() {
                    return false;
                }
            });
        },
        // 节点事件处理
        handleNodeEvents() {
            this.graph.on('node:click', ({ node }) => {
                // 点击场景按钮，切换对应一级产品的展示和隐藏
                if (node.shape === 'scene-button-node') {
                    this.handleSceneButtonNode(node);
                    this.handleNodeClick && this.handleNodeClick(node);
                }
                // 一级产品节点点击事件
                if (node.shape === 'scene-product-node' && node.level === 1) {
                    this.handleSceneProductNode(node);
                }
                // 切换节点点击事件
                if (node.shape === 'scene-toggle-node') {
                    this.handleSceneToggleNode(node);
                }
            });
        },
        // 注册定义的节点
        registerNodes() {
            registerSceneButtonNode();
            registerSceneProductNode();
            registerSceneToggleNode();
            registerSceneEdge();
        },
        // 初始化场景按钮
        initSceneButton(config) {
            Object.keys(config).forEach((key) => {
                const scene = config[key];
                scene.node = addSceneButtonNode(this.graph, {
                    ...scene.point,
                    buttonName: scene.name
                });
                this.sceneButtonNodes.push(scene.node);
            });
        },
        // 初始化场景数据
        initSceneNodes(source, config, data = [], toggle = false) {
            // 先清除子节点
            const res = this.graph.getSuccessors(source);
            if (res.length > 0) {
                this.graph.removeCells(res);
            }
            const { point, children = [], crossPoints } = config;
            const sortedPoints = children.sort((a, b) => {
                const num1 =
                    Math.pow(point.x - b.x, 2) + Math.pow(point.y - b.y, 2);
                const num2 =
                    Math.pow(point.x - a.x, 2) + Math.pow(point.y - a.y, 2);
                return parseInt(Math.sqrt(num2)) - parseInt(Math.sqrt(num1));
            });
            let startIndex = source.startIndex || 0;
            // 切换数据，计算截取数据范围
            if (toggle === true) {
                const nextStartIndex = startIndex + sortedPoints.length;
                startIndex =
                    nextStartIndex > data.length - 1 ? 0 : nextStartIndex;
            }
            source.startIndex = startIndex;
            // 截取数据，截取多了也可以
            const filterData = data.filter(
                (item, index) => index >= startIndex
            );
            // 显示截取数据
            filterData.forEach((item, index) => {
                if (sortedPoints[index]) {
                    const target = addSceneProductNode(this.graph, {
                        ...item,
                        ...sortedPoints[index],
                        moreData: {
                            parentData: item,
                            parentPoint: sortedPoints[index]
                        }
                    });
                    addEdgeLink(this.graph, {
                        source,
                        target,
                        vertices: crossPoints.concat(
                            sortedPoints[index].crossPoints
                        )
                    });
                }
            });
            // 数据数量大于可分配点位数量，需要切换展示
            if (data.length > sortedPoints.length) {
                const { x, y } = crossPoints[crossPoints.length - 1];
                const target = addSceneToggleNode(this.graph, {
                    x: x - 10,
                    y: y - 10,
                    moreData: {
                        level: 1,
                        params: [source, config, data]
                    }
                });
                addEdgeLink(this.graph, { source, target, toggle: true });
            }
        },
        /**
         * 模块级展示处理
         * @param {*} parentNode 父级节点
         * @param {*} parentData 父级数据
         * @param {*} parentPoint 父级节点配置
         * @param {*} toggle 是否需要切换
         */
        async handleNextLevel(
            parentNode,
            parentData,
            parentPoint,
            toggle = false
        ) {
            // 先清除子节点
            const res = this.graph.getSuccessors(parentNode);
            if (res.length > 0) {
                this.graph.removeCells(res);
            }
            if (parentData.children && parentData.children.length > 0) {
                const sortedPoints = parentPoint.nextPoints.sort((a, b) => {
                    const num1 =
                        Math.pow(parentPoint.x - b.x, 2) +
                        Math.pow(parentPoint.y - b.y, 2);
                    const num2 =
                        Math.pow(parentPoint.x - a.x, 2) +
                        Math.pow(parentPoint.y - a.y, 2);
                    return (
                        parseInt(Math.sqrt(num2)) - parseInt(Math.sqrt(num1))
                    );
                });
                let startIndex = parentNode.startIndex || 0;
                // 切换数据，计算截取数据范围
                if (toggle === true) {
                    const nextStartIndex = startIndex + sortedPoints.length;
                    startIndex =
                        nextStartIndex > parentData.children.length - 1
                            ? 0
                            : nextStartIndex;
                }
                // eslint-disable-next-line require-atomic-updates
                parentNode.startIndex = startIndex;
                // 截取数据，截取多了也可以
                const filterData = parentData.children.filter(
                    (item, index) => index >= startIndex
                );
                // 显示截取数据
                filterData.forEach((item, index) => {
                    if (sortedPoints[index]) {
                        const target = addSceneProductNode(this.graph, {
                            ...sortedPoints[index],
                            ...item
                        });
                        addEdgeLink(this.graph, {
                            source: parentNode,
                            target,
                            vertices: sortedPoints[index].crossPoints
                        });
                    }
                });
                // 数据数量大于可分配点位数量，需要切换展示
                if (parentData.children.length > sortedPoints.length) {
                    const { x, y } = parentPoint.nextPoints[0].crossPoints[0];
                    const target = addSceneToggleNode(this.graph, {
                        x: x - 10,
                        y: y - 10,
                        moreData: {
                            level: 2,
                            params: [parentNode, parentData, parentPoint]
                        }
                    });
                    addEdgeLink(this.graph, {
                        source: parentNode,
                        target,
                        toggle: true
                    });
                }
            }
            this.handleNodeClick && this.handleNodeClick(parentNode);
        },
        // 点击场景产品节点
        handleSceneProductNode(node) {
            const res = this.graph.getSuccessors(node);
            if (res.length > 0) {
                this.graph.removeCells(res);
                this.lastShowNode = null;
                this.handleNodeClick && this.handleNodeClick(node);
                return;
            }
            if (this.lastShowNode) {
                const res1 = this.graph.getSuccessors(this.lastShowNode);
                if (res1.length > 0) {
                    this.graph.removeCells(res1);
                }
            }
            const { parentData, parentPoint } = node.data.moreData;
            this.handleNextLevel(node, parentData, parentPoint);
            this.lastShowNode = node;
        },
        // 场景切换节点点击
        handleSceneToggleNode(node) {
            const { moreData } = node.data;
            const { level, params } = moreData;
            if (level === 1) {
                this.initSceneNodes(...params, true);
            } else {
                this.handleNextLevel(...params, true);
            }
        },
        // 重置所有场景节点的状态为收起
        resetAllNodes() {
            const nodes = this.graph.getNodes();
            nodes.forEach((node) => {
                if (node.shape === 'scene-button-node') {
                    const res = this.graph.getSuccessors(node);
                    // 如果当前为展开状态，需要重置
                    if (res.length > 0) {
                        // 已经展开的节点需要移除
                        this.graph.removeCells(res);
                        setTimeout(() => {
                            // 模拟节点点击，这里setTimeout的原因，猜测移除节点为耗时任务
                            this.handleSceneButtonNode(node);
                            this.handleNodeClick && this.handleNodeClick(node);
                        }, 100);
                    }
                }
            });
        }
    }
};
