<template>
  <el-dialog :title="title" :visible.sync="isShow">
    <el-form
      ref="dataForm"
      :model="formDataInfo"
      :rules="rules"
      :inline="false"
      label-width="auto"
    >
      <el-form-item
        :label="`${$t(
          'systemManagement.dictionary.columnName.valueParentName'
        )}`"
      >
        <el-select
          v-model="formDataInfo.parentId"
          clearable
          :popper-append-to-body="false"
          filterable
          :placeholder="`${$t('systemManagement.dictionary.placeholder.valueParent')} ${$t('systemManagement.dictionary.columnName.valueParentName')}`"
          @change="selectChanageParent"
        >
          <el-option
            v-for="item in allList"
            :key="item.valueId"
            :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)"
            :value="item.valueId"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="`${$t(
          'systemManagement.dictionary.columnName.valueCode'
        )}`"
        prop="valueName"
      >
        <el-input
          v-model="formDataInfo.valueName"
          type="text"
          :placeholder="
            $t(
              'systemManagement.dictionary.message.placeholderPre'
            ) +' '+
              $t('systemManagement.dictionary.columnName.valueCode')
          "
          maxlength="20"
        />
      </el-form-item>
      <el-form-item
        :label="`${$t(
          'systemManagement.dictionary.columnName.value'
        )}`"
        prop="valueCode"
      >
        <el-input
          v-model="formDataInfo.valueCode"
          :placeholder="
            $t(
              'systemManagement.dictionary.message.placeholderPre'
            )+' ' + $t('systemManagement.dictionary.columnName.value')
          "
          maxlength="50"
        />
      </el-form-item>
      <el-form-item
        :label="`${$t(
          'systemManagement.dictionary.columnName.valueDesc'
        )}`"
      >
        <el-input
          v-model="formDataInfo.valueDesc"
          type="textarea"
          :placeholder="
            $t(
              'systemManagement.dictionary.message.placeholderPre'
            ) +' '+
              $t('systemManagement.dictionary.columnName.valueDesc')
          "
          maxlength="500"
        />
      </el-form-item>
    </el-form>

    <span slot="footer">
      <el-button @click="cancel">{{
        $t("systemManagement.dictionary.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">{{
        $t("systemManagement.dictionary.save")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import userInfo from '../../../mixins/list-edit-info';

export default {
    name: 'DictValueListEditInfo',
    mixins: [userInfo],
    props: {
        // 当前表单数据
        formData: {
            type: Object,
            default() {
                return {
                    valueId: '',
                    typeCode: '',
                    parentId: '',
                    valueName: '',
                    valueCode: '',
                    valueDesc: '',
                    typeName: ''
                };
            }
        }
    },
    data() {
        return {
            // 所有字典值下拉列表数据源
            allList: []
        };
    },
    computed: {
        title() {
            if (this.isEdit) {
                return this.$t('systemManagement.dictionary.editValue');
            } 
                return this.$t('systemManagement.dictionary.addValue');
            
        },
        // 是否编辑模式
        isEdit: {
            get() {
                return this.edit;
            }
        },
        rules() {
            return {
                valueName: [
                    {
                        required: true,
                        message:
                            this.$t(
                                'systemManagement.dictionary.placeholder.msg3'
                            ),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^(?!-)(?!.*?-$)[a-zA-Z0-9_\u4e00-\u9fa5]{1,20}$/,
                        message: this.$t(
                            'systemManagement.dictionary.message.ruleName'
                        ),
                        trigger: ['blur', 'change']
                    }
                ],
                valueCode: [
                    {
                        required: true,
                        message:
                            this.$t(
                                'systemManagement.dictionary.placeholder.msg2'
                            ),
                        trigger: ['blur', 'change']
                    },
                    {
                        pattern: /^[^\s]{1,50}$/,
                        message: this.$t(
                            'systemManagement.dictionary.message.ruleCodeValue'
                        ),
                        trigger: ['blur', 'change']
                    }
                ]
            };
        }
    },
    created() {
        // bugfix:54956,解决第一次加载，出现id后半秒左右显示名称的问题,
        if (this.formDataInfo.parentId) {
            this.allList = [{ valueId: this.formDataInfo.parentId, valueName: ' ' }];
        }
        this.getAllList();
    },
    methods: {
        // 获取所有的字典值,用于下拉框
        getAllList() {
            this.$service.systemManagement
                .getDictionaryValueListAll({
                    typeCode: this.formDataInfo.typeCode
                })
                .then((response) => {
                    if (response.head.code === '000000' && (response.body && response.body.length) > 0) {
                        this.allList = response.body;
                    }
                });
        },
        // 调用接口保存数据
        postSave() {
            this.$service.systemManagement
                .postSaveDictionaryValue({
                    dataInfo: this.formDataInfo,
                    isUpdate: this.isEdit ? 1 : 0
                })
                .then((response) => {
                    this.doResponse(response);
                });
        },
        selectChanageParent() {
            this.$forceUpdate();
        }
    }
};
</script>
