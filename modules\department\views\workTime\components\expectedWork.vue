<template>
    <div class="box-table">
        <el-table
            :data="expectedData"
            border
            style="width: 100%"
            :header-cell-style="{ background: '#3370ff' }"
            class="snbc-table"
        >
            <el-table-column
                prop="proProjectName"
                label="项目"
                min-width="220"
                header-align="center"
                align="left"
            >
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">{{ scope.row.proProjectName }}</div>
                        <div class="ellipsis">
                            {{ scope.row.proProjectName }}
                        </div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                prop="taskName"
                label="任务"
                min-width="270"
                header-align="center"
                align="left"
            >
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">{{ scope.row.taskName }}</div>
                        <div class="ellipsis">{{ scope.row.taskName }}</div>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                prop="person"
                label="责任人"
                align="center"
                min-width="70"
            ></el-table-column>
            <el-table-column
                prop="startDate"
                label="开始时间"
                align="center"
                min-width="90"
            ></el-table-column>
            <el-table-column
                prop="endDate"
                label="完成时间"
                align="center"
                min-width="90"
            ></el-table-column>
            <el-table-column
                prop="workDays"
                label="工期"
                align="center"
                min-width="50"
            ></el-table-column>
            <el-table-column
                prop="hours"
                label="工时"
                align="center"
                min-width="60"
            ></el-table-column>
            <el-table-column
                prop="planHours"
                label="预计工时"
                align="center"
                min-width="70"
            >
                <template slot-scope="scope">
                    <el-input
                        style="width: 98%; padding: 2px"
                        type="number"
                        v-model="scope.row.planHours"
                        placeholder="选输入预计工时"
                        :clearable="false"
                        @input="handleInput(scope.row)"
                        @change="handleTimeBular(scope.row)"
                    ></el-input>
                </template>
            </el-table-column>
            <el-table-column
                prop="rate"
                label="比率"
                align="center"
                min-width="50"
            ></el-table-column>
            <el-table-column
                prop="planHours"
                label="指派"
                align="center"
                width="80"
            >
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        @click="handleTaskAssign(scope.row)"
                        >指派</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-dialog
            title="任务指派"
            :visible.sync="taskAssignDialogVisible"
            :before-close="beforeTaskAssignDialogClose"
            :destroy-on-close="true"
            width="500px"
        >
            <GroupSelector @input="handleGroupChange" />
            <div slot="footer" class="flex justify-center">
                <el-button @click="beforeTaskAssignDialogClose"
                    >取 消</el-button
                >
                <el-button type="primary" @click="handleTaskAssignConfirm"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import GroupSelector from './GroupSelector.vue';

export default {
    components: {
        GroupSelector
    },
    props: {
        expectedData: {
            type: Array,
            required: true
        },
        taskType: {
            type: String,
            default: 'expectedWorkHour'
        }
    },
    data() {
        return {
            // 控制指派任务的弹窗是否可见
            taskAssignDialogVisible: false,
            // 当前选择的小组信息（包括小组所在的部门）
            selectedGroupArray: [],
            // 当前行的任务id
            taskId: ''
        };
    },
    watch: {
        expectedData: {
            deep: true,
            handler(newVal) {
                newVal.forEach((row) => {
                    const { planHours } = row;
                    const { hours } = row;
                    let rate = parseFloat((planHours / hours).toFixed(1));
                    // 避免出现0/0的情况
                    if (Number.isNaN(rate)) {
                        rate = 0;
                    }
                    row.rate = rate;
                });
            }
        }
    },

    methods: {
        handleInput(row) {
            row.planHours = Math.floor(row.planHours);
            // 检查输入的值是否大于3000
            if (row.planHours > 3000) {
                // 如果大于3000，将其截断为3000
                row.planHours = 3000;
            }
        },
        // 输入预计工时
        handleTimeBular(value) {
            this.$emit('handle-time-bular', value);
        },
        /**
         * 点击指派按钮之后的处理
         * @param {Object} row 当前任务数据
         */
        handleTaskAssign(row) {
            this.taskAssignDialogVisible = true;
            this.taskId = row.taskId;
        },
        /**
         * 小组变更
         * @param  {Array} value 含有小组信息的数组
         */
        handleGroupChange(value) {
            this.selectedGroupArray = value;
        },
        /**
         * 弹窗关闭前的处理
         */
        beforeTaskAssignDialogClose() {
            this.taskAssignDialogVisible = false;
            this.selectedGroupArray = [];
        },
        /**
         * 确认指派
         */
        async handleTaskAssignConfirm() {
            if (this.selectedGroupArray.length === 0) {
                this.$message.warning('请选择小组');
                return;
            }
            const params = {
                firstDeptCode: this.selectedGroupArray[0],
                secondDeptCode: this.selectedGroupArray[1],
                teamId: this.selectedGroupArray[2],
                taskId: this.taskId,
                taskType: this.taskType,
                hourProcessFlag: '否'
            };
            const api = this.$service.department.workTime.assignTask;
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.$message.success('指派成功');
                    // 通知父组件刷新页面
                    this.$emit('task-assign-success');
                    this.selectedGroupArray = [];
                    this.taskAssignDialogVisible = false;
                    return;
                }
                this.$message.error(res.head.message);
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.box-table {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}
.paging {
    padding-top: 10px;
}
.flex {
    display: flex;
}
.justify-center {
    justify-content: center;
}
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type='number'] {
    -moz-appearance: textfield !important;
}
.ellipsis {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #3370ff;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
/**改变边框颜色*/
::v-deep.el-table th {
    height: 30px !important;
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}
.snbc-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
}
</style>
