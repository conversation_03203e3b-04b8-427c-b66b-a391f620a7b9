<template>
    <el-dialog :title="$t('systemManagement.organization.dialog.assignRoles')" :visible.sync="isShow" custom-class="transfer-dialog role-dialog">
        <el-card>
            <div slot="header">{{ $t('systemManagement.organization.dialog.roleList') }}</div>
            <el-input v-model="filterText" :placeholder="$t('systemManagement.organization.dialog.placeholder.msg8')" for="msg8">
                <i slot="suffix" class="el-input__icon el-icon-search" />
            </el-input>
            <div class="content list">
                <el-checkbox-group v-model="checkList" for="filterRoleList">
                    <el-checkbox v-for="item in filterRoleList" v-show="!item.isShow" :key="item.roleId" :label="item">
                        {{ item.roleName }}
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </el-card>
        <el-card>
            <div class="header">
                <span class="title">{{
          $t("systemManagement.role.message.currentSelectRoleCount", [
            checkList.length
          ])
        }}</span>
                <i v-show="hasSelected" class="el-icon-error text-close-all" v-if="canRemoveList.length > 0" @click.stop="removeAll()" />
            </div>
            <div class="list">
                <div v-for="item in checkListFinal" :key="item.roleId" class="block" for="checkList">
                    <span class="block__text">{{ item.roleName }}</span>
                    <span class="block__btns">
                        <i class="el-icon-error" v-if=" !item.canRemove" @click.stop="removeTargetRole(item)" />
                    </span>
                </div>
            </div>
        </el-card>

        <span slot="footer">
            <el-button @click="isShow = false">{{
        $t("table.cancel")
      }}</el-button>
            <el-button type="primary" @click="confirm">{{
        $t("table.confirm")
      }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        },
        // 当前表单数据
        selection: {
            type: Array,
            default() {
                return [];
            }
        },
        // 不能移除的角色
        reserveRoles:{
            type:Array,
            default(){
                return [];
            }
        }
    },
    data() {
        return {
            roleListData: [],
            filterText: '',
            // 当前已选的数据列表
            checkList: [],
            // 是否有选中的
            hasSelected: false,
            // 可移除的数据列表
            canRemoveList:[]
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        // 当前选中的列表数据，用于管理prop输入属性
        selectionData: {
            get() {
                return this.selection;
            },
            set(val) {
                this.$emit('update:selection', val);
            }
        },
        // 组织机构树默认选中项
        treeDefaultCheckedKeys() {
            return this.selectionData.map((item) => item.orgId);
        },
        filterRoleList() {
            const text = (this.filterText || '').trim();
            return this.roleListData.filter((item) => {
                return item.roleName.includes(text);
            });
        },
        // 展示在右侧的已选角色
        checkListFinal(){
            const arrObj = this.checkList.concat(this.reserveRoles)
            // 数组对象去重
            const map = new Map();
            for (const item of arrObj) {
                if (!map.has(item.roleId)) {
                    map.set(item.roleId, item);
                }
            }
            const filterArr = [...map.values()];
            return filterArr;
        }
    },
    watch: {
        isShow(val) {
            if (val) {
                this.doDefaultCheckList();
            }
        },
        checkList: {
            handler(newVal, oldVal) {
                if (newVal.length > 0) {
                    this.hasSelected = true;
                } else {
                    this.hasSelected = false;
                }
            }
        }
    },
    created() {
        this.getRoleList();
    },
    methods: {
        // 获取机构树
        getRoleList() {
            // 获取角色列表
            this.$service.systemManagement.getRoleListOrg().then((res) => {
                if (res.head.code === '000000') {
                    this.roleListData = res.body;

                    this.doDefaultCheckList();
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        // 处理默认选中的角色列表
        doDefaultCheckList() {
            // 选中值
            this.checkList = this.roleListData.filter((item) => {
                return this.selectionData.some(
                    (role) => role.roleId === item.roleId
                );
            });
        },
        // 多用户 - 调整部门 - 确认
        confirm() {
            this.selectionData = this.checkList;
            this.$emit('confirm-selection', this.checkList);
            this.isShow = false;
        },
        // 移除已选中角色
        removeTargetRole(data) {
            this.checkList = this.checkList.filter((item) => {
                return item.roleId !== data.roleId;
            });
        },
        /**
         * 移除全部已选择用户
         */
        removeAll() {
            this.canRemoveList = this.checkList.filter((item) => {
                return item.canRemove !== 'false';
            });
            if(this.canRemoveList.length > 0 ){
                this.checkList = [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.role-dialog {
    .header {
        .text-close-all {
            font-size: 15px;
            color: #8996a8;
            cursor: pointer;
        }
    }
    .list {
        .block {
            background: #eaf0ff;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 14px;
            .block__text {
                margin-left: 11px;
            }
            .block__btns {
                .el-icon-error {
                    color: #c7c7c7;
                }
            }
        }
    }
}
</style>
