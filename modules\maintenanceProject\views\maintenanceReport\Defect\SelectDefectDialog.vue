<template>
    <el-dialog
        title="选择缺陷"
        :visible.sync="dialogVisible"
        :before-close="reset"
        width="1000px"
        append-to-body
    >
        <el-table
            ref="defectTable"
            :data="tableData"
            @selection-change="handleSelectionChange"
            style="width: 100%; margin-bottom: 20px"
            max-height="400px"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="faultTitle"
                label="缺陷名称"
                header-align="center"
                align="left"
            >
                <template slot-scope="scope">
                    <el-link
                        type="primary"
                        @click="handleTitleClick(scope.row)"
                        >{{ scope.row.faultTitle }}</el-link
                    >
                </template>
            </el-table-column>
            <el-table-column
                prop="faultSource"
                label="缺陷来源"
                width="170"
                header-align="center"
                align="left"
            >
            </el-table-column>
            <el-table-column
                prop="faultFindDate"
                label="发现时间"
                width="100"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="faultLevel"
                label="缺陷等级"
                width="100"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="faultStatus"
                label="缺陷状态"
                width="80"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="whetherHasRisk"
                label="风险"
                width="60"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="hasActiveSupportItems"
                label="需支持事项"
                width="90"
                align="center"
            ></el-table-column>
        </el-table>
        <div class="footer" slot="footer">
            <el-button @click="closeDialog">取 消</el-button>
            <el-button type="primary" @click="save">保 存</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'SelectDefectDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            tableData: [],
            selectedRows: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 维护项目信息：产品线/细分产品线/项目经理
        projectStore() {
            return (
                this.$store.state.maintenanceProject.maintenanceProjectStore ||
                []
            );
        },
        // 周报ID
        weeklyId() {
            return this.$store.state.maintenanceProject.maintenanceReportHeader
                .weeklyId;
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.fetchData();
            }
        }
    },
    methods: {
        /**
         * 获取表格数据
         */
        async fetchData() {
            const api = this.$service.maintenanceProject.weekly.getDefectList;
            const params = {
                productLine: this.projectStore[0],
                subProductLine: this.projectStore[1],
                projectManager: this.projectStore[2],
                weekReportId: this.weeklyId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.tableData = res.body;

                // 根据whetherChecked字段设置行的选中状态
                this.$nextTick(() => {
                    if (this.$refs.defectTable && this.tableData.length > 0) {
                        this.tableData.forEach((row) => {
                            if (row.whetherChecked === true) {
                                this.$refs.defectTable.toggleRowSelection(
                                    row,
                                    true
                                );
                            }
                        });
                    }
                });
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 处理表格选择变化
         * @param {Array} selection - 选中的行数据
         */
        handleSelectionChange(selection) {
            this.selectedRows = selection;
        },
        /**
         * 处理缺陷标题点击事件
         * 跳转到缺陷详情页面
         * @param {Object} row - 行数据
         */
        handleTitleClick(row) {
            this.dialogVisible = false;

            this.$router.push({
                path: 'maintenanceDefectDetail',
                query: {
                    defect_id: row.faultId
                }
            });
        },
        /**
         * 关闭对话框
         */
        closeDialog() {
            this.reset();
        },
        /**
         * 保存选中的缺陷
         */
        async save() {
            const api = this.$service.maintenanceProject.weekly.addDefect;
            const params = {
                maintainWeekReportId: this.weeklyId,
                managerFaultList: this.selectedRows.map((i) => ({
                    ...i,
                    whetherChecked: true
                }))
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.$emit('success');
                this.closeDialog();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 重置组件状态
         */
        reset() {
            this.form = {
                supplement: ''
            };
            this.tableData = [];
            this.selectedRows = [];
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.w-50 {
    width: 50%;
}
.w-100 {
    width: 100%;
}
.space-between {
    justify-content: space-between;
}
.ml-10 {
    margin-left: 10px;
}
.pl-20 {
    padding-left: 20px;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}

::v-deep.form .el-form-item__label {
    font-weight: bold;
}
</style>
