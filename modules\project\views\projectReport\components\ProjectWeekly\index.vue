<template>
    <div>
        <div class="weekly-header" v-if="!outsideWeeklyId">
            <el-select
                v-if="options.length > 0"
                v-model="weeklyId"
                size="medium"
                placeholder="请选择周报"
                class="select-week"
                @change="handleSelectChange"
            >
                <el-option
                    v-for="item in options"
                    :key="item.weekReportId"
                    :label="`${item.yearVal}年第${item.week}周(${item.startDate}-${item.endDate})`"
                    :value="item.weekReportId"
                >
                </el-option>
            </el-select>
            <div style="margin-left: auto">
                <el-button
                    v-if="weeklyId && shouldUpdate"
                    :disabled="!isProjectManagerOrPQA"
                    :type="projectStatus === '已更新' ? 'success' : 'primary'"
                    style="height: 30px"
                    @click="handleRadioChange"
                    >{{ projectStatus }}</el-button
                >
                <el-button
                    v-if="
                        showAddButton && isProjectManagerOrPQA && !isPureProject
                    "
                    @click="handleAddNewWeekly"
                    type="primary"
                    style="height: 30px"
                    >新增周报</el-button
                >
            </div>
        </div>
        <el-empty
            description="暂无数据"
            :image-size="60"
            v-if="!weeklyId"
        ></el-empty>
        <template v-if="weeklyId">
            <BaseInfo :projectInfo="projectInfo" />
            <MarketTarget
                class="market-target"
                :marketTargetList="marketTargetList"
            />
            <Risk
                class="risk-container"
                :weeklyId="weeklyId"
                :riskList="riskList"
                :editable="
                    isToBeUpdated && isProjectManagerOrPQA && !outsideWeeklyId
                "
                :projectId="projectId"
                @refresh="handleRiskChange"
                @selectSuccess="getRiskList"
            />
            <LevelOneMileStone
                class="progress-situation"
                :editable="
                    isToBeUpdated && isProjectManagerOrPQA && !outsideWeeklyId
                "
                :milestones="levelOneMileStoneList"
                :projectName="projectName"
                @refresh="getLevelOneMileStone"
            />
            <LevelTwoMileStone
                class="progress-situation"
                :phases="phases"
                :weeklyId="weeklyId"
                :changeMark="changeMark"
                :currentMilestone="currentMilestone"
                :projectName="projectName"
            />
            <LastWeekProgressAndPlan
                class="last-week-progress-and-plan"
                :isProjectManagerOrPQA="
                    isToBeUpdated && isProjectManagerOrPQA && !outsideWeeklyId
                "
                :isProjectMember="
                    isToBeUpdated && isProjectMember && !outsideWeeklyId
                "
                :projectMembers="projectMembers"
                :list="lastWeekProgressAndPlanList"
                :weeklyId="weeklyId"
                @success="getLastWeekProgressAndPlanList"
            />
            <BudgetImplementation
                class="budget-implementation"
                :overallBudget="projectInfo.budgetExecutionRatio"
                :projectBudget="projectBudget"
            />
        </template>
    </div>
</template>

<script>
import BaseInfo from './BaseInfo';
import MarketTarget from './MarketTarget';
import LevelOneMileStone from './LevelOneMileStone';
import LevelTwoMileStone from './LevelTwoMileStone';
import Risk from './Risk';
import BudgetImplementation from './BudgetImplementation';
import LastWeekProgressAndPlan from './LastWeekProgressAndPlan';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';
import { Loading } from 'element-ui';

/**
 * 项目周报的查询分为按顺序的四个步骤：
 * 1.根据顶部级联菜单查询该项目下的项目周报
 * 2.根据该项目周报的信息判断是否需要更新项目周报
 * 3.根据项目周报的id查询项目信息等等，同时根据获得的里程碑信息整理出阶段
 * 4.根据当前阶段查询二级里程碑
 */
export default {
    name: 'ProjectWeekly',
    components: {
        BaseInfo,
        MarketTarget,
        LevelOneMileStone,
        LevelTwoMileStone,
        Risk,
        BudgetImplementation,
        LastWeekProgressAndPlan
    },
    data() {
        return {
            // 周报id
            weeklyId: '',
            // 项目状态
            projectStatus: '待更新',
            // 项目经理
            projectManager: '',
            // PQA
            pqa: '',
            // 项目成员
            projectMembers: [],
            // 周报列表
            options: [],
            // 项目基本信息
            projectInfo: {},
            // 项目预算
            projectBudget: [],
            // 市场目标表格数据
            marketTargetList: [],
            // 一级里程碑列表
            levelOneMileStoneList: [],
            // 是否需要更新二级里程碑的标志
            changeMark: 0,
            // 风险列表
            riskList: [],
            // 上周进度和计划列表
            lastWeekProgressAndPlanList: [],
            // 当前选择的周报信息
            currentOption: {},
            // 是否展示新增按钮，如果不是最新周报就展示新增按钮
            showAddButton: false
        };
    },
    computed: {
        // 项目周报是否需要更新（是否在窗口期）
        // 不在窗口期代表也没有编辑权限
        // 如果项目周报在窗口期（一般为上周五、本周一和本周二），则需要调用更新接口
        // 如果项目周报在窗口期之外（包括过去的项目周报），则不需要调用更新接口
        shouldUpdate() {
            return (
                this.options.find((i) => i.weekReportId === this.weeklyId)
                    ?.whetherUpdate || false
            );
        },
        // 是否是项目经理或PQA
        isProjectManagerOrPQA() {
            return (
                getUserAccount(this) === this.projectManager ||
                this.pqa.includes(getUserAccount(this))
            );
        },
        // 是否是项目成员
        isProjectMember() {
            return this.projectMembers.includes(getUserAccount(this));
        },
        // 里程碑阶段(一级里程碑与二级里程碑的阶段一样)
        phases() {
            // 获取所有阶段
            const phases = this.levelOneMileStoneList.map((milestone) => {
                return milestone.detailStage;
            });
            // 去重
            return [...new Set(phases)];
        },
        // 当前里程碑
        currentMilestone() {
            return this.levelOneMileStoneList.find(
                (milestone) => milestone.detailProgressStatus === '进行中'
            )?.detailStage;
        },
        // 当前项目周报生命周期内的项目名称
        projectName() {
            return this.projectInfo.projectName;
        },
        // 周报状态是否为待更新，待更新才能编辑（注意：在待更新状态，就一定是窗口期内部）
        isToBeUpdated() {
            return this.projectStatus === '待更新';
        },
        // 项目ID，注意只能从store里面取
        projectId() {
            const len = this.$store.state.project.projectStore.length;
            return this.$store.state.project.projectStore[len - 1];
        },
        // 如果外部传入周报ID，周报就变为仅查看，并且不展示任何按钮
        // 外部传入的周报ID（报表页面）
        outsideWeeklyId() {
            if (this.$store.state.reportForm.weeklyValue.length !== 3) {
                return '';
            }
            return this.$store.state.reportForm.weeklyOption[2].devReportId;
        },
        // 是否是纯软件项目，是的话不展示新增项目按钮
        isPureProject() {
            if (!this.$store.state.project?.projectInfo) return true;
            return (
                this.$store.state.project?.projectInfo.projectSource ===
                'PLOUTO'
            );
        }
    },
    watch: {
        projectId(newVal) {
            if (this.outsideWeeklyId !== '') return;
            newVal && this.handleProjectChange();
        },
        // 外部传入的周报ID（报表页面）变更，也进行查询
        // 注意查询的时候不会进行更新
        outsideWeeklyId(newVal) {
            if (newVal) {
                this.getDataWithoutRefresh();
            } else {
                // 当外部周报ID被清除时，同时清除内部的weeklyId
                this.weeklyId = '';
            }
        }
    },
    activated() {
        if (this.outsideWeeklyId !== '') return;
        this.handleProjectChange();
    },
    methods: {
        /**
         * 项目变更的处理
         */
        async handleProjectChange() {
            this.showAddButton = false;
            let loadingInstance;
            try {
                // 选择项目之后，获取项目团队配置信息和项目周报列表
                await Promise.all([
                    this.getProjectTeamConfig(),
                    this.getProjectWeeklyList()
                ]);
                await this.getWindowPeriod();
                if (!this.weeklyId) return;
                loadingInstance = Loading.service({
                    text: '项目周报更新中...',
                    background: 'rgba(0, 0, 0, 0.1)'
                });
                // 更新当前option
                this.currentOption = this.options.find(
                    (i) => i.weekReportId === this.weeklyId
                );
                // 更新项目周报状态
                this.projectStatus = this.currentOption?.weekReportStatus;

                // 如果项目周报在窗口期（一般为上周五、本周一和本周二），则需要调用更新接口
                if (this.shouldUpdate) {
                    await this.updateWeekly();
                }

                // 这里防止因为总体预算与表格中的预算接口返回时间不一致导致数据没有更新
                this.projectBudget = [];
                await Promise.all([
                    this.getProjectBaseInfo(),
                    this.getMarketTargetList(),
                    this.getLevelOneMileStone(),
                    this.getRiskList(),
                    this.getBudgetInfo(),
                    this.getLastWeekProgressAndPlanList()
                ]);
                this.$nextTick(() => {
                    this.changeMark += 1;
                });
            } catch (error) {
                console.error('Error in handleProjectChange:', error);
            } finally {
                loadingInstance && loadingInstance.close();
            }
        },
        /**
         * 用户手动更新周报状态
         * @param {String} value 周报状态
         */
        async handleRadioChange() {
            // 点击按钮切换更新状态
            this.projectStatus =
                this.projectStatus === '待更新' ? '已更新' : '待更新';
            const api = this.$service.project.weekly.editWeeklyStatus;
            const params = {
                weekReportId: this.weeklyId,
                weekReportStatus: this.projectStatus
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 重新获取项目周报下拉列表
                this.getProjectWeeklyList(this.weeklyId);
                this.$message.success('更新项目周报状态成功');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 周报变更的处理
         */
        async handleSelectChange() {
            this.currentOption = this.options.find(
                (i) => i.weekReportId === this.weeklyId
            );
            // 更新项目周报状态
            this.projectStatus = this.currentOption?.weekReportStatus;

            let loadingInstance;
            try {
                loadingInstance = Loading.service({
                    text: '项目周报更新中...',
                    background: 'rgba(0, 0, 0, 0.1)'
                });
                if (!this.weeklyId) return;
                // 如果项目周报在窗口期（一般为上周五、本周一和本周二），则需要调用更新接口
                if (this.shouldUpdate) {
                    await this.updateWeekly();
                }
                this.getWindowPeriod();
                this.projectBudget = [];
                await Promise.all([
                    this.getProjectBaseInfo(),
                    this.getMarketTargetList(),
                    this.getLevelOneMileStone(),
                    this.getRiskList(),
                    this.getBudgetInfo(),
                    this.getLastWeekProgressAndPlanList()
                ]);
                this.changeMark += 1;
            } catch (error) {
                console.error('Error in handleSelectChange:', error);
            } finally {
                loadingInstance && loadingInstance.close();
            }
        },
        /**
         * 获取项目团队配置信息，用于控制按钮权限
         */
        async getProjectTeamConfig() {
            if (!this.projectId) return;
            const params = {
                projectId: this.projectId
            };

            try {
                const res = await this.$service.project.schedule.getProjectTeam(
                    params
                );
                if (res.head.code === '000000') {
                    this.handleTeamGroupData(res.body);
                    return Promise.resolve(res.body);
                }
                this.$message.error(res.head.message);
                return Promise.reject(new Error(res.head.message));
            } catch (err) {
                console.error('Error in getProjectTeamConfig:', err);
                return Promise.reject(err);
            }
        },
        /**
         * 获取项目团队成员、项目经理、PQA
         * @param {Obejct} data 原始数据
         */
        handleTeamGroupData(data) {
            this.projectManager =
                data.projectTeams.find((i) => i.position === '项目经理')
                    .leader[0] || '';
            this.pqa =
                data.projectTeams.find((i) => i.position === 'PQA').leader ||
                [];
            this.projectMembers = [
                ...data.projectTeams
                    .map((i) => [...i.leader, ...this.pqa, ...i.personnel])
                    .flat(),
                ...data.projectTeamOtherData
            ];
        },
        /**
         * 获取项目周报列表
         * @param {Boolean} oriWeeklyId 是否保持原来的周报选择，不默认选择第一个
         */
        async getProjectWeeklyList(oriWeeklyId = '') {
            if (!this.projectId) return;
            this.weeklyId = '';
            this.whetherUpdate = false;
            const params = {
                projectId: this.projectId
            };
            const api = this.$service.project.weekly.getProjectWeekReportList;

            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject(new Error(res.head.message));
                }

                this.options = res.body;
                if (this.options.length > 0) {
                    if (!oriWeeklyId) {
                        this.weeklyId = this.options[0].weekReportId;
                        this.whetherUpdate = this.options[0].whetherUpdate;
                    } else {
                        this.weeklyId = oriWeeklyId;
                        this.whetherUpdate = this.options.find(
                            (i) => i.weekReportId === oriWeeklyId
                        ).whetherUpdate;
                    }
                }

                return Promise.resolve(res.body);
            } catch (error) {
                console.error('Error in getProjectWeeklyList:', error);
                return Promise.reject(error);
            }
        },
        /**
         * 更新项目周报
         */
        async updateWeekly() {
            const { startDate, endDate, week, ...rest } = this.currentOption;

            const formattedStartDate = startDate
                ? `20${startDate.replace(/\//g, '-')}`
                : '';
            const formattedEndDate = endDate
                ? `20${endDate.replace(/\//g, '-')}`
                : '';

            const api = this.$service.project.weekly.updateWeekReportInfo;
            const params = {
                ...rest,
                projectId: this.projectId,
                weekReportId: this.weeklyId,
                startDate: formattedStartDate,
                endDate: formattedEndDate,
                weekNumber: Number(week)
            };

            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject(new Error(res.head.message));
                }

                return Promise.resolve(res.body);
            } catch (error) {
                console.error('Error in updateWeekly:', error);
                return Promise.reject(error);
            }
        },
        /**
         * 获取周报基本信息
         */
        async getProjectBaseInfo() {
            try {
                const params = {
                    weekReportId: this.weeklyId
                };
                const api = this.$service.project.weekly.getProjectBaseInfo;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject(new Error(res.head.message));
                }
                this.$nextTick(() => {
                    this.projectInfo = res.body;
                });
                return Promise.resolve(res.body);
            } catch (error) {
                console.error('Error:', error);
                return Promise.reject(error);
            }
        },
        /**
         * 获取市场目标表格数据
         */
        async getMarketTargetList() {
            try {
                const params = {
                    weekReportId: this.weeklyId
                };
                const api = this.$service.project.weekly.getMarketTargetList;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject(new Error(res.head.message));
                }
                this.marketTargetList = res.body.map((i, index) => ({
                    ...i,
                    index: index + 1
                }));
                return Promise.resolve(res.body);
            } catch (error) {
                console.error('Error:', error);
                return Promise.reject(error);
            }
        },
        /**
         * 获取一级里程碑信息
         */
        async getLevelOneMileStone() {
            try {
                const params = {
                    weekReportId: this.weeklyId
                };
                const api = this.$service.project.weekly.getLevelOneMileStone;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return Promise.reject(new Error(res.head.message));
                }
                this.levelOneMileStoneList = res.body;
                return Promise.resolve(res.body);
            } catch (error) {
                console.error('Error:', error);
                return Promise.reject(error);
            }
        },
        /**
         * 获取风险列表
         */
        async getRiskList() {
            const api = this.$service.project.weekly.getRiskList;
            const params = {
                weekReportId: this.weeklyId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                const riskList = res.body.map((i, index) => ({
                    index: index + 1,
                    ...i
                }));
                const riskArray = [];
                for (let i = 0; i < riskList.length; i++) {
                    const { riskSupportList, ...rest } = riskList[i];
                    if (riskList[i].riskSupportList.length === 0) {
                        riskArray.push(riskList[i]);
                        continue;
                    }
                    for (
                        let j = 0;
                        j < riskList[i].riskSupportList.length;
                        j++
                    ) {
                        riskArray.push({
                            ...riskList[i].riskSupportList[j],
                            ...rest
                        });
                    }
                }
                this.riskList = riskArray;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取预算执行情况表格
         */
        async getBudgetInfo() {
            const api = this.$service.project.weekly.getBudgetInfo;
            const params = { weekReportId: this.weeklyId };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$nextTick(() => {
                    this.projectBudget = res.body;
                });
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 上周进度和本周计划列表（已勾选）
         */
        async getLastWeekProgressAndPlanList() {
            const api =
                this.$service.project.weekly.getLastWeekProgressAndPlanList;
            const params = { weekReportId: this.weeklyId, checkedFlag: 'true' };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.lastWeekProgressAndPlanList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 风险编辑结束的处理
         */
        handleRiskChange() {
            this.getProjectBaseInfo();
            this.getRiskList();
        },
        /**
         * 不判断项目是否更新，直接进行查询列表的操作
         */
        async getDataWithoutRefresh() {
            // 如果报表页面使用了这个组件，保证不会进行默认赋值
            // 可以为null，与默认值区分开即可，这样就展示暂无数据
            if (this.outsideWeeklyId !== '') {
                this.weeklyId = this.outsideWeeklyId;
                if (this.outsideWeeklyId) {
                    this.whetherUpdate = this.options.find(
                        (i) => i.weekReportId === this.weeklyId
                    )?.whetherUpdate;
                }
            }
            try {
                await Promise.all([
                    this.getProjectBaseInfo(),
                    this.getMarketTargetList(),
                    this.getLevelOneMileStone(),
                    this.getRiskList(),
                    this.getBudgetInfo(),
                    this.getLastWeekProgressAndPlanList()
                ]);
                this.changeMark += 1;
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 如果项目经理或者PQA认为缺少周报，可以进行手动新增
         */
        async handleAddNewWeekly() {
            const params = {
                projectId: this.projectId
            };

            const api = this.$service.project.weekly.addNewWeekly;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('新增成功');
                this.handleProjectChange();
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 获取窗口期信息，用于处理新建项目的显示和隐藏
         */
        async getWindowPeriod() {
            const { projectStatus = '' } =
                this.$store.state.project.projectInfo;

            if (this.options.length === 0 && projectStatus === '进行中') {
                this.showAddButton = true;
                return;
            }
            if (this.options.length === 0) return;
            const api = this.$service.maintenanceProject.weekly.getWindowPeriod;
            try {
                const res = await api();
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (!res.body) {
                    // 返回null的时候不展示
                    this.showAddButton = false;
                    return;
                }

                // 如果开始日期不等于最新周报的开始日期，就展示新增周报按钮
                this.showAddButton =
                    `20${this.options[0].startDate.split('/').join('-')}` !==
                    res.body?.startDate;
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.weekly-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}
.select-week {
    width: 600px;
    ::v-deep .el-input__inner {
        font-weight: bold;
        border: none;
        padding-left: 0;
    }
}
.market-target {
    margin-top: 30px;
}
.progress-situation {
    margin-top: 30px;
}
.risk-container {
    margin-top: 30px;
}
.budget-implementation {
    margin-top: 30px;
}
.last-week-progress-and-plan {
    margin-top: 30px;
}
</style>
