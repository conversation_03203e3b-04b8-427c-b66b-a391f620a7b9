import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        // 查询部门负责人
        getDirectorpeople(query) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: `/department/getDeptAndSubDeptAccountInfo`,
                method: 'get',
                params: query
            });
        },
        // 修改部门负责人
        editDirectorpeople(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: `/department/updateDepartmentAccountInfo`,
                method: 'post',
                data
            });
        }
    };

    return service;
};
