/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        business_project_finance: {
            // 通过场景名称查询产品分类
            get_product_type_by_scene_name(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project_finance/get_product_type_by_scene_name',
                    method: 'post',
                    data
                });
            },
            // 通过产品分类查询关键模块
            get_key_module_by_product_type(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project_finance/get_key_module_by_product_type',
                    method: 'post',
                    data
                });
            },
            // 产品综述查询(金融机具与物流分拣)
            getOverviewDescription(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project_finance/get_product_overview_desc',
                    method: 'post',
                    data
                });
            },
            // 产品综述查询(其余场景)
            getOverviewOtherDescription(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/business_project/get_product_overview_desc',
                    method: 'post',
                    data
                });
            }
        },
        // 获取对应的项目列表
        getProjectList(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/business_project/get_project_info_list',
                method: 'post',
                data,
                loading: false
            });
        },
        // 查询所有待新增项目名称(过滤了已有的项目)
        getRestProjectList(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/business_project/getAllToBeAddProject',
                method: 'get',
                params: data
            });
        },
        // 在一体两翼中新增项目
        addProject(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/business_project/insertBusinessProject',
                method: 'post',
                data
            });
        },
        // 在一体两翼中删除项目
        deleteProject(query) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: `/business_project/deleteBusinessProject?projectNumber=${query}`,
                method: 'delete'
            });
        },
        // 新增查询产品系列
        getproductLine(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/common/getParamCodeValues',
                method: 'get',
                params: data
            });
        },
        // 查询金融机具顶部软件开发数据
        getTopData(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/business_project_finance/getProjectNumOfProduct',
                method: 'post',
                data
            });
        },
        // 查询维护产品
        getmaintenanceData(data) {
            return http({
                baseDomain: basePath.bossapi.pmService,
                url: '/business_project_finance/addProductType',
                method: 'post',
                data
            });
        }
    };

    return service;
};
