<template>
  <el-dialog :title="title" :visible.sync="isShow">
    <el-form
      ref="dataForm"
      :model="formDataInfo"
      :rules="rules"
      :inline="false"
      size="normal"
    >
{{#each fieldList}}
      <el-form-item
        :label="$t('{{ @root.moduleName }}.{{ @root.viewName }}.columnName.{{this}}')"
        prop="field1"
      >
        <el-input
          v-model="formDataInfo.{{this}}"
          type="text"
          :disabled="isEdit"
          :placeholder="$t('{{ @root.moduleName }}.{{ @root.viewName }}.placeholder.{{this}}')"
          maxlength="20"
        />
      </el-form-item>
{{/each}}
    </el-form>

    <span slot="footer">
      <el-button @click="cancel">\{{
        $t("common.cancel")
      }}</el-button>
      <el-button type="primary" @click="save">\{{
        $t("common.done")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import listEdiftInfoMixin from '../../../mixins/list-edit-info';
export default {
    mixins: [listEdiftInfoMixin],
    props: {
        // 当前表单数据
        formData: {
            type: Object,
            default() {
                return {
{{#each fieldList}}
                    {{this}}: ''
    {{~#unless @last~}}
                        ,
    {{/unless~}}
{{/each}}
                };
            }
        }
    },
    data() {
        return {
            // 表单验证规则
            rules: {
{{#each fieldList}}
                {{this}}: [
                    {
                        required: true,
                        message: this.$t('{{ @root.moduleName }}.{{ @root.viewName }}.message.{{this}}'),
                        trigger: ['blur', 'change']
                    }
                ]
    {{~#unless @last~}}
                    ,
    {{/unless~}}
{{/each}}
            }
        };
    },
    computed: {
        title() {
            // 这里的title是根据编辑、新增的标识进行计算的
            const prefix = this.isEdit
                ? this.$t('common.edit')
                : this.$t('common.add');
            return prefix + this.$t('{{ moduleName }}.{{ viewName }}.title');
        },
        // 是否编辑模式
        isEdit: {
            get() {
                return this.edit;
            }
        }
    },
    methods: {
        // 调用接口保存数据
        postSave() {
            if (this.isEdit) {
                this.$service.{{ moduleName }}
                    .post{{pascalCase viewName}}{{servicePrefix}}Update(this.formDataInfo)
                    .then((response) => {
                        // 调用mixins的方法，进行结果处理
                        this.doResponse(response);
                    });
            } else {
                this.$service.{{ moduleName }}
                    .post{{pascalCase viewName}}{{servicePrefix}}Add(this.formDataInfo)
                    .then((response) => {
                        // 调用mixins的方法，进行结果处理
                        this.doResponse(response);
                    });
            }
        }
    }
};
</script>
