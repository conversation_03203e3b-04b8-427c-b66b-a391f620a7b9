<template>
    <div :style="boxStyle">
        <div :style="outScale">
            <div ref="sceneViewRef" class="scene-view"></div>
        </div>
    </div>
</template>
<script>
import sceneMixin from 'scene/mixins/scene-mixin.js';
import sceneConfig from 'scene/views/scene/config/sort-system-config.js';
import sceneBg from 'scene/assets/sorting-scene-bg.png';

export default {
    name: 'SortingCenterScene',
    mixins: [sceneMixin],
    props: {
        // 父节点宽度
        parentWidth: {
            type: Number,
            default: 0
        },
        // scale
        scale: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            // 背景图
            sceneBg,
            // 场景按钮展开
            expand: false,
            // 场景数据对象
            sceneData: {},
            sceneType: '国内网点'
        };
    },
    computed: {
        outScale() {
            if (!this.expand) {
                return {
                    'height': 'calc(100vh - 45px)',
                    'transform': 'scale(1.2)',
                    'transform-origin': 'center 100px',
                    'overflow': 'hidden'
                };
            }
            return {
                'height': 'calc(100vh - 45px)',
                'overflow-y': 'hidden'
            };
        },
        boxStyle() {
            const { height } = this.outScale;
            return {
                width: '100%',
                height
            };
        },
        // 新增项目时的标志
        count() {
            return this.$store.state.scene.changeProjectCountSuccess;
        }
    },
    watch: {
        count(newVal) {
            if (newVal) {
                this.querySceneProductData().then(this.resetAllNodes());
            }
        }
    },
    mounted() {
        // 初始化场景按钮
        this.initSceneButton(sceneConfig);
        // 获取场景产品数据
        this.querySceneProductData();
    },
    methods: {
        // 获取场景产品数据
        querySceneProductData() {
            return new Promise((resolve, reject) => {
                // 参数设置
                const params = [
                    {
                        businessUnit: '物流分拣',
                        sceneType: '分拣中心',
                        sceneNameList: [
                            '自动分拣',
                            '自动识别',
                            '自动输送',
                            '自动装卸'
                        ]
                    }
                ];
                this.$service.scene.business_project_finance
                    .get_product_type_by_scene_name(params)
                    .then((res) => {
                        if (res.head.code !== '000000') {
                            this.$message.error(res.head.message);
                            reject();
                        } else {
                            const list = res.body || [];
                            list.forEach((item) => {
                                const { sceneName } = item;
                                this.sceneData[sceneName] =
                                    item.productTypeVoList.map((product) => {
                                        return {
                                            ...product.calculateFieldVo,
                                            name: product.productType,
                                            sceneName: item.sceneName,
                                            iconName: product.iconName,
                                            productSet: null,
                                            sceneType: '分拣中心',
                                            businessUnit: '物流分拣'
                                        };
                                    });
                            });
                            resolve();
                        }
                    })
                    .catch((err) => {
                        console.error(err, '无法获取场景数据');
                        reject();
                    });
            });
        },
        // 点击场景按钮事件
        handleSceneButtonNode(node) {
            const res = this.graph.getSuccessors(node);
            if (res.length > 0) {
                this.graph.removeCells(res);
            } else {
                const { buttonName } = node.data;
                const { [buttonName]: config } = sceneConfig;
                const { [buttonName]: data } = this.sceneData;
                this.initSceneNodes(sceneConfig[buttonName].node, config, data);
            }
        },
        // 节点点击事件
        handleNodeClick(node) {
            if (node.shape === 'scene-button-node') {
                this.checkExpandStatus(node);
            }
        },
        // 检测场景是否展开
        checkExpandStatus() {
            for (let i = 0; i < this.sceneButtonNodes.length; i++) {
                const node = this.sceneButtonNodes[i];
                const res = this.graph.getSuccessors(node);
                if (res.length > 0) {
                    this.expand = true;
                    return;
                }
            }
            this.expand = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.scene-view {
    width: 1250px;
    height: 825px;
    transform: scale(1);
    transform-origin: 0 0;
    position: relative;
    overflow: hidden;
}
</style>
