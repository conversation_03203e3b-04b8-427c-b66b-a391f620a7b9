import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        group: {
            // 查询子级部门（有权限的）
            getSubDepartment(query) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: `/common/getOrgChildrenWithPermission`,
                    method: 'get',
                    params: query
                });
            },
            // 查询子级部门（无权限的）
            getAllSubDepartment(query) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: `/common/getOrgChildren`,
                    method: 'get',
                    params: query
                });
            },
            // 查询小组名称
            getGroupInfo(query) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/console/staffTeam/getTeamList',
                    method: 'get',
                    params: query
                });
            },
            // 查询小组名称（有权限）
            getGroupInfoWithPermission(query) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/console/staffTeam/getTeamListWithPermission',
                    method: 'get',
                    params: query
                });
            },
            // 获取部门下成员信息
            getDepartName(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/common/getEmployeesByOrgCode',
                    method: 'get',
                    params: query
                });
            },
            // 新增小组信息
            postAddGroup(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/console/staffTeam/insertTeamInfo',
                    method: 'post',
                    data: query
                });
            },
            // 删除组织机构
            deleteGroup(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/console/staffTeam/deleteTeamInfo',
                    method: 'delete',
                    params: query
                });
            },
            // 获取小组下成员信息
            getPeople(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/console/staffTeam/getTeamDetail`,
                    method: 'get',
                    params: query
                });
            },
            // 编辑小组
            postEditGroup(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/console/staffTeam/updateTeamInfo',
                    method: 'post',
                    data: query
                });
            }
        }
    };

    return service;
};
