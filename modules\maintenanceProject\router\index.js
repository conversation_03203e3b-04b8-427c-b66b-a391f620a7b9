// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/maintenanceProject',
        alwaysShow: true,
        useLayout: true,
        redirect: 'noRedirect',
        name: 'MaintenanceProject',
        meta: {
            title: '维护项目',
            icon: 'el-icon-setting'
        },
        children: [
            {
                path: 'maintenanceProjectList',
                component: () => import('../views/maintenanceProjectList'),
                name: 'MaintenanceProjectList',
                hidden: true,
                meta: {
                    title: '列表'
                }
            },
            {
                path: 'maintenanceResource',
                component: () => import('../views/maintenanceResource'),
                name: 'MaintenanceResource',
                meta: {
                    title: '资源'
                }
            },
            {
                path: 'maintenanceFinance',
                component: () => import('../views/maintenanceFinance'),
                name: 'MaintenanceFinance',
                meta: {
                    title: '财务'
                }
            },
            {
                path: 'maintenanceDefect',
                component: () => import('../views/maintenanceDefect'),
                name: 'MaintenanceDefect',
                meta: {
                    title: '缺陷'
                }
            },
            {
                path: 'maintenanceDefectDetail/:defect_id?',
                component: () =>
                    import(
                        '../views/maintenanceDefect/components/maintenanceDefectDetail'
                    ),
                name: 'MaintenanceDefectDetail',
                meta: {
                    title: '缺陷详情'
                },
                hidden: true
            },
            {
                path: 'maintenanceRisk',
                component: () => import('../views/maintenanceRisk'),
                name: 'MaintenanceRisk',
                meta: {
                    title: '风险'
                }
            },
            {
                path: 'maintenanceRiskDetail/:risk_id?',
                component: () =>
                    import(
                        '../views/maintenanceRisk/components/maintenanceRiskDetail'
                    ),
                name: 'MaintenanceRiskDetail',
                meta: {
                    title: '风险详情'
                },
                hidden: true
            },
            {
                path: 'maintenanceDemand',
                component: () => import('../views/maintenanceDemand'),
                name: 'MaintenanceDemand',
                meta: {
                    title: '需求'
                }
            },
            {
                path: 'maintenanceDemandDetail/:demand_id?',
                component: () =>
                    import(
                        '../views/maintenanceDemand/components/maintenanceDemandDetail'
                    ),
                name: 'MaintenanceDemandDetail',
                meta: {
                    title: '需求详情'
                },
                hidden: true
            },
            {
                path: 'maintenanceOrder',
                component: () => import('../views/maintenanceOrder'),
                name: 'MaintenanceOrder',
                meta: {
                    title: '订单'
                }
            },
            {
                path: 'maintenanceOrderDetail/:order_id?',
                component: () =>
                    import(
                        '../views/maintenanceOrder/components/maintenanceOrderDetail'
                    ),
                name: 'MaintenanceOrderDetail',
                meta: {
                    title: '订单详情'
                },
                hidden: true
            },
            {
                path: 'maintenanceReport',
                component: () => import('../views/maintenanceReport'),
                name: 'MaintenanceReport',
                meta: {
                    title: '汇报'
                }
            }
        ]
    }
];
