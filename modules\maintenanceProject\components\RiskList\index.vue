<template>
    <div class="risk-table-container">
        <div class="add-risk">
            <el-button
                v-if="showAddRiskButton"
                v-permission="['maintenanceProjectRiskUpdate']"
                type="primary"
                @click="addRisk"
                >新增风险</el-button
            >
        </div>
        <el-table
            class="risk-list"
            :data="riskList"
            style="width: 100%"
            max-height="260"
        >
            <el-table-column align="center" label="风险编号" width="140">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click="handleRoutingJump(scope.row)"
                        >{{ scope.row.riskId }}</el-button
                    >
                </template>
            </el-table-column>
            <el-table-column
                prop="riskDesc"
                label="风险描述及影响"
                align="left"
                header-align="center"
                min-width="300"
            >
            </el-table-column>
            <el-table-column
                prop="riskLevel"
                align="center"
                label="风险等级"
                width="80"
            >
            </el-table-column>
            <el-table-column
                prop="riskStatus"
                align="center"
                label="风险状态"
                width="100"
            >
            </el-table-column>
        </el-table>
        <RiskUpdateDialog
            type="add"
            :relatedObject="relatedObject"
            :visible.sync="dialogVisible"
            :relatedObjectId="assObjectId"
            @success="getList"
        />
    </div>
</template>

<script>
import RiskUpdateDialog from 'maintenanceProject/components/RiskUpdateDialog';

export default {
    name: 'RiskList',
    components: { RiskUpdateDialog },
    props: {
        assObjectId: {
            type: String,
            default: ''
        },
        riskListCounter: {
            type: Number,
            default: 0
        },
        relatedObject: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            riskList: [],
            dialogVisible: false,
            isQuerying: false,
            showAddRiskButton: true,
            // 是否在维护项目界面
            isMaintenance: true
        };
    },
    watch: {
        riskListCounter(newVal) {
            newVal && this.getList();
        }
    },
    created() {
        const { name } = this.$route;
        this.showAddRiskButton = !name.includes('Detail');
        this.isMaintenance = name.includes('Maintenance');
    },
    activated() {
        const { name } = this.$route;
        this.showAddRiskButton = !name.includes('Detail');
        this.isMaintenance = name.includes('Maintenance');
    },
    methods: {
        handleRoutingJump(row) {
            this.$router.push({
                path: this.isMaintenance
                    ? 'maintenanceRiskDetail'
                    : 'projectRiskDetail',
                query: {
                    risk_id: row.riskId
                }
            });
        },
        addRisk() {
            this.dialogVisible = true;
        },
        /**
         * 执行查询操作
         * @param {Object} form - 查询表单参数，默认使用this.queryParams
         */
        async getList() {
            if (this.isQuerying) return;
            this.isQuerying = true;
            const params = {
                assObjectId: this.assObjectId,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件',
                pageSize: 9999,
                currentPage: 1
            };
            try {
                const api = this.$service.maintenanceProject.risk.getList;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.riskList = res.body.list;
            } catch (error) {
                console.error(error, 'error');
            } finally {
                this.isQuerying = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.risk-list {
    border: 1px solid #8c8c8c !important;
}
.add-risk {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
