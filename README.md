
# BOSS管理平台   

## 项目简介
	
	此项目是一个服务端平台类项目模板，主要为新北洋的金融、物流、新零售产品线提供服务端技术支持，使各产品线开发人员更专注于业务开发。
	此项目主要提供了系统管理、商户管理、产品服务三个基础业务模块，其他项目人员可以做到拿来即用，如果有模块不是二次开发团队的项目所需要的，直接把该模块删除即可。

## 技术栈
       
	此项目基于vue-element-admin 后台管理系统模板进行开发；
	主要技术栈：vue v2.6.10  element-ui v2.13.2  vue-router v3.0.2 vue-i18n v7.3.2

## 下载项目源码

    git clone http://gitlab.xtjc.net/xtjc/TechnologyPlatform/BBPF/bbpf-2.0/si-bbpf-boss-console.git

## 启动
        
#### 安装运行环境
- [vscode](https://dev-docs.newbeiyang.cn/standards/vscode/10%E5%AE%89%E8%A3%85%E9%85%8D%E7%BD%AE.html) （推荐使用）
- [nodejs](http://nodejs.cn/download/) ≥ 10
- [npm](https://www.npmjs.cn/)
#### 安装依赖
    ```sh
	# 1. 进入项目目录：
	cd si-bbpf-boss-console
	# 2. 创建/切换到自己的项目分支
	# 3. 安装依赖
    npm i --registry=http://maven.xtjc.net/repository/npm-all

    ```	
#### 本地启动
    npm run dev
#### 发布部署
- 本地打包：执行 npm run build 命令
- 流水线发布：
	开发环境jenkins任务地址：http://jenkins.xtjc.net/jenkins/job/BBPF-Platform-Web_SI-WTF2-0-System-Web_DEPLOY_DEV/
	测试环境jenkins任务地址：http://jenkins.xtjc.net/jenkins/job/BBPF-Platform-Web_SI-WTF2-0-System-Web_DEPLOY_TEST/

## 功能特性

- 系统管理模块
- 产品服务模块
- 商户管理模块
- 按钮权限
- 路由权限 

## 项目结构

```sh
|-- si-bbpf-boss-console
    |-- modules               # 项目代码主目录，对应这项目的每个模块
       |-- frame              # 系统管理框架功能，包含登录、授权信息获取、退出登录基础功能
       |-- merchantManagement # 商户管理业务功能，包含商户列表、商户日志等业务
       |-- systemManagement   # 系统管理业务功能，包含字典管理、组织机构、权限管理等基础业务
	   |-- produceService     # 产品服务业务功能，包含产品服务管理、服务订单记录、支付配置等基础业务
    |-- public                # 页面模板文件，一般不用动
    |-- src                   # 入口文件、项目公共文件放在这里
	├── .babelrc              # babel编译器配置文件
	├── .editorconfig         # 配置编码格式规范
	├── .env.xxx              # 环境变量配置
	├── .eslintignore         # 配置eslint检查忽略项
    ├── .eslintrc.js          # eslint 配置项
	├── .gitignore            # git提交忽略项配置
    ├── .README.md            # 对项目主要信息进行描述
    ├── .babel.config.js      # babel配置文件
    ├── jsconfig.json         # 文件目录智能检索
    ├── package-lock.json     # 版本锁
	├── package.json          # 依赖包管理
	├── package_back.json     # 回滚依赖包
	├── plopfile.js           # 预设工具配置文件
	├── postcss.config.js     # postcss 配置
	├── vue.config-back.js    # 可选的配置文件
	├── vue.config-module.js  # 可选的配置文件
	├── vue.config.js         # 可选的配置文件
    └── 
``` 

## 项目状态
    
	维护阶段（会修复一些问题以及新增一些小功能）

## 来源 

	vue-element-admin：https://gitee.com/panjiachen/vue-element-admin?utm_source=alading&utm_campaign=repo


## 项目代码管理规范
     
- 必须先创建个人分支后，在进行项目功能开发
- 本地代码必须通过ESLint检测通过才能提交
- 提交分支合并，必须先获取develop分支代码，并且将最新develop代码合并到当前分支，并解决冲突后提交合并
- 项目中可以进行修改的文件：
   1）modules文件夹下的merchantManagement、productService、systemManagement 这些文件夹下的代码都可以修改
   2）src下的envConst.js、settings文件可以修改
- 剩余的文件不要去修改，如果由于修改了不允许修改的文件导致问题，项目组自己负责。

## 注意事项：

- 使用此项目模板需要配置后台的标准接口一起使用，如登录登出等
- 如果此项目组提供的基础业务模块二次开发团队并没有都用到，可以将没有用到的模块直接删除。
- 特别强调：不要修改package.json 和 package-lock.json 文件，除非项目组新增了其他依赖。

## 项目访问地址

https://test-bbpfboss.xinbeiyang.info/

## 版本更新日志
- v2.0.25 2023-07-16
  * 组织机构优化
  * 角色管理优化
  * 信息公告通知范围优化
- v2.0.24  2023-06-25  
  * 商户详情开发
  * 延期功能开发
  * 界面变形修订
  * 组织机构优化 
  * 菜单展示优化
  * 多语言展示优化
- v2.0.23  2022-06-04  
  * 消息模板开发
  * 登录页组件抽离
- v2.0.22  2022-05-22  
  * 系统优化
- v2.0.21  2022-04-15  
  * 系统优化
- v2.0.20  2023-03-31  
  * 安全改造国际化
  * 前端统一入口
- v2.0.19  2023-03-10  
  * 导入用户优化
  * 灰度发布管理功能开发
- v2.0.18  2023-02-17  
  * 商户信息优化
- v2.0.17  2023-01-19  
  * BBPF安全改造
- v2.0.16  2023-01-06  
  * 信息公告功能开发
  * 顶部菜单功能开发
- v2.0.15  2022-12-09  
  * 数据导出异步处理
  * 列可选组件开发
  * 日志内容改造
- v2.0.14  2022-11-11  
  * 商户列表编辑优化
- v2.0.13  2022-10-14  
  * WTF2.0框架优化
- v2.0.12  2022-09-15  
  * 登录功能优化
- v2.0.11  2022-08-19  
  * boss端国际化
- v2.0.10  2022-07-22  
  * 字典值国际化
  * 租户端国际化
- v2.0.9   2022-06-24  
  * 字典管理重构
- v2.0.8   2022-05-31  
  * 消息通知建立websocket长链接
- v2.0.29   2024-03-20  
  * 此版本是将bbpf2.0与angular项目单应用融合的版本
    ##  通用性处理如下：
      1. 改国际化的时候要通过配置文件配置是否需要国际化
      2. 隐藏修改密码功能时，通过配置文件来设置
      3. 左侧菜单通过权限管理来配置，考虑到通用性，如果angular应用是单应用，则路由地址从上下文开始配置；如果是多应用，路由地址直接配置完整的url。
        settings中的permissionDataPriority 需要配置为'server'
    
      