/* eslint-disable max-lines-per-function */
// 排队中
const queued = (scope) => {
    return [
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '立项类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '市场需求导入时间',
            prop: 'marketDemandImportDate',
            show: true,
            minWidth: 160,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '已下达天数',
            prop: 'marketDemandHavenDays',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '未启动原因',
            prop: 'stopReason',
            show: true,
            minWidth: 360,
            elTableColumnAttrs: {
                'resizable': false,
                'show-overflow-tooltip': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '市场需求导入实例号',
            prop: 'marketDemandImportNo',
            show: true,
            minWidth: 200,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        }
    ];
};
// 进行中
const onGoing = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 130,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            },
            slot: 'projectNumber'
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            },
            slot: 'projectName'
        },
        {
            label: '立项类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModel',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '难度等级',
            prop: 'difficultyLevel',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '所处阶段',
            prop: 'projectStage',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '当前里程碑',
            prop: 'detailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            },
            slot: 'currentMilestone'
        },
        {
            label: '里程碑计划完成时间',
            prop: 'detailPlanFinishDate',
            show: true,
            minWidth: 140,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '进度状态',
            prop: 'progressStatus',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 已暂停
const paused = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '立项类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModel',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '暂停时间',
            prop: 'stopDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '暂停时已完成里程碑',
            prop: 'detailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '暂停原因',
            prop: 'stopReason',
            show: true,
            minWidth: 360,
            elTableColumnAttrs: {
                'resizable': false,
                'show-overflow-tooltip': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        }
    ];
};
// 已终止
const terminated = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '立项类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModel',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '终止时间',
            prop: 'stopDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '终止时已完成里程碑',
            prop: 'detailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '终止原因',
            prop: 'stopReason',
            show: true,
            minWidth: 360,
            elTableColumnAttrs: {
                'resizable': false,
                'show-overflow-tooltip': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        }
    ];
};
// 已结项
const completed = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '立项类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModel',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '结项前已完成里程碑',
            prop: 'detailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '里程碑完成时间',
            prop: 'detailFinishDate',
            show: true,
            minWidth: 140,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        }
    ];
};
const getStatusArray = (index, scope) => {
    const statusMap = [
        queued(scope),
        onGoing(scope),
        paused(scope),
        terminated(scope),
        completed(scope)
    ];
    return statusMap[index];
};
export { getStatusArray };
