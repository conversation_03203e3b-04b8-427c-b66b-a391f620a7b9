<template>
    <div>
        <el-form v-if="showForgetPassOne" ref="passOneForm" :model="passOneForm" :rules="passOneFormRules" class="login-form passOneForm" autocomplete="off" label-position="left" label-width="0px" :validate-on-rule-change="false">
            <div class="title-container">
                <img class="pass-one-back" src="../../../assets/arrow-left.png" @click="forgetPassBack">
                {{ $t("frame.forgetPassword") }}
            </div>
            <!-- 解决错误信息切换登录类型时依然存在的问题 key="phoneInput" -->
            <el-form-item key="passPhoneInput" prop="phone" class="phone-login-item phone-item">
                <span class="split-line" />

                <el-input v-model="passOneForm.phone" :placeholder="$t('frame.phonePlaceholder')" class="input-with-select" type="text" maxlength="11" clearable @input="passPhoneInput">
                    <el-select slot="prepend" v-model="passAreaCode" :placeholder="$t('frame.pleaseSelect')">
                        <el-option label="+86" value="1" />
                        <el-option label="+852" value="2" />
                        <el-option label="+853" value="3" />
                        <el-option label="+886" value="4" />
                    </el-select>
                </el-input>
            </el-form-item>
            <el-form-item key="passVerCodeInput" prop="verificationCode" class="phone-login-item code-item">
                <el-input v-model="passOneForm.verificationCode" :placeholder="$t('frame.verCodePlaceholder')" type="text" maxlength="6" clearable @input="passVerCodeInput" />
                <span class="split-line" />
                <span v-if="passCountDown <= 0" class="get-code-nomal get-code" @click="passGetVerCode">{{ $t("frame.getVerificationCode") }}</span>
                <!-- 这样来实现动态国际化 -->
                <span v-else class="get-code-nomal code-count">{{
          $t("frame.changePwd.sendCountdown", [
            passCountDown,
          ])
        }}</span>
            </el-form-item>
            <el-button type="primary" class="login-btn" @click="nextStepClick">
                {{ $t("frame.nextStep") }}
            </el-button>
        </el-form>
        <el-form v-if="showForgetPassTwo" ref="passTwoForm" :model="passTwoForm" :rules="passTwoFormRules" class="login-form passOneForm" autocomplete="off" label-position="left" label-width="0px" :validate-on-rule-change="false">
            <div class="title-container">
                {{ $t("frame.pleaseEnterNewPassword") }}
            </div>
            <el-form-item key="passwordNext" prop="password" class="password-strength-wrap">
                <span class="svg-container">
                    <svg-icon icon-class="password" />
                </span>
                <el-input key="passwordTwoType" ref="passwordTwoType" v-model.trim="passTwoForm.password" :type="passwordTwoType" :placeholder="$t('frame.pleaseEnterNewPassword')" maxlength="20" :clearable="false" />
                <span class="show-pwd" @click="showPwdTwo">
                    <svg-icon :icon-class="passwordTwoType === 'password' ? 'eye' : 'eye-open'" />
                </span>
            </el-form-item>
            <el-form-item key="confirmPasswordNext" prop="confirmPassWord">
                <span class="svg-container">
                    <svg-icon icon-class="password" />
                </span>
                <el-input key="confrmPassword" ref="confrmPassword" v-model.trim="passTwoForm.confirmPassWord" :type="confirmPassWordType" :placeholder="$t('frame.confirmPassword')" maxlength="20" :clearable="false" />
                <span class="show-pwd" @click="showConfirmPassWord">
                    <svg-icon :icon-class="confirmPassWordType === 'password' ? 'eye' : 'eye-open'" />
                </span>
            </el-form-item>
            <el-button type="primary" class="login-btn" @click="changePassword">
                {{ $t("frame.nextStep") }}
            </el-button>
        </el-form>
        <!-- 修改密码成功 -->
        <el-form v-if="changePassSuccPopup" ref="passSuccForm" class="login-form passOneForm passSuccForm" label-position="left" label-width="0px">
            <div class="title-container">
                {{ $t("frame.changeSucc") }}
            </div>
            <div class="change-pass-text">{{ $t("frame.changeSuccText") }}</div>
            <el-button type="primary" class="login-btn" @click="goLogin">
                {{ $t("frame.loginNow") }}
            </el-button>
        </el-form>
    </div>
</template>
<script>
import { regData } from '../../../constants/regular';

export default {
    name: 'ForgetPassword',
    // eslint-disable-next-line max-lines-per-function
    data() {
        // 校验手机号
        const validatePhone = (rule, value, callback) => {
            if (!value) {
                return callback(new Error(this.$t('frame.msg.checkPhone')));
            }
            const phoneReg = /^[1][3,4,5,7,8,9][0-9]{9}$/;
            if (!phoneReg.test(value)) {
                return callback(new Error(this.$t('frame.msg.checkPhone')));
            }
            callback();
        };
        // 校验验证码
        const validateVerCode = (rule, value, callback) => {
            if (!value) {
                return callback(new Error(this.$t('frame.msg.validVerCode')));
            }
            callback();
        };
        // 密码校验
        const rules = (value, callback) => {
            if (value.length < 8) {
                return callback(
                    new Error(this.$t('frame.msg.checkPasswordLength'))
                );
            }
            if (this.cipherStrength === 'low') {
                const passwordReg = regData.PASSWORD_LOW;
                if (!passwordReg.test(value)) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordLength'))
                    );
                }
                callback();
            } else if (this.cipherStrength === 'middle') {
                const passwordReg = regData.PASSWORD_MIDDLE;
                if (!passwordReg.test(value)) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordMiddle'))
                    );
                }
                callback();
            } else {
                const passwordReg = regData.PASSWORD_HIGH;
                if (!passwordReg.test(value)) {
                    return callback(
                        new Error(this.$t('frame.msg.checkPasswordHigh'))
                    );
                }
                callback();
            }
        };
        // 忘记密码 校验密码
        const validPassword = (rule, value, callback) => {
            if (!value) {
                return callback(
                    new Error(this.$t('frame.msg.checkPasswordLength'))
                );
            }
            rules(value, callback);
        };
        // 忘记密码 校验确认密码
        const validConfirmPassword = (rule, value, callback) => {
            if (!value) {
                return callback(
                    new Error(this.$t('frame.msg.checkPasswordLength'))
                );
            }
            if (value !== this.passTwoForm.password) {
                return callback(
                    new Error(this.$t('frame.msg.checkPasswordSame'))
                );
            }
            rules(value, callback);
        };
        return {
            passTwoForm: {
                password: '',
                confirmPassWord: ''
            },
            // 忘记密码第一步验证
            passOneForm: {
                phone: '',
                verificationCode: ''
            },
            // 忘记密码第一步验证 校验规则
            passOneFormRules: {
                phone: [
                    {
                        required: true,
                        validator: validatePhone,
                        // 解决改变区号时触发校验的问题
                        trigger: 'blur'
                    }
                ],
                verificationCode: [
                    {
                        required: true,
                        validator: validateVerCode,
                        trigger: 'blur'
                    }
                ]
            },
            // 忘记密码第二步校验规则
            passTwoFormRules: {
                password: [
                    {
                        required: true,
                        validator: validPassword,
                        trigger: 'blur'
                    }
                ],
                confirmPassWord: [
                    {
                        required: true,
                        validator: validConfirmPassword,
                        trigger: 'blur'
                    }
                ]
            },
            // 忘记密码第一步
            showForgetPassOne: true,
            // 显示忘记密码第二步
            showForgetPassTwo: false,
            // 忘记密码第二步密码框类型
            passwordTwoType: 'password',
            // 忘记密码第二步确认密码框类型
            confirmPassWordType: 'password',
            // 修改密码成功弹窗
            changePassSuccPopup: false,
            // 忘记密码区号
            passAreaCode: '1',
            // 忘记密码 验证码倒计时计时器
            passCountDown: 0,
            // 当前密码强度
            cipherStrength: 'high'
        };
    },
    mounted() {
        // 清空当前忘记密码第一步的输入内容,解决二次修改密码时，残留上次输入内容问题
        this.passOneForm = {
            phone: '',
            verificationCode: ''
        };
    },
    methods: {
        // 忘记密码第一步点击返回
        forgetPassBack() {
            this.showForgetPassOne = false;
            // 展示账密登录框
            this.$emit('passwordBack');
        },
        // 忘记密码 手机号输入监听 只能输入数字 ，如输入字母或符号时，输入框内不显示
        passPhoneInput() {
            if (this.passOneForm.phone.length === 1) {
                this.passOneForm.phone = this.passOneForm.phone.replace(
                    /[^0-9]/g,
                    ''
                );
            } else {
                this.passOneForm.phone = this.passOneForm.phone.replace(
                    /\D/g,
                    ''
                );
            }
        },
        // 忘记密码 验证码输入监听 只能输入数字
        passVerCodeInput() {
            if (this.passOneForm.verificationCode.length === 1) {
                this.passOneForm.verificationCode =
                    this.passOneForm.verificationCode.replace(/[^0-9]/g, '');
            } else {
                this.passOneForm.verificationCode =
                    this.passOneForm.verificationCode.replace(/\D/g, '');
            }
        },
        // 忘记密码 获取验证码
        passGetVerCode() {
            // 前端验证手机号
            this.$refs['passOneForm'].validateField(['phone'], (error) => {
                if (!error) {
                    // 执行倒计时
                    this.passCountDown = 60;
                    // 开启倒计时之前先关闭倒计时
                    clearInterval(this.passCountTimer);
                    this.passCountTimer = setInterval(() => {
                        if (this.passCountDown === 0) {
                            clearInterval(this.passCountTimer);
                        }
                        // eslint-disable-next-line no-plusplus
                        this.passCountDown--;
                    }, 1000);
                    const { phone } = this.passOneForm;
                    this.getVerCodeRq(1, phone);
                }
            });
        },
        // 忘记密码第一步弹窗 点击下一步
        nextStepClick() {
            this.$refs.passOneForm.validate((valid) => {
                if (valid) {
                    const params = {
                        phone: this.passOneForm.phone,
                        smsCode: this.passOneForm.verificationCode,
                        msgType: 1,
                        isDelSmsCode: false
                    };
                    this.$service.frame
                        .PostCheckVerifCode(params)
                        .then((res) => {
                            const { code } = res.head;
                            if (code === '000000') {
                                this.showForgetPassOne = false;
                                this.showForgetPassTwo = true;
                                // 调用获取密码强度的接口
                                this.$service.frame
                                    .getPasswordStrength()
                                    .then((subRes) => {
                                        if (subRes.head.code === '000000') {
                                            this.cipherStrength =
                                                subRes.body.cipherStrength;
                                        }
                                    });
                            } else {
                                const msg = `frame.bgReturnError[${code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error'
                                });
                            }
                        });
                }
            });
        },
        // 忘记密码第二步 点击下一步
        changePassword() {
            this.$refs.passTwoForm.validate((valid) => {
                if (valid) {
                    const params = {
                        confirmPwd: this.$tools.encrypt(
                            this.passTwoForm.confirmPassWord,
                            '3DES'
                        ),
                        newPwd: this.$tools.encrypt(
                            this.passTwoForm.password,
                            '3DES'
                        ),
                        smsCaptcha: this.passOneForm.verificationCode,
                        phone: this.passOneForm.phone,
                        sysType: '0'
                    };
                    this.$service.frame
                        .PutChangePassword(params)
                        .then((res) => {
                            const { code } = res.head;
                            if (code === '000000') {
                                this.showForgetPassTwo = false;
                                this.changePassSuccPopup = true;
                            } else {
                                let messageTitle = '';
                                if (code === '990313') {
                                    messageTitle = res.head.message;
                                } else {
                                    const msg = `frame.bgReturnError[${code}]`;
                                    messageTitle = this.$t(msg);
                                }
                                this.$message({
                                    message: messageTitle,
                                    type: 'error'
                                });
                                if (code === '990310' || code === '990311') {
                                    window.location.reload();
                                }
                            }
                        });
                }
            });
        },
        // 手机登录，请求获取验证码接口
        getVerCodeRq(msgType, phone) {
            const params = {
                userPhone: phone,
                msgType
            };
            this.$service.frame.getLoginVerifCode(params).then((res) => {
                const { code } = res.head;
                if (code !== '000000') {
                    const msg = `frame.bgReturnError[${code}]`;
                    this.$message({
                        message: this.$t(msg),
                        type: 'error'
                    });
                }
            });
        },
        // 忘记密码显示密码按钮
        showPwdTwo() {
            if (this.passwordTwoType === 'password') {
                this.passwordTwoType = '';
            } else {
                this.passwordTwoType = 'password';
            }
            this.$nextTick(() => {
                this.$refs.passwordTwoType.focus();
            });
        },
        // 忘记密码显示确认密码
        showConfirmPassWord() {
            if (this.confirmPassWordType === 'password') {
                this.confirmPassWordType = '';
            } else {
                this.confirmPassWordType = 'password';
            }
            this.$nextTick(() => {
                this.$refs.confrmPassword.focus();
            });
        },
        // 修改密码成功 去登录
        goLogin() {
            this.changePassSuccPopup = false;
            // 展示账密登录框
            this.$emit('passwordBack');
        }
    }
};
</script>
<style lang="scss" scoped>
// $bg: #2d3a4b;
$bg: #121217;
$dark_gray: #889aa4;
// $light_gray: #eee;
$light_gray: #373d41;

.login-container {
    background: url('../../../assets/login-bg.png') #121217 no-repeat 50px 350px;
    min-height: 100%;
    width: 100%;
    background-color: $bg;
    overflow: hidden;

    .carousel {
        height: 100vh;
        display: flex;
        align-items: center;

        .el-carousel__item {
            text-align: center;
        }
    }

    .login-form {
        background: #fff;
        position: relative;
        width: 460px;
        height: 460px;
        border-radius: 10px;
        box-sizing: border-box;
        padding: 40px;

        .login-btn {
            width: 100%;
            height: 60px;
            font-size: 24px;
            font-weight: normal;
            margin-top: 10px;
            background: linear-gradient(0deg, #3370ff, #6b91f8);
            border-radius: 33px;
        }

        ::v-deep.el-form-item {
            margin-bottom: 30px;
        }
    }

    .tips {
        font-size: 14px;
        color: #373d41;
        margin-bottom: 10px;

        span {
            &:first-of-type {
                margin-right: 16px;
            }
        }
    }

    .svg-container {
        padding: 10px 5px 6px 15px;
        color: $dark_gray;
        vertical-align: middle;
        width: 45px;
        display: inline-block;

        .svg-icon {
            width: 1.5em;
            height: 1.9em;
        }
    }

    .title-container {
        position: relative;
        margin-bottom: 40px;

        .title {
            font-size: 26px;
            color: $light_gray;
            margin: 0px auto 20px auto;
            text-align: center;
            font-weight: bold;
        }

        .set-language {
            color: #373d41;
            position: absolute;
            top: 3px;
            font-size: 26px;
            right: 0px;
            cursor: pointer;
            background: #6f6f6f;
            border-radius: 5px;
        }
    }

    .show-pwd {
        position: absolute;
        right: 10px;
        top: 14px;
        font-size: 16px;
        color: $dark_gray;
        cursor: pointer;
        user-select: none;
    }

    .thirdparty-button {
        position: absolute;
        right: 0;
        bottom: 6px;
    }

    @media only screen and (max-width: 470px) {
        .thirdparty-button {
            display: none;
        }
    }
}

::v-deep .el-form-item--medium {
    .el-form-item__content {
        height: 60px;

        .el-form-item__error {
            margin-right: 40px;
        }
    }
}

::v-deep .el-input--medium {
    .el-input__inner {
        font-size: 18px;

        // 自动填充文本样式
        &:-webkit-autofill::first-line {
            font-size: 18px;
        }
    }
}

.tab-box {
    display: flex;
    padding: 0 52px;
    justify-content: center;
    font-size: 26px;
    font-family: PingFang SC;
    font-weight: bold;
    box-sizing: border-box;

    .tab-item {
        color: #7f7f7f;
        padding-bottom: 10px;
        border-bottom: 4px solid transparent;
        cursor: pointer;
    }

    .tab-acitve {
        color: #3370ff;
        border-bottom: 4px solid #3370ff;
    }
}
.tab-box-new {
    justify-content: space-between;
}

.phone-login-item {
    position: relative;

    .el-input {
        display: inline-table;
    }

    .split-line {
        position: absolute;
        display: block;
        width: 1px;
        height: 30px;
        background-color: #d8d8d8;
    }
}

.phone-item {
    .el-select {
        width: 90px;
        margin: -20px -20px;

        ::v-deep .el-input__suffix {
            right: -8px;
            top: 5px;
        }
    }

    .input-with-select {
        width: 98%;

        /*解决右边边框显示异常问题*/
        ::v-deep .el-input-group__prepend {
            background-color: #fff;
            border-left: 0;
            border-top: 0;
            border-bottom: 0;
        }
    }

    .split-line {
        left: 90px;
        top: 14px;
    }
}

.code-item {
    .el-input {
        width: 65%;

        ::v-deep .el-input-group__append {
            background-color: #fff;
            border-left: 0;
            border-top: 0;
            border-bottom: 0;
            border-right: 0;
        }
    }

    .get-code-nomal {
        position: absolute;
        color: #3370ff;
        top: 12px;
    }

    .get-code {
        right: 25px;
        cursor: pointer;
    }

    .code-count {
        right: 12px;
    }

    .split-line {
        right: 120px;
        top: 14px;
    }
}

.forget-password {
    text-align: right;
    margin-top: 12px;

    .forget-password-text {
        color: #2f2f2f;
        cursor: pointer;
    }

    .forget-password-text:hover {
        color: #3370ff;
    }
}

.password-strength-wrap {
    .password-strength-box {
        display: flex;
        position: absolute;
        bottom: -14px;
        right: 0;

        .password-strength-item {
            width: 40px;
            height: 5px;
            border-radius: 3px;
            margin-right: 8px;
            background: #47ad1f;
        }

        .password-strength-item:last-child {
            margin-right: 0;
        }
    }
}

.passSuccForm {
    .change-pass-text {
        margin-top: 110px;
        color: #2f2f2f;
        text-align: center;
    }
}
.passOneForm {
    .title-container {
        position: relative;
        color: #2f2f2f;
        text-align: center;
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: bold;

        .pass-one-back {
            position: absolute;
            width: 28px;
            height: 23px;
            left: 0;
            top: 4px;
            cursor: pointer;
        }
    }
}
</style>
