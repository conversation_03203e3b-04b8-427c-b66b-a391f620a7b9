import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange, peopleSelector } = CommonItems;
const eventName = {
    ...input,
    name: '消息标题',
    modelKey: 'eventName'
};
const objectType = {
    ...select,
    name: '消息类型',
    modelKey: 'objectType',
    elOptions: [
        {
            label: '系统',
            value: '1'
        },
        {
            label: '邮件',
            value: '2'
        },
        {
            label: '短信',
            value: '3'
        },
        {
            label: '微信',
            value: '4'
        },
        {
            label: '钉钉',
            value: '5'
        }
    ]
};

const calculateDateRange = {
    ...dateRange,
    name: '发送时间',
    modelKey: 'calculateDateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};
const executeResult = {
    ...select,
    name: '发送结果',
    modelKey: 'executeResult',
    elOptions: [
        {
            label: '成功',
            value: '成功'
        },
        {
            label: '失败',
            value: '失败'
        }
    ]
};

const messageReceivePersonLoginName = {
    ...peopleSelector,
    name: '消息接收人',
    modelKey: 'messageReceivePersonLoginName',
    elSelectAttrs: {
        isMultipled: false
    }
};
// 查询参数初始化
const queryParams = {
    eventName: '',
    calculateDateRange: [],
    executeResult: '',
    objectType: '2',
    messageReceivePersonLoginName: ''
};

// 查询区域配置项
const queryConfigItems = [
    eventName,
    objectType,
    executeResult,
    calculateDateRange,
    messageReceivePersonLoginName
];

const getTableConfig = (scope) => {
    return {
        // 列表查询参数
        queryParams,
        // 查询项配置
        queryConfig: {
            items: queryConfigItems
        },
        // 查询api配置
        queryApi: scope.$service.monitor.message.getMessageCondition,
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: [
            {
                label: '消息标题',
                prop: 'eventName',
                show: true,
                minWidth: 320,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '消息类型',
                prop: 'objectType',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '接收人',
                prop: 'messageReceivePersonName',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '发送时间',
                prop: 'executeTime',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '发送结果',
                prop: 'executeResult',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '描述',
                prop: 'remark',
                show: true,
                minWidth: 140,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        // 固定表头
        elTableAttrs: {
            'header-cell-style': '{text-align:center}'
        },
        hooks: {
            queryParamsHook: scope.queryParamsHook,
            resetSearch: scope.resetSearch
        }
    };
};
export { getTableConfig };
