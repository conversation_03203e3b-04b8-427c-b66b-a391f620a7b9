<template>
    <el-drawer
        :visible.sync="dialogVisible"
        :show-close="false"
        :wrapperClosable="false"
        @close="close"
        size="480px"
        custom-class="role-edit-drawer"
    >
        <template #title>
            <div class="title-box">
                <span class="blue-line"></span>
                <span class="title">{{
                    $t('systemManagement.role.roleTitle')
                }}</span>
            </div>
        </template>
        <el-form
            ref="dataForm"
            :model="formDataInfo"
            :rules="rules"
            :inline="false"
            label-width="100px"
            size="normal"
        >
            <el-form-item
                :label="`${$t('systemManagement.role.columnName.roleName')}`"
                prop="roleName"
            >
                <el-input
                    v-model="formDataInfo.roleName"
                    type="text"
                    clearable
                    :readonly="isView ? true : false"
                    :placeholder="
                        $t('systemManagement.role.message.placeholderPre') +
                        $t('systemManagement.role.columnName.roleName')
                    "
                />
            </el-form-item>
            <el-form-item
                :label="`${$t('systemManagement.role.columnName.department')}`"
                prop="belongOrgIds"
            >
                <div
                    class="org-container"
                    contenteditable="false"
                    :placeholder="
                        $t(
                            'systemManagement.organization.dialog.placeholder.msg5'
                        )
                    "
                    @click="showOrgTransfer"
                >
                    <el-tag
                        v-for="tag in dialogOrgInfo.orgTagList"
                        :key="tag.orgId"
                        closable
                        type="info"
                        @close="removeOrgTag(tag)"
                    >
                        {{ tag.orgName }}
                    </el-tag>
                </div>
            </el-form-item>
        </el-form>
        <div>
            <div class="checkbox-control">
                <el-checkbox
                    v-model="isExpandAll"
                    :disabled="isView"
                    @change="onExpandAllChange"
                >
                    {{ $t('systemManagement.role.expand') }}</el-checkbox
                >
                <el-checkbox
                    v-model="isCheckedAll"
                    :disabled="isView"
                    @change="onCheckedAllChange"
                    >{{ $t('systemManagement.role.allSelect') }}</el-checkbox
                >
                <el-checkbox v-model="isLinkage" :disabled="isView">{{
                    $t('systemManagement.role.linkage')
                }}</el-checkbox>
            </div>
            <div>
                <el-input
                    :placeholder="
                        $t(
                            'systemManagement.organization.dialog.placeholder.msg13'
                        )
                    "
                    maxlength="20"
                    v-model="filterText"
                    suffix-icon="fa fa-search"
                >
                </el-input>
                <div class="tree-box">
                    <el-tree
                        v-if="showTree"
                        ref="tree"
                        :data="dataPermission"
                        :props="defaultProps"
                        :default-expand-all="isExpandAll"
                        show-checkbox
                        :check-strictly="!isLinkage"
                        :filter-node-method="filterNode"
                        node-key="permissionId"
                        :default-checked-keys="checkedKeys"
                        :class="isView ? 'disabled-tree' : ''"
                        @check="getCheckedNodes"
                    ></el-tree>
                </div>
            </div>
        </div>
        <div class="footer">
            <el-button @click="close">{{
                $t('systemManagement.role.cancel')
            }}</el-button>
            <el-button v-if="!isView" type="primary" @click="save">{{
                $t('systemManagement.components.treeSelectOrg.confirm')
            }}</el-button>
        </div>
        <user-org-change
            v-if="dialogOrgInfo.isShow"
            :showModal="false"
            :org-source-data="orgTreeData"
            :show.sync="dialogOrgInfo.isShow"
            :selection.sync="dialogOrgInfo.orgTagList"
            @confirm-selection="dialogOrgInfo.isShow = false"
            :reserveOrgs="reserveOrgs"
        />
    </el-drawer>
</template>
  
<script>
import userInfo from '../../../mixins/list-edit-info';
import UserOrgChange from '../../organization/components/UserOrgChange.vue';

export default {
    components: { UserOrgChange },
    mixins: [userInfo],
    props: {
        // 当前表单数据
        formData: {
            type: Object,
            default() {
                return {
                    roleId: '',
                    roleName: '',
                    roleDesc: '',
                    belongOrgIds: ''
                };
            }
        },
        // 权限树数据
        dataPermission: {
            type: Array,
            default() {
                return [];
            }
        },
        // 是否查看
        isView: {
            type: Boolean,
            default: false
        },
        /**
         * 是否显示弹框
         */
        show: {
            type: Boolean,
            default: false
        },
        // 当前选中的节点数组
        checkedKeys: {
            type: Array,
            default() {
                return [];
            }
        },
        orgList: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            // 控制树状态更新
            showTree: true,
            // 是否展开
            isExpandAll: true,
            // 是否全选
            isCheckedAll: false,
            // 是否父子联动
            isLinkage: false,
            // 搜索树
            filterText: '',
            defaultProps: {
                children: 'childrenPermissions',
                label: 'permissionName'
            },
            // 部门dialog窗口
            dialogOrgInfo: {
                // 用户 - 调整组织机构
                orgTagList: [],
                isShow: false
            },
            // 组织机构树，数据源。
            orgTreeData: [],
            // 保留不可删除的部门
            reserveOrgs: [],
            // 当前选中的节点
            currentCheckedKeys: [],
            // 标识是否触发了复选框操作
            isTrgger: false
        };
    },
    computed: {
        // 是否编辑模式
        isEdit: {
            get() {
                return this.edit;
            },
            set(val) {
                this.$emit('update:edit', val);
            }
        },
        rules() {
            return {
                roleName: [
                    {
                        required: true,
                        message:
                            this.$t(
                                'systemManagement.role.message.placeholderPre'
                            ) +
                            this.$t(
                                'systemManagement.role.columnName.roleName'
                            ),
                        trigger: ['blur', 'change']
                    },
                    {
                        validator: (rule, value, callback) => {
                            const str = value;
                            let count = 0;
                            for (let i = 0; i < str.length; i++) {
                                const c = str.charAt(i);
                                // 检测如果是汉字，字节数加1
                                if (/^[\u0000-\u00ff]$/.test(c)) {
                                    count += 1;
                                } else {
                                    count += 2;
                                }
                            }
                            // 如果字节数超过20
                            if (count > 20) {
                                callback(
                                    new Error(
                                        this.$t(
                                            'systemManagement.role.roleNameTip'
                                        )
                                    )
                                );
                            } else {
                                callback();
                            }
                        },
                        trigger: ['blur', 'change']
                    }
                ],
                belongOrgIds: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (this.dialogOrgInfo.orgTagList.length > 0) {
                                callback();
                            } else {
                                return callback(
                                    new Error(
                                        this.$t(
                                            'systemManagement.organization.dialog.orgError'
                                        )
                                    )
                                );
                            }
                        }
                    }
                ]
            };
        },
        dialogVisible: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    mounted() {
        // 如果是查看状态或编辑直接赋值，否则赋值默认的组织机构
        if (this.isView || this.isEdit) {
            this.dialogOrgInfo.orgTagList = this.orgList;
        } else {
            // 清空所属机构
            this.getDefaultOrg();
        }
        // 调用组织机构树接口
        this.getOrgTree();
    },
    methods: {
        // 调用接口保存数据
        postSave() {
            let parms = this.getSaveRoleRequest();
            if (this.isEdit) {
                parms = Object.assign(
                    { roleId: this.formDataInfo.roleId },
                    parms
                );
                this.$service.systemManagement
                    .postUpdateRole(parms)
                    .then((response) => {
                        this.doResponse(response);
                    });
            } else {
                this.$service.systemManagement
                    .postAddRole(parms)
                    .then((response) => {
                        this.doResponse(response);
                    });
            }
        },
        // 获取保存角色的请求参数
        getSaveRoleRequest() {
            this.formDataInfo.belongOrgIds = this.dialogOrgInfo.orgTagList
                .map((item) => item.orgId)
                .join(',');
            const currentKeys = this.currentCheckedKeys;
            let permissionIds = [];
            // 解决直接点开编辑没做操作直接点击保存的情况（将之前打开的id传过去）
            if (!this.isTrgger) {
                permissionIds = this.checkedKeys;
            } else {
                permissionIds = currentKeys.map((item) => item.permissionId);
            }
            // 当前选中的权限
            const requestData = Object.assign(
                { permissionIds },
                this.formDataInfo
            );

            return requestData;
        },
        // 组织机构选择弹窗
        showOrgTransfer() {
            if (!this.isView) {
                this.dialogOrgInfo.isShow = true;
            }
        },
        // 获取机构树
        getOrgTree() {
            this.treeLoading = true;
            const data = {
                // 返回的组织机构层级 0：全部 1：根节点下一级 2：根节点下两级
                orgLevel: 0
            };
            this.$service.systemManagement
                .getOrgTree(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.orgTreeData = res.body;
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch(() => {
                    this.treeLoading = false;
                });
        },
        // 获取默认所属机构
        getDefaultOrg() {
            this.$service.systemManagement
                .getCurrentOrgs({})
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.dialogOrgInfo.orgTagList = JSON.parse(
                            JSON.stringify(response.body)
                        );
                        this.reserveOrgs = response.body.filter((item) => {
                            return item.canRemove === 'false';
                        });
                    }
                });
        },
        // 移除已选的某个所属机构
        removeOrgTag(tag) {
            if (!this.isView) {
                const idx = this.dialogOrgInfo.orgTagList.indexOf(tag);
                this.dialogOrgInfo.orgTagList.splice(idx, 1);
            }
        },
        /**
         * 关闭弹框
         */
        close() {
            this.$refs.dataForm.clearValidate();
            this.$refs.tree.setCheckedNodes([]);
            this.isExpandAll = true;
            this.isCheckedAll = false;
            this.isLinkage = false;
            this.dialogVisible = false;
        },
        /**
         * 过滤树
         * @param {*} value 过滤的关键字
         * @param {*} data  过滤的数据
         * @returns {Boolean} 返回符合条件的数据
         */
        filterNode(value, data) {
            if (!value) {
                return true;
            }
            return (data.permissionName || '').includes(value.trim());
        },
        /**
         * 当展开的复选框切换时更新树的状态
         */
        onExpandAllChange() {
            // 记录下当前选中的所有数据
            const nodes = this.$refs.tree.getCheckedNodes();
            this.showTree = false;
            setTimeout(() => {
                this.showTree = true;
                this.$nextTick(() => {
                    this.$refs.tree.setCheckedNodes(nodes);
                    // 重新复原过滤
                    this.$refs.tree.filter(this.filterText);
                });
            }, 0);
        },
        /**
         * 遍历树
         * @param {*} nodes 数据
         * @param {*} result 数组
         * @returns {Array} 返回数据
         */
        traverse(nodes, result) {
            nodes.forEach((node) => {
                // 将当前节点添加到结果数组中
                result.push(node);
                if (
                    node.childrenPermissions &&
                    node.childrenPermissions.length > 0
                ) {
                    // 递归遍历子节点
                    this.traverse(node.childrenPermissions, result);
                }
            });

            return result;
        },

        /**
         * 控制全选和全不选
         * @param {*} value 值
         */
        onCheckedAllChange(value) {
            // 只要点击了就算触发了
            this.isTrgger = true;
            if (value) {
                const result = [];
                // 将data中children都放到一个数组中进行选中
                const nodes = this.traverse(this.dataPermission, result);
                this.currentCheckedKeys = nodes;
                this.$refs.tree.setCheckedNodes(nodes);
            } else {
                this.currentCheckedKeys = [];
                this.$refs.tree.setCheckedNodes([]);
            }
        },
        /**
         * 复选框发生变化时的回调
         * @param {*} node 节点 data
         * @param {*} data} 数据 data
         */
        getCheckedNodes(node, data) {
            this.isTrgger = true;
            // 获取当前选中的节点
            this.currentCheckedKeys = data.checkedNodes;
        }
    }
};
</script>
  
  <style lang="scss" scoped>
.center {
    text-align: center;
}
// 调整返回的样式
.detail-view {
    .detail-back {
        // 发票详情的样式
        display: flex; // 设置flex布局
        border-bottom: 1px solid #ececec; // 底部外边框
        padding-bottom: 20px; // 底部内边距
        .back {
            color: #5584fb; // 颜色
            cursor: pointer; // 悬浮出现小手
        }
        .title {
            font-size: 14px; // 字体
            font-weight: bold; // 加粗
            color: #000000; // 颜色
        }
    }
    .detail-context {
        padding-bottom: 20px; // 下内边距
        .header {
            margin-top: 20px;
        }
    }
}
.org-container {
    border: 1px solid #dfe4ed;
    border-radius: 4px;
    padding-left: 15px;
    height: 40px;
    overflow-y: auto;

    .el-tag {
        margin: 5px;
    }
    &:empty {
        &:before {
            content: attr(placeholder);
            color: #bbb;
        }
    }
    &:hover,
    &:focus {
        border: 1px solid #3370ff;
        &:empty {
            &:before {
                content: attr(placeholder);
                color: #bbb;
            }
        }
    }
}
.checkbox-control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0px;
}
.tree-box {
    height: calc(100vh - 350px);
    overflow: auto;
    margin-top: 10px;
}
::v-deep .role-edit-drawer {
    .el-drawer__header {
        margin-bottom: 0px;
        box-sizing: border-box;
        padding: 10px 20px;
        background: #f5f6fa;
        display: block;
        .title-box {
            display: flex;
            align-items: center;
            .blue-line {
                display: inline-block;
                width: 5px;
                height: 15px;
                background: #3370ff;
                margin-right: 5px;
            }
            .title {
                font-size: 16px;
                color: #000000;
            }
        }
    }
    .el-drawer__body {
        box-sizing: border-box;
        padding: 20px;
    }
}
</style>
<style lang="scss">
.disabled-tree {
    .el-tree-node__content {
        pointer-events: none; /* 阻止点击事件 */
        opacity: 0.6; /* 降低透明度以表示禁用 */
    }
}
</style>
  