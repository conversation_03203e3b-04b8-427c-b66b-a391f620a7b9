/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.maintenanceProject.xxx
 */

const state = {
    // 顶部级联组件的值
    maintenanceProjectStore: [],
    // 维护项目头部状态
    maintenanceReportHeader: {
        weeklyId: '',
        weeklyStatus: '待更新',
        shouldUpdate: true,
        activeName: 'demand',
        isPqa: false
    },
    // 当前选中的周报选项
    currentSelectedOption: { weekReportIdGetList: [] }
};

const mutations = {
    CHANGE_MAINTENANCEPROJECT(state, maintenanceProject) {
        state.maintenanceProjectStore = maintenanceProject;
    },
    // 维护项目头部状态相关mutations
    SET_WEEKLY_ID(state, weeklyId) {
        state.maintenanceReportHeader.weeklyId = weeklyId;
    },
    SET_WEEKLY_STATUS(state, weeklyStatus) {
        state.maintenanceReportHeader.weeklyStatus = weeklyStatus;
    },
    SET_SHOULD_UPDATE(state, shouldUpdate) {
        state.maintenanceReportHeader.shouldUpdate = shouldUpdate;
    },
    SET_ACTIVE_NAME(state, activeName) {
        state.maintenanceReportHeader.activeName = activeName;
    },
    SET_IS_PQA(state, isPqa) {
        state.maintenanceReportHeader.isPqa = isPqa;
    },
    // 当前选中的周报选项相关mutations
    SET_CURRENT_SELECTED_OPTION(state, option) {
        state.currentSelectedOption = option;
    }
};

const actions = {
    changeMaintenanceProject({ commit }, maintenanceProject) {
        commit('CHANGE_MAINTENANCEPROJECT', maintenanceProject);
    },
    // 维护项目头部状态相关action
    setWeeklyId({ commit }, weeklyId) {
        commit('SET_WEEKLY_ID', weeklyId);
    },
    setWeeklyStatus({ commit }, weeklyStatus) {
        commit('SET_WEEKLY_STATUS', weeklyStatus);
    },
    setShouldUpdate({ commit }, shouldUpdate) {
        commit('SET_SHOULD_UPDATE', shouldUpdate);
    },
    setActiveName({ commit }, activeName) {
        commit('SET_ACTIVE_NAME', activeName);
    },
    setIsPqa({ commit }, isPqa) {
        commit('SET_IS_PQA', isPqa);
    },
    // 当前选中的周报选项相关action
    setCurrentSelectedOption({ commit }, option) {
        commit('SET_CURRENT_SELECTED_OPTION', option);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
