<template>
    <div>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="我的会议" name="MeetingTaskSearch" lazy>
                    <MyMeetingsQueryList
                        class="query-list"
                    ></MyMeetingsQueryList>
                </el-tab-pane>
                <el-tab-pane label="会议室查询" name="MeetingRoomQuery" lazy>
                    <BookDraggableRoom></BookDraggableRoom>
                </el-tab-pane>
                <el-tab-pane
                    label="参会人员查询"
                    name="JudgeAttendanceSearch"
                    lazy
                >
                    <JudgeAttendanceQuery></JudgeAttendanceQuery>
                </el-tab-pane>
                <el-tab-pane label="会议列表" name="MeetingSearch" lazy>
                    <MeetingSearch></MeetingSearch>
                </el-tab-pane>

                <el-tab-pane label="会议任务" name="MeetingTaskTracker" lazy>
                    <MeetingTaskTrace></MeetingTaskTrace>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import JudgeAttendanceQuery from './components/judgeAttendanceQuery';
import MeetingSearch from './components/meetingSearch';
import MeetingTaskTrace from './components/meetingTaskTrace';
import MyMeetingsQueryList from './components/myMeetings/MyMeetingsQueryList.vue';
import BookDraggableRoom from './components/BookDraggableRoom';
import { getExternalStaffPeople } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MeetingManagement',
    components: {
        JudgeAttendanceQuery,
        MeetingSearch,
        MeetingTaskTrace,
        MyMeetingsQueryList,
        BookDraggableRoom
    },
    data() {
        return {
            activeName: 'MeetingTaskTracker'
        };
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
        getExternalStaffPeople(this);
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: #ffffff;
}
::v-deep #pane-second {
    border: 1px solid #8c8c8c !important;
}
</style>
