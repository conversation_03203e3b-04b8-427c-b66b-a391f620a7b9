export default {
    自动分拣: {
        name: '自动分拣',
        // 按钮坐标
        point: { x: 650, y: 238 },
        crossPoints: [
            { x: 910, y: 250 },
            { x: 960, y: 250 }
        ],

        children: [0, 1, 2].map((num) => {
            const x = 1020;
            const y = 95 + num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'right',
                crossPoints: [
                    { x: 960, y: 130 + num * 120 },
                    { x: 960, y: 130 + num * 120 }
                ]
            };
        })
    },
    自动输送: {
        name: '自动输送',
        point: { x: 490, y: 238 },
        crossPoints: [{ x: 530, y: 430 }],
        children: [0, 1, 2, 3, 4].map((num) => {
            const isOdd = num % 2 !== 0;
            const position = isOdd ? -1 : 1;
            const calcNum = num === 0 ? 0 : Math.round(num / 2);
            const x = 495 + calcNum * 120 * position;
            const y = 480;
            return {
                x,
                y,
                level: 1,
                position: 'bottom',
                crossPoints: [
                    { x: 530 + calcNum * 120 * position, y: y - 50 },
                    { x: 530 + calcNum * 120 * position, y }
                ]
            };
        })
    },
    自动装卸: {
        name: '自动装卸',
        point: { x: 350, y: 228 },
        crossPoints: [{ x: 300, y: 240 }],
        children: [0, 1, 2].map((num) => {
            const x = 150;
            const y = 85 + num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'left',
                crossPoints: [
                    { x: 300, y: 120 + num * 120 },
                    { x: 300, y: 120 + num * 120 }
                ]
            };
        })
    }
};
