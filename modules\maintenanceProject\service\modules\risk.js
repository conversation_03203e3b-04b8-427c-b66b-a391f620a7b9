import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        risk: {
            // 风险列表查询
            getList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/risk/get_risk_list`,
                    method: 'post',
                    loading: false,
                    data
                });
            },
            // 风险详情查询
            getDetail(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/risk/get_risk_detail_info`,
                    method: 'post',
                    data
                });
            },
            // 新建/编辑风险
            update(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/risk/update_or_insert_risk_info `,
                    method: 'post',
                    data
                });
            },
            // 删除风险
            delete(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/risk/delete_risk_info',
                    method: 'post',
                    data
                });
            },
            // 创建禅道任务并关联风险
            createZentaoTask(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/risk/create_pro_task_of_risk`,
                    method: 'post',
                    data
                });
            },
            // 获取新建/编辑风险页面下需求列表下拉菜单选项
            getDemandList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/demand/getDemandListForRisk`,
                    method: 'post',
                    data
                });
            },
            // 获取新建/编辑风险页面下缺陷列表下拉菜单选项
            getDefectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/fault/get_fault_list_of_risk`,
                    method: 'post',
                    data
                });
            },
            // 获取新建/编辑风险页面下订单列表下拉菜单选项
            getOrderList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/order/getOrderListByRisk`,
                    method: 'get',
                    params: data
                });
            }
        }
    };

    return service;
};
