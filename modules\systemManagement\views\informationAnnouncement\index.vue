<template>
    <div class="view">
        <div v-if="!isShowDetail && !isShowAdd" class="container">
            <div class="filter">
                <div class="header">
                    {{ $t("systemManagement.filterTitle") }}
                </div>
                <el-form ref="informationItem" :model="queryInfo" label-width="150px">
                    <el-form-item :label="$t('systemManagement.informationAnnounce.title')" prop="msgTitle">
                        <el-input v-model="queryInfo.msgTitle" :placeholder="
                $t(
                  'systemManagement.informationAnnounce.message.inputTheme'
                )
              " @keyup.enter.native="handleSearch" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.informationAnnounce.content')" prop="msgContent">
                        <el-input v-model="queryInfo.msgContent" :placeholder="
                $t(
                  'systemManagement.informationAnnounce.message.inputContent'
                )
              " @keyup.enter.native="handleSearch" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.informationAnnounce.publisher')" prop="senderName">
                        <el-input v-model="queryInfo.senderName" :placeholder="
                $t(
                  'systemManagement.informationAnnounce.message.inputPublisher'
                )
              " @keyup.enter.native="handleSearch" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.informationAnnounce.timeRange')">
                        <el-tooltip :content="queyrDateText" placement="bottom" :disabled="queyrDateTooltipDisabled">
                            <el-date-picker v-model="queryDate" unlink-panels :placeholder="
                  $t('systemManagement.informationAnnounce.timeRange')
                " type="datetimerange" :start-placeholder="
                  $t('systemManagement.informationAnnounce.startDate')
                " :end-placeholder="
                  $t('systemManagement.informationAnnounce.endDate')
                " :default-time="['00:00:00', '23:59:59']" />
                        </el-tooltip>
                    </el-form-item>

                    <el-form-item :label="$t('systemManagement.informationAnnounce.publishStatus')" prop="releaseStatus">
                        <el-select v-model="queryInfo.releaseStatus" clearable :popper-append-to-body="false" :placeholder="$t('systemManagement.informationAnnounce.message.inputPublishStatus')">
                            <el-option v-for="item in optionStatus" :key="item.valueId" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                        </el-select>
                    </el-form-item>
                    <div class="filter__btns">
                        <el-button type="primary" size="normal" icon="fa fa-search" class="margin-left" @click="handleSearch">{{
              $t("systemManagement.logger.query")
            }}</el-button>
                        <el-button size="normal" icon="fa fa-undo" class="margin-left" @click="resetForm">{{
              $t("systemManagement.logger.reset")
            }}</el-button>
                    </div>
                </el-form>
            </div>

            <div class="btn-groups">
                <div class="btns">
                    <el-button type="primary" @click="addNoticeAction">{{ $t("systemManagement.informationAnnounce.add") }}</el-button>
                    <el-button type="primary" @click="handleDeleteItems">{{ $t("systemManagement.informationAnnounce.delete") }}</el-button>
                </div>
            </div>
            <el-table id="configurationTable" :data="dataList" border :header-cell-style="{ background: '#F5F6FA' }" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column :label="$t('systemManagement.informationAnnounce.columnName.number')" type="index" width="100px">
                    <template slot-scope="scope">
                        {{ queryInfo.pageSize* (queryInfo.pageNum - 1) + scope.$index+1 }}
                    </template>
                </el-table-column>
                <el-table-column prop="messageTitle" :label="$t('systemManagement.informationAnnounce.columnName.title')">
                    <template slot-scope="{row}">
                        <span class="column-title" @click="goToDetail(row)">{{ row.messageTitle }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="senderName" :label="$t('systemManagement.informationAnnounce.columnName.publisher')" />
                <el-table-column prop="messageType" :label="$t('systemManagement.informationAnnounce.columnName.class')" width="160px">
                    <template slot-scope="{ row }">
                        {{ $t('systemManagement.dictionaryManagement.value.' + row.messageType) }}
                    </template>
                </el-table-column>
                <el-table-column prop="releaseTime" :label="$t('systemManagement.informationAnnounce.columnName.publishTime')" width="160px" />
                <el-table-column prop="readingTimes" :label="$t('systemManagement.informationAnnounce.columnName.readTimes')" width="160px" />
                <el-table-column prop="releaseStatus" :label="$t('systemManagement.informationAnnounce.columnName.status')" width="160px">
                    <template slot-scope="{ row }">
                        {{ $t('systemManagement.dictionaryManagement.value.' + row.releaseStatus) }}
                    </template>
                </el-table-column>

                <el-table-column :label="$t('systemManagement.informationAnnounce.columnName.operateType')" width="280px">
                    <template slot-scope="{ row }">
                        <el-button v-if="row.releaseStatus === 'unpublished'" type="primary" icon="fa fa-pencil" size="mini" class="button table-inner-button" @click="handleEdit(row)">
                            {{ $t("table.edit") }}
                        </el-button>
                        <el-button v-if="row.releaseStatus === 'unpublished'" type="primary" size="mini" class="button table-inner-button" @click="handleRelease(row)">
                            <i class="fa fa-paper-plane-o" aria-hidden="true" />
                            {{ $t('productService.serviceManage.publishNew') }}
                        </el-button>
                        <el-button size="mini" icon="fa fa-trash-o" class="button table-delete-button" @click="handleDelete(row)">
                            {{ $t("table.delete") }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination background for="pagination" :current-page.sync="queryInfo.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size.sync="queryInfo.pageSize" layout="total, prev, pager, next,sizes,  jumper" :total="totalCount" class="right" @size-change="getList" @current-change="getList" />
        </div>
        <add-information-notice v-if="!isShowDetail && isShowAdd" :show.sync="isShowAdd" :notice-form="noticeForm" @goBack="goBack" />
        <detail v-if="isShowDetail" :notice-form="noticeForm" @handleDelete="handleDelete" @goBack="goBack" />
    </div>

</template>

<script>
import addInformationNotice from './components/AddInformationNotice';
import detail from './components/detail';

export default {
    name: 'InformationAnnouncement',
    components: {
        addInformationNotice,
        detail
    },
    data() {
        return {
            isShowAdd: false,
            isShowDetail: false,
            // 查询表单数据
            queryInfo: {
                msgTitle: '',
                msgContent: '',
                senderName: '',
                releaseStatus: '',
                pageSize: 10,
                pageNum: 1,
                startTime: '',
                endTime: ''
            },
            noticeForm: {
                msgTitle: '',
                msgModel: '',
                msgContent: '',
                sysCode: 'bbpfboss',
                fileUrl: [],
                orgs: [],
                users: [],
                range: []
            },
            totalCount: 0,
            optionStatus: [],
            isShowSelect: false,
            // 列表数据源
            dataList: [],
            // 列表选中项
            multipleSelection: []
        };
    },
    computed: {
        // 查询的时间范围
        queryDate: {
            get() {
                // 将开始时间、结束时间，组装为组件所需要的格式
                return [this.queryInfo.startTime, this.queryInfo.endTime];
            },
            set(val) {
                // 如果值无效，则将查询条件时间清空
                if (!val) {
                    this.queryInfo.startTime = '';
                    this.queryInfo.endTime = '';
                    return;
                }
                // 调用框架工具函数处理时间格式
                this.queryInfo.startTime = this.$tools.parseTime(
                    val[0],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
                this.queryInfo.endTime = this.$tools.parseTime(
                    val[1],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
            }
        },
        // 用于时间框较短，时间显示不全时。tooltip的文本值
        queyrDateText() {
            return `${this.queryInfo.startTime}-${this.queryInfo.endTime}`;
        },
        // 如果时间为空，则tooltip显示无效。这种常见下，直接禁用tooltip
        queyrDateTooltipDisabled() {
            return !this.queryInfo.startTime && !this.queryInfo.endTime;
        }
    },
    created() {
        this.currentColumnData = this.columnData;
        this.getList();
        this.getReleaseStatus();
    },
    methods: {
        // 处理开始时间、和结束时间，时分秒部分
        formatTime(time, flag) {
            const date = new Date(time);
            const year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            // 月份自动添加0前缀
            if (month < 10) {
                month = `0${month}`;
            }
            // 天的日期自动添加前缀
            if (day < 10) {
                day = `0${day}`;
            }
            let str = '';
            if (flag === 'start') {
                // 开始时间，时分秒部分
                str = ' 00:00:00';
            } else {
                // 结束时间，时分秒部分
                str = ' 23:59:59';
            }
            return `${year}-${month}-${day}${str}`;
        },
        // 获取列表数据
        getList() {
            this.$service.systemManagement
                .getInformationList(this.queryInfo)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.dataList = response.body.list;
                        // 分页与服务器保持一致
                        this.totalCount = response.body.total;
                        this.queryInfo.pageNum = response.body.pageNum;
                        this.queryInfo.pageSize = response.body.pageSize;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 获取发布状态
        getReleaseStatus() {
            const param = {
                dictTypeCode: 'release_type'
            };
            this.$service.systemManagement
                .getMessageDictValueList(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.optionStatus = response.body;
                    }
                });
        },
        // 返回
        goBack(flag) {
            this.isShowAdd = false;
            this.isShowDetail = false;
            // 如果是新增或编辑调用的，刷新列表
            if (flag) {
                this.getList();
            }
        },
        // 新增信息公告
        addNoticeAction() {
            this.isShowAdd = true;
            this.noticeForm = {
                msgTitle: '',
                msgModel: '',
                msgContent: '',
                sysCode: 'bbpfboss',
                fileUrl: [],
                orgs: [],
                users: [],
                range: []
            };
        },
        // 用户列表check选择
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // 查询按钮事件
        handleSearch() {
            this.getList();
        },
        // 重置按钮事件，将查询表单内容重置
        resetForm() {
            this.$refs.informationItem.resetFields();
            this.queryInfo.startTime = '';
            this.queryInfo.endTime = '';
        },
        // 调用删除接口
        deleteItem(param, flag) {
            this.$service.systemManagement
                .deleteInformation(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.$message({
                            message: this.$t(
                                'systemManagement.informationAnnounce.message.deleteSuccess'
                            ),
                            type: 'success'
                        });
                        this.getList();
                        if (flag) {
                            this.isShowDetail = false;
                        }
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 单个删除
        handleDelete(row) {
            const _this = this;
            this.$confirm(
                this.$t(
                    'systemManagement.informationAnnounce.message.deleteItem'
                ),
                this.$t(
                    'systemManagement.informationAnnounce.message.deleteTitle'
                ),
                {
                    type: 'warning'
                }
            )
                .then(() => {
                    if (row.flag) {
                        _this.deleteItem({ msgIds: [row.msgId] }, row.flag);
                    } else {
                        _this.deleteItem({ msgIds: [row.msgId] });
                    }
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: this.$t('systemManagement.msg.canceled')
                    });
                });
        },
        // 批量删除
        handleDeleteItems() {
            const _this = this;
            let msgIds = '';
            if (this.multipleSelection.length > 0) {
                msgIds = this.multipleSelection.map((item) => item.msgId);
            }
            if (msgIds) {
                this.$confirm(
                    this.$t(
                        'systemManagement.informationAnnounce.message.deleteItems'
                    ),
                    this.$t(
                        'systemManagement.informationAnnounce.message.deleteTitle'
                    ),
                    {
                        type: 'warning'
                    }
                )
                    .then(() => {
                        _this.deleteItem({ msgIds });
                    })
                    .catch(() => {
                        this.$message({
                            type: 'info',
                            message: this.$t('systemManagement.msg.canceled')
                        });
                    });
            } else {
                this.$message({
                    message: this.$t(
                        'systemManagement.informationAnnounce.message.toDeleteItems'
                    ),
                    type: 'warning'
                });
            }
        },
        // 编辑
        handleEdit(row) {
            const param = { msgId: row.msgId };
            // 获取详情
            this.$service.systemManagement
                .informationDetail(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.noticeForm = response.body;
                        this.noticeForm.msgId = row.msgId;
                        this.noticeForm.range = this.noticeForm.orgs.concat(
                            this.noticeForm.users
                        );
                        // 进入编辑页面
                        this.isShowAdd = true;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 发布信息公告
        handleRelease(row) {
            const _this = this;
            this.$confirm(
                this.$t(
                    'systemManagement.informationAnnounce.message.releaseBefore'
                ) +
                    row.messageTitle +
                    this.$t(
                        'systemManagement.informationAnnounce.message.releaseBack'
                    ),
                this.$t(
                    'systemManagement.informationAnnounce.message.releaseTitle'
                ),
                {
                    type: 'warning'
                }
            )
                .then(() => {
                    _this.$service.systemManagement
                        .publishInformation({ msgId: row.msgId })
                        .then((response) => {
                            if (response.head.code === '000000') {
                                this.$message({
                                    message: this.$t(
                                        'systemManagement.informationAnnounce.message.releaseSuccess'
                                    ),
                                    type: 'success'
                                });
                                this.getList();
                            } else {
                                const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error'
                                });
                            }
                        });
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: this.$t('systemManagement.msg.canceled')
                    });
                });
        },
        // 查看详情
        goToDetail(row) {
            const param = { msgId: row.msgId };
            // 获取详情
            this.$service.systemManagement
                .informationDetail(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.noticeForm = response.body;
                        this.noticeForm.msgId = row.msgId;
                        // 进入查看页面
                        this.isShowDetail = true;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        }
    }
};
</script>

<style lang="scss" scoped>
.btn-groups {
    margin-bottom: 10px;
    overflow: hidden;
    .btns {
        float: right;
    }
}
.column-title {
    color: #3370ff;
    cursor: pointer;
}
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.btn-box {
    display: flex;
}
.operate-btn {
    span {
        color: #3370ff;
        margin: 0 5px;
        cursor: pointer;
    }
}
</style>

