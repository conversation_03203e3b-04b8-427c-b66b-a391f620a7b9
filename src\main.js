import 'core-js';
import 'babel-polyfill';
import Vue from 'vue';

// 富文本编辑器
import VueQuillEditor from 'vue-quill-editor';
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';

import './styles/wtf-variables.scss';

import WtfCoreVue from 'wtf-core-vue';
import store from '../node_modules/wtf-core-vue/src/store/index.js';
import settings from './settings.js';
import './global.scss';
import ElementUI from 'element-ui';
import { expandTools } from './tools.js';

expandTools();

Vue.use(VueQuillEditor);

// eslint-disable-next-line no-useless-escape
const modulesFiles = require.context('../modules', true, /\module_index.js$/);
// 引入BASE64解码
const { Base64 } = require('js-base64');
// eslint-disable-next-line no-shadow
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    // set './app.js' => 'app'
    const moduleName = modulePath.replace(/^\.\/(.*)\/module_index.\w+$/, '$1');
    const value = modulesFiles(modulePath);
    modules[moduleName] = value.default;
    return modules;
}, {});

// 获得模块排序数组
const { modulesSortList } = settings;
if (modulesSortList && modulesSortList.length > 0) {
    // 如果配置了模块排序数组，则按照模块排序数组加载
    for (let i = 0; i < modulesSortList.length; i++) {
        if (modules[modulesSortList[i]]) {
            WtfCoreVue.add(modules[modulesSortList[i]]);
        }
    }
    // 未配置模块排序数组，则把所有模块对象都进行加载
} else {
    // eslint-disable-next-line guard-for-in
    for (const moduleName in modules) {
        WtfCoreVue.add(modules[moduleName]);
    }
}

// ====设置element组件全局默认属性值===============
// 弹窗组件--设置点击背景不允许关闭
ElementUI.Dialog.props.closeOnClickModal.default = false;
ElementUI.Tooltip.props.openDelay.default = 300;
ElementUI.Tooltip.props.transition.default = '';
// 弹窗组件--设置不允许滚动(否则出现滚动条时会闪烁)
ElementUI.Dialog.props.lockScroll.default = false;

// 修改element ui的size
store.dispatch('app/setSize', 'mini');
// 创建实例
const appElement = document.createElement('div');
const app = new WtfCoreVue({
    settings,
    Base64
});
// 挂载实例时更新size
Vue.prototype.$ELEMENT.size = 'mini';
app.$mount(appElement);
document.body.appendChild(app.$el);
