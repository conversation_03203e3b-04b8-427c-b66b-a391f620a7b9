<template>
    <div class="expense-container">
        <div class="flex info-container">
            <el-date-picker
                v-model="year"
                value-format="yyyy"
                type="year"
                placeholder="选择年"
                format="yyyy自然年"
                :picker-options="{
                    disabledDate(time) {
                        return (
                            time.getTime() < new Date('2024-01-01').getTime()
                        );
                    }
                }"
                :clearable="false"
                @input="handleYearChange"
                :disabled="!productLine"
            >
            </el-date-picker>
            <div class="info">
                当前支出总计：
                <span class="fee">¥ {{ spendSummary }}</span>
                万
            </div>
        </div>
        <SnbcBaseTable class="table" ref="tableRef" :table-config="tableConfig">
            <template #cost="{ row }">
                ￥{{
                    row.calculateAmount.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })
                }}
            </template>
            <template #flowName="{ row }">
                <div class="flow-name">
                    <el-link
                        v-if="row.flowName === '人工周费用核算'"
                        type="primary"
                        @click="handleClick('人工周费用核算', row.flowId, row)"
                        >{{ row.flowName }}</el-link
                    >
                    <el-link
                        v-else-if="row.flowName === '样机试制备料申请'"
                        type="primary"
                        @click="
                            handleClick('样机试制备料申请', row.flowId, row)
                        "
                        >{{ row.flowName }}</el-link
                    >
                    <el-link
                        v-else-if="row.flowName === '内部样机转销售扣减'"
                        type="primary"
                        @click="
                            handleClick('内部样机转销售扣减', row.flowId, row)
                        "
                        >{{ row.flowName }}</el-link
                    >
                    <span v-else> {{ row.flowName }}</span>
                    <el-tooltip
                        effect="dark"
                        :content="row.flowId"
                        placement="top"
                    >
                        <svg-icon
                            icon-class="maintenanceProject-flowId"
                            class="flowId-flag"
                        ></svg-icon>
                    </el-tooltip>
                </div>
            </template>
        </SnbcBaseTable>
        <EmpolyeeCostDetail
            :visible.sync="employeeVisble"
            :data="employeeData"
            @employee-cost-confirm="handleConfirm('人工周费用核算')"
        ></EmpolyeeCostDetail>
        <SampleCostDetail
            :visible.sync="sampleVisble"
            :data="sampleData"
            @sample-cost-confirm="handleConfirm"
        ></SampleCostDetail>
    </div>
</template>
<script>
import { getTableConfig } from './tableConfig';
import EmpolyeeCostDetail from './EmpolyeeCostDetail.vue';
import SampleCostDetail from './SampleCostDetail.vue';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import moment from 'moment';

export default {
    name: 'ExpenseDetails',
    components: { SnbcBaseTable, EmpolyeeCostDetail, SampleCostDetail },
    props: {
        productLine: {
            type: String,
            default: ''
        },
        subProductLine: {
            type: String,
            default: ''
        },
        projectManager: {
            type: String,
            default: ''
        },
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 列表各项配置
            tableConfig: getTableConfig(this),
            // 人工费弹窗
            employeeVisble: false,
            // 样式试制弹窗
            sampleVisble: false,
            employeeData: {},
            sampleData: {},
            // 当前弹窗的流程ID
            curFlowId: '',
            curRow: {},
            // 支出总计
            spendSummary: '0.00',
            // 当前年份
            year: String(new Date().getFullYear()),
            // 上一次选择的年份，用于项目经理不匹配时的回退
            lastSelectYear: String(new Date().getFullYear()),
            loading: false
        };
    },
    watch: {
        productLine(newVal) {
            if (this.activeName === 'expenseDetails') {
                this.handleQuery();
            }
        },
        subProductLine(newVal) {
            if (this.activeName === 'expenseDetails') {
                this.handleQuery();
            }
        },
        projectManager(newVal) {
            if (this.activeName === 'expenseDetails') {
                this.handleQuery();
            }
        },
        activeName(newVal) {
            if (newVal === 'expenseDetails') {
                this.handleQuery();
            }
        }
    },
    mounted() {
        this.getQueryOptions('费用科目');
        this.getQueryOptions('费用流程简称');
        this.handleQuery();
    },
    methods: {
        /**
         * 查询
         */
        handleQuery() {
            if (!this.productLine) {
                this.$message.warning('请选择产品线或产品经理后查询');
                return;
            }
            this.getOverallCost();
            this.$refs.tableRef.handleQuery();
        },
        /**
         * 排序前的钩子
         * @param {Object} params 参数
         */
        sortChangeHook(params) {
            // 0:默认排序（申请日期的倒序）1：费用升序 2：费用降序
            const { order: sortOrder } = params;
            const order = sortOrder === 'ascending' ? 1 : 2;
            this.tableConfig.queryParams.sortStr = order;
        },
        /**
         * 查询前的钩子
         * @param {Object} params 参数
         */
        queryParamsHook(params) {
            const { calculateDateRange } = params;
            if (calculateDateRange.length > 0) {
                params.applyStartDate = calculateDateRange[0];
                params.applyEndDate = calculateDateRange[1];
            } else {
                // 如果没有选择申请日期，则使用上方选择的一年的范围
                params.applyStartDate = `${this.year}-01-01`;
                params.applyEndDate = `${this.year}-12-31`;
            }
            params.productLine = this.productLine;
            params.subProductLine = this.subProductLine;
            params.projectManager = this.projectManager;
            if (!params.sortKey) {
                // 这里排序字段默认就是0
                params.sortStr = 0;
            }
        },
        /**
         * 获取下拉列表选项
         * @param {String} type 哪种option
         */
        async getQueryOptions(type) {
            try {
                const api = this.$service.project.finance.getSelectOptions;
                const res = await api({ paramName: type, paramType: '' });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 处理选项并去重
                const options = res.body
                    .map((i) => {
                        return {
                            value: i.paramName,
                            label: i.paramName
                        };
                    })
                    .filter(
                        (item, index, self) =>
                            index ===
                            self.findIndex((t) => t.value === item.value)
                    );
                if (type === '费用科目') {
                    this.tableConfig.queryConfig.items[1].elOptions = options;
                } else if (type === '费用流程简称') {
                    this.tableConfig.queryConfig.items[0].elOptions = options;
                }
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 处理流程的点击，显示弹窗
         * @param {String} type 类型
         * @param {String} flowId 流程id
         * @param {Object} row 行数据
         */
        async handleClick(type, flowId, row) {
            // 查扣减信息
            try {
                this.curRow = row;
                this.curFlowId = flowId;
                let api;
                if (type === '人工周费用核算') {
                    api = this.$service.project.finance.getEmpolyeeCostByWeek;
                } else {
                    api = this.$service.project.finance.getSampleCostInfo;
                }
                const res = await api({ flowId });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                if (type === '人工周费用核算') {
                    this.employeeData = {
                        projectName: row.projectName,
                        projectId: this.projectId,
                        ...res.body
                    };
                    this.employeeVisble = true;
                } else {
                    this.sampleVisble = true;
                    this.sampleData = {
                        projectName: row?.projectName,
                        title: row?.flowName,
                        ...res.body
                    };
                }
            } catch (err) {
                console.error('Rrror', err);
            }
        },
        /**
         * 确认操作
         * @param {Object} data 数据
         */
        async handleConfirm(data) {
            try {
                let api;
                let params;
                if (data === '人工周费用核算') {
                    params = { flowId: this.curFlowId };
                    api = this.$service.project.finance.confirmEmoloyeeCost;
                } else {
                    params = {
                        ...data
                    };
                    api = this.$service.project.finance.confirmSampleCost;
                }
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('提交成功');
                this.handleQuery();
                this.employeeVisble = false;
                this.sampleVisble = false;
            } catch (err) {
                console.error('Rrror', err);
            }
        },
        /**
         * 人工费用导出（各阶段的人工费）
         */
        async handleEmployeeFeeExport() {
            if (!this.projectId) {
                this.$message.warning('请选择项目');
                return;
            }
            try {
                const api = this.$service.project.finance.exportEmployeeFee;
                const res = await api({
                    projectId: this.projectId
                });
                // 解析响应blob流，如果是json格式，则提示消息
                if (res && res?.type.toLowerCase().includes('json')) {
                    // 这里是用于读取响应内容
                    const reader = new FileReader();
                    // 异步读取响应内容结果
                    reader.onload = () => {
                        const response = JSON.parse(reader.result);
                        this.$message.error(response.head.message);
                    };
                    // 调用响应方法，开始读取响应的blob内容
                    reader.readAsText(res, 'utf-8');
                    return;
                }
                this.$tools
                    .downloadExprotFile(res, '人工费用数据', 'xlsx')
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 每次年份变更之后就要获取选择项，然后看当前的项目经理是否在未来的选择项中
         * 如果没找到对应的项目经理，给提示，然后返回到上一个选择的年份
         * 如果找到了对应的项目经理，更新上面的级联选择框，清空下面的查询条件，然后正常查询
         */
        async handleYearChange() {
            this.reset();
            if (this.loading) return;
            // 避免产品线/细分产品线/项目经理均变更时导致多次请求
            this.loading = true;
            const api = this.$service.maintenanceProject.header.getProjectList;
            const params = {
                costYear: this.year,
                queryType: this.$route.meta.title
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 上方级联选择器当前选中的值
                const curSelectedVal =
                    this.$store.state.maintenanceProject
                        .maintenanceProjectStore || [];
                // 检查当前value是否在新的options中存在
                const isValid = this.checkValueInOptions(
                    res.body,
                    curSelectedVal
                );
                if (!isValid) {
                    this.$message.warning('年份与项目经理不匹配，请重新选择');
                    // 不匹配，恢复到上一次选择的年份，同时不查询
                    this.year = this.lastSelectYear;
                    return;
                }
                // 如果值有效，向上传递选中的值，这样父组件会改变key值
                // 让顶部级联选择框重新挂载，从而触发options的变更
                // 限定只有年份改变的时候，上方级联选择器才会重新渲染
                if (this.year !== this.lastSelectYear) {
                    this.$emit('year-change', this.year);
                }
                this.lastSelectYear = this.year;
                this.handleQuery();
            } catch (error) {
                console.error(error);
            } finally {
                // 年份变更之后，重新限定申请日期选择范围
                this.changeTableConfig();
                // 请求结束，重置loading状态
                this.loading = false;
            }
        },
        /**
         * 递归遍历options，看当前value是否能够找到
         * @param {Object} options 选项
         * @param {String} value 当前选择的值
         * @param {number} [depth=0] 递归深度
         * @returns {Boolean} 当前值是否存在于options中
         */
        checkValueInOptions(options, value, depth = 0) {
            if (!Array.isArray(value)) return false;
            if (depth >= value.length) return true;

            const currentValue = value[depth];
            const matchedOption = options.find(
                // 这里的account是后端返回的数据，其实就是options中的value
                (opt) => opt.account === currentValue
            );

            if (!matchedOption) return false;
            if (!matchedOption.children) return depth === value.length - 1;

            return this.checkValueInOptions(
                matchedOption.children,
                value,
                depth + 1
            );
        },
        /**
         * 处理snbcTable配置变更
         */
        changeTableConfig() {
            const dateComponentConfig = this.tableConfig.queryConfig.items[3];

            // 获取当前年的第一天
            const startOfYear = moment(this.year).startOf('year');
            // 转换为Date对象
            const startDate = startOfYear.toDate();

            // 获取下一年的第一天
            const endOfYear = moment(this.year).add(1, 'year').startOf('year');
            // 转换为Date对象
            const endDate = endOfYear.toDate();

            dateComponentConfig.elDatePickerAttrs['picker-options'] = {
                disabledDate(time) {
                    // 注意这里必须为>=下一年的第一天，否则当前最后一天会无法选择
                    return (
                        time.getTime() < startDate || time.getTime() >= endDate
                    );
                }
            };
        },
        /**
         * 重置查询参数
         */
        reset() {
            this.tableConfig.queryParams = {
                flowName: '',
                costSubject: '',
                flowId: '',
                calculateDateRange: [],
                // 0:默认的排序（申请日期的倒序）1：费用升序 2：费用降序
                sortStr: 0
            };
        },
        /**
         * 获取当前总计支出
         */
        async getOverallCost() {
            try {
                const api = this.$service.maintenanceProject.finance.getOverall;
                const params = {
                    productLine: this.productLine,
                    subProductLine: this.subProductLine,
                    projectManager: this.projectManager,
                    queryYear: this.year
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.spendSummary = Math.trunc(res.body / 100) / 100;
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.expense-container {
    padding-top: 10px;
}
.info-container {
    width: 100%;
    .info {
        line-height: 30px;
        margin-left: 20px;
        font-weight: 600;
        .fee {
            color: #0054ca;
            font-size: 18px;
            margin-right: 2px;
        }
    }
}
.flow-name {
    display: flex;
    align-items: center;
}
.flowId-flag {
    width: 15px;
    height: 15px;
    margin-left: 5px;
    fill: #0064f0;
    padding: 0;
    &:hover {
        cursor: pointer;
        scale: 1.2;
    }
}
// 保证选择框宽度
::v-deep .el-select {
    width: 100%;
}
// 修改排序箭头样式
::v-deep .el-table .ascending .sort-caret.ascending {
    border-bottom-color: #ffdc37;
}
::v-deep .el-table .descending .sort-caret.descending {
    border-top-color: #ffdc37;
}
.table {
    // 设置最小高度，避免下拉框的option显示不全
    min-height: 500px;
}
</style>
