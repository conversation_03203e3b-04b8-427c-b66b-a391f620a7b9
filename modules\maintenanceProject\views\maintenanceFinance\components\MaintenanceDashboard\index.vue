<template>
    <div class="dashboard-container" ref="overallChartBox">
        <div class="flex info-container">
            <el-date-picker
                v-model="year"
                value-format="yyyy"
                type="year"
                placeholder="选择年"
                format="yyyy自然年"
                :picker-options="{
                    disabledDate(time) {
                        return (
                            time.getTime() < new Date('2024-01-01').getTime()
                        );
                    }
                }"
                :clearable="false"
                @input="handleYearChange"
                :disabled="!productLine"
            >
            </el-date-picker>
            <div class="info">
                当前支出总计：
                <span class="fee">¥ {{ spendSummary }}</span>
                万
            </div>
        </div>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">按月份执行情况</div>
                <el-radio-group
                    class="radio-group"
                    v-model="monthChart"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="line">
                        <svg-icon icon-class="lineChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <div
                v-show="monthChart === 'line'"
                class="chart"
                ref="monthBarChart"
            ></div>
            <div
                v-show="monthChart === 'pie'"
                class="chart"
                ref="monthPieChart"
            ></div>
        </el-card>
        <el-card class="card chart-container">
            <div class="flex">
                <div class="chart-box-title">按科目执行情况</div>
                <el-radio-group
                    class="radio-group"
                    v-model="subjectChart"
                    @input="handleRadioClick"
                >
                    <el-radio-button label="line">
                        <svg-icon icon-class="lineChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <div
                v-show="subjectChart === 'line'"
                class="chart"
                ref="subjectBarChart"
            ></div>
            <div
                v-show="subjectChart === 'pie'"
                class="chart"
                ref="subjectPieChart"
            ></div>
        </el-card>
    </div>
</template>

<script>
import {
    getBarChartOptionsByMonth,
    getPieChartOptionsByMonth,
    getBarChartOptionsBySubject,
    getPieChartOptionsBySubject
} from './chartOptions.js';
import * as echarts from 'echarts';
import { debounce } from 'lodash';

export default {
    name: 'MaintenanceDashboard',
    props: {
        activeName: {
            type: String,
            default: ''
        },
        productLine: {
            type: String,
            default: ''
        },
        subProductLine: {
            type: String,
            default: ''
        },
        projectManager: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            year: new Date().getFullYear().toString(),
            // 支出总计
            spendSummary: '0.00',
            // 图表缓存
            charts: {},
            optionsMap: {},
            monthChart: 'line',
            subjectChart: 'line',
            // 费用科目列表
            subjectList: [],
            // 避免重复查询
            loading: false
        };
    },
    watch: {
        activeName(newVal) {
            if (newVal === 'dashboard') {
                this.handleYearChange();
            }
        },
        productLine(newVal) {
            if (this.activeName === 'dashboard') {
                this.handleYearChange();
            }
        },
        subProductLine(newVal) {
            if (this.activeName === 'dashboard') {
                this.handleYearChange();
            }
        },
        projectManager(newVal) {
            if (this.activeName === 'dashboard') {
                this.handleYearChange();
            }
        }
    },
    mounted() {
        this.getQueryOptions('费用科目').then(this.handleYearChange);
    },
    activated() {
        if (this.activeName === 'dashboard') {
            this.handleYearChange();
        }
    },
    methods: {
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const observer = new ResizeObserver(
                debounce((entries) => {
                    this.activeName === 'dashboard' && myChart.resize();
                }, 50)
            );
            observer.observe(this.$refs.overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 切换图表显示时，重新确定图表尺寸
         */
        handleRadioClick() {
            this.$nextTick(() => {
                for (const option in this.optionsMap) {
                    if (
                        Object.prototype.hasOwnProperty.call(
                            this.optionsMap,
                            option
                        )
                    ) {
                        this.charts[option] && this.charts[option].resize();
                    }
                }
            });
        },
        /**
         * 每次年份变更之后就要获取选择项，然后看当前的项目经理是否在未来的选择项中
         * 如果没找到对应的项目经理，给提示，然后返回到上一个选择的年份
         * 如果找到了对应的项目经理，更新上面的级联选择框，清空下面的查询条件，然后正常查询
         */
        async handleYearChange() {
            if (!this.productLine) return;
            if (this.loading) return;
            // 避免产品线/细分产品线/项目经理均变更时导致多次请求
            this.loading = true;
            const api = this.$service.maintenanceProject.header.getProjectList;
            const params = {
                costYear: this.year,
                queryType: this.$route.meta.title
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 上方级联选择器当前选中的值
                const curSelectedVal =
                    this.$store.state.maintenanceProject
                        .maintenanceProjectStore || [];
                // 检查当前value是否在新的options中存在
                const isValid = this.checkValueInOptions(
                    res.body,
                    curSelectedVal
                );
                if (!isValid) {
                    this.$message.warning('年份与项目经理不匹配，请重新选择');
                    // 不匹配，恢复到上一次选择的年份，同时不查询
                    this.year = this.lastSelectYear;
                    return;
                }
                // 如果值有效，向上传递选中的值，这样父组件会改变key值
                // 让顶部级联选择框重新挂载，从而触发options的变更
                // 限定只有年份改变的时候，上方级联选择器才会重新渲染
                if (this.year !== this.lastSelectYear) {
                    this.$emit('year-change', this.year);
                }
                this.lastSelectYear = this.year;
                this.setMonthChart();
                this.setSubjectChart();
                this.getOverallCost();
            } catch (error) {
                console.error(error);
            } finally {
                // 请求结束，重置loading状态
                this.loading = false;
            }
        },
        /**
         * 按月份执行情况
         */
        async setMonthChart() {
            try {
                const api =
                    this.$service.maintenanceProject.finance
                        .getMaintainProjectByMonth;
                const params = {
                    queryYear: this.year.toString(),
                    productLine: this.productLine,
                    subProductLine: this.subProductLine,
                    projectManager: this.projectManager
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.monthBar = getBarChartOptionsByMonth(
                    res.body,
                    this.subjectList
                );
                this.initChart('monthBar');
                this.optionsMap.monthPie = getPieChartOptionsByMonth(res.body);
                this.initChart('monthPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 按科目执行情况
         */
        async setSubjectChart() {
            try {
                const api =
                    this.$service.maintenanceProject.finance
                        .getMaintainProjectBySubject;
                const params = {
                    queryYear: this.year.toString(),
                    productLine: this.productLine,
                    subProductLine: this.subProductLine,
                    projectManager: this.projectManager
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.optionsMap.subjectBar = getBarChartOptionsBySubject(
                    res.body
                );
                this.initChart('subjectBar');
                this.optionsMap.subjectPie = getPieChartOptionsBySubject(
                    res.body
                );
                this.initChart('subjectPie');
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 获取下拉列表选项
         * @param {String} type 哪种option
         * @param {String} value 类型
         * @returns {Promise} 是否成功获取选项
         */
        async getQueryOptions(type, value = '') {
            try {
                const api = this.$service.project.finance.getSelectOptions;
                const res = await api({
                    paramName: type,
                    paramType: value
                });
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    throw new Error(res.head.message);
                }
                // 处理选项
                const options = res.body.map((i) => {
                    return {
                        value: i.paramName,
                        label: i.paramName
                    };
                });

                this.subjectList = options.map((i) => i.value);
            } catch (err) {
                console.error('Error:', err);
                throw err;
            }
        },
        /**
         * 递归遍历options，看当前value是否能够找到
         * @param {Object} options 选项
         * @param {String} value 当前选择的值
         * @param {number} [depth=0] 递归深度
         * @returns {Boolean} 当前值是否存在于options中
         */
        checkValueInOptions(options, value, depth = 0) {
            if (!Array.isArray(value)) return false;
            if (depth >= value.length) return true;

            const currentValue = value[depth];
            const matchedOption = options.find(
                // 这里的account是后端返回的数据，其实就是options中的value
                (opt) => opt.account === currentValue
            );

            if (!matchedOption) return false;
            if (!matchedOption.children) return depth === value.length - 1;

            return this.checkValueInOptions(
                matchedOption.children,
                value,
                depth + 1
            );
        },
        /**
         * 获取当前总计支出
         */
        async getOverallCost() {
            try {
                const api = this.$service.maintenanceProject.finance.getOverall;
                const params = {
                    productLine: this.productLine,
                    subProductLine: this.subProductLine,
                    projectManager: this.projectManager,
                    queryYear: this.year
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.spendSummary = Math.trunc(res.body / 100) / 100;
            } catch (error) {
                console.error(error);
            }
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
.dashboard-container {
    padding-top: 10px;
    .info-container {
        width: 100%;
        .info {
            line-height: 30px;
            margin-left: 20px;
            font-weight: 600;
            .fee {
                color: #0054ca;
                font-size: 18px;
                margin-right: 2px;
            }
        }
    }
    .card {
        margin-top: 10px;
    }
    .last {
        margin-bottom: 15px;
    }
    .chart-container {
        margin-top: 10px;
        width: 100%;
        height: 300px;
        padding: 10px;
        height: 400px;
        .chart-box-title {
            width: 150px;
            height: 30px;
            background-color: #3370ff;
            color: #fff;
            font-weight: 600;
            text-align: center;
            font-size: 12px;
            padding: 8px;
            border-radius: 10px;
        }
        .radio-group {
            margin-left: auto;
            line-height: 30px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin-top: 20px;
        }
        .select-icon {
            width: 15px;
            height: 15px;
        }
        .selector {
            margin-left: 10px;
            margin-top: 2px;
        }
    }
}
</style>
