<template>
    <div>
        <el-card>
            <el-descriptions
                :title="`${meetingInfo.meetingTitle || ''} [ ${
                    meetingInfo.meetingType || ''
                } ]`"
                size="medium"
                border
                :column="12"
            >
                <el-descriptions-item
                    label="评审形式"
                    v-if="
                        meetingInfo.meetingType === '一级TR评审' ||
                        meetingInfo.meetingType === '二级TR评审'
                    "
                    >{{ meetingInfo.organizeForm }}</el-descriptions-item
                >
                <el-descriptions-item label="会议时间">{{
                    meetingTime
                }}</el-descriptions-item>
                <el-descriptions-item label="会议地点">{{
                    meetingInfo.meetingLocation
                }}</el-descriptions-item>
                <el-descriptions-item label="会议组织人">{{
                    organizer
                }}</el-descriptions-item>
                <el-descriptions-item label="会议材料">
                    <span v-if="meetingInfo.materialsGrantFlag === '未发放'"
                        >未发放</span
                    >
                    <span
                        v-else-if="
                            meetingInfo.materialsGrantFlag === '已发放' &&
                            !meetingInfo.meetingMaterials
                        "
                        >已发放</span
                    >
                    <el-link type="primary" v-else
                        ><a
                            :href="meetingInfo.meetingMaterials"
                            noopener
                            noreferrer
                            target="_blank"
                            >查看</a
                        ></el-link
                    ></el-descriptions-item
                >
            </el-descriptions>
        </el-card>
    </div>
</template>

<script>
export default {
    name: 'HeaderInfo',
    props: {
        meetingInfo: {
            type: Object,
            default: () => ({})
        },
        meetingPartRelateList: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        meetingTime() {
            // 线上评审没有结束时间，就显示日期就行，同样时间待定的也没有结束时间，返回空
            if (!this.meetingInfo.endTime) {
                if (this.meetingInfo.startTime) {
                    return this.meetingInfo?.startTime?.split(' ')[0];
                }
                return '';
            }
            const date = this.meetingInfo?.startTime.split(' ')[0];
            const startTime = this.meetingInfo?.startTime.split(' ')[1];
            const endTime = this.meetingInfo?.endTime.split(' ')[1];
            return `${date} ${startTime}-${endTime}`;
        },
        // 会议组织人
        organizer() {
            if (this.meetingPartRelateList.length > 0) {
                return this.meetingPartRelateList.filter(
                    (i) => i.meetingRole === '会议组织人'
                )[0].userName;
            }
            return '';
        }
    }
};
</script>

<style lang="scss" scoped></style>
