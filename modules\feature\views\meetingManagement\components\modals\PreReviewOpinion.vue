<!-- 用于创建/编辑会议 -->
<template>
    <div>
        <el-dialog
            title="会前预审意见"
            :visible.sync="dialogVisible"
            width="85%"
        >
            <HeaderInfo
                :meetingInfo="headerInfo.meetingInfo"
                :meetingPartRelateList="headerInfo.meetingPartRelateList"
            ></HeaderInfo>
            <el-button type="primary" @click="addMinutes" class="add-minutes"
                >新增</el-button
            >
            <el-form ref="form" :model="form" class="minutes-form">
                <el-table
                    :data="form.tableData"
                    class="minutes-table"
                    :row-style="{ background: '#fff' }"
                    empty-text="无预审意见"
                >
                    <el-table-column
                        prop="prop"
                        label="会前预审意见"
                        align="center"
                    >
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="creatorAccount"
                        >
                            <template #header>
                                <RedStar class="required" />提出人
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.creatorAccount`"
                                    :rules="required"
                                >
                                    <PeopleSelector
                                        placeholder=""
                                        v-model="scoped.row.creatorAccount"
                                        :clearable="false"
                                        :isMultipled="false"
                                        :ref="`proposer${scoped.$index}`"
                                        :disabled="
                                            scoped.row.creatorAccount !==
                                                user && !combinedPermission
                                        "
                                        :options="meetingPeopleOptions"
                                    ></PeopleSelector>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="prepContent"
                        >
                            <template #header>
                                <RedStar class="required" />预审意见
                            </template>
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.prepContent`"
                                    :rules="required"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="500"
                                        v-model="scoped.row.prepContent"
                                        :disabled="
                                            scoped.row.creatorAccount !==
                                                user && !combinedPermission
                                        "
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="responsibleAccount"
                            label="责任人"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.responsibleAccount`"
                                >
                                    <PeopleSelector
                                        placeholder=""
                                        v-model="scoped.row.responsibleAccount"
                                        :isMultipled="false"
                                        :ref="`responsible${scoped.$index}`"
                                        :disabled="
                                            scoped.row.creatorAccount !==
                                                user && !combinedPermission
                                        "
                                        @input="
                                            responsibleChange(scoped.$index)
                                        "
                                        :options="meetingPeopleOptions"
                                    ></PeopleSelector>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <!-- 注意这里下面几个的表单校验规则要动态控制 -->
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="finishDate"
                            label="完成时间"
                            width="140"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.finishDate`"
                                >
                                    <el-date-picker
                                        class="date-picker"
                                        type="date"
                                        placeholder=""
                                        v-model="scoped.row.finishDate"
                                        value-format="yyyy-MM-dd"
                                        :disabled="
                                            scoped.row.responsibleAccount !==
                                                user && !combinedPermission
                                        "
                                    ></el-date-picker>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="finishStatus"
                            label="状态"
                            width="200"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.finishStatus`"
                                >
                                    <el-select
                                        v-model="scoped.row.finishStatus"
                                        :disabled="
                                            scoped.row.responsibleAccount !==
                                                user && !combinedPermission
                                        "
                                    >
                                        <el-option
                                            label="进行中"
                                            value="进行中"
                                        ></el-option>
                                        <el-option
                                            label="已完成"
                                            value="已完成"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            prop="finishDesc"
                            label="完成结果"
                        >
                            <template #default="scoped">
                                <el-form-item
                                    :prop="`tableData.${scoped.$index}.finishDesc`"
                                >
                                    <el-input
                                        type="textarea"
                                        :autosize="{ minRows: 3 }"
                                        maxlength="500"
                                        v-model="scoped.row.finishDesc"
                                        :disabled="
                                            scoped.row.responsibleAccount !==
                                                user && !combinedPermission
                                        "
                                    ></el-input>
                                </el-form-item>
                            </template>
                        </el-table-column>
                        <el-table-column
                            header-align="center"
                            align="center"
                            label="操作"
                            width="80"
                            v-if="this.title !== '处理评委反馈预审问题'"
                        >
                            <template #default="scoped">
                                <el-button
                                    type="danger"
                                    @click="
                                        deleteRow(scoped.$index, scoped.row.id)
                                    "
                                    v-show="
                                        scoped.row.creatorAccount === user ||
                                        combinedPermission
                                    "
                                    >删除</el-button
                                >
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-form>
            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';
import HeaderInfo from './HeaderInfo.vue';
import {
    getUserAccount,
    getSelectedLabel,
    getUuid,
    getMeetingPeopleOptions,
    isOrganizerOrWriter
} from '../../commonFunction';
import RedStar from 'feature/components/redStar';

export default {
    name: 'PreReviewOpinion',
    components: { PeopleSelector, HeaderInfo, RedStar },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        meetingId: {
            type: String,
            default: ''
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            CONSTANTS,
            headerInfo: [],
            // 需要删除的意见列表，该数组存储的是待删除项的id
            deletePreviewIdList: [],
            // 会议结论
            meetingConclusion: '',
            form: {
                tableData: []
            },
            required: {
                required: true,
                // 注意这里必填提示是一个空格，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            },
            externalOptions: [],
            // 会议数据管理员专有的编辑权限
            dataManagerPermission:
                this.$store.state.permission.btnDatas.includes(
                    'MeetingDataManagerEditButton'
                )
        };
    },
    computed: {
        // 会议归类
        meetingClass() {
            if (
                this.form.meetingType === '一级TR评审' ||
                this.form.meetingType === '二级TR评审'
            ) {
                return '评审会议';
            }
            return '一般会议';
        },
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 校验时的提示信息
        validMessage() {
            return '请填写必填项';
        },
        // 当前用户
        user() {
            return getUserAccount(this);
        },
        // 由参会人员组成的下拉列表
        meetingPeopleOptions() {
            return getMeetingPeopleOptions(this.headerInfo);
        },
        // 会议纪要是否审核通过
        isMinutesPass() {
            return (
                this.headerInfo.meetingInfo.minutesStatus ===
                    '未关闭（有任务未完成）' ||
                this.headerInfo.meetingInfo.minutesStatus === '全部关闭'
            );
        },
        // 组织者或编写人是否有编辑权限（在恰当的时机）
        isOrganizerOrWriterHasPermission() {
            if (
                this.headerInfo.meetingInfo?.meetingStatus === '已取消' ||
                (this.headerInfo.meetingInfo?.meetingStatus === '结束' &&
                    this.headerInfo.meetingInfo?.minutesStatus === '无纪要')
            ) {
                return false;
            }
            const bool = isOrganizerOrWriter(
                this.headerInfo.meetingPartRelateList
            );
            return bool;
        },
        combinedPermission() {
            return (
                (this.isOrganizerOrWriterHasPermission &&
                    !this.isMinutesPass) ||
                this.dataManagerPermission
            );
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getMeetingInfo();
                this.getOnlineReviewData();
            }
        }
    },
    methods: {
        closeDialog() {
            this.dialogVisible = false;
        },
        save() {
            const data = this.handleInputData();

            // 对整个表单进行校验
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.editPreReviewData(data);
                } else {
                    this.$message.warning(this.validMessage);
                    return false;
                }
            });
        },
        /**
         * 新增一条会议纪要
         */
        addMinutes() {
            const holder = this.headerInfo.meetingPartRelateList.filter((i) => {
                return i.userRole === '主持人';
            });
            this.form.tableData.push({
                creatorAccount: getUserAccount(this),
                prepContent: '',
                responsibleAccount:
                    holder.length > 0 ? holder[0].userAccount : '',
                finishDate: '',
                finishStatus: '',
                finishDesc: '',
                responsibleName: '',
                id: getUuid()
            });
        },
        /**
         * 删除项
         * @param {Number} index 序号
         * @param {String} id 条数的id
         */
        async deleteRow(index, id) {
            await this.$tools.confirm('确定删除吗?');
            this.deletePreviewIdList.push(id);
            this.form.tableData.splice(index, 1);
        },
        /**
         * 获取会前预审意见
         */
        async getOnlineReviewData() {
            const api = this.$service.feature.preReview.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.form.tableData = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 编辑会前预审意见
         * @param {Object} data 数据
         */
        async editPreReviewData(data) {
            const api = this.$service.feature.preReview.edit;
            try {
                const res = await api(data);
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取会议信息
         */
        async getMeetingInfo() {
            const api = this.$service.feature.meeting.getMeetingInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.headerInfo = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 转换用户输入的数据
         * @returns {Object} 转换后的数据
         */
        handleInputData() {
            const user = getUserAccount(this);
            let meetingBeforePreviewList = this.form.tableData;
            // 这个数组存储的是筛选出来的需要传参的数组，在tableData里的每个索引，给getSelectedLabel使用
            let indexArr = [];
            if (!this.combinedPermission) {
                const type =
                    this.title === '反馈预审意见'
                        ? 'creatorAccount'
                        : 'responsibleAccount';
                meetingBeforePreviewList = this.form.tableData.filter(
                    (i, index) => {
                        if (i[type] === user) {
                            indexArr.push(index);
                        }
                        return i[type] === user;
                    }
                );
            } else {
                indexArr = Array.from(
                    { length: meetingBeforePreviewList.length },
                    (item, index) => index
                );
            }

            meetingBeforePreviewList.forEach((i, index) => {
                i.creatorName = getSelectedLabel(
                    this.$refs[`proposer${indexArr[index]}`]
                );

                i.responsibleName = getSelectedLabel(
                    this.$refs[`responsible${indexArr[index]}`]
                );
            });

            const res = {
                deletePreviewIdList: this.deletePreviewIdList,
                meetingBeforePreviewList,
                meetingId: this.meetingId
            };
            return res;
        },
        /**
         * 责任人变更时的函数
         * @param {Number} index 数组索引
         */
        responsibleChange(index) {
            const data = this.form.tableData[index];
            data.finishDate = '';
            data.finishStatus = '';
            data.finishDesc = '';
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.wrap {
    flex-wrap: wrap;
}
.meeting-material {
    margin-bottom: 12px;
}

.meeting-conclusion {
    background-color: white;
    width: 100%;
    color: black;
    padding: 15px 0;
}

.add-minutes {
    float: right;
    margin-top: 15px;
    margin-bottom: 5px;
}

.minutes-form {
    ::v-deep .el-table__row > td {
        padding: 5px;
    }
    ::v-deep .cell {
        padding: 0 !important;
    }
    .el-form-item {
        margin-bottom: 0px;
    }
    ::v-deep .el-form-item__error {
        top: calc(50% - 10px);
        left: 16px;
    }
    ::v-deep .el-form-item__content {
        margin: 0 !important;
    }
    // 令输入框无边框
    ::v-deep .el-input__inner {
        border: none !important;
    }

    // 令输入框无边框
    ::v-deep .el-textarea__inner {
        border: none;
        resize: none;
    }
    // 令鼠标移入之后不变色
    ::v-deep .el-table tbody tr:hover > td {
        background-color: #fff !important;
    }

    .minutes-table {
        border: 1px solid #8c8c8c !important;
    }
}

.date-picker {
    width: 100%;
}

.required {
    margin-right: 2px;
}

::v-deep .meeting-conclusion .el-form-item__label {
    font-weight: bold;
}
::v-deep.form .el-form-item__label {
    font-weight: bold;
    padding-right: 10px;
    width: fit-content;
}
.review-form {
    margin-left: 40px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
