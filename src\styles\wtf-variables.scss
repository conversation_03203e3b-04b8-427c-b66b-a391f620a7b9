/* theme color */
// $--color-primary: #3370ff;
// /* 改变 icon 字体路径变量，必需 */
// $--font-path: '~element-ui/lib/theme-chalk/fonts';
// $--color-success: #13ce66;
// $--color-warning: #ffba00;
// $--color-danger: #ff4949;
// // $--color-info: #1E1E1E !default;

// $--button-font-weight: 400;

// // $--color-text-regular: #1f2d3d;

// $--border-color-light: #dfe4ed ;
// $--border-color-lighter: #e6ebf5;

// $--table-border: 1px solid #dfe6ec;
$sideBarWidth : 170px;

// element-ui 配置样式
@import '~wtf-core-vue/src/styles/element-variables.scss';
// 核心框样式
@import '~wtf-core-vue/src/styles/index.scss';
