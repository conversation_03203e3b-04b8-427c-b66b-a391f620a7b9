import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        common: {
            // 根据产品型号查询产品信息
            getProductInfoByProductModel(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/common/getProductByModel',
                    loading: false,
                    method: 'get',
                    params: query
                });
            },
            // 查询公司静态组织框架信息（一级部门至四级部门）
            getCompanyOrgInfo(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/org-snbc/getOrgSnbc',
                    method: 'get',
                    params: query
                });
            }
        }
    };

    return service;
};
