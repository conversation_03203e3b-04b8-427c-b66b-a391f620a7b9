const getDetail = (scope) => {
    return (row) => {
        scope.$router.push({
            path: '/project/finance',
            query: {
                id: row.projectId,
                productLine: row.productLine
            }
        });
    };
};
const elButtonFn = () => {
    // 去除button的padding，设置max-width，否则tooltip与省略号不会正常显示
    return {
        type: 'text',
        style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align:left; padding:0px!important; max-width:100%;'
    };
};
// eslint-disable-next-line max-lines-per-function
const getTableConfig = (scope) => {
    return {
        // 查询api配置
        queryApi: scope.$service.reportForm.finance.getOverBudgetTable,
        elTableColumns: [
            {
                label: '项目名称',
                prop: 'projectName',
                show: true,
                minWidth: 180,
                renderMode: 'button',
                elButtonAttrsFn: elButtonFn,
                handleClick: getDetail(scope),
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '项目编号',
                prop: 'projectNo',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '产品线',
                prop: 'productLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '细分产品线',
                prop: 'subProductLine',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目预算',
                prop: 'budgetAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'budgetAmount'
            },
            {
                label: '实际支出',
                prop: 'costAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'costAmount'
            },
            {
                label: '超支',
                prop: 'overExpendAmount',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    'sortable': 'custom',
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'right'
                },
                slot: 'overExpendAmount'
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        hooks: {
            sortChangeHook: scope.sortChangeHook
        }
    };
};
export { getTableConfig };
