<template>
    <div class="support-section">
        <div class="support-header">
            <div class="support-title">
                本周需部门支持事项（新增
                <span class="new-count">{{ supportSummary.newCount }}</span>
                项 / 共
                <span style="font-weight: bold">{{
                    supportSummary.totalCount
                }}</span>
                项）
            </div>
            <div class="support-filters">
                <el-radio-group
                    v-model="supplymentType"
                    @change="handleRadioChange"
                >
                    <el-radio label="部门">部门维度</el-radio>
                    <el-radio label="产品线">产品线维度</el-radio>
                </el-radio-group>
            </div>
        </div>

        <!-- 支持卡片网格 -->
        <div
            v-for="(row, rowIndex) in supportCardRows"
            :key="rowIndex"
            class="support-cards"
        >
            <div
                v-for="card in row"
                :key="card.id"
                class="support-card"
                @click="handleSupportCardClick(card)"
            >
                <div class="support-card-title">
                    {{ card.supportDimensionName }}
                </div>
                <div class="support-card-stats">
                    新增
                    <span class="new-count">{{ card.newSupportNo || 0 }}</span>
                    项 / 共 {{ card.sumSupportNo || 0 }} 项
                </div>
            </div>
        </div>

        <SupplymentListDialog
            :visible.sync="supplymentDialogVisible"
            :riskList="riskList"
            :title="title"
        ></SupplymentListDialog>
    </div>
</template>

<script>
import SupplymentListDialog from './SupplymentListDialog';

export default {
    name: 'SupportSection',
    components: {
        SupplymentListDialog
    },
    props: {
        supplymentList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            // raido选项
            supplymentType: '部门',
            supplymentDialogVisible: false,
            // 风险及需支持事项列表
            riskList: [],
            // 当前选择的部门/产品线
            supportDimensionName: ''
        };
    },
    computed: {
        // 将支持卡片按行分组（每行6个）
        supportCardRows() {
            const rows = [];
            for (let i = 0; i < this.supplymentList.length; i += 6) {
                rows.push(this.supplymentList.slice(i, i + 6));
            }
            return rows;
        },
        // 数量总和
        supportSummary() {
            if (this.supplymentList.length === 0)
                return {
                    newCount: 0,
                    totalCount: 0
                };
            // 新增数量
            const newCount = this.supplymentList.reduce((acc, cur) => {
                if (cur.newSupportNo) {
                    return acc + cur.newSupportNo;
                }
                return acc;
            }, 0);
            // 总数量
            const totalCount = this.supplymentList.reduce((acc, cur) => {
                if (cur.sumSupportNo) {
                    return acc + cur.sumSupportNo;
                }
                return acc;
            }, 0);

            return { newCount, totalCount };
        },
        // 选中的选项
        weelyOption() {
            return this.$store.state.reportForm.weeklyOption;
        },
        // 弹窗标题
        title() {
            if (this.supplymentType === '部门') {
                return `本周需【${this.supportDimensionName}】支持事项`;
            }
            return `本周【${this.supportDimensionName}】项目需支持事项`;
        }
    },
    created() {
        this.$emit('radio-change', this.supplymentType);
    },
    methods: {
        /**
         * 切换radio
         */
        handleRadioChange() {
            this.$emit('radio-change', this.supplymentType);
        },
        /**
         * 点击对应的卡片，跳出弹窗展示对应的支持事项列表
         * @param {Object} card 点击的卡片对象
         */
        async handleSupportCardClick(card) {
            if (!card.sumSupportNo) {
                this.$message.warning('无支持事项');
                return;
            }
            // 存入当前选择的产品线/部门
            this.supportDimensionName = card.supportDimensionName;
            const api = this.$service.reportForm.weekly.getSupplymentDetailList;
            const params = {
                // 具体的部门或者产品线
                productLineOrDept: card.supportDimensionName,
                // 选择的维度
                supportDimensionType: this.supplymentType,
                weekNumber: this.weelyOption[0].weekNumber,
                yearVal: this.weelyOption[0].year
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.riskList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
            this.supplymentDialogVisible = true;
        }
    }
};
</script>

<style scoped lang="scss">
.support-section {
    margin-bottom: 30px;
}

.support-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.support-title {
    font-size: 14px;
    color: #333;
    font-weight: 700;
}

.support-filters {
    display: flex;
    gap: 20px;
}

.filter-item {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
        opacity: 0.8;
    }
}

.filter-radio {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid #999;
    position: relative;
    transition: border-color 0.2s ease;

    &.active {
        border-color: #409eff;

        &::after {
            content: '';
            width: 6px;
            height: 6px;
            background: #409eff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

.support-cards {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.support-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    background-color: #f2f2f2;
}

.support-card-title {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.support-card-stats {
    color: #666;
}

.new-count {
    color: #f56c6c;
    font-weight: bold;
}
</style>
