<template>
    <div class="product-box">
        <div class="box-top">
            <div></div>
            <div>产品线负责人信息</div>
        </div>
        <el-table
            class="snbc-table"
            :data="tableData"
            :header-cell-style="{
                'text-align': 'center',
                'background': '#3370ff'
            }"
            :span-method="objectSpanMethod"
            border
            stripe
        >
            <el-table-column
                prop="productLine"
                label="产品线"
                align="center"
                width="150"
                key="productLine"
            />
            <el-table-column
                prop="subProductLine"
                label="细分产品线"
                align="center"
                width="150"
                key="subProductLine"
            />
            <el-table-column
                prop="productLineDirector"
                label="产品线方案总监"
                align="center"
                key="productLineDirector"
            >
                <template slot-scope="scope">
                    <people-selector
                        placeholder="请输入人员"
                        v-model="scope.row.productLineDirector"
                        style="width: 95%; padding: 2px"
                    ></people-selector>
                </template>
            </el-table-column>
            <el-table-column
                prop="productLineManager"
                label="产品线经理"
                align="center"
                key="productLineManager"
            >
                <template slot-scope="scope">
                    <people-selector
                        v-model="scope.row.productLineManager"
                        style="width: 95%; padding: 2px"
                        placeholder="请输入人员"
                    ></people-selector>
                </template>
            </el-table-column>
            <el-table-column prop="pqa" label="PQA" align="center" key="pqa">
                <template slot-scope="scope">
                    <people-selector
                        v-model="scope.row.pqa"
                        style="width: 95%; padding: 2px"
                        placeholder="请输入人员"
                    ></people-selector>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                align="center"
                width="100"
                key="handler"
            >
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        size="small"
                        @click="updateProductLine(scope.row)"
                        >保存</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import PeopleSelector from 'Components/PeopleSelector';

export default {
    components: { PeopleSelector },
    data() {
        return {
            tableData: []
        };
    },
    mounted() {
        this.getTableData();
    },
    methods: {
        getProductLineManager() {
            this.$service.project.productLine.getProductLine().then((res) => {
                if (res.head.code === '000000') {
                    this.tableData = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        getTableData() {
            const params = {
                orgCode: '',
                isAll: 0
            };
            this.$service.department.group
                .getDepartName(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.justPersonData = res.body || [];
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .then(() => this.getProductLineManager());
        },
        updateProductLine(data) {
            this.$service.project.productLine
                .updateProductLineManagerInfo(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message.success('修改成功');
                        this.getProductLineManager();
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        /**
         * 合并单元格
         * @param {Object} param { row, column, rowIndex, columnIndex }
         * @returns {Object} 单元格合并方式
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只在“产品线”这一列进行合并, 其他保持现状
            if (columnIndex !== 0) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }
            // 如果当前行的“产品线”与上一行的“产品线”相同，则合并
            if (
                rowIndex > 0 &&
                row.productLine === this.tableData[rowIndex - 1].productLine
            ) {
                return {
                    // 隐藏当前行的单元格
                    rowspan: 0,
                    colspan: 0
                };
            }
            // 计算当前“项”需要合并的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.tableData.length; i++) {
                if (row.productLine === this.tableData[i].productLine) {
                    rowspan += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.product-box {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 10px 20px 20px 20px;
}
.box-top {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
}
.box-top div:first-child {
    width: 5px;
    height: 25px;
    background-color: #3370ff;
}
.box-top div:nth-child(2) {
    font-weight: bolder;
    padding-left: 10px;
}

::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep .el-table {
    border: 1px solid #8c8c8c !important;
}

::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}
.snbc-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
}
</style>
