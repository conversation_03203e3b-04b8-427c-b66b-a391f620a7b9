<template>
    <div class="view">
        <div class="container">
            <div class="filter">
                <div class="header">{{ $t("systemManagement.messageTemplate.search") }}</div>
                <el-form label-width="150px">
                    <el-form-item :label="$t('systemManagement.messageTemplate.templateNumber')" prop="messageTemplateNo">
                        <el-input v-model="listQuery.messageTemplateNo" :placeholder="$t('systemManagement.messageTemplate.placeholder.number')" @keyup.enter.native="getList" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.messageTemplate.templateName')" prop="messageTemplateTitle">
                        <el-input v-model="listQuery.messageTemplateTitle" :placeholder="$t('systemManagement.messageTemplate.placeholder.name')" @keyup.enter.native="getList" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.messageTemplate.templateType')" prop="messageType">
                        <el-select v-model="listQuery.messageType" clearable :popper-append-to-body="false" :placeholder="
                $t('systemManagement.messageTemplate.placeholder.templateType')
              ">
                            <el-option v-for="item in templateType" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                        </el-select>
                    </el-form-item>

                    <el-form-item :label="$t('systemManagement.messageTemplate.messageSendType')" prop="messageSendType">
                        <el-select v-model="listQuery.messageSendType" clearable :popper-append-to-body="false" :placeholder="
                $t('systemManagement.messageTemplate.placeholder.messageType')
              ">
                            <el-option v-for="item in templateChannel" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.messageTemplate.sendingPlatform')" prop="messageSendType">
                        <el-select v-model="listQuery.messageSendTypePlatform" clearable :popper-append-to-body="false" :placeholder="
                $t('systemManagement.messageTemplate.placeholder.platform')
              ">
                            <el-option v-for="item in templateForm" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.messageTemplate.platformId')" prop="messageTemplateTitle">
                        <el-input v-model="listQuery.templateId" :placeholder="$t('systemManagement.messageTemplate.placeholder.platformId')" @keyup.enter.native="getList" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.messageTemplate.templateStatus')" prop="messageSendType">
                        <el-select v-model="listQuery.state" clearable :popper-append-to-body="false" :placeholder="
                $t('systemManagement.messageTemplate.placeholder.tempplateStatus')
              ">
                            <el-option v-for="item in templateStatus" :key="item.valueCode" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.tenant.columnName.createTime')" prop="createTime">
                        <el-tooltip :content="queyrDateText" placement="bottom" :disabled="queyrDateTooltipDisabled">
                            <el-date-picker v-model="queyrDate" popper-class="tenantListCreateTime" :placeholder="
                  $t('systemManagement.tenant.placeholder.timeRange')
                " type="datetimerange" :start-placeholder="
                  $t('systemManagement.tenant.placeholder.startDate')
                " :end-placeholder="
                  $t('systemManagement.tenant.placeholder.endDate')
                " :default-time="['00:00:00', '23:59:59']" />
                        </el-tooltip>
                    </el-form-item>

                    <div class="filter__btns">
                        <el-button type="primary" icon="fa fa-search" class="button option-button" @click="getList">
                            {{ $t("systemManagement.tenant.search") }}
                        </el-button>
                        <el-button v-waves class="button default-button" icon="fa fa-undo" @click="queryReset">
                            {{ $t("systemManagement.tenant.reset") }}
                        </el-button>
                    </div>

                </el-form>
            </div>
            <div class="header">
                <span class="header__title">{{ $t("systemManagement.listTitle") }}</span>
                <span class="header__btns btn-box">
                    <el-button v-waves type="primary" icon="fa fa-plus-square-o" @click="showAdd">
                        {{ $t("systemManagement.messageTemplate.operation.add") }}
                    </el-button>
                    <el-button v-waves type="primary" icon="fa fa-sign-in" @click="showImport">
                        {{ $t("systemManagement.messageTemplate.operation.export") }}

                    </el-button>
                    <el-button v-waves type="primary" @click="batchDelete('more')">
                        {{ $t("systemManagement.messageTemplate.operation.batchDelete") }}
                    </el-button>
                    <column-optional :column-data="columnData" :backend="backend" width="280" @changeStatus="changeStatus" @saveSet="saveSet" />
                </span>
            </div>
            <el-table id="tenantListTable" ref="multipleTable" :key="isUpdate" :data="list" border align="left" :header-cell-style="{background:'#F5F6FA'}" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column :fixed="true" type="index" width="70" :label="$t('systemManagement.messageTemplate.column.number')">
                    <template slot-scope="scope">
                        {{ (listQuery.pageNum - 1) * listQuery.pageSize + scope.$index + 1 }}
                    </template>
                </el-table-column>
                <el-table-column v-for="item in currentColumnData" :key="item.value+item.desc" :fixed="item.fixed" :prop="item.value" :width="item.width" :label="$t(item.desc)" :align="item.align">
                    <template slot-scope="{ row }">
                        <div v-if="item.value === 'state'">
                            <el-switch v-model="row.state" :active-value="'1'" :inactive-value="'0'" @change="dataStatusChanage(row)" />
                        </div>
                        <div v-else-if="item.value === 'messageType'">
                            <span v-if="row.messageType">{{ $t('systemManagement.dictionaryManagement.value.'+row.messageType) }}</span>
                        </div>
                        <div v-else-if="item.value === 'messageSendType'">
                            <span v-if="row.messageSendType">{{ $t('systemManagement.dictionaryManagement.value.'+row.messageSendType) }}</span>
                        </div>
                        <div v-else-if="item.value === 'messageSendTypePlatform'">
                            <span v-if="row.messageSendTypePlatform">{{ $t('systemManagement.dictionaryManagement.value.'+row.messageSendTypePlatform) }}</span>
                        </div>
                        <div v-else-if="item.value === 'messageTemplateContent'" class="text-flow" :title="row[item.value]">
                            {{ row[item.value] }}
                        </div>
                        <div v-else class="text-flow">
                            {{ row[item.value] }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('systemManagement.messageTemplate.column.operate')" width="280">
                    <template slot-scope="{ row }">
                        <el-button type="primary" icon="fa fa-pencil" size="mini" @click="showEdit(row,'edit')">
                            {{ $t("systemManagement.messageTemplate.operation.edit") }}
                        </el-button>
                        <el-button type="primary" icon="fa fa-pencil" size="mini" @click="showEdit(row,'detail')">
                            {{ $t("systemManagement.messageTemplate.operation.detail") }}
                        </el-button>
                        <el-button type="primary" icon="fa fa-pencil" size="mini" @click="batchDelete('singal',row)">
                            {{ $t("systemManagement.messageTemplate.operation.delete") }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background for="pagination" :current-page.sync="listQuery.pageNum" :page-sizes="[10, 20, 50, 100]" :page-size.sync="listQuery.pageSize" layout="total, prev, pager, next,sizes,  jumper" :total="totalCount" class="right" @size-change="getList" @current-change="getList" />
        </div>
        <add-template v-if="params.isShow" :operate-type="operateType" :params="params" :show.sync="params.isShow" @addTemplate="addTemplate" @editTemplate="editTemplate" />
        <template-import v-if="isShowImport" :show.sync="isShowImport" @refreshList="getList" />
    </div>
</template>

<script>
import list from '../../mixins/list';
import AddTemplate from './components/AddTemplate.vue';
import TemplateImport from './components/TemplateImport.vue';

export default {
    name: 'MessageTemplate',
    components: { AddTemplate, TemplateImport },
    filters: {
        tenantTagFilter(tags) {
            if (!tags) {
                return '';
            }
            return tags
                .map((item) => {
                    return item.tagName;
                })
                .join(',');
        }
    },
    mixins: [list],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 查询、分页表单
            listQuery: {
                pageSize: 10,
                pageNum: 1,
                messageTemplateNo: '',
                messageSendType: '',
                messageSendTypePlatform: '',
                messageTemplateTitle: '',
                messageType: '',
                templateId: '',
                state: '',
                stime: '',
                endtime: ''
            },
            // 消息模板列表
            list: [],
            totalCount: 0,
            // 当前编辑行租户id
            curTenantId: '',
            channelList: [],
            // 在下拉框中的列的相关数据
            columnData: [
                {
                    // table中展示的列对应的字段名
                    value: 'messageTemplateNo',
                    // 字段名称国际化
                    desc: 'systemManagement.messageTemplate.templateNumber',
                    // 是否展示此字段
                    checked: true,
                    // 是否展示tooltip
                    tooltip: false,
                    // 左对齐、右对齐
                    align: 'left',
                    // 是否在表格中固定此列
                    fixed: true,
                    // 是否禁止操作此列
                    isDisabled: true,
                    width: '180',
                    isFreeze: true
                },
                {
                    value: 'messageTemplateTitle',
                    desc: 'systemManagement.messageTemplate.templateName',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180'
                },
                {
                    value: 'messageTemplateContent',
                    desc: 'systemManagement.messageTemplate.column.templateContent',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '280',
                    isFreeze: false
                },
                {
                    value: 'messageTemplateParam',
                    desc: 'systemManagement.messageTemplate.column.templateParamMap',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '280',
                    isFreeze: false
                },
                {
                    value: 'messageType',
                    desc: 'systemManagement.messageTemplate.templateType',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180',
                    isFreeze: false
                },
                {
                    value: 'messageSendType',
                    desc: 'systemManagement.messageTemplate.messageSendType',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180',
                    isFreeze: false
                },
                {
                    value: 'messageSendTypePlatform',
                    desc: 'systemManagement.messageTemplate.sendingPlatform',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180',
                    isFreeze: false
                },
                {
                    value: 'templateId',
                    desc: 'systemManagement.messageTemplate.platformId',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180',
                    isFreeze: false
                },
                {
                    value: 'state',
                    desc: 'systemManagement.messageTemplate.templateStatus',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180',
                    isFreeze: false
                },
                {
                    value: 'createTime',
                    desc: 'systemManagement.messageTemplate.createTime',
                    checked: true,
                    tooltip: false,
                    align: 'left',
                    fixed: false,
                    isDisabled: false,
                    width: '180',
                    isFreeze: false
                }
            ],
            // 当前实际展示的列
            currentColumnData: [],
            // 后台是否保存排序
            backend: true,
            // 解决列发生变化时表格不更新的问题
            isUpdate: false,
            // 模板类型列表
            templateType: [],
            //  消息渠道列表
            templateChannel: [],
            // 发送平台
            templateForm: [],
            // 模板状态
            templateStatus: [
                { name: '启用', value: '1', valueName: 'enable' },
                { name: '禁用', value: '0', valueName: 'disable' }
            ],
            // 传给 新增、编辑、详情组件的参数
            params: {
                isShow: false,
                // 标题
                title: '新增消息模板',
                // 是否禁止编辑
                disabled: false,
                // 用于编辑和详情返显数据
                data: {
                    messageTemplateNo: '',
                    messageSendType: '',
                    messageSendTypePlatform: '',
                    messageTemplateTitle: '',
                    messageTemplateContent: '',
                    messageTemplateParam: '',
                    templateId: '',
                    messageType: '',
                    messageTemplateId: ''
                }
            },
            // 操作组件的类型
            operateType: 'add',
            // 列表选中项
            multipleSelection: [],
            isShowImport: false
        };
    },
    computed: {
        // 查询的时间范围
        queyrDate: {
            get() {
                return [this.listQuery.stime, this.listQuery.endtime];
            },
            set(val) {
                if (!val) {
                    this.listQuery.stime = '';
                    this.listQuery.endtime = '';
                    return;
                }
                this.listQuery.stime = this.$tools.parseTime(
                    val[0],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
                this.listQuery.endtime = this.$tools.parseTime(
                    val[1],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
            }
        },
        queyrDateText() {
            return `${this.listQuery.stime}-${this.listQuery.endtime}`;
        },
        queyrDateTooltipDisabled() {
            return !this.listQuery.stime && !this.listQuery.endtime;
        }
    },
    created() {
        // 过滤不展示的列
        this.changeStatus();
        this.getList();
        // 获取模板类型
        this.getTemplateType();
        // 获取消息渠道
        this.getTemplateChannel();
        //  获取发送平台
        this.getTemplatePlatform();

        this.getCurrentOrder();
    },
    methods: {
        // 获取模板类型数据
        getTemplateType() {
            this.$service.systemManagement
                .getMessageDictType()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.templateType = response.body;
                    } else {
                        this.templateType = [];
                    }
                });
        },
        // 获取消息渠道数据
        getTemplateChannel() {
            this.$service.systemManagement
                .getMessageChannel()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.templateChannel = response.body;
                    } else {
                        this.templateChannel = [];
                    }
                });
        },
        // 获取平台类型数据
        getTemplatePlatform() {
            this.$service.systemManagement
                .getMessagePlatformType()
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.templateForm = response.body;
                    } else {
                        this.templateForm = [];
                    }
                });
        },
        // 获取列表数据
        getList() {
            this.$service.systemManagement
                .getMessageTemplateList(this.listQuery)
                .then((response) => {
                    if (response.head.code === '000000') {
                        const resultObj = response.body;
                        this.list = resultObj.list;
                        this.totalCount = resultObj.total;

                        this.listQuery.pageNum = resultObj.pageNum;
                        this.listQuery.pageSize = resultObj.pageSize;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 显示新增窗口
        showAdd(row) {
            this.params.isShow = true;
            this.operateType = 'add';
            this.params.title = '新增消息模板';
            this.params.data = {};
        },
        // 显示编辑窗口
        showEdit(row, type) {
            this.params.isShow = true;
            if (type === 'edit') {
                this.operateType = 'edit';
                this.params.title = '编辑消息模板';
            } else {
                this.operateType = 'detail';
                this.params.title = '消息模板详情';
            }
            // 调用详情接口
            const data = { messageTemplateId: row.messageTemplateId };
            this.$service.systemManagement
                .detailTemplateItem(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.params.data = res.body;
                    }
                });
        },
        queryReset() {
            // 清空数据
            Object.assign(this.listQuery, this.$options.data().listQuery);
        },
        // 数据状态变更
        dataStatusChanage(row) {
            const { messageTemplateId, state } = row;
            const params = {
                messageTemplateId,
                state
            };
            // 设置不同状态的提示语
            let messageTip = '';
            if (state === '1') {
                messageTip = this.$t(
                    'systemManagement.messageTemplate.endbleTemplate'
                );
            } else {
                messageTip = this.$t(
                    'systemManagement.messageTemplate.disabledTemplate'
                );
            }
            // 弹出确认框
            this.$confirm(
                messageTip,
                this.$t('systemManagement.messageTemplate.statusTipTitle'),
                {
                    type: 'warning'
                }
            )
                .then(() => {
                    this.$service.systemManagement
                        .templateStatus(params)
                        .then((response) => {
                            if (response.head.code === '000000') {
                                this.$message({
                                    message: this.$t(
                                        'systemManagement.messageTemplate.operation.operateSuccess'
                                    ),
                                    type: 'success'
                                });
                            } else {
                                // 状态值取反，即还原设置的值
                                row.state = String(Math.abs(row.state - 1));
                                this.$message({
                                    message: this.$t(
                                        'systemManagement.messageTemplate.operation.operateFailure'
                                    ),
                                    type: 'error'
                                });
                            }
                        });
                })
                .catch(() => {
                    // 状态值取反，即还原设置的值
                    row.state = String(Math.abs(row.state - 1));
                });
        },
        // 选择需要展示的列
        changeStatus(data) {
            this.isUpdate = !this.isUpdate;
            if (data) {
                this.columnData = data;
            }
            this.currentColumnData = this.columnData.filter((item) => {
                // 过滤出checked 为true的数据
                return item.checked === true;
            });
        },
        // 获取当前的列排序
        getCurrentOrder() {
            this.$service.merchantManagement
                .getCurrentOrder({
                    profileCode: 'messageTemplateList'
                })
                .then((response) => {
                    if (response.head.code === '000000') {
                        if (response.body) {
                            // 更新列可选数据
                            this.columnData = JSON.parse(
                                response.body.profileContent
                            );
                            // 更新表格数据
                            this.changeStatus();
                        }
                    }
                });
        },
        // 保存列可选的配置
        saveSet(data) {
            // 调用后台接口保存排序
            this.$service.merchantManagement
                .saveColumnOrder({
                    profileCode: 'messageTemplateList',
                    profileContent: JSON.stringify(data)
                })
                .then((response) => {
                    // 预留保存成功的回调
                });
        },
        // 新增模板
        addTemplate(data) {
            this.$service.systemManagement
                .addTemplateItem(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message({
                            title: this.$t('common.success'),
                            message: this.$t(
                                'systemManagement.messageTemplate.operation.success'
                            ),
                            type: 'success',
                            duration: 2000
                        });
                        this.params.isShow = false;
                        // 新增成功后刷新列表
                        this.getList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 编辑模板
        editTemplate(data) {
            const params = {
                messageTemplateNo: data.messageTemplateNo,
                messageSendType: data.messageSendType,
                messageSendTypePlatform: data.messageSendTypePlatform,
                messageTemplateTitle: data.messageTemplateTitle,
                messageTemplateContent: data.messageTemplateContent,
                messageTemplateParam: data.messageTemplateParam,
                templateId: data.templateId,
                messageType: data.messageType,
                messageTemplateId: data.messageTemplateId
            };
            this.$service.systemManagement
                .editTemplateItem(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.$message({
                            title: this.$t('common.success'),
                            message: this.$t(
                                'systemManagement.messageTemplate.operation.success'
                            ),
                            type: 'success',
                            duration: 2000
                        });
                        this.params.isShow = false;
                        // 编辑成功后刷新列表
                        this.getList();
                    } else {
                        const msg = `systemManagement.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 用户列表check选择
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        // eslint-disable-next-line max-lines-per-function
        batchDelete(flag, row) {
            // 删除
            let msgIds = [];
            if (flag === 'singal') {
                msgIds = [row.messageTemplateId];
            } else if (this.multipleSelection.length > 0) {
                msgIds = this.multipleSelection.map(
                    (item) => item.messageTemplateId
                );
            }
            if (msgIds.length > 0) {
                this.$confirm(
                    this.$t(
                        'systemManagement.messageTemplate.operation.confirmDelete'
                    ),
                    this.$t(
                        'systemManagement.messageTemplate.operation.deleteTitle'
                    ),
                    {
                        type: 'warning'
                    }
                )
                    .then(() => {
                        const params = { messageTemplateIds: msgIds };
                        this.$service.systemManagement
                            .batchDelete(params)
                            .then((res) => {
                                if (res.head.code === '000000') {
                                    // 提示操作成功
                                    this.$message({
                                        message: this.$t(
                                            'systemManagement.informationAnnounce.message.deleteSuccess'
                                        ),
                                        type: 'success'
                                    });
                                    this.getList();
                                } else {
                                    const { code } = res.head;
                                    if (code === '950212') {
                                        const { message } = res.head;
                                        let resultMsg = '';
                                        // 如果存在分号就进行分割
                                        if (message.indexOf(';') > -1) {
                                            const combinationArr =
                                                message.split(';');
                                            resultMsg =
                                                combinationArr[0] +
                                                this.$t(
                                                    'systemManagement.messageTemplate.placeholder.deleteFailure'
                                                ) +
                                                combinationArr[1] +
                                                this.$t(
                                                    'systemManagement.messageTemplate.placeholder.templateIdUnexcixt'
                                                );
                                        } else {
                                            resultMsg =
                                                message +
                                                this.$t(
                                                    'systemManagement.messageTemplate.placeholder.templateIdUnexcixt'
                                                );
                                        }
                                        this.$message({
                                            message: resultMsg,
                                            type: 'error'
                                        });
                                    }
                                }
                            });
                    })
                    .catch(() => {
                        this.$message({
                            type: 'info',
                            message: this.$t('systemManagement.msg.canceled')
                        });
                    });
            } else {
                this.$message({
                    message: this.$t(
                        'systemManagement.messageTemplate.operation.toDeleteItems'
                    ),
                    type: 'warning'
                });
            }
        },
        // 展示导入弹窗
        showImport() {
            this.isShowImport = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.components-container {
    height: calc(100vh - 106px) !important;
    min-height: auto;
    overflow: auto !important;
}
.btn-box {
    display: flex;
}
.text-flow {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
}
</style>
