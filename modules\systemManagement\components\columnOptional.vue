<template>
    <div class="colunm-box">
        <el-popover v-model="visible" :placement="placement" :width="width">
            <div v-for="(item, index) in columnData" :key="index" class="item">
                <span>{{ $t(item.desc) }}</span>
                <el-switch v-model="item.checked" @change="changeStatus" />
            </div>
            <el-button
                slot="reference"
                type="primary"
                size="middle"
                class="margin-left"
            >
                <i class="fa fa-th-list" />
                <span>{{
                    $t(
                        'systemManagement.messageTemplate.operation.columnOptional'
                    )
                }}</span>
            </el-button>
        </el-popover>
    </div>
</template>

<script>
export default {
    name: 'ColumnOptional',
    props: {
        columnData: {
            type: Array,
            default: () => []
        },
        width: {
            type: String,
            default: '180'
        },
        placement: {
            type: String,
            default: 'bottom'
        }
    },
    data() {
        return {
            visible: false
        };
    },
    methods: {
        changeStatus() {
            this.$emit('changeStatus', this.columnData);
        }
    }
};
</script>
<style scoped lang="scss">
.colunm-box {
    display: inline-block;
    margin-left: 10px;
}
.item {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    .el-switch {
        width: 40px;
        margin-left: 15px;
    }
    &:span {
        flex: 1;
    }
}
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
</style>
