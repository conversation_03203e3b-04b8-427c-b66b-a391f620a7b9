/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();

    const service = {
        meeting: {
            // 创建会议 弹窗
            create(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting/addMeetingInfo',
                    method: 'post',
                    data
                });
            },
            // 修改会议 弹窗
            update(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting/updateMeetingInfo',
                    method: 'post',
                    data
                });
            },
            // 修改会议 弹窗
            sendEmail(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: 'meeting/sendMeetingMail ',
                    method: 'post',
                    data
                });
            },
            // 获取所有首次的会议信息，用于关联会议的下拉列表选择
            getFirstMeeting(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getAllFirstMeetingList',
                    method: 'get',
                    params: data
                });
            },
            // 查询会议信息
            getMeetingInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting/getMeetingInfo',
                    method: 'get',
                    params: data
                });
            },
            // 结束或取消会议
            cancelMeeting(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting/endOrCancelMeeting',
                    method: 'post',
                    data
                });
            },
            // 获取该会议关联的所有会议
            getRelatedMeetings(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getRelateMeetingList',
                    method: 'get',
                    params: data
                });
            },
            getJudgeAttendanceInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getJudgesPartMeetingList',
                    method: 'post',
                    data
                });
            },
            getMeetingList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getMeetingList',
                    method: 'post',
                    data
                });
            },
            // 参会评委会议冲突校验
            validateDuplicateJudges(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting/checkJudgesPartTime',
                    method: 'post',
                    data
                });
            },
            // 获取历史会议下拉选项
            getHistoryMeetingOptions(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getMeetingDownList',
                    method: 'get',
                    params: data
                });
            }
        },
        employee: {
            // 获取员工列表(包含外部评委)
            getExternalStaffList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/common/getEmployees',
                    method: 'get',
                    params: data
                });
            },
            // 获取人员列表，包含部门和人员
            getPeopleListWithOrg(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/department/getOrgAndEmployee',
                    method: 'get',
                    params: data
                });
            }
        },
        myMeetings: {
            // 我的周会议列表
            list(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getMyMeetingList',
                    method: 'post',
                    data
                });
            },
            // 我的代办任务列表
            taskList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getMyMeetingToDoTask',
                    method: 'get',
                    params: data
                });
            }
        },
        meetingMinutes: {
            // 获取所有首次的会议信息，用于关联会议的下拉列表选择
            getInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/getMeetingMinutes',
                    method: 'get',
                    params: data
                });
            },
            // 编辑纪要
            edit(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/updateMeetingMinutes',
                    method: 'post',
                    data
                });
            },
            // 编辑纪要
            import() {
                return `${basePath.bossapi.pmService}/meeting-operate/importMeetingMinutes`;
            }
        },
        onlineReview: {
            // 查询线上评审意见
            getInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/getMeetingOnlinePreview',
                    method: 'get',
                    params: data
                });
            },
            // 编辑线上评审意见
            edit(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/updateMeetingOnlinePreview',
                    method: 'post',
                    data
                });
            }
        },
        preReview: {
            // 查询会前预审意见
            getInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/getMeetingBeforePreview',
                    method: 'get',
                    params: data
                });
            },
            // 编辑会前预审意见
            edit(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/updateMeetingBeforePreview',
                    method: 'post',
                    data
                });
            }
        },
        meetingQuality: {
            // 查询会议效果评价
            getInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/getMeetingEvaluation',
                    method: 'get',
                    params: data
                });
            },
            // 编辑会议效果评价
            edit(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/addMeetingEvaluation',
                    method: 'post',
                    data
                });
            }
        },
        meetingTaskTrace: {
            getList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getMeetingTaskTrackList',
                    method: 'post',
                    data
                });
            },
            edit(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/updateMeetingTrackTask',
                    method: 'post',
                    data
                });
            }
        },
        meetingRoom: {
            // 获取会议室列表
            getRoomList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meetingOA/getOaMeetingRoomInfo',
                    method: 'get',
                    params: data
                });
            },
            // 获取会议占用情况
            getMeetingInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meetingOA/getOaMeetingOrderInfo',
                    method: 'get',
                    params: data
                });
            },
            // 预定会议室
            bookRoom(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meetingOA/createMeeting',
                    method: 'post',
                    data
                });
            },
            // 取消预定的会议室
            cancelBook(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meetingOA/cancelMeeting',
                    method: 'get',
                    params: data
                });
            },
            // 提前结束会议（会议开始了但是没结束，这时候取消会议）
            cancelOngoingMeeting(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meetingOA/overMeeting',
                    method: 'get',
                    params: data
                });
            },
            // 修改预定会议室（取消了再预定）
            editRoom(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meetingOA/changeMeeting',
                    method: 'post',
                    data
                });
            }
        },
        unavailableTime: {
            // 新增不可用时间段
            add(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/insertMeetingUnavailableTime',
                    method: 'post',
                    data
                });
            },
            // 修改不可用时间段
            edit(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/updateMeetingUnavailableTime',
                    method: 'put',
                    data
                });
            },
            // 查询不可用时间段
            getInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-query/getMeetingUnavailableTime',
                    method: 'post',
                    data
                });
            },
            // 删除会议不可用时间登记
            delete(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/meeting-operate/deleteMeetingUnavailableTime',
                    method: 'delete',
                    params: data
                });
            }
        },
        toDoList: {
            // 获取我的代办任务列表
            getList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/to-do-task/getToDoTask',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
