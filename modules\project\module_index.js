import './icons';

import moduleRoutes from './router';
import moduleI18n from './lang/index.js';
import moduleStore from './store';
import moduleService from './service';
import config from './config.js';
import initProjectModule from './init';

const { moduleName } = config;

export default ({ Vue, router, store, i18n }) => {
    // 加载国际化
    Vue.prototype.$addI18n(moduleName, moduleI18n);
    // 注册路由
    Vue.prototype.$addRoutes(moduleName, moduleRoutes);
    // 注册状态树
    Vue.prototype.$addStore(moduleName, moduleStore);
    // 注册模块service
    Vue.prototype.$service[moduleName] = moduleService(Vue);

    // 立即初始化从 localStorage 加载持久化状态
    initProjectModule(store);

    // 在 Vue 实例创建后也尝试加载一次，确保在所有组件创建后仍能正确加载
    Vue.mixin({
        created() {
            // 仅在根组件创建时执行一次
            if (this.$root === this) {
                // 延迟执行，确保所有初始化完成
                setTimeout(() => {
                    initProjectModule(store);
                }, 100);
            }
        }
    });
};
