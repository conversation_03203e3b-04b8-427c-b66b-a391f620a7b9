<template>
    <el-tabs v-model="tabsConfig.activeName" type="border-card" @tab-click="handleTabClick">
        <el-tab-pane v-for="(tabName, index) in tabsConfig.tabItems" :key="index" :label="tabName" :name="tabName" />
    </el-tabs>
</template>
<script>
export default {
    name: 'SnbcTableTabs',
    props: {
        /**
         * 标签页
         */
        tabsConfig: {
            type: Object,
            default() {
                return {
                    activeName: '',
                    tabItems: []
                };
            }
        }
    },
    data() {
        return {};
    },
    methods: {
        // 标签点击处理
        handleTabClick(tab, event) {
            this.tabsConfig.handleTabClick && this.tabsConfig.handleTabClick(tab, event);
        }
    }
};
</script>
<style lang="scss" scoped></style>
