<template>
    <div class="calendar-main">
        <department-selector :infoDisabled="true"></department-selector>
        <div class="calendar-top">
            <select v-model="selectedYear" @change="changeCalendar()">
                <option v-for="year in years" :key="year" :value="year">
                    {{ year }} 年
                </option>
            </select>
            <select v-model="weekendMode" @change="changeMode()">
                <option value="">单休</option>
                <option value="双">双休</option>
            </select>
            <el-button
                type="primary"
                size="mini"
                @click="saveSelectedDate"
                icon="el-icon-folder"
                class="save-btn"
                >保存选中日期</el-button
            >
        </div>
        <div v-if="monthData.length" class="calendar-container">
            <div
                v-for="(monthData, index) in monthData"
                :key="index"
                class="calendar"
            >
                <div class="calendar-header">
                    {{ selectedYear }} 年 {{ months[monthData.month - 1] }}
                </div>
                <div class="calendar-grid">
                    <div
                        class="calendar-day-header"
                        v-for="day in dayHeaders"
                        :key="day"
                    >
                        {{ day }}
                    </div>
                    <div
                        class="calendar-day"
                        v-for="(day, dayIndex) in monthData.days"
                        :key="`${monthData.month}-${dayIndex}`"
                        @click="selectDate($event, day)"
                        :class="[
                            {
                                'marked-date': shouldMarkRed(day, monthData)
                            }
                        ]"
                    >
                        {{ day.date }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import departmentSelector from 'department/components/departmentSelector';

export default {
    components: {
        departmentSelector
    },
    data() {
        return {
            selectedYear: new Date().getFullYear(),
            years: [],
            months: [
                '1月',
                '2月',
                '3月',
                '4月',
                '5月',
                '6月',
                '7月',
                '8月',
                '9月',
                '10月',
                '11月',
                '12月'
            ],
            dayHeaders: ['日', '一', '二', '三', '四', '五', '六'],
            weekendMode: '',
            days: [],
            markedDates: [],
            monthData: []
        };
    },
    mounted() {
        this.generateYearOptions();
        this.getworkCalendar();
    },
    methods: {
        // 通过年份查非工作日日期
        getworkCalendar() {
            const params = {
                year: this.selectedYear,
                dateType: this.weekendMode
            };
            this.$service.department.getWorkDays(params).then((res) => {
                if (res.head.code === '000000') {
                    this.markedDates = res.body || [];
                    this.generateCalendarData(this.markedDates);
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        changeCalendar() {
            this.cancelAllSelect();
            this.generateCalendarData();
            this.getworkCalendar();
        },
        changeMode() {
            this.cancelAllSelect();
            this.generateCalendarData();
            this.getworkCalendar();
        },
        cancelAllSelect() {
            const markedDateElements =
                document.querySelectorAll('.marked-date');
            markedDateElements.forEach((el) => {
                el.classList.remove('marked-date');
            });
        },
        generateCalendarData() {
            // 清空之前的数据
            this.monthData = [];

            setTimeout(() => {
                const monthData = [];
                // 生成每个月的日期日历
                for (let i = 1; i <= 12; i++) {
                    const days = this.generateMonthDays(this.selectedYear, i);
                    // 将每个月的日期数据保存到 monthData 数组中
                    monthData.push({ month: i, days });
                }
                this.monthData = monthData;
            }, 0);
        },
        // 生成日历
        generateMonthDays(year, month) {
            const firstDayOfMonth = new Date(year, month - 1, 1);
            const startingDay = firstDayOfMonth.getDay();
            const daysInMonth = new Date(year, month, 0).getDate();
            const days = [];
            // 补充前面的空白日期
            for (let i = 0; i < startingDay; i++) {
                days.push({ date: '', isCurrentMonth: false });
            }
            // 当月日期
            for (let i = 1; i <= daysInMonth; i++) {
                const date = new Date(year, month - 1, i);
                const dayOfWeek = date.getDay();
                days.push({ date: i, isCurrentMonth: true, dayOfWeek });
            }
            return days;
        },
        // 年份为2023年到2060年
        generateYearOptions() {
            for (let i = 2023; i <= 2060; i++) {
                this.years.push(i);
            }
        },

        // 判断日期是否需要标记为红色
        shouldMarkRed(day, monthData) {
            const dateString = `${this.selectedYear}-${monthData.month
                .toString()
                .padStart(2, '0')}-${day.date.toString().padStart(2, '0')}`;
            if (this.markedDates.indexOf(dateString) !== -1) {
                return true;
            }
            return false;
        },
        selectDate(event, day) {
            if (day.date === '') return;
            // 如果已经标记，则取消标记
            if (event.target.classList.contains('marked-date')) {
                event.target.classList.remove('marked-date');
            } else {
                // 否则添加标记
                event.target.classList.add('marked-date');
            }
        },
        saveSelectedDate() {
            const markedDateElements =
                document.querySelectorAll('.marked-date');
            const markedDates = [];
            markedDateElements.forEach((element) => {
                const year = this.selectedYear;
                const calendarHeader = element
                    .closest('.calendar')
                    .querySelector('.calendar-header')
                    .textContent.trim();
                const month = calendarHeader
                    .split(' ')[2]
                    .replace('月', '')
                    .padStart(2, '0');
                const date = element.textContent.trim();
                // 组合成 yyyy-mm-dd 格式
                const formattedDateString = `${year}-${month}-${date.padStart(
                    2,
                    '0'
                )}`;

                markedDates.push(formattedDateString);
            });
            const params = {
                year: this.selectedYear,
                dateType: this.weekendMode,
                days: markedDates
            };
            this.$service.department.addWorkDays(params).then((res) => {
                if (res.head.code === '000000') {
                    this.$message.success('日历保存成功!');
                    this.getworkCalendar();
                } else {
                    this.$message.error('日历保存失败!');
                }
            });
        }
    }
};
</script>

<style scoped>
.calendar-main {
    width: 100%;
    height: 100%;
    padding: 10px 0px 10px 0px;
}
.calendar-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0px 10px 10px 10px;
    overflow-y: auto;
    height: calc(100vh - 60px);
}
.calendar {
    width: calc(100% / 4 - 10px);
    margin: 5px;
    border: 1px solid #ccc;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.calendar-header {
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: #fff;
    background-color: #3370ff;
    padding: 5px;
}
.calendar-top {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 10px;
}
select {
    width: 120px;
    height: 30px;
    margin-left: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
}
.save-btn {
    margin-left: 10px;
}
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    padding: 10px;
}
.calendar-day-header {
    text-align: center;
    font-weight: bold;
}
.calendar-day {
    text-align: center;
    padding: 5px;
    cursor: pointer;
}
.marked-date {
    background-color: red;
    color: #fff;
}
</style>
