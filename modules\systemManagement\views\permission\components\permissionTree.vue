<template>
    <div class="container">
        <el-input v-model="searchValue" :placeholder="$t('systemManagement.permission.placeholder.msg1')" class="search-input-view">
            <i slot="suffix" class="fa fa-search el-input__icon" />
        </el-input>

        <el-tree ref="tree" draggable :allow-drop="allowDropHandler" :allow-drag="allowDragHandler" :highlight-current="true" :expand-on-click-node="false" :check-on-click-node="true" :default-expanded-keys="defaultShowNodes" :data="treeSource" :props="defaultProps" node-key="permissionId" :filter-node-method="filterNode" :empty-text="$t('systemManagement.logger.noData')" @current-change="currentChangeHandler" @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" @node-drop="handleDragEnd">
            <el-tooltip slot-scope="{ node, data }" :content="node.label" placement="right">
                <span class="block">
                    <span class="block__text">{{
            node.label
          }}</span>
                    <span class="block__btns">
                        <el-button v-if="node.isCurrent" slot="suffix" v-permission="['permission_manage_add_menu']" type="text" size="mini" icon="fa fa-plus-circle" @click="addNodeHandler(node)" />
                        <el-button v-if="node.isCurrent && (+data.permissionType === 1 || +data.permissionType === 2)" slot="suffix" v-permission="['permission_manage_edit_menu']" type="text" size="mini" icon="fa fa-pencil" @click="updateNodeHandler(node)" />
                        <el-button v-if="node.isCurrent && (+data.permissionType === 1 || +data.permissionType === 2)" slot="suffix" v-permission="['permission_manage_delete_menu']" type="text" size="mini" icon="fa fa-trash-o" @click="deleteNodeHandler(node)" />
                    </span>
                </span>
            </el-tooltip>
        </el-tree>
    </div>
</template>

<script>
export default {
    name: 'DictValueList',
    props: {
        code: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 搜索框输入的值
            searchValue: '',
            // 树节点绑定的值
            defaultProps: {
                children: 'children',
                label: 'permissionName'
            },
            // 权限树数据源
            treeSource: [],
            // 权限树loading
            treeLoading: false,
            // 当前选中的节点对象
            currentNode: null,
            // 这里存放要默认展开的节点 id
            defaultShowNodes: ['-1'],
            // 在外面删除节点后，需要这里强制调用remove方法删除节点的数组（解决节点已经不存在，但是getCurrentKey和getNode还可以查询到节点的bug）
            needDeleteArray: [],
            // 存储当前所有node节点id的对象
            nodeIdObject: {},
            // 当前选中的节点key
            cNode: '',
            // 当前选中的父节点key
            pNode: ''
        };
    },
    computed: {},
    watch: {
        searchValue(val) {
            this.$refs.tree.filter(val);
        }
    },
    created() {
        this.getPermissionTreeData();
    },
    methods: {
        // 获得权限树    数据源
        getPermissionTreeData() {
            this.treeLoading = true;
            this.$service.systemManagement
                .getPermissionTreeList({ level: 0 })
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.treeSource = response.body;
                        // 存储当前所有的nodeId
                        this.nodeIdObject = {};
                        this.nodeIdObjectHandler(this.treeSource);
                        // 设置选中的节点
                        this.setCurrentNode();
                        this.$nextTick(() => {
                            this.$refs.tree.filter(this.searchValue);
                        });
                    }
                    this.treeLoading = false;
                })
                .catch(() => {
                    this.treeLoading = false;
                });
        },
        // 遍历树数据源  把所有的permissionId存储起来，在删除节点后用来判断移除节点
        nodeIdObjectHandler(datas) {
            // eslint-disable-next-line guard-for-in
            for (const i in datas) {
                const nodeItem = datas[i];
                this.nodeIdObject[nodeItem.permissionId] =
                    nodeItem.permissionId;
                if (nodeItem.children) {
                    this.nodeIdObjectHandler(nodeItem.children);
                }
            }
        },
        // 设置当前选中的节点
        setCurrentNode() {
            this.$nextTick(() => {
                if (this.currentNode) {
                    for (let i = 0; i < this.needDeleteArray.length; i++) {
                        const tempItem = this.needDeleteArray[i];
                        // 如果找到了当前节点，说明当前节点被删除，则把当前节点的父节点设置为当前选中节点
                        if (this.currentNode.data.permissionId === tempItem) {
                            const tempParentNode = this.currentNode.parent;
                            this.$refs.tree.remove(tempItem);
                            this.currentNode = tempParentNode;
                        } else {
                            // 移除掉已经删除的节点
                            this.$refs.tree.remove(tempItem);
                        }
                    }
                    this.$refs.tree.getNode(
                        this.currentNode.key
                    ).expanded = true;
                    this.$refs.tree.setCurrentKey(this.currentNode.key);
                    if (this.currentNode.data.permissionId !== '-1') {
                        this.setNodeParentExpanded(this.currentNode);
                    }
                } else {
                    this.$refs.tree.getNode('-1').expanded = true;
                    this.$refs.tree.setCurrentKey('-1');
                }
                this.$emit(
                    'nodeSelectedEvent',
                    this.$refs.tree.getCurrentNode()
                );
            });
        },
        // 设置为选中时，把该节点的所有父节点设置为展开，设置参数节点的父节点为展开状态
        setNodeParentExpanded(nodeItem) {
            if (nodeItem.parent) {
                if (nodeItem.parent.key) {
                    this.$refs.tree.getNode(
                        nodeItem.parent.key
                    ).expanded = true;
                }
                this.handleNodeExpand(nodeItem.parent.data);
                if (nodeItem.parent.data.permissionId !== '-1') {
                    this.setNodeParentExpanded(nodeItem.parent);
                }
            }
        },
        // 树节点展开
        handleNodeExpand(data) {
            // 保存当前展开的节点
            let flag = false;
            // eslint-disable-next-line array-callback-return
            this.defaultShowNodes.some((item) => {
                if (item === data.permissionId) {
                    // 判断当前节点是否存在， 存在不做处理
                    flag = true;
                    return true;
                }
            });
            if (!flag) {
                // 不存在则存到数组里
                this.defaultShowNodes.push(data.permissionId);
            }
        },
        // 树节点关闭
        handleNodeCollapse(data) {
            // eslint-disable-next-line array-callback-return
            this.defaultShowNodes.some((item, i) => {
                if (item === data.permissionId) {
                    // 删除关闭节点
                    this.defaultShowNodes.length = i;
                }
            });
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.permissionName.indexOf(value) !== -1;
        },
        // 节点是否允许拖拽的规则，目前是根节点不允许拖拽
        allowDragHandler(draggingNode) {
            return +draggingNode.data.permissionId !== -1;
        },
        // 拖拽规则
        allowDropHandler(draggingNode, dropNode, type) {
            // 非跟节点才可以
            if (dropNode.level !== 1) {
                // 第二级是系统类型，只允许同级拖拽
                if (dropNode.level === 2) {
                    // 菜单类型的不允许拖拽到系统类型同级
                    if (draggingNode.level > 2) {
                        return false;
                    }
                    // 向上拖拽 || 向下拖拽
                    return type === 'prev' || type === 'next';
                }
                // 第三级以后的节点都是菜单类型，不能拖拽到根节点下
                if (dropNode.level === 1) {
                    return false;
                }
                // 系统类型的不允许拖拽到菜单类型中去
                if (dropNode.level > 2 && draggingNode.level === 2) {
                    return false;
                }
                return true;
            }
            // 根节点不处理
            return false;
        },
        // 拖拽结束处理 before、after、inner 。（拖拽不支持取消，恢复拖拽前的状态，暂时不实现）
        handleDragEnd(draggingNode, dropNode, dropType, ev) {
            // 处理两种例外情况，不发请求
            if (
                draggingNode.data.permissionId === dropNode.data.permissionId ||
                draggingNode.data.permissionId === dropNode.data.parentId
            ) {
                return;
            }

            let tempType = '';
            switch (dropType) {
                case 'before':
                    tempType = 'prev';
                    break;
                case 'after':
                    tempType = 'next';
                    break;
                case 'inner':
                    tempType = 'inner';
                    break;
                default:
                    break;
            }
            const params = {
                // 当前节点
                currentNodeId: draggingNode.data.permissionId,
                // 目标节点
                targetNodeId: dropNode.data.permissionId,
                // 目标节点父id
                parentId: dropNode.data.parentId,
                // 排序类型，prev目标节点前，next目标节点后，inner目标节点内
                type: tempType
            };
            this.treeLoading = true;
            this.$service.systemManagement
                .postPermissionDragOrderBy(params)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.getPermissionTreeData();
                    }
                    this.treeLoading = false;
                });
        },
        // 点击新增触发
        addNodeHandler(node) {
            this.$emit('addNodeEvent', node);
        },
        // 点击编辑触发
        updateNodeHandler(node) {
            this.$emit('updateNodeEvent', node);
        },
        // 点击删除触发
        deleteNodeHandler(node) {
            this.$emit('deleteNodeEvent', node);
        },
        // 父组件再删除成功需要调用该方法来删除之前的节点（组件内部有bug，需要手动删除旧节点）
        deleteSuccessHandler(deleteList) {
            this.needDeleteArray = deleteList;
            // 刷新树
            this.getPermissionTreeData();
        },
        // 节点选中状态变化时触发
        currentChangeHandler(node) {
            // 当前选中节点的data
            const currentNodeData = this.$refs.tree.getCurrentNode();
            // 当前选中节点的node
            const currentNode = this.$refs.tree.getNode(currentNodeData);
            // 保存当前节点node对象
            this.currentNode = currentNode;
            // 抛出事件通知父页面，选中的节点已经变化
            this.$emit('nodeSelectedEvent', currentNodeData);
        }
    }
};
</script>

