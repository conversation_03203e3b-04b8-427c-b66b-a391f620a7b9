/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.department.xxx
 */

const state = {
    // 顶部级联组件部门的值
    departmentStore: [],
    // 金融机具场景的产品数据
    financeSceneProductStore: [],
    // 分拣系统的产品数据
    sortingSystemProductStore: [],
    // 新增项目成功的次数
    changeProjectCountSuccess: 0
};

const mutations = {
    CHANGE_DEPARTMENT(state, department) {
        state.departmentStore = department;
    },
    CHANGE_FINANCE_SCENE_PRODUCT(state, financeSceneProduct) {
        state.financeSceneProductStore = financeSceneProduct;
    },
    CHANGE_SORTING_SYSTEM_PRODUCT(state, sortingSystemProduct) {
        state.sortingSystemProductStore = sortingSystemProduct;
    },
    CHANGE_PROJECT_COUNT_SUCCESS(state, changeProjectCountSuccess) {
        state.changeProjectCountSuccess = changeProjectCountSuccess;
    }
};

const actions = {
    changeDepartment({ commit }, department) {
        commit('CHANGE_DEPARTMENT', department);
    },
    changeFinanceSceneProduct({ commit }, financeSceneProduct) {
        commit('CHANGE_FINANCE_SCENE_PRODUCT', financeSceneProduct);
    },
    changeSortingSystemProduct({ commit }, sortingSystemProduct) {
        commit('CHANGE_SORTING_SYSTEM_PRODUCT', sortingSystemProduct);
    },
    changeProjectCountSuccess({ commit }, changeProjectCountSuccess) {
        commit('CHANGE_PROJECT_COUNT_SUCCESS', changeProjectCountSuccess);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
