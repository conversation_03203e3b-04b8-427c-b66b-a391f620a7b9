'use strict'
const path = require('path')

const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'wtf frame Admin' // page title

module.exports = {
  publicPath: './',
  productionSourceMap: false,
  lintOnSave: false,
  css: {
    extract: false
  },
  configureWebpack: {
    output: {
      libraryExport: 'default'
    },
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  }
}