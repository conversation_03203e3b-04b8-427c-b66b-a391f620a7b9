import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        header: {
            // 获取维护项目上方选择框选项
            getProjectList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/project-maintain/getMaintainProjectHeader`,
                    method: 'get',
                    params: data
                });
            }
        }
    };

    return service;
};
