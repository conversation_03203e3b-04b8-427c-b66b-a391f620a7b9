/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 项目资源负载接口
        resourceLoad: {
            // 项目资源负载-日视图
            getTableListByDay(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_resource_load/resource_day_view_project',
                    method: 'post',
                    data
                });
            },
            // 项目资源负载-月视图
            getTableListByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_resource_load/resource_month_view_project',
                    method: 'post',
                    data
                });
            },
            // 项目资源负载-日视图-任务列表查询
            getTaskDetailByDay(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_resource_load/resource_day_view_task_info_project',
                    method: 'post',
                    data
                });
            },
            // 项目资源负载-月视图-任务列表查询
            getTaskDetailByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project_resource_load/resource_month_view_task_info_project',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
