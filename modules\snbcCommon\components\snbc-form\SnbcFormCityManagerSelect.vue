<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-select v-model="config.modelObj[config.modelKey]" v-bind="elSelectAttrs" @change="handleChange">
            <el-option v-for="(option, index) in elOptions" :key="index" :label="option.label" :value="option.value" />
        </el-select>
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormCityManagerSelect',
    props: {
        /**
         * SnbcFormSelect组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    dictTypeCode: '',
                    elFormItemAttrs: {},
                    elSelectAttrs: {},
                    dictResultHook: null
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-select组件默认属性设置
            defaultElSelectAttrs: {
                clearable: true
            },
            // 下拉数据
            elOptions: []
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-select组件应用属性
        elSelectAttrs() {
            return {
                ...this.defaultElSelectAttrs,
                placeholder: `请选择${this.config.name}`,
                ...(this.config.elSelectAttrs || {})
            };
        }
    },
    created() {
        this.queryDictOptions();
    },
    methods: {
        // 城市负责人查询
        async queryDictOptions() {
            try {
                const res = await this.$service.providerManagement.personnel.getList();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                this.elOptions = result.map((item) => {
                    return {
                        ...item,
                        label: item.engineerName,
                        value: item.engineerUserId
                    };
                });
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        // 下拉数据切换操作
        handleChange() {
            this.config.changeHandler && this.config.changeHandler();
        }
    }
};
</script>
