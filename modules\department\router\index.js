// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/department',
        redirect: 'noRedirect',
        name: 'department',
        useLayout: true,
        meta: {
            title: '部门',
            icon: 'el-icon-wallet'
        },
        children: [
            {
                path: 'workTime',
                component: () => import('../views/workTime'),
                name: 'WorkTime',
                meta: {
                    title: '工时',
                    icon: 'el-icon-notebook-1'
                }
            },
            {
                path: 'naturalResources',
                component: () => import('../views/naturalResources'),
                name: 'NaturalResources',
                meta: {
                    title: '资源',
                    icon: 'el-icon-s-finance'
                }
            },
            {
                path: 'riskControl',
                component: () => import('../views/riskControl'),
                name: 'RiskControl',
                meta: {
                    title: '风控'
                }
            }
        ]
    }
];
