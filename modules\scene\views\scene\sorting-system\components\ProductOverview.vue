<template>
    <div
        class="product-overview-container"
        :style="{
            height: `${containerHeight}px`
        }"
    >
        <div
            class="product-overview"
            :style="{ transform: `scale(${scale})` }"
            ref="productOverviewRef"
        >
            <div class="product-overview-title">
                <div>
                    物流分拣产品，主要应用在{{
                        description.sceneDescription
                    }}
                    ，总计<span class="bold-number">{{
                        description.sceneNumber
                    }}</span
                    >个应用领域：
                </div>
                <div>
                    其中，
                    <span
                        v-for="(i, index) in description.financeDescSceneInfoVo"
                        :key="i.sceneType"
                    >
                        {{ i.sceneType }}包含
                        <span class="bold-number">
                            {{ i.productTypeNum }}
                        </span>
                        类产品{{
                            index ===
                            description.financeDescSceneInfoVo.length - 1
                                ? '。'
                                : '，'
                        }}
                    </span>
                </div>
                <div>
                    目前总计开发
                    <span class="bold-number">{{
                        description.totalProjectNum
                    }}</span
                    >个项目，其中已发布
                    <span class="bold-number">
                        {{ description.publishProjectNum }}</span
                    >个项目，在研
                    <span class="bold-number">
                        {{ description.doingProjectNum }}</span
                    >个项目，规划中
                    <span class="bold-number">{{
                        description.planProjectNum
                    }}</span>
                    个项目。
                </div>
            </div>
            <!-- 产品情况表 -->
            <product-situation ref="productSituationRef" />
            <!-- 产品分类图 -->
            <product-category
                ref="productCategoryRef"
                :config="productCategroyList"
            />
            <!-- 产品列表 -->
            <product-list
                ref="productListRef"
                :config="productCategroyList"
                :list="list"
            />
        </div>
    </div>
</template>

<script>
import ProductSituation from './ProductSituation.vue';
import ProductCategory from './ProductCategory.vue';
import ProductList from 'scene/views/scene/components/ProductWholeList.vue';

const { origin, hostname } = window.location;
let ori = origin;
if (hostname === '127.0.0.1' || hostname === 'localhost') {
    ori = 'http://dev-pmis.xtjc.net';
}
export default {
    components: {
        ProductSituation,
        ProductCategory,
        ProductList
    },
    data() {
        return {
            containerHeight: 1820,
            scale: 1,
            // 图表实例
            sceneChart: null,
            productCategroyList: [],
            // 顶部文字描述
            description: {},
            list: [],
            // 上一次的宽度
            lastWidth: 0
        };
    },
    computed: {
        // 新增项目时的标志
        count() {
            return this.$store.state.scene.changeProjectCountSuccess;
        }
    },
    watch: {
        count(newVal) {
            if (newVal) {
                // 获取顶部文字描述
                this.getOverviewDescription();
                // 调用接口获取产品情况列表
                this.getFinanceProductOverviewList();
                // 获取产品情况
                this.getProductSetList();
            }
        }
    },
    beforeDestroy() {
        const container = this.$refs.productOverviewRef;
        const parent = container.parentElement;
        if (this.resizeObserver) {
            this.resizeObserver.unobserve(parent);
        }
    },
    mounted() {
        // 获取顶部文字描述
        this.getOverviewDescription();
        // 计算产品综述整体节点的缩放及高度值
        this.computeContainerStyle();
        // 监听父元素尺寸变化，重新计算缩放比例
        this.initResizeObserver();
        // 调用接口获取产品情况列表
        this.getFinanceProductOverviewList();
        // 获取产品情况
        this.getProductSetList();
    },
    methods: {
        computeContainerStyle() {
            const container = this.$refs.productOverviewRef;
            const parent = container.parentElement;
            this.scale = parent.offsetWidth / container.offsetWidth;
            this.containerHeight = container.offsetHeight * this.scale;
        },
        // 监听父元素尺寸变化，重新计算缩放比例
        initResizeObserver() {
            const el = this.$refs.productOverviewRef.parentElement;
            this.lastWidth = el.offsetWidth;
            // 定义变化的阈值
            const THRESHOLD = 50;
            if (el) {
                this.resizeObserver = new ResizeObserver(() => {
                    const currentWidth = el.offsetWidth;
                    if (Math.abs(currentWidth - this.lastWidth) >= THRESHOLD) {
                        this.computeContainerStyle();
                        // 记录当前宽度
                        this.lastWidth = currentWidth;
                    }
                });
                this.resizeObserver.observe(el);
            }
        },
        // 产品情况查询并绘制图表
        async getFinanceProductOverviewList() {
            this.$refs.productSituationRef.sceneChart.showLoading();
            try {
                const params = { businessUnit: '物流分拣' };
                const res =
                    await this.$service.scene.finance.getProductOverviewList(
                        params
                    );
                if (res.head.code === '000000') {
                    const list = res.body || [];
                    this.$refs.productSituationRef.drawChart(
                        list,
                        'sceneChart'
                    );
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            } finally {
                this.$refs.productSituationRef.sceneChart.hideLoading();
            }
        },
        // 产品综述查询
        async getOverviewDescription() {
            try {
                const params = {
                    businessUnit: '物流分拣'
                };
                const res =
                    await this.$service.scene.business_project_finance.getOverviewDescription(
                        params
                    );
                if (res.head.code === '000000') {
                    const list = res.body || [];
                    this.description = list;
                    const map = this.description.financeDescSceneInfoVo.map(
                        (i) => i.sceneType
                    );
                    this.description.sceneDescription = map.join('、');
                    this.description.sceneNumber = map.length;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        // 产品分类查询
        async getProductSetList() {
            try {
                const api =
                    this.$service.scene.finance
                        .getProductSetListInFineneceScene;
                const params = {
                    businessUnit: '物流分拣'
                };
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.list = res.body || [];
                    this.$store.dispatch(
                        'scene/changeSortingSystemProduct',
                        this.list
                    );
                    const width = `${Math.floor(100 / this.list.length)}%`;
                    this.productCategroyList = this.list.map((i) => {
                        return {
                            name: i.productSet,
                            style: { width },
                            img: `${ori}/upload/${i.iconName}.png`
                        };
                    });
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.product-overview-container {
    width: 100%;
    background: url(../../../../assets/product-overview-page-bg.png) no-repeat;
    background-size: cover;
    overflow: hidden;
    .product-overview {
        width: 2503px;
        height: 1896px;
        padding-top: 116px;
        transform-origin: 0 0;
        overflow: auto;
        padding: 116px 100px 0 100px;
        .product-overview-title {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            height: 180px;
            border: 1px solid #ffffff;
            border-radius: 20px;
            color: #00123d;
            background: linear-gradient(180deg, #d4e7fe 0%, #e1e8fa 100%);
            text-align: center;
            font-size: 28px;
            margin: 0 auto 20px auto;
            font-weight: 500;
            line-height: auto;
            padding-left: 20px;
            > div {
                margin: 10px;
            }
            .bold-number {
                font-weight: 600;
                margin: 0 8px;
                color: #0054bb;
                font-size: 34px;
            }
        }
    }
}
</style>
