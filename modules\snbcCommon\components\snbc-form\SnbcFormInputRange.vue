<template>
    <el-form-item class="input-range" v-bind="elFormItemAttrs">
        <el-input
            v-model="config.modelObj[config.modelKey][0]"
            oninput="value=value.replace(/[^\d.]/g,'')"
            v-bind="elInputAttrs"
        >
        </el-input>
        <div style="width: auto; margin: 0 8px"><i class="el-icon-minus"></i></div>
        <el-input
            v-model="config.modelObj[config.modelKey][1]"
            oninput="value=value.replace(/[^\d.]/g,'')"
            v-bind="elInputAttrs"
        >
        </el-input>
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormInput',
    props: {
        /**
         * SnbcFormInput组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elInputAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-input组件默认属性设置
            defaultElInputAttrs: {
                clearable: true
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-input组件应用属性
        elInputAttrs() {
            return {
                ...this.defaultElInputAttrs,
                placeholder: `请输入${this.config.name}`,
                ...(this.config.elInputAttrs || {})
            };
        }
    },
    methods: {}
};
</script>
<style lang="scss" scoped>
.input-range {
    ::v-deep.el-form-item__content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;

        .el-input {
            flex: 1 !important;
        }
    }
}
</style>
