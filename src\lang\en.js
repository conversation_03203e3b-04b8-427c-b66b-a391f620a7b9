export default {
    route: {
        dashboard: 'Dashboard',
        documentation: 'Documentation',
        guide: 'Guide',
        permission: 'Permission',
        pagePermission: 'Page Permission',
        rolePermission: 'Role Permission',
        directivePermission: 'Directive Permission',
        icons: 'Icons',
        components: 'Components',
        tinymce: 'Tinymce',
        markdown: 'Markdown',
        jsonEditor: 'JSON Editor',
        dndList: 'Dnd List',
        splitPane: 'SplitPane',
        avatarUpload: 'Avatar Upload',
        dropzone: 'Dropzone',
        sticky: 'Sticky',
        countTo: 'Count To',
        componentMixin: 'Mixin',
        backToTop: 'Back To Top',
        dragDialog: 'Drag Dialog',
        dragSelect: 'Drag Select',
        dragKanban: 'Drag Kanban',
        charts: 'Charts',
        keyboardChart: 'Keyboard Chart',
        lineChart: 'Line Chart',
        mixChart: 'Mix Chart',
        example: 'Example',
        nested: 'Nested Routes',
        menu1: 'Menu 1',
        'menu1-1': 'Menu 1-1',
        'menu1-2': 'Menu 1-2',
        'menu1-2-1': 'Menu 1-2-1',
        'menu1-2-2': 'Menu 1-2-2',
        'menu1-3': 'Menu 1-3',
        menu2: 'Menu 2',
        Table: 'Table',
        dynamicTable: 'Dynamic Table',
        dragTable: 'Drag Table',
        inlineEditTable: 'Inline Edit',
        complexTable: 'Complex Table',
        tab: 'Tab',
        form: 'Form',
        createArticle: 'Create Article',
        editArticle: 'Edit Article',
        articleList: 'Article List',
        errorPages: 'Error Pages',
        page401: '401',
        page404: '404',
        errorLog: 'Error Log',
        excel: 'Excel',
        exportExcel: 'Export Excel',
        selectExcel: 'Export Selected',
        mergeHeader: 'Merge Header',
        uploadExcel: 'Upload Excel',
        zip: 'Zip',
        pdf: 'PDF',
        exportZip: 'Export Zip',
        theme: 'Theme',
        clipboardDemo: 'Clipboard',
        i18n: 'I18n',
        externalLink: 'External Link',
        profile: 'Profile',
        columnOptional: 'Column optional',
        headerSettings: 'Header Settings',
        showFields: 'Show Fields',
        fieldNotDisplayed: 'Field not displayed'
    },
    table: {
        dynamicTips1: 'Fixed header, sorted by header order',
        dynamicTips2: 'Not fixed header, sorted by click order',
        dragTips1: 'The default order',
        dragTips2: 'The after dragging order',
        title: 'Title',
        importance: 'Imp',
        type: 'Type',
        remark: 'Remark',
        search: 'Search',
        add: 'Add',
        export: 'Export',
        reviewer: 'reviewer',
        id: 'ID',
        date: 'Date',
        author: 'Author',
        readings: 'Readings',
        status: 'Status',
        actions: 'Actions',
        edit: 'Edit',
        publish: 'Publish',
        draft: 'Draft',
        delete: 'Delete',
        cancel: 'Cancel',
        confirm: 'Confirm'
    },
    navbar: {
        dashboard: 'Dashboard',
        github: 'Github',
        logOut: 'Log Out',
        profile: 'Basic information',
        changePWD: 'Change Password',
        theme: 'Theme',
        size: 'Global Size',
        search: 'Search Menu',
        sizeChange: 'Switch Size Success'
    },
    documentation: {
        documentation: 'Documentation',
        github: 'Github Repository'
    },
    permission: {
        addRole: 'New Role',
        editPermission: 'Edit',
        roles: 'Your roles',
        switchRoles: 'Switch roles',
        tips: 'In some cases, using v-permission will have no effect. For example: Element-UI  el-tab or el-table-column and other scenes that dynamically render dom. You can only do this with v-if.',
        delete: 'Delete',
        confirm: 'Confirm',
        cancel: 'Cancel'
    },
    guide: {
        description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',
        button: 'Show Guide'
    },
    example: {
        warning: 'Creating and editing pages cannot be cached by keep-alive because keep-alive include does not currently support caching based on routes, so it is currently cached based on component name. If you want to achieve a similar caching effect, you can use a browser caching scheme such as localStorage. Or do not use keep-alive include to cache all pages directly. See details'
    },
    errorLog: {
        tips: 'Please click the bug icon in the upper right corner',
        description: 'Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.',
        documentation: 'Document introduction'
    },
    excel: {
        export: 'Export',
        selectedExport: 'Export Selected Items',
        placeholder: 'Please enter the file name (default excel-list)'
    },
    zip: {
        export: 'Export',
        placeholder: 'Please enter the file name (default file)'
    },
    pdf: {
        tips: 'Here we use window.print() to implement the feature of downloading PDF.'
    },
    theme: {
        change: 'Change Theme',
        documentation: 'Theme documentation',
        tips: 'Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.'
    },
    tagsView: {
        refresh: 'Refresh',
        close: 'Close',
        closeOthers: 'Close Others',
        closeAll: 'Close All'
    },
    settings: {
        title: 'Page style setting',
        theme: 'Theme Color',
        tagsView: 'Open Tags-View',
        fixedHeader: 'Fixed Header',
        sidebarLogo: 'Sidebar Logo'
    },
    common: {
        success: 'Success',
        failed: 'Failed',
        downloadT: 'Download',
        uploadT: 'Upload',
        tips: 'Tips',
        application: 'Application',
        searchHeader: 'Query by criteria',
        searchBtn: 'Query',
        resetBtn: 'Reset',
        highSearch: 'Advanced Query',
        queryList: 'Query list',
        add: 'Add',
        edit: 'Edit',
        preview: 'Preview',
        detail: 'View Details',
        upload: 'Import',
        delete: 'Delete',
        download: 'Download',
        chooseOrg: 'Select organization',
        chooseArea: 'Select region',
        choosePower: 'Select authority',
        addPerson: 'Add person',
        addUser: 'Add user',
        bindRegion: 'Binding region',
        freeze: 'Freeze',
        unfreeze: 'Unfreeze',
        passwordReset: 'Password reset',
        enabled: 'Enabled',
        notUse: 'disabled',
        addDicType: 'Add dictionary type',
        sendAgain: 'Send again',
        disabled: "Don't refresh",
        save: 'Save',
        return: 'Return',
        done: 'Done',
        cancel: 'Cancel',
        choose: 'Pls Choose',
        returnTop: 'Return top',
        filter: 'Filter',
        settings: 'Settings',
        profile: 'Change password',
        logout: 'Log out',
        help: 'Help',
        notifications: 'Notifications',
        seeAll: 'View more information',
        have: 'Have',
        fixHeader: 'Fixed header',
        fixAside: 'Fixed aside menu',
        foldAside: 'Folded aside menu',
        dockAside: 'Dock aside menu',
        boxLayout: 'Boxed layout',
        No: 'Num',
        handle: 'Handle',
        uploadFile: 'Upload import file',
        placeholderFile: 'Please select upload file',
        uploadAction: 'Upload attachment',
        column: 'Column optional',
        close: 'Close',
        clear: 'Clear',
        today: 'Today',
        definedList: 'Defined List',
        deleteTip: 'Confirm to delete the item?',
        deleteChild: 'Pls delete submenu first!',
        chooseRolePerm: 'Pls choose role permission!',
        chooseOneOrg: 'Pls select an organization!',
        chooseOneOrEditOrg: 'Pls select or edit an organization!',
        chooseOneOpt: 'please select an operation!',
        parentMenu: 'Parent menu name',
        addChildren: 'Add a chile',
        order: 'Adjust the order',
        dragOrder: 'Please drag to adjust the order',
        lastStep: 'Last step',
        nextStep: 'Next step',
        total: 'Total',
        count: '',
        currentP: 'Current page',
        welcome: 'Welcome to use this platform',
        pageNoPerssion: 'Dear user, you do not have permission for this function',
        noPerssionLinkAdm: 'If necessary, please contact your administrator to open this function for you',
        noTokenTip: 'The login is invalid, please log out and log in again!',
        noNetWork: 'The current network is not available, please check your network settings',
        changeLanguage: 'Successfully switched to English',
        interfaceFailed: 'Interface call exception',
        tentant: 'Merchant MGT',
        pleaseWait: 'Processing, please wait…',
        requestFaile: 'request was aborted...'
    },
    httpCode: {
        http401: '401 no permission -- core',
        http404: '404 page not found --core!!!'
    }
};

