export default {
    前台结账场景: {
        name: '前台结账场景',
        point: { x: 720, y: 363 },
        crossPoints: [{ x: 930, y: 375 }],
        children: [0, 1, 2].map((num) => {
            const x = 980;
            const y = 340 - num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'right',
                crossPoints: [
                    { x: 930, y: 375 - num * 120 },
                    { x: 930 + 50, y: 375 - num * 120 }
                ],
                nextPoints: getRightPoints2(x, y)
            };
        })
    },
    后台现金管理: {
        name: '后台现金管理',
        point: { x: 420, y: 258 },
        crossPoints: [{ x: 330, y: 270 }],
        children: [0, 1, 2].map((num) => {
            const x = 200;
            const y = 115 + num * 120;
            return {
                x,
                y,
                level: 1,
                position: 'left',
                crossPoints: [
                    { x: 330, y: 150 + num * 120 },
                    { x: x + 70, y: 150 + num * 120 }
                ],
                nextPoints: getLeftPoints2(x, y)
            };
        })
    }
};

/**
 * 获取右边点位配置，第二排
 * @param {*} parentX 父级x坐标
 * @param {*} parentY 父级y坐标
 * @returns {array} 底部点位配置
 */
function getRightPoints2(parentX, parentY) {
    return [0, 1, 2].map((num) => {
        const x = parentX + 160;
        const y = 135 + num * 120;
        return {
            x,
            y,
            level: 2,
            position: 'right',
            crossPoints: [
                { x: parentX + 70 + 40, y: parentY + 35 },
                { x: parentX + 70 + 40, y: 170 + num * 120 },
                { x, y: 170 + num * 120 }
            ]
        };
    });
}

/**
 * 获取左边点位配置，第二排
 * @param {*} parentX 父级x坐标
 * @param {*} parentY 父级y坐标
 * @returns {array} 底部点位配置
 */
function getLeftPoints2(parentX, parentY) {
    return [0, 1, 2].map((num) => {
        const x = parentX - 160;
        const y = 355 - num * 120;
        return {
            x,
            y,
            level: 2,
            position: 'left',
            crossPoints: [
                { x: parentX - 40, y: parentY + 35 },
                { x: parentX - 40, y: y + 35 },
                { x: x + 70, y: 390 - num * 120 }
            ]
        };
    });
}
