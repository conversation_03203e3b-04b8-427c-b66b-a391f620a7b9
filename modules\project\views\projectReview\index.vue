<template>
    <div class="finance-container">
        <project-selector
            @input="handleChange"
            :key="projectSelectorKey"
        ></project-selector>
        <el-tabs v-model="activeName" class="tabs">
            <el-tab-pane label="评审列表" name="reviewList" :lazy="true">
                <ProjectReviewList
                    :activeName="activeName"
                    :projectId="projectId"
                ></ProjectReviewList>
            </el-tab-pane>
            <el-tab-pane label="评审纪要跟踪" name="reviewMinutes" :lazy="true">
                <ReviewMinutesTracking
                    :activeName="activeName"
                    :projectId="projectId"
                ></ReviewMinutesTracking>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector.vue';
import ProjectReviewList from './ProjectReviewList';
import ReviewMinutesTracking from './ReviewMinutesTracking';

export default {
    name: 'ProjectReview',
    components: { ProjectSelector, ProjectReviewList, ReviewMinutesTracking },
    data() {
        return {
            projectSelectorKey: 0,
            activeName: 'reviewList',
            projectId: ''
        };
    },
    async mounted() {
        // 缓存页面
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    activated() {
        this.projectSelectorKey += 1;
    },
    methods: {
        /**
         * 选择后的回调
         * @param {Array} value  选中的值
         */
        handleChange(value) {
            this.projectId = value[value.length - 1];
        }
    }
};
</script>

<style lang="scss" scoped>
.finance-container {
    overflow: auto;
    height: 100vh;
}
.tabs {
    margin: 10px 10px 0 16px;
}
</style>
