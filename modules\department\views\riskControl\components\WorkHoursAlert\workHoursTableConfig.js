import CommonItems from 'snbcCommon/common/form-items.js';

const { select, input, dateRange } = CommonItems;

const calculateDateRange = {
    ...dateRange,
    name: '预警日期',
    modelKey: 'calculateDateRange',
    elDatePickerAttrs: {
        'value-format': 'yyyy-MM-dd'
    }
};
const critical = {
    ...select,
    name: '标志',
    modelKey: 'critical',
    elOptions: [
        {
            label: '关键',
            value: '1'
        },
        {
            label: '非关键',
            value: '0'
        }
    ]
};
const warningLevel = {
    ...select,
    name: '预警时程度',
    modelKey: 'warningLevel',
    elOptions: []
};

const projectName = {
    ...input,
    name: '项目',
    modelKey: 'projectName'
};
// 查询参数初始化
const queryParams = {
    calculateDateRange: [],
    critical: '',
    warningLevel: '',
    projectName: ''
};

// 查询区域配置项
const queryConfigItems = [
    calculateDateRange,
    critical,
    warningLevel,
    projectName
];

// eslint-disable-next-line max-lines-per-function
const getTableConfig = (scope) => {
    return {
        // 列表查询参数
        queryParams,
        // 查询项配置
        queryConfig: {
            items: queryConfigItems
        },
        // 查询api配置
        queryApi: scope.$service.department.riskControl.getWaringList,
        headerButtons: [
            { name: '导出', type: 'primary', handleClick: scope.handleExport }
        ],
        // 列表各列配置,默认展示进行中的项目
        elTableColumns: [
            {
                label: '预警日期',
                prop: 'warningDate',
                show: true,
                minWidth: 110,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '预警时程度',
                prop: 'warningLevel',
                show: true,
                minWidth: 140,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目类别',
                prop: 'projectType',
                show: true,
                minWidth: 110,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '项目',
                prop: 'projectName',
                show: true,
                minWidth: 250,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                }
            },
            {
                label: '任务',
                show: true,
                minWidth: 350,
                elTableColumnAttrs: {
                    'resizable': false,
                    'header-align': 'center',
                    'align': 'left'
                },
                slot: 'task'
            },
            {
                label: '标志',
                prop: 'critical',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '计划完成时间',
                prop: 'planFinishDate',
                show: true,
                minWidth: 110,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '责任人',
                prop: 'taskAccount',
                show: true,
                minWidth: 80,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '当前剩余工期',
                prop: 'leftDuration',
                show: true,
                minWidth: 100,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '当前剩余工时',
                prop: 'remainingDuration',
                show: true,
                minWidth: 100,
                elTableColumnAttrs: {
                    resizable: false
                }
            },
            {
                label: '当前工时工期比率',
                prop: 'rate',
                show: true,
                minWidth: 120,
                elTableColumnAttrs: {
                    resizable: false
                }
            }
        ],
        // 分页
        pageParams: {
            currentPage: 1,
            pageSize: 50
        },
        // 固定表头
        elTableAttrs: {
            'height': 'calc(100vh - 340px)',
            'header-cell-style': '{text-align:center}'
        },
        hooks: {
            queryParamsHook: scope.queryParamsHook
        },
        operations: [
            {
                name: '指派',
                type: 'primary',
                handleShow: (row) => {
                    const showButton =
                        row.transferFlag === '转出' || !row.transferFlag;
                    return showButton;
                },
                handleClick: scope.handleAssignClick
            }
        ]
    };
};
export { getTableConfig };
