<template>
    <div>
        <el-dialog :title="title" :visible.sync="dialogVisible" width="90%">
            <el-table
                :data="processedRiskList"
                style="width: 100%"
                :span-method="objectSpanMethod"
                class="vertical-align-top-table"
            >
                <el-table-column
                    prop="index"
                    label="序号"
                    width="55"
                    align="center"
                />
                <el-table-column
                    prop="projectName"
                    label="项目名称"
                    width="200"
                    header-align="center"
                >
                    <template slot-scope="scope">
                        <div style="white-space: pre-line">
                            {{ scope.row.projectName }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="projectManager"
                    label="项目经理"
                    width="70"
                    align="center"
                />
                <el-table-column
                    prop="riskDesc"
                    label="描述及影响"
                    min-width="180"
                    header-align="center"
                />
                <el-table-column
                    prop="riskLevel"
                    label="风险等级"
                    width="70"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="supportItem"
                    label="需支持事项"
                    min-width="180"
                    header-align="center"
                />
                <el-table-column
                    prop="expectedDate"
                    label="期望达成日期"
                    width="95"
                    align="center"
                />
                <el-table-column
                    prop="formattedResponsibleOrg"
                    label="提供支持责任部门"
                    width="150"
                    header-align="center"
                >
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'SupplymentListDialog',
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        // 风险列表
        riskList: {
            type: Array,
            default: () => []
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableData: [],
            selectedRows: [],
            loading: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        },
        processedRiskList() {
            if (!this.riskList || this.riskList.length === 0) {
                return [];
            }
            let riskIndex = 0;
            let lastRiskId = null;

            return this.riskList.map((item) => {
                if (item.riskId !== lastRiskId) {
                    riskIndex += 1;
                    lastRiskId = item.riskId;
                }
                return {
                    ...item,
                    index: riskIndex,
                    formattedResponsibleOrg: this.formatResponsibleOrg(item)
                };
            });
        }
    },
    methods: {
        /**
         * 格式化组织部门
         * @param {Object} row 行数据
         * @returns {String} 格式化后的部门
         */
        formatResponsibleOrg(row) {
            const { responsibleOrg, responsiblePerson } = row;
            if (responsiblePerson) {
                return `${responsibleOrg || ''}（${responsiblePerson}）`;
            }
            return responsibleOrg || '';
        },
        /**
         * 合并单元格
         * @param {Object} param 表格数据
         * @returns {Object} 合并单元格
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只处理序号，描述及影响、风险等级的合并
            if (![0, 3, 4].includes(columnIndex)) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }

            // 根据列确定要比较的字段
            const getCompareField = (colIndex) => {
                switch (colIndex) {
                    case 0:
                        return 'riskDesc';
                    case 3:
                        return 'riskDesc';
                    case 4:
                        return 'solutionMeasures';
                    default:
                        return '';
                }
            };

            const compareField = getCompareField(columnIndex);
            if (rowIndex === 0) {
                let count = 1;
                for (let i = 1; i < this.processedRiskList.length; i++) {
                    if (
                        this.processedRiskList[i][compareField] ===
                            row[compareField] &&
                        this.processedRiskList[i].riskId === row.riskId
                    ) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                return {
                    rowspan: count,
                    colspan: 1
                };
            }

            // 与上一行比较，判断是否需要合并
            const prevRow = this.processedRiskList[rowIndex - 1];
            // 必须是相同会议才会进行合并
            if (
                prevRow[compareField] === row[compareField] &&
                prevRow.riskId === row.riskId
            ) {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }

            // 计算当前行需要合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < this.processedRiskList.length; i++) {
                if (
                    this.processedRiskList[i][compareField] ===
                        row[compareField] &&
                    this.processedRiskList[i].riskId === row.riskId
                ) {
                    count += 1;
                } else {
                    break;
                }
            }

            return {
                rowspan: count,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.vertical-align-top-table ::v-deep .el-table__body td {
    vertical-align: top;
}

// 预先定义表格行最小高度，避免布局抖动
::v-deep .el-table .el-table__row {
    min-height: 60px;
}

.progress-input-container {
    display: flex;
    align-items: center;
    justify-content: center;

    .percent-sign {
        margin-left: 2px;
        font-size: 14px;
    }
}
.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
}

// 添加loading时的样式覆盖
::v-deep .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.5);
}
</style>
