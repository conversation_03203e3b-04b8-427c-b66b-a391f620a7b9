<template>
    <el-dialog :title="$t('systemManagement.role.assignRole')" :visible.sync="isShow" custom-class="transfer-dialog role-dialog" @close="cancel">
        <el-card>
            <div class="header">{{ $t('systemManagement.role.roleList') }}</div>
            <el-input v-model="filterText" for="msg1" :placeholder="$t('systemManagement.role.placeholder.msg1')" size="normal" clearable>
                <i slot="suffix" class="el-input__icon fa fa-search" />
            </el-input>
            <div class="content list">
                <el-checkbox-group v-model="dataRoleTargetList" for="dataUserSourceFilterList">
                    <div v-for="item in dataUserSourceFilterList" :key="item.roleId">
                        <el-checkbox :label="item">
                            {{ item.roleName }}
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
            </div>
        </el-card>

        <el-card>
            <div class="header">
                <span class="title">{{
          $t("systemManagement.role.message.currentSelectRoleCount", [
            dataRoleTargetList.length
          ])
        }}</span>
                <i v-show="hasSelected" class="el-icon-error text-close-all" @click.stop="removeAllTargetRole()" />
            </div>
            <div class="list">
                <div v-for="item in dataRoleTargetList" :key="item.roleId" class="block" for="dataRoleTargetList">
                    <span class="block__text">{{ item.roleName }}</span>
                    <span class="block__btns">
                        <i class="el-icon-error" @click.stop="removeTargetRole(item)" />
                    </span>
                </div>
            </div>
        </el-card>

        <span slot="footer">
            <el-button @click="cancel">{{
        $t("systemManagement.role.cancel")
      }}</el-button>
            <el-button type="primary" @click="save">{{
        $t("systemManagement.role.save")
      }}</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    props: {
        // 是否显示窗口
        show: {
            type: Boolean,
            default: false
        },
        // 选中的用户列表
        userList: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            filterText: '',
            // 所有角色列表
            dataRoleSourceList: [],
            // 已选角色列表
            dataRoleTargetList: [],
            // 是否有选中的
            hasSelected: false
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        // 传入组件的用户信息，即待分配角色的用户信息
        selectUserList() {
            return this.userList;
        },
        // 表格数据过滤
        dataUserSourceFilterList() {
            return this.dataRoleSourceList.filter((item) => {
                return (item.roleName || '').includes(this.filterText);
            });
        }
    },
    watch: {
        dataRoleTargetList: {
            handler(newVal, oldVal) {
                if (newVal.length > 0) {
                    this.hasSelected = true;
                } else {
                    this.hasSelected = false;
                }
            }
        }
    },
    created() {
        this.getRoleList();
    },
    methods: {
        // 获取角色列表
        getRoleList() {
            this.$service.systemManagement.getRoleList().then((response) => {
                if (response.head.code === '000000') {
                    this.dataRoleSourceList = response.body;
                } else {
                    this.$message({
                        message:
                            response.head.message ||
                            this.$t(
                                'systemManagement.role.message.queryListFailure'
                            ),
                        type: 'error'
                    });
                }
            });
        },
        // 添加用户关系
        postAddUserRelation() {
            this.$service.systemManagement
                .postAddUserRoleRelation({
                    userIds: this.selectUserList.map((item) => {
                        return item.userId;
                    }),
                    roleName: this.dataRoleTargetList
                        .map((item) => {
                            return item.roleName;
                        })
                        .join(','),
                    roleIds: this.dataRoleTargetList.map((item) => {
                        return item.roleId;
                    }),
                    userNames: this.selectUserList
                        .map((item) => {
                            return item.userName;
                        })
                        .join(',')
                })
                .then((response) => {
                    const isSuccess = response.head.code === '000000';
                    if (isSuccess) {
                        this.$message({
                            message: this.$t(
                                'systemManagement.role.message.saveSuccess'
                            ),
                            type: 'success'
                        });
                        this.isShow = false;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message:
                                this.$t(msg) ||
                                this.$t(
                                    'systemManagement.role.message.saveFailure'
                                ),
                            type: 'error'
                        });
                    }

                    this.$emit('save-finish', isSuccess);
                });
        },
        // 移除已选中角色
        removeTargetRole(data) {
            // 重新计算已选择的列表
            this.dataRoleTargetList = this.dataRoleTargetList.filter((item) => {
                return item.roleId !== data.roleId;
            });
        },
        // 清除所有选项
        removeAllTargetRole() {
            this.dataRoleTargetList = [];
        },
        // 取消
        cancel() {
            this.isShow = false;
        },
        // 保存
        save() {
            // 如果一个角色都没选，则提示
            if (this.dataRoleTargetList.length === 0) {
                this.$message({
                    message: this.$t(
                        'systemManagement.role.message.assignRoleEmptySelect'
                    ),
                    type: 'error'
                });
                return;
            }

            this.postAddUserRelation();
        }
    }
};
</script>

<style lang="scss" scoped>
.role-dialog {
    .header {
        .text-close-all {
            font-size: 15px;
            color: #8996a8;
            cursor: pointer;
        }
    }
    .list {
        margin-top: 10px;
        .block {
            background: #eaf0ff;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 14px;
            .block__text {
                margin-left: 11px;
            }
            .block__btns {
                .el-icon-error {
                    color: #c7c7c7;
                }
            }
        }
        .el-checkbox-group {
            .el-checkbox {
                margin-top: 15px;
                &:first-of-type {
                    margin-top: 10px;
                }
            }
        }
    }
}
</style>
