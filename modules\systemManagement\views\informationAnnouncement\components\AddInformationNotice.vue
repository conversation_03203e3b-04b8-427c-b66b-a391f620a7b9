<template>
    <div class="view detail-box">
        <div class="container detail-view">
            <div class="detail-back">
                <div class="back">
                    <i class="fa fa-arrow-left" />
                    <span @click="goBack">{{ $t("systemManagement.informationAnnounce.addPage.back") }}</span>
                </div>
                <el-divider direction="vertical" />
                <span class="title">{{ $t("systemManagement.informationAnnounce.addPage.noticeInfo") }}</span>
            </div>
            <el-form ref="noticeForm" :model="noticeForm" :rules="rules" label-width="auto">
                <el-form-item :label="$t('systemManagement.informationAnnounce.addPage.title')" prop="msgTitle">
                    <el-input v-model="noticeForm.msgTitle" style="width: 35%;" :placeholder="$t('systemManagement.informationAnnounce.message.inputTheme')" />
                </el-form-item>
                <el-form-item :label="`${$t(
            'systemManagement.informationAnnounce.addPage.class'
          )}`" prop="msgModel">
                    <el-select v-model="noticeForm.msgModel" style="width: 35%;" clearable :popper-append-to-body="false" :placeholder="
              $t('systemManagement.informationAnnounce.message.selectClass')
            ">
                        <el-option v-for="item in classList" :key="item.valueId" :label="$t('systemManagement.dictionaryManagement.value.'+item.valueName)" :value="item.valueCode" />
                    </el-select>
                </el-form-item>
                <el-form-item :label="$t('systemManagement.informationAnnounce.addPage.noticeRange')" prop="range">
                   <div class="notice-range">
                    <el-tag v-for="tag in orgList" :key="tag.orgId"  :closable="tag.canRemove === 'false' ? false:true" type="info" @close="removeOrgTag(tag)">
                        {{ tag.orgName }}
                    </el-tag>
                    <el-tag v-for="tag in personList" :key="tag.orgId" closable type="info" @close="removePersonTag(tag)">
                        {{ tag.userName }}
                    </el-tag>
                    <i class="fa fa-plus-circle icon-font" aria-hidden="true" @click="showAddUserRelation" />
                   </div>
                </el-form-item>
                <el-form-item :label="$t('systemManagement.informationAnnounce.addPage.attachmentsList')">
                    <div class="file-box">
                        <el-tag v-for="(tag,index) in fileList" :key="tag.name + index" closable type="info" @close="removeFileTag(tag,index)">
                            <i v-if="tag.icon === 'pdf'" class="fa fa-file-pdf-o" aria-hidden="true" />
                            <i v-else-if="tag.icon === 'png' || tag.icon === 'jpg'|| tag.icon === 'jpeg' " class="fa fa-file-image-o" aria-hidden="true" />
                            <i v-else-if="tag.icon === 'doc' || tag.icon === 'docx' " class="fa fa fa-file-word-o" aria-hidden="true" />
                            <i v-else-if="tag.icon === 'xls' || tag.icon === 'xlsx' " class="fa fa-file-excel-o" aria-hidden="true" />
                            <i v-else class="fa fa-file-powerpoint-o" aria-hidden="true" />
                            <span>{{ tag.name }}</span>
                        </el-tag>
                        <el-upload class="upload-pdf" :action="pdfAction" :headers="pdfUploadHeaders" :show-file-list="false" :on-success="handlePdfSuccess" :on-error="handlUploadError" :before-upload="beforePdfUpload">
                            <!-- <i class="fa fa-link icon-font" aria-hidden="true" />
                            <span class="add-file">{{ $t('systemManagement.informationAnnounce.addPage.addFile') }}</span> -->
                            <el-button type="primary" icon="fa fa-link">{{ $t('systemManagement.informationAnnounce.addPage.addFile') }}</el-button>
                        </el-upload>
                    </div>
                </el-form-item>
                <el-form-item class="edit-box">
                    <quill-editor ref="myQuillEditor" v-model="noticeForm.msgContent" :options="editorOption" @blur="onEditorBlur($event)" @focus="onEditorFocus($event)" @change="onEditorChange($event)" />
                    <el-upload class="upload-pdf avatar-uploader" :action="pdfAction" :headers="pdfUploadHeaders" :show-file-list="false" :on-success="uploadSuccess" :on-error="handlUploadError" :before-upload="beforeImgUpload" />
                </el-form-item>
                <el-form-item class="foot-btns">
                    <el-button type="primary" @click="preview">{{ $t('systemManagement.informationAnnounce.addPage.btn.preview') }}</el-button>
                    <el-button type="primary" @click="save('noticeForm')">{{ $t('systemManagement.informationAnnounce.addPage.btn.save') }}</el-button>
                    <el-button type="primary" @click="submitForm('noticeForm')">{{ $t('systemManagement.informationAnnounce.addPage.btn.publish') }}</el-button>
                </el-form-item>
            </el-form>

            <!-- 给角色添加用户 -->
            <add-department-user 
            v-if="addUserRelationInfo.isShow" 
            :show.sync="addUserRelationInfo.isShow" 
            :org-list.sync="orgList" 
            :person-list="personList" 
            :reserveOrgs="reserveOrgs"
            :reserveUsers="reserveUsers"
            @rangeList="rangeList" />

        </div>
        <el-dialog :visible.sync="isShowDetail" custom-class="system-dialog detail-dialog">
            <detail :file-list="fileList" :notice-form="noticeForm" :is-dialog="isDialog" />
        </el-dialog>

    </div>

</template>
<script>
import AddDepartmentUser from './AddDepartmentUser.vue';
import { Loading } from 'element-ui';
import { encryptByMd5 } from 'wtf-core-vue/src/utils/crypto';
import { randomStr, randomTime } from 'wtf-core-vue/src/methods/signature';
import detail from '../components/detail';

export default {
    components: { AddDepartmentUser, detail },
    props: {
        noticeForm: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    // eslint-disable-next-line max-lines-per-function
    data() {
        const random = randomStr(13);
        const ranTime = randomTime();
        const final =
            random +
            ranTime +
            encryptByMd5(
                `${random}&${ranTime}axeonmc&/console/v1/boss/productservice/fileUpload&POST`
            );
        const _this = this;
        return {
            // 富文本配置
            editorOption: {
                placeholder: '',
                modules: {
                    toolbar: {
                        container: [
                            // 字体
                            ['bold', 'italic', 'underline', 'strike'],
                            ['blockquote', 'code-block'],
                            // 样式标题
                            [{ header: 1 }, { header: 2 }],
                            [{ list: 'ordered' }, { list: 'bullet' }],
                            // 下标、上标
                            [{ script: 'sub' }, { script: 'super' }],
                            // 缩进
                            [{ indent: '-1' }, { indent: '+1' }],
                            [{ direction: 'rtl' }],
                            // 字体
                            [{ size: ['small', false, 'large', 'huge'] }],
                            [{ header: [1, 2, 3, 4, 5, 6, false] }],
                            [{ color: [] }, { background: [] }],
                            [{ font: [] }],
                            [{ align: [] }],
                            // 格式清除
                            ['clean'],
                            ['image']
                        ],
                        handlers: {
                            image(value) {
                                if (value) {
                                    // 获取隐藏的上传图片的class，触发上传图片事件
                                    document
                                        .querySelector('.avatar-uploader input')
                                        .click(() => {
                                            // 点击事件
                                        });
                                } else {
                                    this.quill.format('image', false);
                                }
                            }
                        }
                    }
                }
            },
            // 解决切换标签后不能编辑的问题
            tinymceFlag: 1,
            // 使用教程上传地址
            pdfAction: '',
            // 使用教程上传请求头设置
            pdfUploadHeaders: {
                Authorization: `Bearer ${this.$tools.getToken()}`,
                signature: final
            },
            // 上传附件列表
            fileList: [],
            // 分类列表
            classList: [],
            // 校验规则
            rules: {
                msgTitle: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.informationAnnounce.message.inputTheme'
                        ),
                        trigger: 'blur'
                    },
                    {
                        max: 50,
                        message: this.$t(
                            'systemManagement.informationAnnounce.message.titleMax'
                        ),
                        trigger: 'blur'
                    }
                ],
                msgModel: [
                    {
                        required: true,
                        message: this.$t(
                            'systemManagement.informationAnnounce.message.selectClass'
                        ),
                        trigger: 'change'
                    }
                ],
                range: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (this.noticeForm.range.length === 0) {
                                return callback(
                                    new Error(
                                        _this.$t(
                                            'systemManagement.informationAnnounce.message.noticeNoEmpty'
                                        )
                                    )
                                );
                            }
                            callback();
                        },
                        trigger: 'change'
                    }
                ]
            },
            // 角色添加关联用户
            addUserRelationInfo: {
                isShow: false
            },
            orgList: [],
            personList: [],
            isShowDetail: false,
            isDialog: true,
            // 保留不可删除的部门
            reserveOrgs:[],
            // 保留不可删除的人员
            reserveUsers:[]
        };
    },
    computed: {
        // 是否显示窗口
        isShow: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit('update:show', val);
            }
        },
        // 阶梯文案
        ladderText() {
            return `阶梯（${this.productGradeUnit}）`;
        }
    },
    mounted() {
        this.getUploadPdfUrl();
        this.getClass();
        this.orgList = this.noticeForm.orgs;
        this.personList = this.noticeForm.users;
        if (this.noticeForm.fileUrl.length > 0) {
            this.noticeForm.fileUrl.forEach((item) => {
                const fileNameIndex = item.lastIndexOf('/');
                const name = item.slice(fileNameIndex + 15);
                const suffixIndex = item.lastIndexOf('.');
                const suffix = item.slice(suffixIndex + 1);
                this.fileList.push({
                    name,
                    icon: suffix,
                    url: item
                });
            });
        }
        this.reserveOrgs = this.noticeForm.orgs.filter((item) => {
            return item.canRemove === 'false';
        });
        this.reserveUsers = this.noticeForm.users.filter((item) => {
            return item.canRemove === 'false';
        });
    },
    // 动态改变key值
    activated() {
        // eslint-disable-next-line no-plusplus
        this.tinymceFlag++;
    },
    methods: {
        // 富文本编辑器中上传本地图片
        uploadSuccess(res) {
            this.loadingInstance && this.loadingInstance.close();
            if (res.head.code === '000000') {
                const dt = res.body;
                const { quill } = this.$refs.myQuillEditor;
                // 获取光标所在位置
                const length = quill.getSelection().index;

                // 插入图片 dt.url为服务器返回的图片地址
                quill.insertEmbed(length, 'image', dt);
                // 调整光标到最后
                quill.setSelection(length + 1);
            } else {
                const { code } = res.head;
                const msg = `frame.bgReturnError[${code}]`;
                this.$message({
                    message: this.$t(msg),
                    type: 'error'
                });
            }
        },
        // 获取分类
        getClass() {
            const param = {
                dictTypeCode: 'message_category'
            };
            this.$service.systemManagement
                .getMessageDictValueList(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.classList = response.body;
                    }
                });
        },
        // 返回
        goBack(flag) {
            this.$emit('goBack', flag);
        },
        // 发布
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    const param = {
                        msgTitle: this.noticeForm.msgTitle,
                        msgModel: this.noticeForm.msgModel,
                        msgContent: this.noticeForm.msgContent,
                        sysCode: 'bbpfboss',
                        orgIds: this.orgList.map((item) => item.orgId),
                        userIds: this.personList.map((item) => item.userId),
                        fileUrls: this.fileList.map((item) => item.url),
                        msgId: this.noticeForm.msgId
                            ? this.noticeForm.msgId
                            : ''
                    };
                    this.$service.systemManagement
                        .publishInformation(param)
                        .then((response) => {
                            if (response.head.code === '000000') {
                                // 提示发布成功
                                this.$message({
                                    message: this.$t(
                                        'systemManagement.informationAnnounce.message.publishSuccess'
                                    ),
                                    type: 'success'
                                });
                                //  关闭新增页面
                                this.goBack('save');
                            } else {
                                const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                this.$message({
                                    message: this.$t(msg),
                                    type: 'error'
                                });
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        // 保存
        save(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let param = '';
                    if (this.noticeForm.msgId) {
                        param = {
                            msgTitle: this.noticeForm.msgTitle,
                            msgModel: this.noticeForm.msgModel,
                            msgContent: this.noticeForm.msgContent,
                            sysCode: 'bbpfboss',
                            orgIds: this.orgList.map((item) => item.orgId),
                            userIds: this.personList.map((item) => item.userId),
                            fileUrls: this.fileList.map((item) => item.url),
                            msgId: this.noticeForm.msgId
                        };

                        this.$service.systemManagement
                            .editInformation(param)
                            .then((response) => {
                                if (response.head.code === '000000') {
                                    // 提示编辑成功
                                    this.$message({
                                        message: this.$t(
                                            'systemManagement.informationAnnounce.message.editSuccess'
                                        ),
                                        type: 'success'
                                    });
                                    //  关闭新增页面
                                    this.goBack('save');
                                } else {
                                    const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                    this.$message({
                                        message: this.$t(msg),
                                        type: 'error'
                                    });
                                }
                            });
                    } else {
                        param = {
                            msgTitle: this.noticeForm.msgTitle,
                            msgModel: this.noticeForm.msgModel,
                            msgContent: this.noticeForm.msgContent,
                            sysCode: 'bbpfboss',
                            orgIds: this.orgList.map((item) => item.orgId),
                            userIds: this.personList.map((item) => item.userId),
                            fileUrls: this.fileList.map((item) => item.url)
                        };
                        this.$service.systemManagement
                            .addInformation(param)
                            .then((response) => {
                                if (response.head.code === '000000') {
                                    // 提示新增成功
                                    this.$message({
                                        message: this.$t(
                                            'systemManagement.informationAnnounce.message.addSuccess'
                                        ),
                                        type: 'success'
                                    });
                                    //  关闭新增页面
                                    this.goBack('save');
                                } else {
                                    const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                                    this.$message({
                                        message: this.$t(msg),
                                        type: 'error'
                                    });
                                }
                            });
                    }
                } else {
                    return false;
                }
            });
        },
        // 预览
        preview() {
            this.isShowDetail = true;
        },
        // 获取使用教程上传路径地址
        getUploadPdfUrl() {
            this.pdfAction = this.$service.systemManagement.getUploadFileUrl();
        },
        // 上传pdf完成后回调
        handlePdfSuccess(res, file) {
            this.loadingInstance && this.loadingInstance.close();
            if (res.head.code === '000000') {
                const suffixIndex = file.name.lastIndexOf('.');
                const suffix = file.name.slice(suffixIndex + 1);
                this.fileList.push({
                    name: file.name,
                    icon: suffix,
                    url: res.body
                });
            } else {
                const { code } = res.head;
                const msg = `frame.bgReturnError[${code}]`;
                this.$message({
                    message:
                        code === '920303'
                            ? this.$t(msg).replace('{0}', res.head.message)
                            : this.$t(msg),
                    type: 'error'
                });
            }
        },
        // 上传图片、上传pdf失败后回调
        handlUploadError(err) {
            this.loadingInstance && this.loadingInstance.close();
            // 此处注意elemnr-ui对错误信息进行了封装
            const errMessage = JSON.parse(err.message);
            this.$message({
                message: errMessage.head.message,
                type: 'error'
            });
        },
        // 上传编辑器上传图片前处理
        beforeImgUpload() {
            this.loadingInstance = Loading.service({
                text: this.$t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            const random = randomStr(13);
            const ranTime = randomTime();
            this.pdfUploadHeaders.signature =
                random +
                ranTime +
                encryptByMd5(
                    `${random}&${ranTime}axeonmc&/console/file/uploadFile&POST`
                );
            this.pdfUploadHeaders['b-g-version'] = '2.0.16';
        },
        // 上传pdf前验证
        beforePdfUpload(file) {
            const suffixIndex = file.name.lastIndexOf('.');
            const suffix = file.name.slice(suffixIndex + 1);
            const fileTypes = [
                'pdf',
                'ppt',
                'pptx',
                'jpg',
                'png',
                'jpeg',
                'xls',
                'xlsx',
                'doc',
                'docx'
            ];
            // 验证格式
            const isFileType = fileTypes.indexOf(suffix) > -1;
            if (!isFileType) {
                this.$message.error(
                    this.$t(
                        'systemManagement.informationAnnounce.message.fileFormat'
                    )
                );
                return false;
            }
            // 验证大小 file.size / 1024 得到的是kb
            const isSize = file.size / 1024 <= 20 * 1024;
            if (!isSize) {
                this.$message.error(
                    this.$t(
                        'systemManagement.informationAnnounce.message.fileMax'
                    )
                );
                return false;
            }
            this.loadingInstance = Loading.service({
                text: this.$t('frame.msg.handling'),
                background: 'rgba(0, 0, 0, 0.1)'
            });
            const random = randomStr(13);
            const ranTime = randomTime();
            this.pdfUploadHeaders.signature =
                random +
                ranTime +
                encryptByMd5(
                    `${random}&${ranTime}axeonmc&/console/file/uploadFile&POST`
                );
            this.pdfUploadHeaders['b-g-version'] = '2.0.16';
        },
        // 显示添加用户接口
        showAddUserRelation() {
            this.addUserRelationInfo.isShow = true;
        },
        // 回显通知范围
        rangeList(orgList, personList) {
            this.orgList = orgList;
            this.personList = personList;
            this.noticeForm.range = this.orgList.concat(this.personList);
        },
        // 移除部门
        removeOrgTag(tag) {
            this.orgList = this.orgList.filter((item) => {
                return item.orgId !== tag.orgId;
            });
            this.noticeForm.range = this.orgList.concat(this.personList);
        },
        // 移除人员
        removePersonTag(tag) {
            this.personList = this.personList.filter((item) => {
                return item.userId !== tag.userId;
            });
            this.noticeForm.range = this.orgList.concat(this.personList);
        },
        // 移除file
        removeFileTag(tag, index) {
            this.fileList = this.fileList.filter((item, itemIndex) => {
                if (item.name === tag.name && index !== itemIndex) {
                    return item.name === tag.name;
                }
                return item.name !== tag.name;
            });
        },
        onEditorReady(editor) {
            // 准备编辑器
        },
        onEditorBlur() {
            // 失去焦点事件
        },
        onEditorFocus() {
            // 获得焦点事件
        },
        onEditorChange() {
            // 内容改变事件
        }
    }
};
</script>
<style lang="scss" scoped>
.detail-box {
    padding: 0px;
    .edit-box {
        .quill-editor {
            ::v-deep .ql-container {
                height: 215px;
            }
        }
    }
}
.foot-btns {
    margin-top: 20px;
}
.icon-font {
    font-size: 16px;
    color: #5584fb;
    cursor: pointer;
    margin-right: 5px;
}
.tutorial {
    color: #3370ff;
    margin-right: 20px;
}
.detail-view {
    .detail-back {
        // 发票详情的样式
        display: flex; // 设置flex布局
        border-bottom: 1px solid #ececec; // 底部外边框
        padding-bottom: 20px; // 底部内边距
        margin-bottom: 20px;
        .back {
            color: #5584fb; // 颜色
            cursor: pointer; // 悬浮出现小手
        }
        .title {
            font-size: 14px; // 字体
            font-weight: bold; // 加粗
            color: #000000; // 颜色
        }
    }
    .detail-context {
        padding-bottom: 20px; // 下内边距
        .header {
            margin-top: 20px;
        }
    }
    .el-tag.el-tag--info {
        margin-right: 10px;
    }
    .file-box {
        display: flex;
        flex-wrap: wrap;
    }
}
.upload-pdf-box {
    position: relative;
    width: 260px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    background: #efefef;
    border: 1px solid #ececec;
    font-size: 14px;
    cursor: pointer;
    .upload-pdf {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        opacity: 0;
        z-index: 1;
        ::v-deep .el-upload {
            width: 100%;
            height: 100%;
        }
    }
}
::v-deep .el-dialog.detail-dialog {
    .el-dialog__header {
        background: #fff !important;
        padding: 0px;
        .el-dialog__title {
            display: none;
        }
    }
    .el-dialog__body {
        padding-top: 0px;
    }
}
.notice-range{
    border: 1px solid #e9e9eb;
    box-sizing: border-box;
    padding:10px;
    border-radius: 4px;
}
::v-deep .fa.fa-link{
    margin-right:5px;
}
</style>
