<template>
    <div class="box-main">
        <department-selector :infoDisabled="true"></department-selector>
        <div class="product-box">
            <div class="box-top">
                <div></div>
                <div>部门负责人配置</div>
            </div>
            <div class="container-top">
                <span>部门名称</span>
                <el-select
                    v-model="firstDepartvalue"
                    filterable
                    placeholder="请选择部门名称"
                    @change="handleFirstDepartChange"
                    class="onelevel"
                >
                    <el-option
                        v-for="item in firstDepart"
                        :key="item.orgCode"
                        :label="item.orgName"
                        :value="item.orgCode"
                        filterable
                    >
                    </el-option>
                </el-select>
            </div>
            <el-table
                :data="tableData"
                border
                style="width: 100%"
                class="snbc-table"
                :header-cell-style="{ background: '#3370ff' }"
            >
                <el-table-column
                    prop="orgName"
                    label="部门名称"
                    align="center"
                    width="150"
                ></el-table-column>
                <el-table-column
                    prop="orgCode"
                    label="部门编码"
                    align="center"
                    width="130"
                >
                </el-table-column>
                <el-table-column
                    prop="justPerson"
                    label="部门主要负责人"
                    align="center"
                    width="200"
                >
                    <template slot-scope="scope">
                        <people-selector
                            v-model="scope.row.justPerson"
                            :isMultipled="false"
                            placeholder="请输入人员"
                            style="width: 98%; padding: 2px"
                        ></people-selector>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="deputyPersons"
                    label="部门次要负责人"
                    align="center"
                    style="width: 98%; padding: 2px"
                >
                    <template slot-scope="scope">
                        <people-selector
                            v-model="scope.row.deputyPersons"
                            placeholder="请输入人员"
                            style="width: 98%; padding: 2px"
                        ></people-selector>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            size="small"
                            @click="updateProductLine(scope.row)"
                            >保存</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import departmentSelector from 'department/components/departmentSelector';
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';

const { TECH_CENTER_CODE } = CONSTANTS;
export default {
    components: { departmentSelector, PeopleSelector },
    data() {
        return {
            tableData: [],
            justPersonData: [],
            deputyPersonData: [],
            firstDepart: [],
            firstDepartvalue: ''
        };
    },
    mounted() {
        this.getfirstDepart();
    },
    methods: {
        // 获取一级部门下拉列表
        getfirstDepart() {
            const data = {
                orgCode: TECH_CENTER_CODE
            };
            this.$service.department.group
                .getSubDepartment(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.firstDepart = res.body || [];
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 查询
        handleFirstDepartChange() {
            this.getTableData(this.firstDepartvalue);
        },
        getTableData(val) {
            const params = {
                departmentCode: val
            };
            this.$service.department.getDirectorpeople(params).then((res) => {
                if (res.head.code === '000000') {
                    this.tableData = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        updateProductLine(data) {
            if (!data.justPerson) {
                this.$message.error('请输入主要负责人');
                return;
            }
            const params = {
                orgCode: data.orgCode,
                orgName: data.orgName,
                orgLevel: data.orgLevel,
                justPerson: data.justPerson,
                deputyPersons: data.deputyPersons
            };
            this.$service.department.editDirectorpeople(params).then((res) => {
                if (res.head.code === '000000') {
                    this.$message.success('修改成功');
                    this.getTableData(this.firstDepartvalue);
                } else {
                    this.$message.error(res.head.message);
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.box-main {
    width: 100%;
    height: calc(100vh - 10px);
    padding: 10px 20px 20px 20px;
    background-color: #ffffff;
    overflow: auto;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.product-box {
    width: 100%;
    height: 100%;
}
.box-top {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
}
.box-top div:first-child {
    width: 5px;
    height: 25px;
    background-color: #3370ff;
}
.box-top div:nth-child(2) {
    font-weight: bolder;
    padding-left: 10px;
}
.container-top {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}
.onelevel {
    margin-left: 15px;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}

::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}
.snbc-table {
    border: 1px solid #8c8c8c !important;
    ::v-deep .el-table__row,
    ::v-deep .has-gutter {
        height: 40px !important;
    }
    ::v-deep .el-table__header .el-table__cell {
        padding: 0;
        height: 40px !important;
    }
    ::v-deep .el-table__row .el-table__cell {
        padding: 0 !important;
        height: 40px !important;
    }
}
</style>
