<template>
    <div>
        <el-select
            class="query"
            v-model="taskStatus"
            placeholder="任务状态"
            clearable
        >
            <el-option
                v-for="item in CONSTANTS.TASK_STATUS"
                :key="item"
                :label="item"
                :value="item"
            >
            </el-option>
        </el-select>
        <el-table
            :data="dataList"
            :header-cell-style="{
                'text-align': 'center'
            }"
            :span-method="arraySpanMethod"
            height="calc(100vh - 200px)"
        >
            <el-table-column prop="meetingTitle" label="评审名称" width="250">
            </el-table-column>
            <el-table-column
                prop="problemItem"
                label="事项/问题"
                min-width="250"
            ></el-table-column>
            <el-table-column
                prop="creatorName"
                label="提出人"
                width="80"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="meetingRequire"
                label="会议要求"
                min-width="300"
            ></el-table-column>
            <el-table-column
                prop="responsibleName"
                label="责任人"
                width="80"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="planFinishDate"
                label="计划完成时间"
                width="110"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="finishStatus"
                label="任务状态"
                width="90"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="finishDesc"
                label="完成情况"
                min-width="250"
            ></el-table-column>
            <el-table-column
                label="操作"
                align="center"
                width="90"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button type="primary" @click="handleEdit(scope.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <MeetingTraceTaskDialog
            :visible.sync="dialogVisible"
            :data="traceTaskData"
            @update="getDataList"
        ></MeetingTraceTaskDialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import MeetingTraceTaskDialog from 'feature/views/meetingManagement/components/meetingTaskTrace/meetingTraceTaskDialog.vue';

export default {
    name: 'ReviewMinutesTracking',
    components: { MeetingTraceTaskDialog },
    props: {
        activeName: {
            type: String,
            default: ''
        },
        projectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dataList: [],
            taskStatus: '未完成',
            CONSTANTS,
            dialogVisible: false,
            traceTaskData: {}
        };
    },
    computed: {},
    watch: {
        activeName(newVal) {
            if (newVal === 'reviewMinutes') {
                this.getDataList();
            }
        },
        projectId() {
            if (this.activeName === 'reviewMinutes') {
                this.getDataList();
            }
        },
        taskStatus() {
            if (this.activeName === 'reviewMinutes') {
                this.getDataList();
            }
        }
    },
    mounted() {
        this.getDataList();
    },
    activated() {
        this.getDataList();
    },
    methods: {
        /**
         * 获取列表数据
         */
        async getDataList() {
            if (!this.projectId) return;
            const api = this.$service.project.review.getMeetingTrackingTaskList;
            const params = {
                projectId: this.projectId,
                taskStatus: this.taskStatus
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.dataList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 判断会议是否已结束
         * @param {Object} row 每行数据
         * @return {Boolean} 会议是否已结束
         */
        isMinutesFinished(row) {
            return (
                row.minutesStatus === '未关闭（有任务未完成）' ||
                row.minutesStatus === '全部关闭' ||
                row.minutesStatus === '审核中'
            );
        },
        /**
         * 合并单元格
         * @param {Object} param 表格数据
         * @returns {Function} 合并单元格
         */
        arraySpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只处理评审名称、事项/问题、会议要求的合并
            if (![0, 1, 3].includes(columnIndex)) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }

            // 根据列确定要比较的字段
            const getCompareField = (colIndex) => {
                switch (colIndex) {
                    case 0:
                        return 'meetingTitle';
                    case 1:
                        return 'problemItem';
                    case 3:
                        return 'meetingRequire';
                    default:
                        return '';
                }
            };

            const compareField = getCompareField(columnIndex);
            if (rowIndex === 0) {
                let count = 1;
                for (let i = 1; i < this.dataList.length; i++) {
                    if (
                        this.dataList[i][compareField] === row[compareField] &&
                        this.dataList[i].meetingId === row.meetingId
                    ) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                return {
                    rowspan: count,
                    colspan: 1
                };
            }

            // 与上一行比较，判断是否需要合并
            const prevRow = this.dataList[rowIndex - 1];
            // 必须是相同会议才会进行合并
            if (
                prevRow[compareField] === row[compareField] &&
                prevRow.meetingId === row.meetingId
            ) {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }

            // 计算当前行需要合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < this.dataList.length; i++) {
                if (
                    this.dataList[i][compareField] === row[compareField] &&
                    this.dataList[i].meetingId === row.meetingId
                ) {
                    count += 1;
                } else {
                    break;
                }
            }

            return {
                rowspan: count,
                colspan: 1
            };
        },
        /**
         * 编辑任务
         * @param {Object} data 该行数据
         */
        handleEdit(data) {
            this.dialogVisible = true;
            data.startTime = data.meetingStartTime;
            data.endTime = data.meetingEndTime;
            data.id = data.minutesId;
            this.traceTaskData = data;
        }
    }
};
</script>

<style lang="scss" scoped>
.meeting-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
}
.query {
    margin-bottom: 10px;
}
// 统一表头高度，修正固定列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}
// 修改placeholder颜色
::v-deep .query .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685);
}
// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
