/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.moudulName.serviceName
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import httpErrorHandle from '../../common/httpErrorHandle.js';
import httpInterceptors from '../../common/httpInterceptors.js';
import { basePathInit } from '../../../../src/envConst.js';

export default (Vue) => {
    const http = Vue.prototype.$http;
    // 进行请求拦截处理
    httpInterceptors(Vue, http);
    // 响应结果处理
    http.responseHandle(httpErrorHandle);
    // 根服务对象
    const basePath = basePathInit();

    const service = {
        // 登录页面  获取验证图片或者验证码
        getLoginCheckInfo(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/captcha/get',
                method: 'get',
                params: data
            });
        },
        // 登录页面  校验验证码或者滑动验证
        postLoginCheck(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/captcha/check',
                method: 'post',
                data
            });
        },
        // 登录页面  登录接口
        postLogin(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/certification/userLogin',
                method: 'post',
                data
            });
        },
        // 登录页面  获取菜单信息
        getMenuList(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/getPermissionTree',
                method: 'get',
                params: data
            });
        },
        // 登录页面  登出
        putlogOut(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/certification/logOut',
                method: 'put',
                params: data
            });
        },
        // 获取用户信息
        getUserInfo(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/getUserDetail',
                method: 'get',
                params: data
            });
        },
        // 获取短信验证码
        getSmsVerifCode(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/smsVerifCode',
                method: 'get',
                params: data
            });
        },
        // 给当前用户发送手机短信验证码
        getCurrentVerifCode(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/sms/getCurrentUserSmsCode',
                method: 'get',
                params: data
            });
        },
        // 修改头像
        updateAvatar(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updateAvatar',
                method: 'post',
                data
            });
        },
        // 修改头像--上传文件
        updateAvatarFile(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updateAvatarFile',
                method: 'post',
                data
            });
        },
        // 修改密码
        updatePwd(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updatePwd',
                method: 'PUT',
                data
            });
        },
        // 获取上传文件的接口地址
        getUploadAvatarUrl() {
            return `${basePath.bossapi.system}/console/user/updateAvatarFile`;
        },
        // 自定义头像上传
        postUploadFiles(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updateAvatarFile',
                method: 'post',
                headers: { 'Content-Type': 'multipart/form-data' },
                data
            });
        },
        // 获取上传文件的接口地址
        postUploadFileUrl(data) {
            return http({
                baseDomain: basePath.bossapi.productService,
                url: '/console/v1/boss/productservice/fileUpload',
                method: 'post',
                headers: { 'Content-Type': 'multipart/form-data' },
                data
            });
        },
        // 手机登录获取验证码
        getLoginVerifCode(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/sms/getSmsVerifCode',
                method: 'get',
                params: data
            });
        },
        // 忘记密码 校验手机验证码
        PostCheckVerifCode(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/sms/checkSmsCode',
                method: 'post',
                data
            });
        },
        // 忘记密码 修改密码
        PutChangePassword(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updateUserPwd',
                method: 'PUT',
                data
            });
        },
        // 滑动验证配置接口
        getCheckCaptcha() {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/sms/getIsCheckCaptcha',
                method: 'get'
            });
        },
        // 验证手机号是否绑定用户
        getCheckNewPhone(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/getSmsCodeAndVerifPhoneBind',
                method: 'get',
                params: data
            });
        },
        // 更换新手机号接口
        changeNewPhone(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/updatePhone',
                method: 'put',
                data
            });
        },
        // 获得用户登录绑定详情
        getBindDetail() {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/third/getUserBindInfo',
                loading: false,
                method: 'get'
            });
        },
        // 获取用户登录二维码
        getWechatUrl(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/third/getUserLoginQRcode',
                method: 'get',
                params: data
            });
        },
        // 获取用户登录二维码状态
        getCodeStatus(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/third/getUserLoginQRcodeState',
                method: 'get',
                loading: false,
                params: data
            });
        },
        // 检验绑定三方手机短信验证码获得绑定二维码
        checkSmsCode(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/third/checkSmsCode',
                method: 'post',
                data
            });
        },
        // 获得用户绑定二维码状态
        getCodeBindStatus(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/third/getUserBindQRcodeState',
                method: 'get',
                loading: false,
                params: data
            });
        },
        // 解除用户绑定
        unbind(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/third/delUserThirdBind',
                method: 'delete',
                params: data
            });
        },
        // 安全改造添加的对于滑动验证的接口判断登录的次数
        loginAmountGet(data) {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/captcha/isCheckPwdError',
                method: 'get',
                params: data
            });
        },
        /**
         * 获取登录方式
         * @return {function} 返回接口
         */
        getLoginType() {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/certification/getLoginType',
                method: 'get'
            });
        },
        /**
         * 获取密码强度
         * @return {function} 返回接口
         */
        getPasswordStrength() {
            return http({
                baseDomain: basePath.bossapi.system,
                url: '/console/user/getcipherstrength',
                method: 'get'
            });
        }
    };

    return service;
};
