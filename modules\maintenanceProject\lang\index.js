import enLocale from './en';
import zhLocale from './zh';
import config from '../config.js';

const moduleName = config.moduleName;
// 判断国际化配置文件是否满足route、project必填
if (!Object.prototype.hasOwnProperty.call(enLocale, 'route') || !Object.prototype.hasOwnProperty.call(zhLocale, 'route')) {
    console.error(`国际化配置文件中，不包含路由国际化'route'项配置...`);
}

if (!Object.prototype.hasOwnProperty.call(enLocale, 'project') || !Object.prototype.hasOwnProperty.call(zhLocale, 'project')) {
    console.error(`国际化配置文件中，不包含路由国际化'project'项配置...`);
}

// 配置国际化
const messages = {
    en: {
        [moduleName]: enLocale.project,
        route: enLocale.route,
        httpCode: enLocale.httpCode
    },
    zh: {
        [moduleName]: zhLocale.project,
        route: zhLocale.route,
        httpCode: zhLocale.httpCode
    }
};

export default messages;
