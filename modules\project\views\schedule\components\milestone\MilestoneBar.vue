<template>
    <div class="flex" style="width: 100%">
        <div
            class="milestone-bar"
            :style="{
                width: `${progressWidth || 180}px`
            }"
        >
            <div style="height: 47px; width: 100%"></div>
            <div
                class="straight-bar"
                :style="{
                    'background-color': barColor
                }"
            >
                <svg
                    class="before-arrow right"
                    viewBox="0 0 10 15"
                    preserveAspectRatio="none"
                >
                    <polygon points="0,0 10,7.5 0,15" />
                </svg>
                <el-popover
                    placement="bottom"
                    width="600"
                    trigger="click"
                    :disabled="data.projectDetailStatus !== '进行中'"
                    popper-class="base-info-milestone-popover"
                >
                    <!-- 注意这里使用v-if，猜测与popover的v-show冲突 -->
                    <div
                        v-show="
                            Array.isArray(data.projectDetailKeyTaskVoList) &&
                            data.projectDetailKeyTaskVoList.length > 0
                        "
                    >
                        <div
                            v-for="(
                                i, index
                            ) in data.projectDetailKeyTaskVoList"
                            class="key-task"
                            :key="`${index}${i.taskName}`"
                        >
                            <div class="flex">
                                <svg-icon
                                    icon-class="project-clock"
                                    class="prefix-circle"
                                    :style="{
                                        fill:
                                            i.taskStatus === '已延期'
                                                ? 'red'
                                                : i.taskStatus === '有延期风险'
                                                ? 'orange'
                                                : '#00d156'
                                    }"
                                ></svg-icon>
                            </div>
                            <el-tooltip
                                class="key-task-name"
                                effect="dark"
                                :content="i.taskName"
                                placement="bottom-start"
                                :visible-arrow="false"
                            >
                                <div>
                                    {{ i.taskName }}
                                </div>
                            </el-tooltip>
                            <div class="key-task-time">
                                {{ i.startDate }} - {{ i.endDate }}
                            </div>
                            <div class="key-task-owner">
                                {{ i.responsiblePerson }}
                            </div>
                            <div class="key-task-rest">
                                剩余天数：{{ i.residueDays }}
                            </div>
                        </div>
                    </div>
                    <el-empty
                        description="暂无数据"
                        :image-size="50"
                        v-show="
                            Array.isArray(data.projectDetailKeyTaskVoList) &&
                            data.projectDetailKeyTaskVoList.length === 0
                        "
                    ></el-empty>
                    <div
                        slot="reference"
                        v-if="
                            data.projectDetailStatus === '进行中' && isCurrent
                        "
                    >
                        <svg-icon
                            icon-class="project-clock"
                            class="clock-flag"
                            :style="{
                                fill:
                                    data.projectDetailScheduleStatus ===
                                    '已延期'
                                        ? 'red'
                                        : data.projectDetailScheduleStatus ===
                                          '有延期风险'
                                        ? 'orange'
                                        : '#00d156'
                            }"
                        />
                    </div>
                    <div
                        v-else-if="
                            data.projectDetailStatus === '待启动' && isCurrent
                        "
                        slot="reference"
                    ></div>
                    <div
                        v-else-if="
                            data.projectDetailStatus === '已完成' && isCurrent
                        "
                        slot="reference"
                    >
                        <svg-icon
                            icon-class="project-check-mark"
                            class="check-mark-flag"
                        />
                    </div>
                    <div v-else-if="!isCurrent" slot="reference"></div>
                </el-popover>
                <svg
                    class="end-arrow right"
                    viewBox="0 0 10 15"
                    preserveAspectRatio="none"
                    :style="{
                        'border-top': isDelay ? '1.5px dashed black' : 'none',
                        'border-bottom': isDelay ? '1.5px dashed black' : 'none'
                    }"
                >
                    <polygon
                        v-if="isDelay"
                        points="0,0 10,7.5 0,15"
                        :style="{
                            fill: barColor
                        }"
                        stroke="black"
                        stroke-width="1.5"
                        stroke-dasharray="2,2"
                    />
                    <polygon
                        v-else
                        points="0,0 10,7.5 0,15"
                        :style="{
                            fill: barColor
                        }"
                    />
                    <line
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="15"
                        :stroke="barColor"
                        stroke-width="2"
                    />
                </svg>
            </div>
            <div style="margin-top: 1px">
                <div class="info">
                    <b>{{ data.projectDetailName }}</b>
                </div>
                <div class="info">
                    <div class="info-ring info-ring-plan"></div>
                    <div class="info-plan-time">{{ data.planEndDate }}</div>
                    <el-popover
                        ref="changeReasonRef"
                        placement="top-start"
                        width="800"
                        trigger="click"
                        popper-class="base-info-change-reason-popover"
                    >
                        <div style="margin-bottom: 10px">
                            <b>里程碑任务：</b
                            >{{ changeReasonList.projectDetailName }}
                        </div>
                        <div class="flex" style="margin-bottom: 10px">
                            <div class="change-reason-info">
                                <b> 变更前目标：</b
                                >{{ changeReasonList.beforeChangeTarget }}
                            </div>
                            <div class="change-reason-info">
                                <b> 变更后目标：</b
                                >{{ changeReasonList.afterChangeTarget }}
                            </div>
                        </div>
                        <el-table
                            :data="
                                changeReasonList.projectDetailChangeRsnDesVos
                            "
                            style="border: 1px solid #8c8c8c !important"
                            :cell-style="{
                                border: '1px solid #8c8c8c!important'
                            }"
                            :header-cell-style="{ border: '1px solid #8c8c8c' }"
                            max-height="500"
                        >
                            <el-table-column
                                width="150"
                                property="changeReasonType"
                                label="变更原因分类"
                                align="center"
                            ></el-table-column>
                            <el-table-column
                                width="500"
                                property="changeReasonDes"
                                label="计划变更具体原因描述"
                                header-align="center"
                                align="left"
                            ></el-table-column>
                            <el-table-column
                                property="effectWorkDays"
                                label="影响工期"
                                align="center"
                            ></el-table-column>
                        </el-table>
                        <div
                            slot="reference"
                            @click="getChangeReasonList(data.projectDetailName)"
                        >
                            <svg-icon
                                :style="{
                                    visibility: showFlag ? 'initial' : 'hidden'
                                }"
                                icon-class="milestone-flag"
                                class="change-flag"
                            ></svg-icon>
                        </div>
                    </el-popover>
                </div>
                <div class="info" v-if="isCurrent">
                    <div class="info-ring info-ring-actual"></div>
                    <span class="info-actual-time">{{
                        data.actualityEndDate
                    }}</span>
                </div>
            </div>
        </div>
        <!-- 小于父元素的地方补虚线框,同时显示每个里程碑的延期天数 -->
        <div class="placeholder" v-if="isDelay">
            <div class="dash-line">
                <div
                    style="
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        line-height: 100%;
                    "
                >
                    {{ data.detailDelayDays }}
                </div>
            </div>
            <svg
                viewBox="0 0 10 15"
                preserveAspectRatio="none"
                width="10"
                height="15"
            >
                <polygon
                    points="0,0 10,7.5 0,15"
                    fill="none"
                    stroke="black"
                    stroke-width="1.5"
                    stroke-dasharray="2,2"
                    stroke-linejoin="round"
                />
                <line
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="15"
                    stroke="white"
                    stroke-width="2"
                />
            </svg>
        </div>
    </div>
</template>
<script>
export default {
    name: 'MilestoneBar',
    props: {
        data: {
            type: Object,
            default: () => ({})
        },
        // 是否为当前基线
        isCurrent: {
            type: Boolean,
            default: false
        },
        // 进度条的长度
        progressWidth: {
            type: Number,
            default: 0
        },
        // 版本
        version: {
            type: String,
            default: ''
        },
        // 基线时间，超过这个时间里程碑变更原因变成整体显示，在单独基线这里就不显示了
        isAfterBaseline: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            changeReasonList: []
        };
    },
    computed: {
        barColor() {
            if (this.data.projectDetailStatus === '已完成') {
                return '#0064f0';
            } else if (this.data.projectDetailStatus === '进行中') {
                return '#0dbd04';
            }
            return '#f0f0f0';
        },
        showFlag() {
            return this.data.detailChangeFlag && !this.isAfterBaseline;
        },
        isDelay() {
            return (
                this.data?.detailDelayDays && this.data?.detailDelayDays !== 0
            );
        }
    },

    methods: {
        /**
         * 获取里程碑变更原因
         * @param {String} projectDetailName 当前里程碑变更名称
         */
        async getChangeReasonList(projectDetailName) {
            try {
                const api =
                    this.$service.project.dashboard.getMilestoneChangeReason;
                const params = {
                    version: this.version,
                    projectId: this.$store.state.project.projectStore[1],
                    projectDetailName
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(this.head.message || '系统异常');
                    return;
                }
                this.changeReasonList = res.body;
                // 重新确定位置，防止超出屏幕
                this.$nextTick(() => {
                    this.$refs.changeReasonRef.updatePopper();
                });
            } catch (err) {
                console.error('Error:', err);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.change-reason-info {
    width: 160px;
    margin-right: 20px;
}
.milestone-base-delay {
    font-size: 14px;
    margin: 0 auto;
    font-weight: initial;
    height: 20px;
}
.milestone-bar {
    margin-top: 0;
    display: flex;
    flex-direction: column;
    height: 150px;
    position: relative;
    .straight-bar {
        height: 15px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        .clock-flag {
            width: 30px;
            height: 30px;
            cursor: pointer;
            border-radius: 50%;
            background-color: #fff;
            fill: #0dbd04;
            &:focus {
                outline: 0;
            }
        }
        .circle-flag {
            width: 13px;
            height: 13px;
            background-color: #bfbfbf;
            border-radius: 50%;
        }
        .check-mark-flag {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #0064f0;
            border: 2px solid #fff;
        }
        .before-arrow {
            position: absolute;
            width: 10px;
            height: 15px;
            left: 0px;
            fill: #fff;
        }
        .end-arrow {
            position: absolute;
            width: 10px;
            height: 15px;
            background-color: #fff;
        }
    }
    .info {
        max-width: 100%;
        margin: 10px auto 0 auto;
        display: flex;
        .change-flag {
            width: 20px;
            height: 20px;
            fill: red;
            right: 15px;
            padding: 0;
            position: absolute;
            bottom: 13px;
            left: 90px;
            &:hover {
                cursor: pointer;
                fill: #0064f0;
                scale: 1.2;
            }
        }
        .info-ring {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: transparent;
            margin: auto 5px auto 0;
            position: absolute;
        }
        .info-ring-plan {
            border: 2px solid #0dbd04;
            bottom: 16px;
        }
        .info-ring-actual {
            border: 2px solid #0064f0;
            bottom: -4px;
        }
        .info-plan-time {
            position: absolute;
            bottom: 13px;
            left: 15px;
        }
        .info-actual-time {
            position: absolute;
            bottom: -7px;
            left: 16px;
        }
    }
}
.right {
    right: 0px;
}
.left {
    left: 0px;
    transform: rotate(180deg);
}
.key-task {
    display: flex;
    height: 25px;
    margin: 5px;
    line-height: 25px;
    .key-task-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 33%;
    }
    .key-task-time {
        width: 30%;
        margin-left: 10px;
    }
    .key-task-owner {
        width: 10%;
        margin-left: 10px;
    }
    .key-task-rest {
        margin-left: 10px;
    }
    .prefix-circle {
        width: 20px;
        height: 20px;
        margin: auto;
    }
}
.placeholder {
    flex-grow: 1;
    display: flex;
    margin-top: 47px;
    position: relative;
    min-width: 40px;
    .dash-line {
        height: 15px;
        flex-grow: 1;
        border-top: 1.5px dashed #000;
        border-bottom: 1.5px dashed #000;
    }
}

::v-deep.el-table th {
    background-color: #3370ff !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
::v-deep .el-table--mini .el-table__cell {
    padding: 0px !important;
}
::v-deep .el-table .el-table__row {
    height: 35px !important;
}
</style>
<style>
.base-info-milestone-popover {
    padding: 10px 5px;
    max-height: 300px;
    overflow-y: auto;
}
</style>
