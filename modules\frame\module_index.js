import './icons';

import moduleRoutes from './router';
import moduleI18n from './lang/index.js';
import moduleStore from './store';
import moduleService from './service';
import config from './config.js';
import moduleConstant from './common/constant.js';

const { moduleName } = config;

export default ({ Vue, router, store, i18n }) => {
    // 加载国际化
    Vue.prototype.$addI18n(moduleName, moduleI18n);
    // 注册路由
    Vue.prototype.$addRoutes(moduleName, moduleRoutes);
    // 注册状态树
    Vue.prototype.$addStore(moduleName, moduleStore);
    // 注册模块service
    Vue.prototype.$service[moduleName] = moduleService(Vue);
    // 注册静态变量
    Vue.prototype.$addConstant(moduleName, moduleConstant);
    // 判断路由中404、500等异常情况
    router.beforeEach(async (to, from, next) => {
        next();
        // eslint-disable-next-line array-callback-return
        // const hasRouter = moduleRoutes.some((v) => {
        //     v.path === to.path ||
        //         (v.children && v.children.some((v1) => v1.path === to.path));
        // });
        // if (hasRouter) {
        //     next();
        //     return;
        // }
        // // 强制登录判断
        // if (
        //     localStorage.getItem('isOriginPwd') === 'true' &&
        //     to.path !== '/dashboard-index' &&
        //     to.path !== '/login'
        // ) {
        //     next({
        //         path: '/dashboard-index',
        //         query: { time: new Date().getTime() }
        //     });
        // } else {
        //     next();
        // }
    });
};
