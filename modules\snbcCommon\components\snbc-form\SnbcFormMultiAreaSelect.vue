<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-collapse accordion>
            <el-collapse-item :title="title">
                <el-input v-model="filterText" suffix-icon="el-icon-search"> </el-input>
                <el-tree
                    class="filter-tree"
                    :data="options"
                    :props="defaultProps"
                    node-key="nodeId"
                    show-checkbox
                    :default-expand-all="false"
                    :filter-node-method="filterNode"
                    :default-checked-keys="config.modelObj[config.modelKey]"
                    ref="tree"
                    @check-change="handleCheckChange"
                >
                </el-tree>
            </el-collapse-item>
        </el-collapse>
    </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormMultiCitySelect',
    props: {
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    // 值示例: ['北京市', '北京市', '东城区']
                    modelKey: '',
                    // province|city|area
                    type: 'area',
                    elFormItemAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // 省市数据
            options: [],
            props: {
                multiple: true,
                emitPath: false
            },
            filterText: '',
            defaultProps: {
                'children': 'childrenList',
                'label': 'nodeName',
                'node-key': 'nodeId'
            },
            from: '',
            defaultCheckedKeys: []
            // config.modelObj[config.modelKey]
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        title() {
            if (!this.config.modelObj[this.config.modelKey].length) {
                return '请选择服务范围';
            }
            return `已选择${this.config.modelObj[this.config.modelKey].length}个区`;
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    mounted() {
        this.getRegionData();
    },
    methods: {
        filterNode(value, data) {
            if (!value) return true;
            return data.nodeName.indexOf(value) !== -1;
        },
        // 获取省市区数据
        async getRegionData() {
            try {
                const res = await this.$service.providerManagement.personnel.getSelfProviderArea();
                const { code, message, result } = res;
                if (code !== '000000') {
                    this.$tools.message.err(message || '系统异常');
                    return;
                }
                // 格式化数据
                this.options = result.map((province) => {
                    if (this.type === 'city' || this.type === 'area') {
                        province.children = province.childrenList.map((city) => {
                            if (this.type === 'area') {
                                city.children = city.childrenList.map((area) => {
                                    return {
                                        ...area,
                                        label: area.nodeName,
                                        value: area.nodeId
                                    };
                                });
                            }
                            return {
                                ...city,
                                label: city.nodeName,
                                value: city.nodeId
                            };
                        });
                    }
                    return {
                        ...province,
                        label: province.nodeName,
                        value: province.nodeName
                    };
                });
            } catch (error) {
                console.error(error);
                this.$tools.message.err('系统异常');
            }
        },
        handleCheckChange() {
            this.config.modelObj[this.config.modelKey] = this.$refs.tree
                .getCheckedNodes()
                .filter((item) => !item.childrenList)
                .map((item) => item.nodeId);
        }
    }
};
</script>
