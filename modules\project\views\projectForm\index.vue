<!-- eslint-disable max-lines -->
<template>
    <div class="project-form-container">
        <project-selector
            @input="handleChange"
            :placeholder="projectPlaceholder"
            :infoDisabled="!isEdit"
            :key="projectSelectorKey"
        />
        <div class="container">
            <el-tabs
                v-model="projectClassification"
                @tab-click="tabClick"
                class="tabs"
                ><template v-if="!isEdit">
                    <el-tab-pane
                        v-for="item in PROJECT_CLASSFICATION"
                        :key="item"
                        :label="item"
                    ></el-tab-pane
                ></template>
                <div class="form flex" style="flex-direction: column">
                    <el-form
                        label-width="160px"
                        ref="marketFormRef"
                        :model="marketForm"
                        :rules="marketFormRules"
                    >
                        <el-divider content-position="left"
                            ><h3>市场立项信息</h3></el-divider
                        >
                        <el-form-item
                            v-if="isEdit"
                            label="项目状态"
                            style="width: 400px; margin-bottom: 0"
                        >
                            <el-select
                                v-model="projectStatus"
                                placeholder="请选择项目状态"
                                size="medium"
                                @change="projectStatusChange"
                                :disabled="statusDisabled"
                            >
                                <el-option
                                    v-for="item in statusList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <div
                            v-if="
                                statusByClassfication.isQueued ||
                                statusByClassfication.isOngoing
                            "
                            class="flex"
                            style="align-items: center"
                        >
                            <el-form-item
                                label="市场需求导入ID"
                                style="margin-bottom: 0"
                                prop="marketDemandImportID"
                                key="marketDemandImportID"
                            >
                                <el-input
                                    v-model="marketForm.marketDemandImportID"
                                    size="medium"
                                    placeholder="请输入市场需求导入ID"
                                    style="width: 260px"
                                ></el-input
                            ></el-form-item>
                        </div>
                        <el-form-item
                            label="项目名称"
                            prop="projectName"
                            style="width: 600px"
                            size="medium"
                            key="projectName"
                            ><el-input
                                v-model="marketForm.projectName"
                                placeholder="请输入项目名称"
                            ></el-input
                        ></el-form-item>
                        <el-form-item
                            label="市场决议函下达时间"
                            v-if="
                                isEdit &&
                                (statusByClassfication.isQueued ||
                                    statusByClassfication.isOngoing)
                            "
                        >
                            <el-date-picker
                                v-model="marketForm.marketResoluteIssueDate"
                                type="date"
                                placeholder="请选择市场决议函下达时间"
                                value-format="yyyy-MM-dd"
                                size="large"
                                class="marketResoluteIssueDate"
                            >
                            </el-date-picker
                        ></el-form-item>
                        <el-form-item
                            label="产品类别"
                            prop="productCategory"
                            key="productCategory"
                        >
                            <el-radio
                                v-model="marketForm.productCategory"
                                v-for="item in CONSTANTS.PRODUCT_CATEGORY"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-radio>
                        </el-form-item>
                        <el-form-item
                            label="市场定位"
                            prop="marketPositioning"
                            key="marketPositioning"
                        >
                            <el-radio
                                v-model="marketForm.marketPositioning"
                                v-for="item in CONSTANTS.MARKET_POSITIONING"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-radio>
                        </el-form-item>
                        <div class="flex" style="width: 100%">
                            <el-form-item
                                label="所属产品线"
                                prop="productLine"
                                key="productLine"
                                style="flex: 1; margin: 0 20px"
                            >
                                <el-select
                                    v-model="marketForm.productLine"
                                    placeholder="请选择所属产品线"
                                    size="medium"
                                    @input="handleProductLineChange"
                                >
                                    <el-option
                                        v-for="item in this.$store.state.project
                                            .productLine"
                                        :key="item.label"
                                        :label="item.label"
                                        :value="item.label"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="细分产品线"
                                prop="subProductLine"
                                key="subProductLine"
                                style="flex: 1; margin: 0 20px"
                            >
                                <el-select
                                    v-model="marketForm.subProductLine"
                                    placeholder="请选择细分产品线"
                                    size="medium"
                                >
                                    <el-option
                                        v-for="item in subProductLineOptions"
                                        :key="item.paramName"
                                        :label="item.paramName"
                                        :value="item.paramName"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <el-form-item
                            label="立项类型"
                            prop="approvalType"
                            key="approvalType"
                        >
                            <el-radio
                                v-model="marketForm.approvalType"
                                v-for="item in CONSTANTS.APPROVAL_TYPE"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-radio>
                        </el-form-item>
                        <el-form-item
                            label="需求类型"
                            v-if="
                                statusByClassfication.isQueued ||
                                statusByClassfication.isOngoing
                            "
                        >
                            <el-select
                                v-model="marketForm.demandCategory"
                                placeholder="请选择需求类型"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.DEMAND_CATEGORY"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <template v-if="statusByClassfication.isOngoing">
                            <el-form-item label="产品类型">
                                <el-select
                                    v-model="marketForm.productType"
                                    placeholder="请选择产品类型"
                                    size="medium"
                                >
                                    <el-option
                                        v-for="item in CONSTANTS.PRODUCT_TYPE"
                                        :key="item"
                                        :label="item"
                                        :value="item"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </template>
                        <el-form-item
                            label="项目经理"
                            prop="projectManagerLoginName"
                            key="projectManagerLoginName"
                        >
                            <people-selector
                                v-model="marketForm.projectManagerLoginName"
                                size="medium"
                                placeholder="请输入项目经理"
                                :isAll="1"
                                :isMultipled="false"
                            ></people-selector>
                        </el-form-item>
                        <el-form-item
                            label="排队未启动原因"
                            v-if="statusByClassfication.isQueued"
                            prop="queueNotStartedReason"
                            key="queueNotStartedReason"
                            ><el-input
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                placeholder="请输入排队未启动原因"
                                v-model="marketForm.queueNotStartedReason"
                            >
                            </el-input
                        ></el-form-item>
                        <el-form-item
                            label="项目取消原因"
                            v-if="isCancel"
                            prop="projectCancelReason"
                            key="projectCancelReason"
                            ><el-input
                                type="textarea"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                placeholder="请输入取消原因"
                                v-model="marketForm.projectCancelReason"
                                size="medium"
                            >
                            </el-input
                        ></el-form-item>
                    </el-form>
                </div>
                <div style="height: 1px" id="projectForm"></div>
                <div v-if="statusByClassfication.isOngoing" class="form">
                    <el-form
                        label-width="160px"
                        ref="projectFormRef"
                        :model="projectForm"
                        :rules="projectFormRules"
                    >
                        <el-divider content-position="left"
                            ><h3>项目立项信息</h3></el-divider
                        >
                        <el-form-item
                            label="OA项目任务书ID"
                            style="margin-bottom: 0"
                            prop="OAProjectTaskId"
                            key="OAProjectTaskId"
                            ><el-input
                                v-model="projectForm.OAProjectTaskId"
                                size="medium"
                                placeholder="请输入OA项目任务书ID"
                                style="width: 300px"
                            ></el-input
                        ></el-form-item>

                        <el-form-item label="任务书类型" v-if="!isSoftware">
                            <el-select
                                v-model="projectForm.taskBookType"
                                placeholder="请选择任务书类型"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.TASK_BOOK_TYPE"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="
                                isSoftware ? '项目开始时间' : '项目启动时间'
                            "
                            prop="time"
                            key="time"
                        >
                            <el-date-picker
                                v-model="projectForm.time"
                                type="date"
                                :placeholder="
                                    isSoftware
                                        ? '请选择项目开始时间'
                                        : '请选择项目启动时间'
                                "
                                value-format="yyyy-MM-dd"
                                size="large"
                            >
                            </el-date-picker
                        ></el-form-item>
                        <el-form-item
                            label="项目编号"
                            prop="projectNumber"
                            key="projectNumber"
                        >
                            <el-input
                                v-model="projectForm.projectNumber"
                                size="medium"
                                placeholder="请输入项目编号"
                                style="width: 600px"
                            ></el-input
                        ></el-form-item>
                        <el-form-item
                            label="需求导入主体"
                            prop="demandImportSubject"
                            key="demandImportSubject"
                        >
                            <el-radio
                                v-model="projectForm.demandImportSubject"
                                v-for="item in CONSTANTS.DEMAND_IMPORT_SUBJECT"
                                :key="item"
                                :label="item"
                                :value="item"
                            ></el-radio
                        ></el-form-item>
                        <el-form-item label="项目控制等级" v-if="!isSoftware">
                            <el-select
                                v-model="projectForm.projectControlLevel"
                                placeholder="请选择项目控制等级"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.PROJECT_CONTROL_LEVEL"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="项目技术难度">
                            <el-select
                                v-model="projectForm.technicalComplexity"
                                placeholder="请选择项目技术难度"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.TECHNICAL_COMPLEXITY"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="开发流程" v-if="!isSoftware">
                            <el-select
                                v-model="projectForm.developmentProcess"
                                placeholder="请选择开发流程"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.DEVELOPMENT_PROCESS"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="项目归属">
                            <el-select
                                v-model="projectForm.projectOwnership"
                                placeholder="请选择项目归属"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.OWNERSHIP"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="生产归属" v-if="!isSoftware">
                            <el-select
                                v-model="projectForm.productionOwnership"
                                placeholder="请选择生产归属"
                                size="medium"
                            >
                                <el-option
                                    v-for="item in CONSTANTS.OWNERSHIP"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="产品型号"
                            prop="productModel"
                            key="productModel"
                            v-if="!isSoftware"
                            ><el-input
                                size="medium"
                                placeholder="请输入产品型号"
                                v-model="projectForm.productModel"
                            ></el-input
                        ></el-form-item>
                        <el-form-item label="产品经理">
                            <people-selector
                                v-model="projectForm.productManagerLoginName"
                                size="medium"
                                placeholder="请输入产品经理"
                                :isAll="1"
                                :isMultipled="false"
                            ></people-selector>
                        </el-form-item>
                        <el-form-item label="禅道中对应的项目"
                            ><el-input
                                size="medium"
                                placeholder="请选择禅道中对应的项目"
                                @click.native="openZendaoSelectorModal"
                                v-model="zendaoProjectName"
                                :clearable="false"
                            >
                                <template #suffix>
                                    <i
                                        v-show="!!zendaoProjectName"
                                        class="el-input__icon el-icon-circle-close"
                                        @click="clearInput"
                                        style="cursor: pointer"
                                    ></i> </template></el-input
                        ></el-form-item>
                    </el-form>
                    <ZendaoSelectorModal
                        ref="zendaoSelectorModalRef"
                        @getProjectInfo="getZenDaoProjectInfo"
                    ></ZendaoSelectorModal>
                </div>
                <div class="flexCenter" style="height: 120px">
                    <el-button
                        size="medium"
                        @click="goBack"
                        style="width: 150px; margin-right: 30px"
                        >返回</el-button
                    >
                    <el-button
                        type="primary"
                        @click="onSubmit"
                        size="medium"
                        style="width: 150px"
                        >保存</el-button
                    >
                </div>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector';
import PeopleSelector from 'Components/PeopleSelector';
import { CONSTANTS } from '@/constants';
import ZendaoSelectorModal from './components/zendaoSelectorModal';
import {
    projectFormRules,
    marketFormRules,
    defaultMarketForm,
    defaultProjectForm,
    pendingArray,
    queuedArray,
    onGoingArray,
    pasedArray
} from './components/formInit';

const { PROJECT_CLASSFICATION, PROJECT_STATUS } = CONSTANTS;
// 字数校验规则
const shortLengthLimit = {
    min: 0,
    max: 50,
    message: '最多输入50个字符',
    trigger: 'change'
};
export default {
    components: { ProjectSelector, ZendaoSelectorModal, PeopleSelector },
    data() {
        return {
            // 下拉菜单选项
            CONSTANTS,
            // 项目分类的选项
            PROJECT_CLASSFICATION,
            // 项目ID
            projectId: '',
            // 项目归类
            projectClassification: '0',
            // 项目状态
            projectStatus: PROJECT_STATUS[0].value,
            // 项目状态列表
            statusList: PROJECT_STATUS.concat({
                label: '已取消',
                value: '已取消'
            }),
            subProductLineOptions: [],
            // 判断是新增还是编辑
            isEdit: false,
            // 市场立项信息
            marketForm: defaultMarketForm(),
            // 项目立项信息
            projectForm: defaultProjectForm(),
            // 禅道项目名称
            zendaoProjectName: '',
            // 项目状态是否可选
            statusDisabled: false,
            // 是否已经查询过
            alreadyQuery: false,
            // 顶部级联组件的key
            projectSelectorKey: 0,
            // 项目来源
            projectSource: '',
            // 校验规则
            projectFormRules,
            marketFormRules,
            // 接口
            addProjectApi: this.$service.project.projectInfo.addProjectInfo,
            editProjectApi: this.$service.project.projectInfo.editProjectInfo,
            getDetailApi: this.$service.project.projectInfo.getProjectDetail
        };
    },
    computed: {
        // 利用placeholder在级联选择器中回显项目名称
        projectPlaceholder() {
            return '请选择项目';
        },
        // 各项目归类对应的状态
        statusByClassfication() {
            return {
                isPending: this.projectClassification === '0',
                isQueued: this.projectClassification === '1',
                isOngoing: this.projectClassification === '2'
            };
        },
        // 是否为已取消状态
        isCancel() {
            return this.projectStatus === '已取消';
        },
        // 是否是软件的项目
        isSoftware() {
            return this.projectSource === 'PLOUTO';
        }
    },
    watch: {
        // 当前是编辑状态，OA任务书ID必填
        isEdit(newValue) {
            if (newValue) {
                this.projectFormRules.OAProjectTaskId = [
                    shortLengthLimit,
                    {
                        required: true,
                        message: '请输入OA项目任务书ID',
                        trigger: 'change'
                    }
                ];
            } else {
                this.projectFormRules.OAProjectTaskId = [shortLengthLimit];
            }
        },
        projectStatus(newVal) {
            if (
                newVal &&
                newVal !== '市场待立项' &&
                newVal !== '排队中' &&
                newVal !== '已取消'
            ) {
                this.$nextTick(() => {
                    this.$refs.marketFormRef.rules.projectManagerLoginName[0].required = true;
                });
            } else {
                this.$nextTick(() => {
                    this.$refs.marketFormRef.rules.projectManagerLoginName[0].required = false;
                });
            }
        }
    },
    mounted() {
        // 进页面时，清除从项目列表页面带来的tooltip
        document
            .querySelectorAll('.el-tooltip__popper')
            .forEach((el) => el.remove());
        // 路由传入id就是编辑状态，否则就是新增
        if (this.$route?.query?.id) {
            this.projectId = this.$route.query.id;
            this.isEdit = true;
            this.getProjectDetail();
        } else if (this.$route.name === 'ProjectForm') {
            this.$router.push('/project/projectList');
        }
    },
    methods: {
        /**
         * 根据编辑/新增状态构建请求参数
         * @return {Object} 公共参数
         */
        baseRequestData() {
            let baseRequestData = {
                project: {
                    projectName: this.marketForm.projectName,
                    productLine: this.marketForm.productLine,
                    subProductLine: this.marketForm.subProductLine,
                    projectApproval: this.marketForm.approvalType,
                    projectSpecies:
                        this.PROJECT_CLASSFICATION[this.projectClassification],
                    productClassification: this.marketForm.productCategory,
                    projectStatus: this.projectStatus,
                    projectManagerLoginName:
                        this.marketForm.projectManagerLoginName
                },
                projectEx: {
                    marketLocate: this.marketForm.marketPositioning
                }
            };
            if (this.isEdit) {
                baseRequestData = {
                    project: {
                        ...baseRequestData.project,
                        id: this.projectId
                    },
                    projectEx: {
                        ...baseRequestData.projectEx,
                        projectId: this.projectId
                    }
                };
            }
            return baseRequestData;
        },
        /**
         * 将表单数据转换为发送请求的格式
         * @returns {Object} 请求参数
         */
        convertFormToRequestData() {
            const baseRequestData = this.baseRequestData();
            let requestData = {};
            if (this.statusByClassfication.isPending) {
                requestData = {
                    project: {
                        ...baseRequestData.project
                    },
                    projectEx: {
                        ...baseRequestData.projectEx
                    }
                };
                if (this.isCancel) {
                    requestData.project.projectCancelReason =
                        this.marketForm.projectCancelReason;
                }
            } else if (this.statusByClassfication.isQueued) {
                requestData = {
                    project: {
                        ...baseRequestData.project,
                        productType: this.marketForm.productType,
                        storyType: this.marketForm.demandCategory
                    },
                    projectEx: {
                        ...baseRequestData.projectEx,
                        queuingNotStartingReason:
                            this.marketForm.queueNotStartedReason,
                        marketStoryImportFlowInstanceNo:
                            this.marketForm.marketDemandImportID,
                        marketResoluteIssueDate:
                            this.marketForm.marketResoluteIssueDate
                    }
                };
            } else if (this.statusByClassfication.isOngoing) {
                requestData = {
                    project: {
                        ...baseRequestData.project,
                        startDate: this.projectForm.time,
                        projectNumber: this.projectForm.projectNumber,
                        productType: this.marketForm.productType,
                        storyType: this.marketForm.demandCategory,
                        proProjectId: this.projectForm.zendaoProjectId,
                        productManagerLoginName:
                            this.projectForm.productManagerLoginName
                    },
                    projectEx: {
                        ...baseRequestData.projectEx,
                        developFlow: this.projectForm.developmentProcess,
                        produceAttribution:
                            this.projectForm.productionOwnership,
                        projectAttribution: this.projectForm.projectOwnership,
                        oaProjectTaskId: this.projectForm.OAProjectTaskId,
                        assMarketStoryImportId:
                            this.projectForm.assMarketStoryImportId,
                        marketStoryImportFlowInstanceNo:
                            this.marketForm.marketDemandImportID,
                        productModeNo: this.projectForm.productModel,
                        projectControlScale:
                            this.projectForm.projectControlLevel,
                        projectTechnicalDifficulty:
                            this.projectForm.technicalComplexity,
                        storyImportMainBody:
                            this.projectForm.demandImportSubject,
                        taskBookType: this.projectForm.taskBookType,
                        marketResoluteIssueDate:
                            this.marketForm.marketResoluteIssueDate
                    }
                };
            }
            return requestData;
        },
        /**
         * 将请求返回的数据转换为表单数据
         * @param {Object} res 请求返回的数据
         */
        convertRequestDataToForm(res) {
            this.marketForm = {
                marketDemandImportID:
                    res?.projectEx?.marketStoryImportFlowInstanceNo,
                projectName: res?.project?.projectName,
                productCategory: res?.project?.productClassification,
                marketPositioning: res?.projectEx?.marketLocate,
                productLine: res?.project?.productLine,
                subProductLine: res?.project?.subProductLine,
                approvalType: res?.project?.projectApproval,
                demandCategory: res?.project?.storyType,
                productType: res?.project?.productType,
                queueNotStartedReason: res?.projectEx?.queuingNotStartingReason,
                projectCancelReason: res?.project?.projectCancelReason,
                marketResoluteIssueDate:
                    res?.projectEx?.marketResoluteIssueDate,
                projectManagerLoginName: res?.project?.projectManagerLoginName
            };
            this.projectForm = {
                time: res?.project?.startDate || '',
                OAProjectTaskId: res?.projectEx?.oaProjectTaskId,
                assMarketStoryImportId: res?.projectEx?.assMarketStoryImportId,
                projectNumber: res?.project?.projectNumber,
                demandImportSubject: res?.projectEx?.storyImportMainBody,
                projectControlLevel: res?.projectEx?.projectControlScale,
                technicalComplexity: res?.projectEx?.projectTechnicalDifficulty,
                developmentProcess: res?.projectEx?.developFlow,
                projectOwnership: res?.projectEx?.projectAttribution,
                productionOwnership: res?.projectEx?.produceAttribution,
                productModel: res?.projectEx?.productModeNo,
                productManagerLoginName: res?.project?.productManagerLoginName,
                zendaoProjectId: res?.project?.proProjectId,
                taskBookType: res?.projectEx?.taskBookType
            };
            this.projectSpecies = res?.project?.projectSpecies;
            this.projectClassification = this.PROJECT_CLASSFICATION.indexOf(
                this.projectSpecies
            ).toString();
            this.projectStatus = res?.project?.projectStatus;
            this.projectId = res?.projectEx?.projectId;
            this.zendaoProjectName = res.proProjectName;
            // 项目来源：PLOUTO为纯软件,其他为硬件和软硬混合
            this.projectSource = res?.project?.projectSource;
            // 缓存值
            const value = [this.marketForm.productLine, this.projectId];
            this.$store.dispatch('project/changeProject', value);
            // 避免key无限更新
            if (!this.alreadyQuery) {
                this.alreadyQuery = true;
                // 更新key让级联组件重新渲染,否则状态与值不会更新
                this.projectSelectorKey += 1;
            }
            if (
                this.projectStatus === '已结项' ||
                this.projectStatus === '已终止' ||
                this.projectStatus === '已取消'
            ) {
                this.statusDisabled = true;
            } else {
                this.statusDisabled = false;
            }
            // 更新项目状态列表
            this.setStatusList();
            // 更新细分产品线列表
            this.getSubProductLineOptions(this.marketForm.productLine);
        },
        /**
         * 保存时，针对项目状态改变提出的警告
         */
        async changeWarning() {
            // 查询的时候就是结项/终止/取消状态，不需要提示
            if (this.statusDisabled) return;
            if (
                this.projectStatus === '已结项' ||
                this.projectStatus === '已终止' ||
                this.projectStatus === '已取消'
            ) {
                await this.$confirm(
                    `项目${this.projectStatus}后无法变更状态，是否确认保存?`,
                    '提示',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                );
            }
        },
        /**
         * 保存
         */
        async saveData() {
            try {
                if (!this.projectClassification) {
                    this.projectClassification = '0';
                }
                const requestData = this.convertFormToRequestData();
                let api = this.addProjectApi;

                if (this.isEdit) {
                    api = this.editProjectApi;
                    await this.changeWarning();
                }
                const res = await api(requestData);
                if (res.head.code === '000000') {
                    this.$message({
                        message: '保存成功',
                        type: 'success'
                    });
                    this.goBack();
                } else {
                    this.$message({
                        message: `${res.head.message}`,
                        type: 'error'
                    });
                }
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * tab切换
         */
        tabClick() {
            // 切换项目状态
            this.projectStatus =
                this.statusList[parseInt(this.projectClassification)].value;
            // 清除校验
            this.$refs.marketFormRef.clearValidate();
            if (!this.isEdit) {
                // 新增时清空数据
                this.marketForm = defaultMarketForm();
                this.projectForm = defaultProjectForm();
            }
        },
        /**
         * 重置表单
         */
        resetForm() {
            this.$refs.marketFormRef.resetFields();
            if (this.statusByClassfication.isOngoing) {
                this.$refs.marketFormRef.resetFields();
            }
            this.projectId = '';
            this.isEdit = false;
            this.projectStatus = '';
        },
        /**
         * 表单校验并提交
         * @returns {boolean} 校验结果
         */
        onSubmit() {
            let marketValid = false;
            // 有的表单没有项目立项信息，所以默认为true
            let projectValid = true;
            this.$refs.marketFormRef.validate((valid) => {
                marketValid = valid;
            });
            if (this.statusByClassfication.isOngoing) {
                this.$refs.projectFormRef.validate((valid) => {
                    projectValid = valid;
                });
            }
            if (marketValid && projectValid) {
                this.saveData();
            } else {
                this.$message({
                    message: '请检查并正确填写所有表单项',
                    type: 'warning'
                });
                return false;
            }
        },
        /**
         * 清除输入框的值，并组织弹窗显示
         *
         * @param {MouseEvent} event 鼠标点击事件对象
         */
        clearInput(event) {
            this.zendaoProjectName = '';
            this.projectForm.zendaoProjectId = 0;
            event.stopPropagation();
        },
        /**
         * 返回
         */
        goBack() {
            // 如果从基本信息页面来，返回projectId
            if (this.$route?.query?.type) {
                this.$router.push({
                    path: '/project/baseInfo',
                    query: {
                        id: this.projectId
                    }
                });
            } else if (this.$route.name === 'AddProject') {
                // 这里保证刷新页面后路由状态丢失也能返回到项目列表
                this.$router.push('/project/projectList');
            } else {
                this.$router.go(-1);
            }
            this.resetForm();
        },
        /**
         * 打开禅道ID选择弹窗
         */
        openZendaoSelectorModal() {
            this.$refs.zendaoSelectorModalRef.openZendaoSelectorModal();
        },
        /**
         * 获取禅道项目信息
         * @param {*} projectInfo 项目信息
         */
        getZenDaoProjectInfo(projectInfo) {
            const { projectName, projectId } = projectInfo;
            this.projectForm.zendaoProjectId = projectId;
            this.zendaoProjectName = projectName;
        },
        /**
         * 项目详情回显
         */
        getProjectDetail() {
            if (!this.projectId) return;
            this.getDetailApi({ projectId: this.projectId })
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.convertRequestDataToForm(res.body);
                    } else {
                        this.$message.error(res.head.message);
                    }
                })
                .catch((err) => {
                    console.error(err.message);
                });
        },
        /**
         * 顶部级联选择处理
         * @param {Array} value 级联路径
         */
        handleChange(value) {
            if (!this.isEdit) return;
            // 有id的情况只会查询一次，不需要再次查询
            if (this.projectId && !this.alreadyQuery) {
                return;
            }
            this.projectId = value[value.length - 1];
            this.getProjectDetail();
        },
        /**
         * 选择项目状态变更时，对应的项目归类也进行变更
         * @param {String} curStatus 当前状态
         */
        projectStatusChange(curStatus) {
            const arr = ['进行中', '已结项', '已终止', '已暂停'];
            if (arr.includes(curStatus)) {
                this.projectClassification = '2';
            } else if (this.projectStatus === '排队中') {
                this.projectClassification = '1';
            } else {
                this.projectClassification = '0';
            }
        },
        /**
         * 项目状态对应的列表
         */
        setStatusList() {
            switch (this.projectStatus) {
                case '市场待立项':
                    this.statusList = pendingArray;
                    break;
                case '排队中':
                    this.statusList = queuedArray;
                    break;
                case '进行中':
                    this.statusList = onGoingArray;
                    break;
                case '已暂停':
                    this.statusList = pasedArray;
                    break;
                default:
                    this.statusList = PROJECT_STATUS.concat({
                        label: '已取消',
                        value: '已取消'
                    });
            }
        },
        handleProductLineChange(value) {
            this.marketForm.subProductLine = '';
            this.getSubProductLineOptions(value);
        },
        /**
         * 获取细分产品线对应的选项
         * @param {String} value 产品线
         */
        async getSubProductLineOptions(value) {
            const api = this.$service.project.finance.getSelectOptions;
            const params = { paramName: '细分产品线', paramType: value };
            const res = await api(params);
            if (res.head.code !== '000000') {
                this.$message.error(res.head.message);
                return;
            }
            this.subProductLineOptions = res.body;
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.project-form-container {
    height: 100vh;
    overflow-y: scroll;
}
.container {
    span {
        margin-right: 10px;
        font-weight: 800;
    }
}
.flexCenter {
    display: flex;
    justify-content: center;
}
.integration {
    height: fit-content;
}
.el-form-item {
    margin: 20px;
    ::v-deep .el-form-item__label {
        font-size: 14px;
    }
}
.form {
    margin: 0 30px;
}
.tabs {
    margin: 10px 10px 0 16px;
}
.marketResoluteIssueDate {
    width: 260px !important;
}
</style>
