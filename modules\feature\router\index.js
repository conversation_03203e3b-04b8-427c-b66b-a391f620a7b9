// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/feature',
        redirect: 'noRedirect',
        name: 'Feature',
        alwaysShow: true,
        useLayout: true,
        meta: {
            title: '功能'
        },
        children: [
            {
                path: '/meetingManagement',
                component: () => import('../views/meetingManagement'),
                name: 'MeetingManagement',
                meta: {
                    title: '会议管理'
                }
            },
            {
                path: '/meetingDetail/:meeting_id?',
                component: () =>
                    import(
                        '../views/meetingManagement/components/meetingDetail'
                    ),
                name: 'MeetingDetail',
                hidden: true,
                meta: {
                    title: '会议详情'
                }
            }
        ]
    }
];
