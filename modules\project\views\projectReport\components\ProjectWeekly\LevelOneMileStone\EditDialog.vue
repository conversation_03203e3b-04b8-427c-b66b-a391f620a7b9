<template>
    <div class="zandaoModal">
        <el-dialog
            title="编辑一级控制点进度情况"
            width="95%"
            :visible.sync="dialogVisible"
        >
            <!-- 阶段标题行 -->
            <div class="phase-headers">
                <div class="phase-header-spacer"></div>
                <div
                    class="phases-container"
                    :style="{
                        maxWidth: `${formData.milestones.length * 200}px`
                    }"
                >
                    <div
                        v-for="(phase, index) in formData.phases"
                        :key="`phase-${index}`"
                        class="phase-item"
                        :style="{ flex: phase.span }"
                    >
                        {{ phase.name }}
                    </div>
                </div>
            </div>

            <div class="milestone-steps">
                <div class="grid-header-spacer">里程碑</div>
                <el-steps
                    :active="activeStep"
                    align-center
                    class="milestone-steps-content"
                >
                    <el-step
                        v-for="(milestone, index) in formData.milestones"
                        :key="index"
                        :status="milestone.status"
                        style="max-width: 200px"
                    >
                        <template #icon>
                            <svg-icon
                                v-if="
                                    milestone.detailProgressStatus === '已完成'
                                "
                                icon-class="project-check-mark"
                                class="check-mark-flag"
                            />
                            <svg-icon
                                v-else-if="
                                    milestone.detailProgressStatus === '进行中'
                                "
                                icon-class="project-milestone-ongoing"
                                class="clock-flag"
                            />
                            <svg-icon
                                v-else-if="
                                    milestone.detailProgressStatus === '待启动'
                                "
                                icon-class="project-clock"
                                class="clock-flag"
                            />
                        </template>
                        <template #title>
                            <div class="milestone-subtitle">
                                {{ milestone.detailName }}
                            </div>
                        </template>
                    </el-step>
                </el-steps>
            </div>

            <div class="milestone-grid-container" :style="gridStyle">
                <!-- 表头 -->
                <template v-if="isArchived">
                    <div class="grid-header">{{ currentBaselineTitle }}</div>
                    <div
                        v-for="(milestone, index) in formData.milestones"
                        :key="`baseline-${index}`"
                        class="grid-cell"
                    >
                        {{ milestone.currentBaseline }}
                    </div>
                </template>

                <!-- 预调整至 - 可编辑 -->
                <div class="grid-header">预调整至</div>
                <div
                    v-for="(milestone, index) in formData.milestones"
                    :key="`adjustment-${index}`"
                    class="grid-cell adjustment-cell"
                >
                    <el-date-picker
                        v-model="milestone.adjustedBaseline"
                        type="date"
                        placeholder="选择日期"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        size="mini"
                        :clearable="true"
                        class="date-picker"
                        :disabled="milestone.detailProgressStatus === '已完成'"
                    ></el-date-picker>
                </div>

                <!-- 调整原因 - 可编辑 -->
                <div class="grid-header">调整原因</div>
                <div
                    v-for="(milestone, index) in formData.milestones"
                    :key="`reason-${index}`"
                    class="grid-cell adjustment-cell"
                >
                    <el-input
                        v-model="milestone.adjustedReason"
                        type="textarea"
                        placeholder="请输入调整原因"
                        size="mini"
                        :clearable="true"
                        :autosize="{ minRows: 3, maxRows: 5 }"
                        class="date-picker"
                        :disabled="milestone.detailProgressStatus === '已完成'"
                        maxlength="200"
                    ></el-input>
                </div>
            </div>

            <div class="confirm-button">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: 'EditDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: () => ({
                milestones: [],
                phases: []
            })
        },
        currentBaselineTitle: {
            type: String,
            default: ''
        },
        isArchived: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formData: {
                milestones: [],
                phases: []
            },
            reasonDialogVisible: false,
            currentReasonIndex: null,
            currentReason: ''
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        // 当前里程碑
        activeStep() {
            return this.formData.milestones.findIndex(
                (milestone) => milestone.detailProgressStatus === '进行中'
            );
        },
        // 整体里程碑宽度
        gridStyle() {
            return {
                gridTemplateColumns: `var(--grid-header-width, 80px) repeat(${this.formData.milestones.length}, minmax(0, 1fr))`,
                maxWidth: `calc(var(--grid-header-width, 80px) + ${this.formData.milestones.length} * 200px)`
            };
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                // 深拷贝数据，避免直接修改原数据
                this.formData = JSON.parse(JSON.stringify(this.data));
            }
        }
    },
    methods: {
        /**
         * 编辑调整原因
         * @param {Number} index 行索引
         */
        editReason(index) {
            this.currentReasonIndex = index;
            this.currentReason =
                this.formData.milestones[index].plannedAdjustmentReason || '';
            this.reasonDialogVisible = true;
        },
        /**
         * 保存调整原因
         */
        saveReason() {
            if (this.currentReasonIndex !== null) {
                this.formData.milestones[
                    this.currentReasonIndex
                ].plannedAdjustmentReason = this.currentReason;
                this.reasonDialogVisible = false;
            }
        },
        /**
         * 编辑一级里程碑
         */
        async handleSubmit() {
            const api = this.$service.project.weekly.editLevelOneMileStone;
            try {
                const res = await api(this.formData.milestones);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('保存成功');
                this.$emit('refresh');
                this.dialogVisible = false;
            } catch (error) {
                console.error('Error:', error);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.phase-headers {
    display: flex;
    width: 100%;
    margin-bottom: 15px;

    .phase-header-spacer {
        width: var(--grid-header-width, 80px);
        flex-shrink: 0;
    }

    .phases-container {
        display: flex;
        flex: 1;
    }

    .phase-item {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        font-weight: bold;
        font-size: 14px;
        border-right: 1px solid #3370ff;

        &:first-child {
            border-left: 1px solid #3370ff;
        }
    }
}

.milestone-steps {
    display: flex;
    width: 100%;

    .grid-header-spacer {
        display: flex;
        justify-content: center;
        align-items: center;
        width: var(--grid-header-width, 80px);
        flex-shrink: 0;
        background-color: #f5f7fa;
        padding: 8px;
        font-weight: bold;
    }

    .milestone-steps-content {
        flex: 1;
        padding-bottom: 20px;
    }
    .milestone-title {
        font-weight: bold;
        font-size: 14px;
    }

    .milestone-subtitle {
        font-weight: bold;
        font-size: 14px;
        margin-top: 5px;
        white-space: normal;
        line-height: 1.2;
        width: 100%;
        text-align: center;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        min-width: 0;
    }

    .custom-icon {
        width: 15px;
        height: 15px;
        border-radius: 50%;

        &.process {
            background-color: #e6a23c;
        }

        &.wait {
            background-color: #909399;
        }
    }
}

.milestone-grid-container {
    width: 100%;
    display: grid;
    gap: 1px;
    margin-bottom: 20px;

    .grid-header {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f7fa;
        padding: 8px;
        font-weight: bold;
    }

    .grid-cell {
        padding: 2px;
        text-align: center;
        max-width: 200px;

        &.adjustment-cell {
            flex-direction: column;
            min-height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .date-picker {
            width: 100% !important;
            ::v-deep .el-input__inner {
                padding-right: 0;
            }
        }
    }
}

.reason-button {
    margin-top: 5px;
    font-size: 12px;
    color: #3370ff;
}

.reason-editor {
    padding: 10px;
    min-width: 250px;
}

.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
}

.adjustment-text {
    position: relative;
    display: inline-block;
}

.adjustment-text-container {
    cursor: pointer;
    .corner-badge {
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 8px 8px 0;
        border-color: transparent #ff4949 transparent transparent;
        transform: translate(50%, -50%);
    }
}

.pre-line {
    white-space: pre-line;
}

// 为自定义图标添加样式
.check-mark-flag {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #0064f0;
}

.clock-flag {
    width: 25px;
    height: 25px;
    padding: 0;
    border-radius: 50%;
    background-color: #fff;
    fill: #999;
    &:focus {
        outline: 0;
    }
}

::v-deep .milestone-steps-content .el-step {
    padding-left: 0;
    padding-right: 0;
    flex-basis: 0;
    flex-grow: 1;
    min-width: 0;
    max-width: 200px;

    .el-step__title {
        font-size: 14px;
    }
    // 完全隐藏默认图标容器
    .el-step__head {
        .el-step__icon {
            width: 100%;
            height: 100%;
            background: none;
            border: none;
            box-shadow: none;
        }
    }
}
</style>
