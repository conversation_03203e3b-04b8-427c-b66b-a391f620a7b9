<template>
    <div>
        <MaintenanceSelector
            @input="handleChange"
            :key="projectSelectorKey"
        ></MaintenanceSelector>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane label="订单列表" name="orderList" :lazy="true">
                    <OrderList
                        :activeName="activeName"
                        :productLine="productLine"
                        :subProductLine="subProductLine"
                        :projectManager="projectManager"
                        projectType="维护"
                    ></OrderList>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import MaintenanceSelector from 'maintenanceProject/components/MaintenanceSelector';
import OrderList from './components/OrderList';

export default {
    name: 'MaintenanceOrder',
    components: {
        MaintenanceSelector,
        OrderList
    },
    data() {
        return {
            activeName: 'orderList',
            // 顶部级联组件key
            projectSelectorKey: 0,
            productLine: '',
            subProductLine: '',
            projectManager: ''
        };
    },
    async created() {
        await this.$store.dispatch('tagsView/addView', this.$route);
    },
    methods: {
        handleChange(value) {
            const [productLine, subProductLine, projectManager] = value;
            this.productLine = productLine;
            this.subProductLine = subProductLine;
            this.projectManager = projectManager;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.box-main {
    width: 100%;
    height: calc(100vh - 80px);
    padding: 0px 20px 0px 20px;
    background-color: #ffffff;
    overflow: auto;
}
</style>
