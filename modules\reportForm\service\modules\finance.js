/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        // 财务报表
        finance: {
            // 按项目类别查询人工费用-双轴图
            getLaborCostLineChartByProductCategory(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_hr_cost_by_product_type_bar',
                    method: 'post',
                    data
                });
            },
            // 按项目类别查询人工费用-饼图
            getLaborCostPieChartByProductCategory(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_hr_cost_by_product_type_pie',
                    method: 'post',
                    data
                });
            },
            // 按级别查询人工费用-按月份分组-折线图
            getLaborCostForLineChart(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_hr_cost_by_level_bar',
                    method: 'post',
                    data
                });
            },
            // 级别查询人工费用-饼图
            getLaborCostForPieChart(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_hr_cost_by_level_pie',
                    method: 'post',
                    data
                });
            },
            // 按产品线查询人工费用-折线图
            getLaborCostByProductLine(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_hr_cost_by_line_bar',
                    method: 'post',
                    data
                });
            },
            // 按流程查询物料消耗费-折线图
            getMaterialConsumptionCostForLineChart(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_material_consumed_cost_by_flow_bar',
                    method: 'post',
                    data
                });
            },
            // 按流程查询物料消耗费-饼图
            getMaterialConsumptionCostForPieChart(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_material_consumed_cost_by_flow_pie',
                    method: 'post',
                    data
                });
            },
            // 查询所有流程
            getWholeFlow(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/dev_year_pay_item/get_material_consumed_flow',
                    method: 'post',
                    data
                });
            },
            // 查询年度支出（汇总）表格
            getSpendSummaryTable(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getExpenditureDetail',
                    method: 'post',
                    data
                });
            },
            // 获取年度支出总金额(顶部组件信息)
            getTopInfo(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getAnnualExpenditureSum',
                    method: 'post',
                    data
                });
            },
            // 获取年度支出金额(按月份)
            getChartDataByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getExpenditureByMonth',
                    method: 'post',
                    data
                });
            },
            // 获取年度支出金额(按产品线)
            getChartDataByProductLine(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getExpenditureByProductLine',
                    method: 'post',
                    data
                });
            },
            // 获取年度支出金额(按项目类别--柱状图)
            getChartDataByProductCategoryBar(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getExpenditureByCostTypeBar',
                    method: 'post',
                    data
                });
            },
            // 获取年度支出金额(按项目类别--饼图)
            getChartDataByProductCategoryPie(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getExpenditureByCostTypePie',
                    method: 'post',
                    data
                });
            },
            // 获取年度支出金额(按科目)
            getChartDataBySubject(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/annual-expenditure-sum/getExpenditureBySubject',
                    method: 'post',
                    data
                });
            },
            // 获取预算概算明细(表格)
            getEstimateCostTable(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/finance-estimates-monitor/getBudgetAndEstimatesDetail',
                    method: 'post',
                    data
                });
            },
            // 获取超概算项目分布占比(饼图)
            getOverEstimateCostChart(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/finance-estimates-monitor/getBudgetExceedEstimatesProject',
                    method: 'post',
                    data
                });
            },
            // 获取超预算明细(表格)
            getOverBudgetTable(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/finance-budget-monitor/getOverBudgetProjectDetail',
                    method: 'post',
                    data
                });
            },
            // 获取超预算项目分布占比(饼图)
            getOverBudgetCostChart(data) {
                return http({
                    baseDomain: basePath.bossapi.reportService,
                    url: '/finance-budget-monitor/getOverBudgetProject',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
