<template>
    <el-form-item v-bind="elFormItemAttrs">
        <el-upload
            ref="uploadFile"
            :class="{ disabled: elUploadAttrs.disabled || !canUpload }"
            :before-upload="beforeUpload"
            :before-remove="beforeRemove"
            :on-progress="onProcess"
            :on-success="onSuccess"
            :on-error="onError"
            :data="config.data"
            :on-remove="onRemove"
            :on-preview="onPreview"
            :file-list="fileList"
            v-bind="elUploadAttrs"
        >
            <!-- 图片 -->
            <template v-if="elUploadAttrs.listType === 'picture-card'">
                <i class="el-icon-plus" />
            </template>
            <!-- 文件 -->
            <template v-if="elUploadAttrs.listType === 'text' && canUpload">
                <el-button slot="trigger" size="small" type="primary"> 选取文件 </el-button>
                <div v-if="config.tip" slot="tip" class="el-upload__tip">
                    {{ config.tip }}
                </div>
            </template>
            <div v-if="config.tip" slot="tip" class="el-upload__tip">{{ config.tip }}</div>
        </el-upload>
        <snbc-image-viewer ref="snbcImageViewerRef" />
    </el-form-item>
</template>
<script>
import Vue from 'vue';
import { Loading } from 'element-ui';
import SnbcImageViewer from 'snbcCommon/components/snbc-image-viewer/SnbcImageViewer.vue';

const { fileUploadApi } = Vue.prototype.$service;
export default {
    name: 'SnbcFormFileUpload',
    components: {
        SnbcImageViewer
    },
    props: {
        /**
         * SnbcFormFileUpload组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    // 图片存储方式 array | string，如'url1,url2' 或 ['url1', 'url2']
                    tip: '',
                    dataType: 'string',
                    modelObj: {},
                    modelKey: '',
                    data: {},
                    elFormItemAttrs: {},
                    elUploadAttrs: {},
                    onPreview: null,
                    beforeUpload: null,
                    beforeRemove: null
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-upload组件默认属性设置
            defaultElUploadAttrs: {
                action: fileUploadApi,
                listType: 'picture-card',
                accept: 'image/*',
                limit: 1
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-upload组件应用属性
        elUploadAttrs() {
            const {
                token_name: tokenName,
                token_prefix: tokenPrefix = '',
                token_suffix: tokenSuffix = ''
            } = this.$store.state.settings.token;
            const { token } = this.$store.getters;
            return {
                ...this.defaultElUploadAttrs,
                ...(this.config.elUploadAttrs || {}),
                headers: {
                    [tokenName]: `${tokenPrefix}${token}${tokenSuffix}`,
                    vfSignature: '994d8349260e4a0d9c956519322b4aed'
                    // signature: getSignature()
                }
            };
        },
        // 文件数量
        fileCount() {
            const { modelObj, modelKey, dataType } = this.config;
            if (dataType === 'array') {
                return modelObj[modelKey].length;
            }
            if (!modelObj[modelKey]) {
                return 0;
            }
            return modelObj[modelKey].split(',').length;
        },
        // 文件上传限制
        canUpload() {
            return this.fileCount < this.elUploadAttrs.limit;
        },
        // 文件列表
        fileList() {
            const { modelObj, modelKey } = this.config;
            const files = modelObj[modelKey];
            // files格式为 ['url1', 'url2']
            if (files && Array.isArray(files)) {
                return files.map((url) => {
                    return {
                        name: url.substring(url.lastIndexOf('/')),
                        url
                    };
                });
            }
            // files格式为 'url1,url2'
            if (files && typeof files === 'string') {
                return files.split(',').map((url) => {
                    return {
                        name: url.substring(url.lastIndexOf('/')),
                        url
                    };
                });
            }
            return [];
        }
    },
    methods: {
        // 文件上传时的钩子
        onProcess(event, file, fileList) {
            Loading.service({
                text: '正在处理...',
                background: 'rgba(255,255,255,0.1)'
            });
        },
        // 文件上传失败时的钩子
        onError(err, file, fileList) {
            console.error(err, file, fileList);
            this.$tools.message.err('文件上传失败');
            Loading.service().close();
        },
        // 文件上传成功时的钩子
        onSuccess(response, file, fileList) {
            Loading.service().close();
            const { code, message, result: url } = response;
            if (code === '000000') {
                const { modelObj, modelKey, dataType } = this.config;
                this.config.onSuccess && this.config.onSuccess();
                if (dataType === 'array') {
                    this.$set(
                        modelObj,
                        modelKey,
                        modelObj[modelKey].concat({
                            url,
                            name: url.substring(url.lastIndexOf('/'))
                        })
                    );
                } else {
                    const value = modelObj[modelKey] ? [modelObj[modelKey], url].join(',') : url;
                    this.$set(modelObj, modelKey, value);
                    this.$emit('validate', modelKey);
                }
            } else {
                this.$tools.message.err(message || '系统异常');
                this.$refs.uploadFile.uploadFiles.splice(this.$refs.uploadFile.uploadFiles.indexOf(file), 1);
            }
        },
        // 删除文件的钩子
        onRemove(file, fileList) {
            const { modelObj, modelKey, dataType } = this.config;
            if (dataType === 'array') {
                this.$set(modelObj, modelKey, [...fileList]);
            } else {
                this.$set(modelObj, modelKey, fileList.map((item) => item.url).join(','));
            }
            this.$emit('validate', modelKey);
        },
        // 点击文件列表中已上传的文件时的钩子
        onPreview(file) {
            if (this.config.onPreview) {
                this.config.onPreview(file);
            } else {
                this.$refs.snbcImageViewerRef.show(file.url, [file.url]);
            }
        },
        // 上传文件之前的钩子
        async beforeUpload(file) {
            this.config.beforeUpload && this.config.beforeUpload(file);
        },
        // 删除文件之前的钩子
        async beforeRemove(file, fileList) {
            this.config.beforeRemove && this.config.beforeRemove(file, fileList);

            await this.$tools.confirm('确认删除？');
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .disabled .el-upload--picture-card {
    display: none !important;
}
::v-deep .el-upload-list__item .el-icon-close-tip {
    display: none !important;
}
::v-deep .el-upload-list__item {
    transition: none !important;
}
</style>
