<template>
    <div class="view">
        <div class="container">
            <div class="filter">
                <div class="header">
                    {{ $t('systemManagement.filterTitle') }}
                </div>
                <el-form ref="logItem" :model="queryInfo" label-width="150px">
                    <el-form-item
                        :label="$t('systemManagement.logger.username')"
                        prop="userName"
                    >
                        <el-input
                            v-model="queryInfo.userName"
                            :placeholder="
                                $t(
                                    'systemManagement.logger.message.inputUsername'
                                )
                            "
                            @keyup.enter.native="handleSearch"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="
                            $t('systemManagement.logger.columnName.loginIp')
                        "
                        prop="ip"
                    >
                        <el-input
                            v-model="queryInfo.ip"
                            :placeholder="
                                $t(
                                    'systemManagement.logger.message.inputUsername'
                                )
                            "
                            @keyup.enter.native="handleSearch"
                        />
                    </el-form-item>
                    <el-form-item
                        :label="$t('systemManagement.logger.timeRange')"
                    >
                        <el-tooltip
                            :content="queyrDateText"
                            placement="bottom"
                            :disabled="queyrDateTooltipDisabled"
                        >
                            <el-date-picker
                                v-model="queryDate"
                                :picker-options="pickerOptions"
                                unlink-panels
                                :placeholder="
                                    $t('systemManagement.logger.timeRange')
                                "
                                type="datetimerange"
                                :start-placeholder="
                                    $t('systemManagement.logger.startDate')
                                "
                                :end-placeholder="
                                    $t('systemManagement.logger.endDate')
                                "
                                :default-time="['00:00:00', '23:59:59']"
                            />
                        </el-tooltip>
                    </el-form-item>
                    <el-form-item
                        :label="$t('systemManagement.logger.operateModule')"
                        prop="logTarget"
                    >
                        <el-select
                            v-model="queryInfo.logTarget"
                            clearable
                            :popper-append-to-body="false"
                            :placeholder="
                                $t(
                                    'systemManagement.logger.message.inputOperateModule'
                                )
                            "
                            @change="logTypeChange"
                        >
                            <el-option
                                v-for="item in logTargetItems"
                                :key="item.valueId"
                                :label="
                                    $t(
                                        'systemManagement.dictionaryManagement.value.' +
                                            item.valueName
                                    )
                                "
                                :value="item.valueName"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        :label="$t('systemManagement.logger.operateType')"
                        prop="logType"
                    >
                        <el-select
                            v-model="queryInfo.logType"
                            clearable
                            :popper-append-to-body="false"
                            :placeholder="
                                $t('systemManagement.logger.operateType')
                            "
                        >
                            <el-option
                                v-for="item in logTypeItems"
                                :key="item.valueId"
                                :label="
                                    $t(
                                        'systemManagement.dictionaryManagement.value.' +
                                            item.valueName
                                    )
                                "
                                :value="item.valueName"
                            />
                        </el-select>
                    </el-form-item>
                    <div class="filter__btns">
                        <el-button
                            type="primary"
                            size="normal"
                            icon="fa fa-search"
                            class="margin-left"
                            @click="handleSearch"
                            >{{
                                $t('systemManagement.logger.query')
                            }}</el-button
                        >
                        <el-button
                            size="normal"
                            icon="fa fa-undo"
                            class="margin-left"
                            @click="resetForm"
                            >{{
                                $t('systemManagement.logger.reset')
                            }}</el-button
                        >
                    </div>
                </el-form>
            </div>

            <div class="header">
                <span class="header__title">
                    {{ $t('systemManagement.listTitle') }}</span
                >
                <span class="header__btns btn-box">
                    <Column
                        :column-data="columnData"
                        width="240"
                        @changeStatus="changeStatus"
                    />
                    <!-- <el-button v-permission="['merchant_log_export']" icon="fa fa-sign-out" class="margin-left" @click="handleExportCmd">{{
            $t("systemManagement.logger.downloadAll")
          }}</el-button> -->
                </span>
            </div>
            <el-table
                ref="multipleTable"
                :data="dataList"
                align="left"
                border
                :header-cell-style="{ background: '#F5F6FA' }"
            >
                <el-table-column
                    v-for="item in currentColumnData"
                    :key="item.value + item.desc"
                    :prop="item.value"
                    :width="item.width"
                    :label="$t(item.desc)"
                    :align="item.align"
                >
                    <template slot-scope="{ row }">
                        <div v-if="item.value === 'logTarget'">
                            {{
                                $t(
                                    'systemManagement.dictionaryManagement.value.' +
                                        row.logTarget
                                )
                            }}
                        </div>
                        <div v-else-if="item.value === 'logType'">
                            {{
                                $t(
                                    'systemManagement.dictionaryManagement.value.' +
                                        row.logType
                                )
                            }}
                        </div>
                        <div
                            v-else-if="item.value === 'logContent'"
                            class="table-content"
                            :title="row[item.value]"
                        >
                            {{ $t(row[item.value]) }}
                        </div>
                        <div v-else>{{ $t(row[item.value]) }}</div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination
                background
                for="pagination"
                :current-page.sync="queryInfo.pageNum"
                :page-sizes="[10, 20, 50, 100]"
                :page-size.sync="queryInfo.pageSize"
                layout="total, prev, pager, next,sizes,  jumper"
                :total="totalCount"
                class="right"
                @size-change="getList"
                @current-change="getList"
            />
        </div>
    </div>
</template>

<script>
import ExcelExport from '../../mixins/excel-export';
import { getLanguage } from 'wtf-core-vue/src/lang/index.js';
// 引入列可选组件
import Column from '../../components/columnOptional.vue';
import TopSelector from 'Components/TopSelector.vue';
// 客户端类型字段值类型---git测试
const DICT_TYPECODE_CLIENTTYPE = 'clientType';

export default {
    name: 'Logger',
    components: {
        Column,
        TopSelector
    },
    mixins: [ExcelExport],
    // eslint-disable-next-line max-lines-per-function
    data() {
        return {
            // 限制日期选择
            pickerOptions: {
                disabledDate(time) {
                    const today = new Date();
                    const lastThirtyDays = new Date();
                    lastThirtyDays.setDate(today.getDate() - 30);
                    return (
                        time.getTime() < lastThirtyDays.getTime() ||
                        time.getTime() > today.getTime()
                    );
                }
            },
            // 查询表单数据
            queryInfo: {
                userName: '',
                // 开始时间、结束时间字段会根据计算属性进行处理
                startTime: '',
                endTime: '',
                logTarget: '',
                logType: '',
                pageSize: 10,
                pageNum: 1,
                ip: '',
                international: ''
            },
            // 总记录数
            totalCount: 0,
            isShowSelect: false,
            // 客户端类型数据源
            dataClientTypeList: [],
            // 列表数据源
            dataList: [],
            // 在下拉框中的列的相关数据
            columnData: [
                {
                    // table中展示的列对应的字段名
                    value: 'logTime',
                    // 字段名称
                    desc: 'systemManagement.logger.columnName.logTime',
                    // 是否展示此字段
                    checked: true,
                    // 是否展示tooltip
                    tooltip: false,
                    // 左对齐、右对齐
                    align: 'left'
                },
                {
                    value: 'userName',
                    desc: 'systemManagement.logger.columnName.username',
                    checked: true,
                    tooltip: false,
                    align: 'left'
                },
                {
                    value: 'logTarget',
                    desc: 'systemManagement.logger.columnName.operateModule',
                    checked: true,
                    tooltip: false,
                    align: 'left'
                },
                {
                    value: 'logType',
                    desc: 'systemManagement.logger.columnName.operateType',
                    checked: true,
                    tooltip: false,
                    align: 'left'
                },
                {
                    value: 'logContent',
                    desc: 'systemManagement.logger.columnName.content',
                    checked: true,
                    tooltip: false,
                    width: '300px',
                    align: 'left'
                },
                {
                    value: 'ip',
                    desc: 'systemManagement.logger.columnName.loginIp',
                    checked: true,
                    tooltip: false,
                    width: '300px',
                    align: 'left'
                },
                {
                    value: 'clientType',
                    desc: 'systemManagement.logger.columnName.clientType',
                    checked: false,
                    tooltip: false,
                    width: '150px',
                    align: 'left'
                },
                {
                    value: 'requestSource',
                    desc: 'systemManagement.logger.columnName.browser',
                    checked: false,
                    tooltip: false,
                    width: '220px',
                    align: 'left'
                },
                {
                    value: 'remarks',
                    desc: 'systemManagement.logger.columnName.remarks',
                    checked: false,
                    tooltip: false,
                    width: '150px',
                    align: 'left'
                }
            ],
            // 当前实际展示的列
            currentColumnData: [],
            logTargetItems: [],
            logTypeItems: []
        };
    },
    computed: {
        // 查询的时间范围
        queryDate: {
            get() {
                // 将开始时间、结束时间，组装为组件所需要的格式
                return [this.queryInfo.startTime, this.queryInfo.endTime];
            },
            set(val) {
                // 如果值无效，则将查询条件时间清空
                if (!val) {
                    this.queryInfo.startTime = '';
                    this.queryInfo.endTime = '';
                    return;
                }
                // 调用框架工具函数处理时间格式
                this.queryInfo.startTime = this.$tools.parseTime(
                    val[0],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
                this.queryInfo.endTime = this.$tools.parseTime(
                    val[1],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
            }
        },
        // 用于时间框较短，时间显示不全时。tooltip的文本值
        queyrDateText() {
            return `${this.queryInfo.startTime}-${this.queryInfo.endTime}`;
        },
        // 如果时间为空，则tooltip显示无效。这种常见下，直接禁用tooltip
        queyrDateTooltipDisabled() {
            return !this.queryInfo.startTime && !this.queryInfo.endTime;
        }
    },
    created() {
        // 结束日期取当天。由于结束日期会去23:59:59，所以这里的时分秒部分不是当前的
        const end = new Date().getTime();
        // 日期部分提前查询最近2天。（在加上开始、结束时分秒，总时间为3天）
        const start = end - 86400000 * 2;
        this.queryInfo.startTime = this.formatTime(start, 'start');
        this.queryInfo.endTime = this.formatTime(end, 'end');
        this.currentColumnData = this.columnData;
        // 过滤不展示的列
        this.changeStatus(this.columnData);
        this.getList();
        // 获取操作模块下拉数据
        this.getLogTypeData();
    },
    methods: {
        // 处理开始时间、和结束时间，时分秒部分
        formatTime(time, flag) {
            const date = new Date(time);
            const year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            // 月份自动添加0前缀
            if (month < 10) {
                month = `0${month}`;
            }
            // 天的日期自动添加前缀
            if (day < 10) {
                day = `0${day}`;
            }
            let str = '';
            if (flag === 'start') {
                // 开始时间，时分秒部分
                str = ' 00:00:00';
            } else {
                // 结束时间，时分秒部分
                str = ' 23:59:59';
            }
            return `${year}-${month}-${day}${str}`;
        },
        // 获取客户端类型数据
        getClientTypeList() {
            // 这里调用字典类型查询接口，获取字典值列表
            this.$service.systemManagement
                .getDictionaryValueListAll({
                    typeCode: DICT_TYPECODE_CLIENTTYPE
                })
                .then((response) => {
                    if (response.head.code === '000000') {
                        // 将返回的数据给下拉框
                        this.dataClientTypeList = response.result.map(
                            (item) => {
                                return {
                                    label: item.valueName,
                                    value: item.valueCode
                                };
                            }
                        );
                    } else {
                        // 如果数据查询失败，类别数据情况，避免失败，数据无法刷新问题
                        this.dataClientTypeList = [];
                        this.$message({
                            message:
                                response.head.message ||
                                this.$t(
                                    'systemManagement.dictionary.message.queryListFailure'
                                ),
                            type: 'error'
                        });
                    }
                });
        },
        // 获取列表数据
        getList() {
            // 先获取当前的语言类型
            this.queryInfo.international = getLanguage();
            this.$service.systemManagement
                .getLogList(this.queryInfo)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.dataList = response.body.list;
                        // 分页与服务器保持一致
                        this.totalCount = response.body.total;
                        this.queryInfo.pageNum = response.body.pageNum;
                        this.queryInfo.pageSize = response.body.pageSize;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 查询按钮事件
        handleSearch() {
            this.queryInfo.international = getLanguage();
            this.getList();
        },
        // 重置按钮事件，将查询表单内容重置
        resetForm() {
            this.$refs.logItem.resetFields();
            this.logTypeItems = [];
            // 结束日期取当天。由于结束日期会去23:59:59，所以这里的时分秒部分不是当前的
            const end = new Date().getTime();
            // 日期部分提前查询最近2天。（在加上开始、结束时分秒，总时间为3天）
            const start = end - 86400000 * 2;
            this.queryInfo.startTime = this.formatTime(start, 'start');
            this.queryInfo.endTime = this.formatTime(end, 'end');
        },
        // 导出Excel文件,cmd:all/所有页,page/当前页
        handleExportCmd() {
            this.$service.systemManagement
                .getLogExport(this.queryInfo)
                .then((response) => {
                    // excel 文件下载
                    this.download(
                        response,
                        `${this.$t(
                            'systemManagement.logger.logExcelName'
                        )}${this.$tools.parseTime(new Date(), '{y}{m}{d}')}.xls`
                    );
                });
        },
        //  选择需要展示的列
        changeStatus(data) {
            this.currentColumnData = data.filter((item) => {
                // 过滤出checked 为true的数据
                return item.checked === true;
            });
        },
        /**
         * 获取操作模块下拉数据
         */
        getLogTypeData() {
            const param = {
                typeCode: 'logTarget',
                parentId: ''
            };
            this.$service.systemManagement
                .getDictionaryValueListAll(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.logTargetItems = response.body;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 选择操作模块，获取对应的操作类型
        logTypeChange(val) {
            // 清空操作模块
            this.queryInfo.logType = '';
            this.logTypeItems = [];
            const param = {
                typeCode: 'logOpration',
                parentId: val
            };
            this.$service.systemManagement
                .getDictionaryValueListAll(param)
                .then((response) => {
                    if (response.head.code === '000000') {
                        this.logTypeItems = response.body;
                    } else {
                        const msg = `systemManagement.bgReturnError[${response.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.btn-box {
    display: flex;
}
.table-content {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
