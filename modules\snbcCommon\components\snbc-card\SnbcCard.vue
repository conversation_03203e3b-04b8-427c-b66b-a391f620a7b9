<template>
  <el-card class="el-card">
    <div slot="header">
      <span>{{ title }}</span>
      <el-button
        v-if="buttonName"
        id="action-btn"
        class="action-btn"
        type="text"
        @click="handleClick"
      >{{ buttonName }}</el-button>
    </div>
    <slot name="card-body" />
  </el-card>
</template>
<script>
export default {
    name: 'SnbcCard',
    props: {
        title: {
            type: String,
            default: ''
        },
        buttonName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {};
    },
    methods: {
        handleClick() {
            this.$emit('action');
        }
    }
};
</script>
<style lang="scss" scoped>
.el-card {
    padding: 0px;
    ::v-deep .el-card__header {
        padding-left: 20px;
        padding-right: 20px;
        align-items: center;
        line-height: 40px;
        background-color: #33add6;
        color: #ffffff;
    }

    ::v-deep .el-card__body {
        padding: 20px;
    }
}
#action-btn {
    position: relative;
    top: 6px;
    height: 28px;
    padding: 3px 15px;
    background-color: #1171ee;
    color: #ffffff;
    float: right;
}
</style>
