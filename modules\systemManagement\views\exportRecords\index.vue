<template>
    <div class="view">
        <div class="container">
            <div class="filter">
                <div class="header">{{
          $t("systemManagement.filterTitle")
        }}</div>

                <el-form ref="exportForm" :model="queryInfo" label-width="130px">
                    <el-form-item :label="$t('systemManagement.exportRecords.fileName')" prop="fileName">
                        <el-input v-model="queryInfo.fileName" :placeholder="
                $t('systemManagement.exportRecords.msg.msg2')
              " @keyup.enter.native="queryConfig" />
                    </el-form-item>

                    <el-form-item :label="$t('systemManagement.exportRecords.operator')" prop="createUser">
                        <el-input v-model="queryInfo.createUser" :placeholder="
                $t('systemManagement.exportRecords.msg.msg3')
              " @keyup.enter.native="queryConfig" />
                    </el-form-item>
                    <el-form-item :label="$t('systemManagement.logger.timeRange')">
                        <el-tooltip :content="queyrDateText" placement="bottom" :disabled="queyrDateTooltipDisabled">
                            <el-date-picker v-model="queryDate" unlink-panels :placeholder="
                  $t('systemManagement.logger.timeRange')
                " type="datetimerange" :start-placeholder="
                  $t('systemManagement.logger.startDate')
                " :end-placeholder="
                  $t('systemManagement.logger.endDate')
                " :default-time="['00:00:00', '23:59:59']" />
                        </el-tooltip>
                    </el-form-item>

                    <div class="filter__btns">
                        <el-button type="primary" icon="fa fa-search" class="button option-button margin-left" @click="queryConfig">{{ $t("common.searchBtn") }}</el-button>
                        <el-button size="normal" icon="fa fa-undo" class="margin-left" @click="queryReset">
                            {{ $t("systemManagement.dictionary.reset") }}
                        </el-button>
                    </div>
                </el-form>
            </div>

            <div class="container">
                <div class="header">
                    <div>
                        <span class="header__title">{{
              $t("common.queryList")
            }}</span>
                        <span>(<span class="tips">{{ $t('systemManagement.exportRecords.msg.msg4') + days +$t('systemManagement.exportRecords.msg.msg5') }}</span>)</span>
                    </div>
                    <span class="header__btns">
                        <el-button type="primary" icon="el-icon-refresh-right refresh" @click="queryConfig">
                            {{ $t("systemManagement.exportRecords.refresh") }}
                        </el-button>
                    </span>
                </div>

                <el-table id="configurationTable" :data="tableData" border :header-cell-style="{ background: '#F5F6FA' }">
                    <el-table-column prop="fileName" :label="$t('systemManagement.exportRecords.table.fileName')" />
                    <el-table-column prop="status" :label="$t('systemManagement.exportRecords.table.status')">
                        <template slot-scope="{row}">
                            <div>
                                <span v-if="(row.status === 1)">文件生成中</span>
                                <span v-else-if="(row.status === 2)">文件已生成</span>
                                <span v-else>文件生成失败</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createUser" :label="$t('systemManagement.exportRecords.table.operator')" />
                    <el-table-column prop="createTime" :label="$t('systemManagement.configuration.createTime')" width="160px">
                        <template slot-scope="{ row }">
                            <span>{{
                row.createTime.split("T").join(" ")
              }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column :label="$t('common.handle')" width="190px">
                        <template slot-scope="{ row, $index }">
                            <div class="btn-box">
                                <el-link :href="row.url" :underline="false">
                                    <el-button type="primary" icon="fa fa-cloud-download" size="mini" class="button table-inner-button" :disabled="(row.status === 1 || row.status === 3)" @click="downLoad(row)">
                                        {{ $t("systemManagement.exportRecords.table.downLoad") }}
                                    </el-button>
                                </el-link>
                                <el-button size="mini" icon="fa fa-trash-o" class="button table-delete-button" :disabled="row.status === 1" @click="handleDelete(row, $index)">
                                    {{ $t("table.delete") }}
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background for="pagination" class="right" layout="total, prev, pager, next, sizes, jumper" :page-sizes="[10, 20, 50, 100]" :total="pager.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ExportRecords',
    data() {
        return {
            queryInfo: {
                // 文件名称
                fileName: '',
                // 操作人
                createUser: '',
                startTime: '',
                endTime: ''
            },
            // 表格数据
            tableData: [],
            pager: {
                pageNum: 1,
                pageSize: 10,
                total: 0
            },
            // 过期天数
            days: 0
        };
    },
    computed: {
        // 查询的时间范围
        queryDate: {
            get() {
                // 将开始时间、结束时间，组装为组件所需要的格式
                return [this.queryInfo.startTime, this.queryInfo.endTime];
            },
            set(val) {
                // 如果值无效，则将查询条件时间清空
                if (!val) {
                    this.queryInfo.startTime = '';
                    this.queryInfo.endTime = '';
                    return;
                }
                // 调用框架工具函数处理时间格式
                this.queryInfo.startTime = this.$tools.parseTime(
                    val[0],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
                this.queryInfo.endTime = this.$tools.parseTime(
                    val[1],
                    '{y}-{m}-{d} {h}:{i}:{s}'
                );
            }
        },
        // 用于时间框较短，时间显示不全时。tooltip的文本值
        queyrDateText() {
            return `${this.queryInfo.startTime}-${this.queryInfo.endTime}`;
        },
        // 如果时间为空，则tooltip显示无效。这种常见下，直接禁用tooltip
        queyrDateTooltipDisabled() {
            return !this.queryInfo.startTime && !this.queryInfo.endTime;
        }
    },
    created() {
        // 结束日期取当天。由于结束日期会去23:59:59，所以这里的时分秒部分不是当前的
        const end = new Date().getTime();
        // 日期部分提前查询最近2天。（在加上开始、结束时分秒，总时间为3天）
        const start = end - 86400000 * 2;
        this.queryInfo.startTime = this.formatTime(start, 'start');
        this.queryInfo.endTime = this.formatTime(end, 'end');
        // 获取导出文件列表
        this.getExportFilelist();
        // 获取过期天数
        this.getExpireDays();
    },
    methods: {
        // 处理开始时间、和结束时间，时分秒部分
        formatTime(time, flag) {
            const date = new Date(time);
            const year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            // 月份自动添加0前缀
            if (month < 10) {
                month = `0${month}`;
            }
            // 天的日期自动添加前缀
            if (day < 10) {
                day = `0${day}`;
            }
            let str = '';
            if (flag === 'start') {
                // 开始时间，时分秒部分
                str = ' 00:00:00';
            } else {
                // 结束时间，时分秒部分
                str = ' 23:59:59';
            }
            return `${year}-${month}-${day}${str}`;
        },
        // 查询
        queryConfig() {
            this.getExportFilelist();
        },
        queryReset() {
            this.$refs.exportForm.resetFields();
            // 结束日期取当天。由于结束日期会去23:59:59，所以这里的时分秒部分不是当前的
            const end = new Date().getTime();
            // 日期部分提前查询最近2天。（在加上开始、结束时分秒，总时间为3天）
            const start = end - 86400000 * 2;
            this.queryInfo.startTime = this.formatTime(start, 'start');
            this.queryInfo.endTime = this.formatTime(end, 'end');
        },
        // 获取导出文件列表
        getExportFilelist() {
            const data = {
                fileName: this.queryInfo.fileName,
                createUser: this.queryInfo.createUser,
                beginTime: this.queryInfo.startTime,
                endTime: this.queryInfo.endTime,
                pageNum: this.pager.pageNum,
                pageSize: this.pager.pageSize
            };
            this.$service.systemManagement
                .getExportFilelist(data)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.tableData = res.body.list;
                        this.pager.total = res.body.total;
                        this.pager.pageNum = res.body.pageNum;
                        this.pager.pageSize = res.body.pageSize;
                    } else {
                        const msg = `productService.bgReturnError[${res.head.code}]`;
                        this.$message({
                            message: this.$t(msg),
                            type: 'error'
                        });
                    }
                });
        },
        // 没写大小改变时触发
        handleSizeChange(val) {
            this.pager.pageSize = val;
            this.getExportFilelist();
        },
        // 当前页码改变触发
        handleCurrentChange(val) {
            this.pager.pageNum = val;
            this.getExportFilelist();
        },
        // 删除
        handleDelete(param) {
            this.paramItemDetete = param;
            this.$confirm(
                this.$t('systemManagement.configuration.message.deleteTypeTip'),
                this.$t(
                    'systemManagement.configuration.message.deleteTipTitle'
                ),
                {
                    type: 'warning'
                }
            ).then(() => {
                this.deleteParam();
            });
        },
        // 删除参数
        deleteParam() {
            const params = {
                fileName: this.paramItemDetete.fileName,
                exportId: this.paramItemDetete.exportId
            };
            this.$service.systemManagement
                .deleteExportFile(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        const _this = this;
                        this.getExportFilelist();
                        this.$message.success(
                            _this.$t(
                                'systemManagement.configuration.message.deleteSuccess'
                            )
                        );
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        // 获取过期天数
        getExpireDays() {
            this.$service.systemManagement
                .showClearTime({ paramCode: 'FileDeleteDay' })
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.days = res.body.paramValue;
                    }
                });
        }
    }
};
</script>
<style lang="scss" scoped>
::v-deep .margin-left.el-button {
    span {
        margin-left: 5px;
    }
}
.btn-box {
    display: flex;
    align-items: center;
}
.el-button--default.table-delete-button {
    margin-left: 10px;
}
.tips {
    color: #3370ff;
}
.header__title {
    min-width: 70px;
}
</style>
