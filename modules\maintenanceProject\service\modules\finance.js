import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        finance: {
            // 仪表盘 - 按月份
            getMaintainProjectByMonth(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/project-maintain-finance/getMaintainProjectFinanceByMonth`,
                    method: 'post',
                    data
                });
            },
            // 仪表盘 - 按科目
            getMaintainProjectBySubject(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/project-maintain-finance/getMaintainProjectFinanceBySubject`,
                    method: 'post',
                    data
                });
            },
            // 当前支出总计
            getOverall(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/project-maintain-finance/getMaintainProjectFinanceOverall`,
                    method: 'post',
                    data
                });
            },
            // 支出明细列表
            getMaintainProjectPayDetailList(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/project-maintain-finance/getMaintainProjectPayDetailList`,
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
