<template>
    <div>
        <el-table
            class="zentao-table"
            align="center"
            :data="tableList"
            style="width: 100%"
            max-height="260"
        >
            <el-table-column
                :label="`任务（禅道ID：${proTaskId}）`"
                min-width="300"
                header-align="center"
                align="left"
            >
                <template slot-scope="scope">
                    <el-tooltip
                        v-if="scope.$index === 0"
                        placement="top"
                        width="200"
                        trigger="hover"
                        :content="`关联禅道项目：${projectTitle}`"
                    >
                        <el-button
                            type="text"
                            class="main-task"
                            @click="handleRoutingJump(scope.row)"
                        >
                            {{ scope.row.name }}
                        </el-button>
                    </el-tooltip>
                    <div v-else class="sub-task">
                        {{ scope.row.name }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="deadline"
                label="计划完成时间"
                align="center"
                width="120"
            ></el-table-column>
            <el-table-column
                prop="assignedTo"
                label="责任人"
                align="center"
                width="110"
            ></el-table-column>
            <el-table-column label="进度" align="center" width="80">
                <template slot-scope="scope">
                    {{ scope.row.progress }}%
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'ZentaoTaskList',
    components: {},
    props: {
        show: {
            type: Boolean,
            default: false
        },
        proProjectId: {
            type: [String, Number],
            default: ''
        },
        proTaskId: {
            type: [String, Number],
            default: ''
        },
        projectManagerAccount: {
            type: String,
            default: ''
        },
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            tableList: [],
            // 禅道项目名称
            projectTitle: ''
        };
    },
    computed: {},
    watch: {
        show(newVal) {
            if (
                newVal &&
                this.row.proProjectId === this.proProjectId &&
                this.row.proTaskId === this.proTaskId
            ) {
                this.getProjectTitle();
                this.getTaskList();
            }
        }
    },
    created() {
        if (this.show) {
            this.getProjectTitle();
            this.getTaskList();
        }
    },
    methods: {
        /**
         * 跳转到禅道任务
         * @param {Object} row 行数据
         */
        handleRoutingJump(row) {
            const { id } = row;
            const url = `http://192.168.28.67/pro/task-view-${id}.html`;
            id && window.open(url);
        },
        /**
         * 获取禅道项目名称
         */
        async getProjectTitle() {
            const api =
                this.$service.maintenanceProject.zentao
                    .getZentaoProjectNameById;
            const params = {
                id: this.proProjectId,
                projectCode: '维护',
                projectManagerAccount: this.projectManagerAccount
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.projectTitle = res.body;
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 获取禅道任务列表
         */
        async getTaskList() {
            const api =
                this.$service.maintenanceProject.zentao
                    .getZentaoTaskListInProject;
            const params = {
                id: this.proTaskId,
                proProjectId: this.proProjectId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // eslint-disable-next-line prefer-const
                let { childTaskList = [], ...parentTask } = res.body[0];
                if (!childTaskList) childTaskList = [];
                this.tableList = [parentTask, ...childTaskList];
            } catch (error) {
                console.error(error, 'error');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.zentao-table {
    border: 1px solid #8c8c8c !important;
}
.header-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100%;
}
.sub-task {
    padding-left: 20px;
}
.main-task {
    word-break: break-all;
    white-space: normal;
    width: 100%; /* 确保按钮占据整个单元格的宽度，这个不加的话换行就不生效 */
    display: flex;
    justify-content: flex-start;
    line-height: 1.2;
    text-align: left;
}
.conclusion {
    background-color: #fff;
    color: black;
    font-weight: bold;
    flex: 1;
    text-align: left;
    padding-left: 15px;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep .el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
