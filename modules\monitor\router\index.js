// 目前框架路由，只支持到二级路由
export default [
    {
        path: '/monitor',
        useLayout: true,
        redirect: 'noRedirect',
        name: 'Monitor',
        alwaysShow: true,
        meta: {
            title: '监控'
        },
        children: [
            {
                path: 'messageCheck',
                component: () => import('../views/messageCheck/index.vue'),
                name: 'MessageCheck',
                meta: {
                    title: '预警'
                }
            }
        ]
    }
];
