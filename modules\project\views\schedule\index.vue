<template>
    <div>
        <project-selector
            :infoDisabled="false"
            @input="handleDepartmentChange"
        ></project-selector>
        <div class="box-main">
            <el-tabs
                v-model="activeName"
                @tab-click="scheduleClick(activeName)"
            >
                <el-tab-pane
                    label="基线进度"
                    name="milestone"
                    v-if="hasBaselineSchedulePermisson"
                >
                    <BaseLineSchedule
                        :projectId="selectValue"
                        :wholeList="wholeScheduleList"
                    ></BaseLineSchedule>
                </el-tab-pane>
                <el-tab-pane
                    label="当前基线"
                    name="currentBaseLine"
                    v-if="hasCurrentBaselineAndReasonAnalysisPermisson"
                >
                    <current-baseline-table
                        :basicLineData="curlineData"
                        @handle-time-change="curtineChange"
                        :projectId="selectValue"
                    ></current-baseline-table>
                </el-tab-pane>
                <el-tab-pane
                    label="历史基线"
                    name="historyLine"
                    v-if="hasHistoryLinePermisson"
                >
                    <history-basic-line-table
                        :tableData="histableData"
                        :dynamicColumns="hisColumnsData"
                        :fixedColumns="fixedColumns"
                    ></history-basic-line-table>
                </el-tab-pane>
                <el-tab-pane
                    label="里程碑延期原因分析"
                    name="reasonAnalysis"
                    v-if="hasCurrentBaselineAndReasonAnalysisPermisson"
                >
                    <el-empty
                        description="暂无数据"
                        :image-size="30"
                        v-if="AnalysisData.length === 0"
                    ></el-empty>
                    <cause-analysis-table
                        v-else
                        :listData="AnalysisData"
                        @editSuccess="refreshTableData"
                        @addSuccess="addreshTableData"
                    ></cause-analysis-table>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import currentBaselineTable from './components/currentBaseline.vue';
import projectSelector from 'project/components/projectSelector';
import causeAnalysisTable from './components/causeAnalysis.vue';
import BaseLineSchedule from './components/BaseLineSchedule.vue';
import historyBasicLineTable from './components/historyBasicLine.vue';

export default {
    components: {
        currentBaselineTable,
        projectSelector,
        causeAnalysisTable,
        BaseLineSchedule,
        historyBasicLineTable
    },
    data() {
        return {
            activeName: 'milestone',
            curlineData: [],
            selectValue: '',
            nostartData: {},
            timedisabled: true,
            AnalysisData: [],
            histableData: [],
            hisColumnsData: [],
            wholeScheduleList: [],
            fixedColumns: [
                { prop: 'name', label: '里程碑' },
                { prop: 'rouse', label: '来源' },
                { prop: 'basicLine', label: '市场基线' }
            ]
        };
    },
    computed: {
        // 是否有基线进度页签权限
        hasBaselineSchedulePermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'ScheduleBaseLineTab'
            );
        },
        // 是否有当前基线与里程碑延期原因分析页签权限
        hasCurrentBaselineAndReasonAnalysisPermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'CurrentBaseLineAndReasonAnalysisTab'
            );
        },
        // 是否有历史基线页签权限
        hasHistoryLinePermisson() {
            return this.$store.state.permission.btnDatas.includes(
                'HistoryLineTab'
            );
        }
    },
    methods: {
        // 调用组件值
        handleDepartmentChange(value) {
            this.selectValue = value[1];
            if (this.selectValue) {
                this.scheduleClick(this.activeName);
            } else {
                this.$message.error('请选择项目后查询！');
            }
        },
        scheduleClick(tab) {
            if (!this.selectValue) {
                this.$message.error('请选择项目后查询！');
            } else {
                switch (tab) {
                    case 'milestone':
                        this.getMilestoneInfo(this.selectValue);
                        break;
                    case 'currentBaseLine':
                        this.basicLine(this.selectValue);
                        break;
                    case 'reasonAnalysis':
                        this.reasonAnalysis(this.selectValue);
                        break;
                    case 'historyLine':
                        this.getHistoryLine(this.selectValue);
                        break;
                    default:
                        return;
                }
            }
        },

        // 获取当前基线数据
        basicLine(value) {
            const params = {
                projectId: value
            };
            this.$service.project.schedule.getbaseLine(params).then((res) => {
                if (res.head.code === '000000') {
                    this.curlineData = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        // 时间选择
        curtineChange(value) {
            const params = {
                id: value.id,
                endDate: value.endDate,
                currentDevEvaluationBaseline:
                    value.currentDevEvaluationBaseline,
                currentDevExecuteBaseline: value.currentDevExecuteBaseline,
                proCurrentPlan: value.proCurrentPlan,
                projectId: this.selectValue
            };
            this.$service.project.schedule.modiftTime(params).then((res) => {
                if (res.head.code === '000000') {
                    this.basicLine(this.selectValue);
                } else {
                    this.$message.error(res.head.message);
                }
            });
        },
        // 历史基线查询
        getHistoryLine(value) {
            const params = {
                projectId: value
            };
            this.$service.project.schedule.hisLineQuery(params).then((res) => {
                if (res.head.code === '000000') {
                    this.hisColumnsData = res.body.dynamicColumns || [];
                    this.histableData = res.body.tableData || [];
                } else {
                    this.hisColumnsData = [];
                    this.histableData = [];
                    this.$message.error(res.head.message);
                }
            });
        },

        handleUpdateTime(key, value) {
            this.nostartData[key] = value;
            const params = this.nostartData;
            this.$service.project.schedule
                .nostartChangetime(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.nostartLine(this.selectValue);
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        refreshTableData() {
            this.reasonAnalysis(this.selectValue);
        },
        addreshTableData() {
            this.reasonAnalysis(this.selectValue);
        },
        // 里程碑原因分析
        reasonAnalysis(value) {
            const params = {
                projectId: value
            };
            this.$service.project.schedule
                .getReasonAnalysis(params)
                .then((res) => {
                    if (res.head.code === '000000') {
                        this.AnalysisData = res.body || [];
                    } else {
                        this.$message.error(res.head.message);
                    }
                });
        },
        /**
         * 获取基线进度数组
         */
        async getMilestoneInfo(selectValue) {
            try {
                const api =
                    this.$service.project.schedule.getMilestoneInfoByBaseLine;
                const params = {
                    projectId: selectValue,
                    projectSourceList: ['IPD', '市场']
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.wholeScheduleList = res.body.reverse();
            } catch (err) {
                console.error('Error:', err);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: #ffffff;
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
::v-deep #pane-second {
    border: 1px solid #8c8c8c !important;
}
</style>
