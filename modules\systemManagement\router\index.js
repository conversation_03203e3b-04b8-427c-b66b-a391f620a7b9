// 目前框架路由，只支持到二级路由
export default [
	{
		path: '/systemManagement',
		useLayout: true,
		redirect: 'noRedirect',
		name: 'SystemManagement',
		meta: {
			title: 'systemManagement',
			icon: 'el-icon-setting'
		},
		children: [
			{
				path: 'informationAnnouncement',
				component: () => import('../views/informationAnnouncement'),
				name: 'InformationAnnouncement',
				meta: { title: 'informationAnnouncement' }
			},

			{
				path: 'configuration',
				component: () => import('../views/configuration'),
				name: 'Configuration',
				meta: { title: 'configuration' }
			},
			{
				path: 'dictionary',
				component: () => import('../views/dictionary'),
				name: 'Dictionary',
				meta: { title: 'dictionary' }
			},
			{
				path: 'area',
				component: () => import('../views/area'),
				name: 'Area',
				meta: { title: 'area' }
			},
			{
				path: 'organization',
				component: () => import('../views/organization'),
				name: 'Organization',
				meta: { title: 'organization' }
			},
			{
				path: 'logger',
				component: () => import('../views/logger'),
				name: 'Logger',
				meta: { title: 'logger' }
			},
			{
				path: 'role',
				component: () => import('../views/role'),
				name: 'Role',
				meta: { title: 'role' }
			},
			{
				path: 'permission',
				component: () => import('../views/permission'),
				name: 'Permission',
				meta: { title: 'permissionManagement' }
			},
			{
				path: 'messageNotice',
				component: () => import('../views/messagenotice'),
				name: 'Messagenotice',
				meta: { title: 'messageNotice' }
			},
			{
				path: 'exportRecords',
				component: () => import('../views/exportRecords'),
				name: 'ExportRecords',
				meta: { title: 'exportRecords' }
			},
			{
				path: 'topMenu',
				component: () => import('../views/topMenu'),
				name: 'TopMenu',
				meta: { title: 'topMenu' }
			},
			{
				path: 'bireport/:id',
				component: () => import('../views/superset/bireport.vue'),
				name: 'Bireport',
				meta: { title: 'superset' }
			},
			{
				path: 'messageTemplate',
				component: () => import('../views/messageTemplate'),
				name: 'MessageTemplate',
				meta: { title: 'messageTemplate' }
			}
		]
	}
];
