/**
 * service服务，所有的接口都在这里管理
 * 文件名和服务名必须相同，在index.js文件中引入，挂载在$service上作为子对象
 * 挂载时会加模块名称用于作用域隔离，调用时: this.$service.department.xxxx
 * 本文件只存在本服务的接口，保证服务的干净，方便维护和查找
 */

import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;
    // 根服务对象
    const basePath = basePathInit();
    const service = {
        schedule: {
            // 查询当前基线数据
            getbaseLine(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: `/project/getProjectDetailInfo`,
                    method: 'get',
                    params: query
                });
            },
            // 修改时间接口
            modiftTime(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/updateDetail',
                    method: 'post',
                    data
                });
            },
            // 历史基线
            hisLineQuery(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectDetailHisInfo',
                    method: 'get',
                    params: query
                });
            },
            // 未启动项目时间估算
            nostartQuery(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectPlanTimeInfo',
                    method: 'get',
                    params: query
                });
            },
            // 未启动项目时间估算修改时间
            nostartChangetime(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/updateProjectPlanTimeInfo',
                    method: 'post',
                    data
                });
            },
            // 里程碑延期原因分析
            getReasonAnalysis(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectDetailAndDelayInfo',
                    method: 'get',
                    params: query
                });
            },
            // 编辑里程碑延期原因分析
            editReasonAnalysis(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/updateProjectDetailDelayInfo',
                    method: 'post',
                    data
                });
            },
            // 查询项目团队
            getProjectTeam(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectTeamInfo',
                    method: 'get',
                    params: query
                });
            },
            // 修改项目团队
            editProjectTeam(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/updateProjectTeamInfo',
                    method: 'post',
                    data
                });
            },
            // 同步禅道
            synchroZentao(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/syncZenTaoProjectTeamInfo',
                    method: 'get',
                    params: query
                });
            },
            // 获取各个基线里程碑信息
            getMilestoneInfoByBaseLine(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectDetailScheduleInfo',
                    method: 'post',
                    data
                });
            },
            // 获取项目团队组织图信息
            getGroupInfoForOrgChart(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjectTeamTreeInfo',
                    method: 'get',
                    params: query
                });
            },
            uploadMilestoneChangeReason() {
                return `${basePath.bossapi.pmService}/project_detail_change/uploadExcel`;
            }
        }
    };
    return service;
};
