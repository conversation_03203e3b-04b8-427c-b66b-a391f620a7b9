import { basePathInit } from '@/envConst';

export default (Vue) => {
    // 框架中的axios插件，这里直接使用
    const http = Vue.prototype.$http;

    // 根服务对象
    const basePath = basePathInit();

    const service = {
        naturalResources: {
            // 获取项目
            getProjects(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getProjects',
                    method: 'get',
                    params: query
                });
            },
            // 获取项目下拉
            getProjectselect() {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/project/getAllProject',
                    method: 'get'
                });
            },

            // 查询项目人力缺编信息
            getnaturalResources(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/projectHrLack/getProjectHrLack',
                    method: 'get',
                    params: query
                });
            },
            // 新增项目人力缺编信息
            addnaturalResources(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/projectHrLack/insertProjectHrLack',
                    method: 'post',
                    data
                });
            },
            // 编辑项目人力缺编信息
            editnaturalResources(data) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/projectHrLack/updateProjectHrLack',
                    method: 'put',
                    data
                });
            },
            // 删除项目人力缺编信息
            delnaturalResources(query) {
                return http({
                    baseDomain: basePath.bossapi.pmService,
                    url: '/projectHrLack/deleteProjectHrLack',
                    method: 'delete',
                    params: query
                });
            },
            // 部门资源负载-日视图查询
            getResourceLoadByDay(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource_load/resource_day_view_department',
                    method: 'post',
                    data
                });
            },
            // 部门资源负载-月视图查询
            getResourceLoadByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource_load/resource_month_view_department',
                    method: 'post',
                    data
                });
            },
            // 部门资源负载-日视图-任务列表查询
            getTaskListByDay(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource_load/resource_day_view_task_info_department',
                    method: 'post',
                    data
                });
            },
            // 部门资源负载-日视图-任务列表查询
            getTaskListByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource_load/resource_month_view_task_info_department',
                    method: 'post',
                    data
                });
            },
            // 资源概况-日视图
            getChartByDay(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource-overview/getResourceOverviewDay',
                    method: 'post',
                    data
                });
            },
            // 资源概况-月视图-职称
            getTitleChartByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource-overview/getResourceOverviewMonthByTitle',
                    method: 'post',
                    data
                });
            },
            // 资源概况-月视图-小组
            getGroupChartByMonth(data) {
                return http({
                    loading: false,
                    baseDomain: basePath.bossapi.pmService,
                    url: '/resource-overview/getResourceOverviewMonthByTeam',
                    method: 'post',
                    data
                });
            }
        }
    };

    return service;
};
