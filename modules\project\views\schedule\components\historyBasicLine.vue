<template>
    <div class="scroll">
        <el-table
            :data="tableData"
            border
            :header-cell-style="{ background: '#3370ff' }"
            :span-method="dynamicSpanMethod"
        >
            <!-- 固定列 -->
            <el-table-column
                v-for="column in fixedColumns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                header-align="center"
                :align="column.label === '里程碑' ? 'left' : 'center'"
                :show-overflow-tooltip="true"
                :min-width="column.label === '里程碑' ? 120 : 80"
            ></el-table-column>
            <!-- 动态列 -->
            <el-table-column
                v-for="column in dynamicColumns"
                :key="column.prop"
                :label="column.label"
                align="center"
            >
                <template slot-scope="scope">
                    <span>{{ scope.row[column.prop] }}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'historyBasicLineTable',
    props: {
        tableData: {
            type: Array,
            required: true
        },
        dynamicColumns: {
            type: Array,
            default: null
        },
        fixedColumns: {
            type: Array,
            default: null
        }
    },
    data() {
        return {};
    },
    methods: {
        dynamicSpanMethod({ row, column, rowIndex, columnIndex }) {
            if (rowIndex < 3 && columnIndex > 2 && columnIndex % 2 === 1) {
                return {
                    rowspan: 1,
                    colspan: 2
                };
            }
            if (rowIndex < 3 && columnIndex > 2 && columnIndex % 2 === 0) {
                return {
                    rowspan: 1,
                    colspan: 0
                };
            }
            return {
                rowspan: 1,
                colspan: 1
            };
        }
    }
};
</script>
<style lang="scss" scoped>
.scroll {
    height: calc(100vh - 150px);
    overflow: auto;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
}
/**改变边框颜色*/

::v-deep .el-table {
    border: 1px solid #8c8c8c !important;
}

::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
    border-left: none !important;
    border-bottom: none !important;
}
</style>
