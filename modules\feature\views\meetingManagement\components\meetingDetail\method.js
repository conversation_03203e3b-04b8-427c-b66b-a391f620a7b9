import { CONSTANTS } from '@/constants';

/**
 * 获取会议分享链接
 * @param {Object} scope - 作用域对象
 */
export const getShareLink = (scope) => {
    const { origin } = window.location;
    const text = `${origin}/#/meetingDetail/${scope.meetingId}`;
    if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
    } else {
        const textarea = document.createElement('textarea');
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = 'fixed';
        textarea.style.clip = 'rect(0 0 0 0)';
        textarea.style.top = '10px';
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand('copy', true);
        // 移除输入框
        document.body.removeChild(textarea);
    }
    scope.$message.success('链接已复制到剪贴板!');
};

/**
 * 将对应的数字转换为文本评价条件
 * @param {Object} data 评价会议效果数据
 * @param {Object} scope this
 */
export const handleQualityInfoData = (data, scope) => {
    data.forEach((i) => {
        if (i.evaluationScore === null) {
            i.evaluationScore = '';
        } else if (i.evaluationItems === '评委选择合理性') {
            i.evaluationScore = CONSTANTS.REVIEWER_REASONABLENESS.filter(
                (item) => i.evaluationScore === item.value
            )[0].label;
        } else if (i.evaluationItems === '预审质量') {
            i.evaluationScore = CONSTANTS.PREVIEW_QUALITY.filter(
                (item) => i.evaluationScore === item.value
            )[0].label;
        } else {
            i.evaluationScore = CONSTANTS.REVIEW_QUALITY.filter(
                (item) => i.evaluationScore === item.value
            )[0].label;
        }
    });
    scope.qualityInfoList = data;
};
/**
 * 线上评审意见合并单元格
 * @param {Array} dataList 线上评审意见
 * @returns {Function} 合并单元格
 */
export const createOnlineObjectSpanMethod = (dataList) => {
    return function ({ row, column, rowIndex, columnIndex }) {
        // 只在“项”这一列进行合并, 其他保持现状
        if (columnIndex !== 0) {
            return {
                rowspan: 1,
                colspan: 1
            };
        }

        // 如果当前行的“项”与上一行的“项”相同，则合并
        if (
            rowIndex > 0 &&
            row.reviewItem === dataList[rowIndex - 1].reviewItem
        ) {
            return {
                // 隐藏当前行的单元格
                rowspan: 0,
                colspan: 0
            };
        }

        // 计算当前“项”需要合并的行数
        let rowspan = 1;
        for (let i = rowIndex + 1; i < dataList.length; i++) {
            if (row.reviewItem === dataList[i].reviewItem) {
                rowspan += 1;
            } else {
                break;
            }
        }

        return {
            rowspan,
            colspan: 1
        };
    };
};

/**
 * 会议纪要合并单元格
 * @param {Array} dataList 会议纪要
 * @returns {Function} 合并单元格
 */
export const createMinutesObjectSpanMethod = (dataList) => {
    return function ({ row, column, rowIndex, columnIndex }) {
        // 只处理前三列的合并
        if (columnIndex > 2) {
            return {
                rowspan: 1,
                colspan: 1
            };
        }

        // 根据列确定要比较的字段
        const getCompareField = (colIndex) => {
            switch (colIndex) {
                case 0:
                    return 'problemItem';
                case 1:
                    return 'creatorAccount';
                case 2:
                    return 'meetingRequire';
                default:
                    return '';
            }
        };

        const compareField = getCompareField(columnIndex);

        if (rowIndex === 0) {
            let count = 1;
            for (let i = 1; i < dataList.length; i++) {
                if (dataList[i][compareField] === row[compareField]) {
                    count += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan: count,
                colspan: 1
            };
        }

        // 与上一行比较，判断是否需要合并
        const prevRow = dataList[rowIndex - 1];
        if (prevRow[compareField] === row[compareField]) {
            return {
                rowspan: 0,
                colspan: 0
            };
        }

        // 计算当前行需要合并的行数
        let count = 1;
        for (let i = rowIndex + 1; i < dataList.length; i++) {
            if (dataList[i][compareField] === row[compareField]) {
                count += 1;
            } else {
                break;
            }
        }

        return {
            rowspan: count,
            colspan: 1
        };
    };
};

/**
 * 会议效果评价合并单元格
 * @param {Array} dataList 会议效果评价
 * @returns {Function} 合并单元格
 */
export const createQualityReviewObjectSpanMethod = (dataList) => {
    return function ({ row, column, rowIndex, columnIndex }) {
        // 只在“评价项”这一列进行合并, 其他保持现状
        if (columnIndex !== 0) {
            return {
                rowspan: 1,
                colspan: 1
            };
        }
        // 如果当前行的“项”与上一行的“项”相同，则合并
        if (
            rowIndex > 0 &&
            row.evaluationItems === dataList[rowIndex - 1].evaluationItems
        ) {
            return {
                // 隐藏当前行的单元格
                rowspan: 0,
                colspan: 0
            };
        }
        // 计算当前“项”需要合并的行数
        let rowspan = 1;
        for (let i = rowIndex + 1; i < dataList.length; i++) {
            if (row.evaluationItems === dataList[i].evaluationItems) {
                rowspan += 1;
            } else {
                break;
            }
        }
        return {
            rowspan,
            colspan: 1
        };
    };
};
