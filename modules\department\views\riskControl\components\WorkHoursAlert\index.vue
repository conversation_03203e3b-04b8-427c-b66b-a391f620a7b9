<template>
    <div>
        <SnbcBaseTable
            class="table"
            ref="tableRef"
            :table-config="tableConfig"
            :shouldQueryList="shouldQueryList"
            @query="handleUnexpectedQuery"
        >
            <template #task="{ row }">
                <div class="flex">
                    <div class="icon-outer">
                        <svg-icon
                            v-if="row.transferFlag === '转入'"
                            icon-class="assign-responsible-person-import"
                            class="assign-people-icon"
                            @click="handleAssignIconClick(row)"
                        />
                        <svg-icon
                            v-else-if="row.transferFlag === '转出'"
                            icon-class="assign-responsible-person-export"
                            class="assign-people-icon"
                            @click="handleAssignIconClick(row)"
                        />
                        <div v-else class="block"></div>
                    </div>
                    <div class="tooltip">{{ row.taskName }}</div>
                </div>
            </template>
        </SnbcBaseTable>
        <AlertAssignPeopleDialog
            :visible.sync="dialogVisible"
            :rowData="rowData"
            :isInfo="isInfo"
            @submit-success="handleQuery"
        ></AlertAssignPeopleDialog>
    </div>
</template>

<script>
import { getTableConfig } from './workHoursTableConfig';
import SnbcBaseTable from 'snbcCommon/components/snbc-table/SnbcBaseTable.vue';
import { Message } from 'element-ui';
import AlertAssignPeopleDialog from './AlertAssignPeopleDialog';

export default {
    name: 'WorkHoursAlert',
    components: { SnbcBaseTable, AlertAssignPeopleDialog },
    props: {
        department: {
            type: String,
            default: ''
        },
        group: {
            type: String,
            default: ''
        },
        orgValue: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableConfig: getTableConfig(this),
            shouldQueryList: false,
            dialogVisible: false,
            // 每行数据
            rowData: {},
            // 是否只显示信息
            isInfo: false
        };
    },
    watch: {
        orgValue(newVal) {
            if (newVal) {
                this.shouldQueryList = true;
                this.handleQuery();
            }
        }
    },
    created() {
        // 获取程度选项
        this.getQueryOptions();
    },
    methods: {
        /**
         * 查询
         */
        handleQuery() {
            this.$nextTick(() => {
                this.$refs.tableRef.handleQuery();
            });
        },
        /**
         * 处理异常的点击
         */
        handleUnexpectedQuery() {
            if (!this.orgValue) {
                Message.closeAll();
                this.$message.warning('请选择部门或小组');
            }
        },
        /**
         * 查询前的钩子
         * @param {Object} params 参数
         */
        queryParamsHook(params) {
            const { calculateDateRange } = params;
            if (calculateDateRange && calculateDateRange.length > 0) {
                params.warningStartDate = calculateDateRange[0];
                params.warningEndDate = calculateDateRange[1];
            }
            params.orgCode = this.department;
            params.teamId = this.group;
        },
        /**
         * 获取下拉列表选项
         * @param {String} type 哪种option
         */
        async getQueryOptions(type) {
            try {
                const api =
                    this.$service.department.riskControl.getWaringLevelOptions;
                const res = await api();
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 处理选项
                const options = res.body.map((i) => {
                    return {
                        value: i.paramName,
                        label: i.paramName
                    };
                });
                this.tableConfig.queryConfig.items[2].elOptions = options;
            } catch (err) {
                console.error('Error:', err);
            }
        },
        /**
         * 导出excel
         */
        async handleExport() {
            if (!this.orgValue) {
                Message.closeAll();
                this.$message.warning('请选择部门或小组');
                return;
            }
            try {
                const exportList =
                    this.$service.department.riskControl.exportRiskControlExcel;
                const params = this.$tools.cloneDeep(
                    this.tableConfig.queryParams
                );
                params.orgCode = this.department;
                params.teamId = this.group;
                const stream = await exportList(params);
                this.$tools
                    .downloadExprotFile(stream, '工期工时预警数据', 'xlsx')
                    .catch((e) => {
                        this.$tools.message.err(e || '导出失败');
                    });
            } catch (err) {
                console.error(err);
            }
        },
        handleAssignClick(row) {
            this.rowData = row;
            this.isInfo = false;
            this.dialogVisible = true;
        },
        handleAssignIconClick(row) {
            this.rowData = row;
            this.isInfo = true;
            this.dialogVisible = true;
        }
    }
};
</script>

<style scoped lang="scss">
.flex {
    display: flex;
}
// 保证选择框宽度
::v-deep .el-select {
    width: 100%;
}
.tooltip {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
}
.icon-outer {
    height: 23px;
    height: 20px;
}
.assign-people-icon {
    width: 20px;
    height: 20px;
    margin-right: 5px;
    color: blue;
    &:hover {
        cursor: pointer;
        scale: calc(1.3);
    }
}
.block {
    width: 20px;
    height: 20px;
    margin-right: 5px;
}
</style>
