<template>
  <el-form-item v-bind="elFormItemAttrs">
    <el-input
      v-model="config.modelObj[config.modelKey]"
      v-bind="elInputAttrs"
    />
  </el-form-item>
</template>
<script>
export default {
    name: 'SnbcFormTextarea',
    props: {
        /**
         * SnbcFormTextarea组件配置
         */
        config: {
            type: Object,
            default() {
                return {
                    name: '',
                    modelObj: {},
                    modelKey: '',
                    elFormItemAttrs: {},
                    elInputAttrs: {}
                };
            }
        }
    },
    data() {
        return {
            // el-form-item组件默认属性设置
            defaultElFormItemAttrs: {},
            // el-input组件默认属性设置
            defaultElInputAttrs: {
                type: 'textarea',
                clearable: true
            }
        };
    },
    computed: {
        // el-form-item组件应用属性
        elFormItemAttrs() {
            return {
                prop: this.config.modelKey,
                ...this.defaultElFormItemAttrs,
                ...(this.config.elFormItemAttrs || {})
            };
        },
        // el-input组件应用属性
        elInputAttrs() {
            return {
                ...this.defaultElInputAttrs,
                placeholder: `请输入${this.config.name}`,
                ...(this.config.elInputAttrs || {})
            };
        }
    },
    methods: {}
};
</script>
